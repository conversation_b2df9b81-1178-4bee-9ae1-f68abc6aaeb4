<template>
  <div ref="page" v-loading="loading" class="enquiry-staff">
    <div ref="filters" class="filters">
      <el-row style="margin-bottom: 5px">
        <el-col :span="19" class="filter">
          <el-row>
            <el-col :span="6">
              <div class="enquiry-title">
                <i class="edac-icon edac-icon-office_clerk view-icon" />
                <span class="view-title">{{ $t('router.enquiryStaff') }}</span>
              </div>
            </el-col>
            <el-col :span="18">
              <el-form :inline="true" class="mini-form mini-width">
                <el-form-item>
                  <el-select
                    v-model="preferences.filters.type"
                    class="time-type"
                    style="width: 110px"
                    @change="onChangeTimeType"
                  >
                    <el-option :label="$t('enquiry.timeType.all')" value="A" />
                    <el-option :label="$t('enquiry.timeType.year')" value="Y" />
                    <el-option :label="$t('enquiry.timeType.to')" value="T" />
                    <el-option :label="$t('enquiry.timeType.month')" value="M" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="'YTM'.includes(preferences.filters.type)">
                  <el-select
                    v-model="preferences.filters.fy_id"
                    :disabled="preferences.filters.type === 'A'"
                    style="width: 100px"
                    class="year"
                    @change="onChangeYear"
                  >
                    <el-option
                      v-for="item in year_list"
                      :key="item.fy_id"
                      :label="item.fy_name"
                      :value="item.fy_id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="'TM'.includes(preferences.filters.type)">
                  <el-select
                    v-model="preferences.filters.pd_code"
                    :disabled="preferences.filters.type === 'A'"
                    class="month"
                    style="width: 100px"
                    @change="fetchData"
                  >
                    <el-option
                      v-for="item in month_list"
                      :key="item.pd_id"
                      :label="item.pd_name"
                      :value="item.pd_code"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="5">
          <div class="actions-icon">
            <i
              :title="$t('btnTitle.pageSetting')"
              class="edac-icon action-icon edac-icon-setting1"
              @click="onSetting"
            />
            <i
              v-if="hasPermission_Output"
              :title="$t('btnTitle.exportExcelPage')"
              class="edac-icon action-icon edac-icon-excel"
              @click="onExport('PAGE')"
            />
            <i
              v-if="hasPermission_Output"
              :title="$t('btnTitle.exportExcelAll')"
              class="edac-icon action-icon edac-icon-excel_add"
              @click="onExport('ALL')"
            />
          </div>
        </el-col>
      </el-row>
      <el-row class="staff-filter">
        <!-- <i
          v-if="preferences.filters.lock "
          class="edac-icon action-icon edac-icon-lock"
        />
        <i
          v-else
          class="edac-icon action-icon edac-icon-unlock"
        /> -->
        <!-- 會計科目 -->
        <SelectTree
          v-model="preferences.filters.st_code"
          :options="treeData"
          :props="{
            label: language === 'en' ? 'name_en' : 'name_cn',
            value: 'code',
            code: 'code',
            group_id: 'st_type_id',
            value_id: 'st_id',
            children: 'children',
          }"
          :group-id.sync="preferences.filters.st_type_id"
          :select-group.sync="preferences.filters.st_selectGroup"
          :show-all-option="false"
          style="width: 300px"
          @change="onSelectStaff"
        />
        <i :class="bClass" @click="changeACB" />
        <i :class="iClass" @click="changeACI" />
        <i :class="eClass" @click="changeACE" />
      </el-row>
    </div>
    <el-row class="table-row">
      <b-table
        ref="table"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :data="tableData"
        :show-actions="true"
        :actions-min-width="5"
        :auto-height="false"
        :show-summary="true"
        :sortable="true"
        :span-method="spanMethod"
        :summary-method="summaryMethod"
        :highlight-current-row="true"
        :sum-text="$t('enquiry.staff.label.sumText')"
        :height="tableHeight"
        :footer-method="footerMethod"
        :full-column="true"
        :last-column-auto-width="true"
        show-footer
        show-footer-overflow
        action-label=" "
        border
        @changeWidth="changeColumnWidth"
        @cell-click="onRowClick"
      >
        <template slot="columns">
          <vxe-table-column
            v-for="item in columnList"
            :key="item.ss_key"
            :align="item.alignment"
            :title="$t(langKey + $refs.table.column_label(item))"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :field="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :params="{ key: item.ss_key }"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)],
                  customDateFormat
                )
              }}</span>
            </template>
          </vxe-table-column>
        </template>
      </b-table>
    </el-row>

    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      :ss_code="ss_code"
      :has-theme="true"
      table-type="full-screen-without-first-field"
      @reloadStyleSheets="loadUserStyle(true)"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import FilterTime from './FilterTime'
import SelectTree from '@/components/SelectTree'
import BTable from '@/components/BTable/index'
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import mixinPermission from '@/views/mixins/permission'
import loadPreferences from '@/views/mixins/loadPreferences'
import enquiryComponentProxy from './enquiryComponentProxy'

// API
import { getStaffsTree } from '@/api/assistance/staff'
import { enquiryStaff } from '@/api/enquiry/staff'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { enquiryStaffExport } from '@/api/report/excel'

import dateUtil from '@/utils/date'
import { amountFormat, toDecimal } from '@/utils'
import { exportExcel } from '@/utils/excel'
import { listenTo } from '@/utils/resizeListen'
import {
  NATURE_CODE_G,
  NATURE_CODE_IE,
  NATURE_CODE_I,
  NATURE_CODE_E,
  NATURE_CODE_DR,
  NATURE_CODE_CR,
} from '@/constants/account'

export default {
  name: 'EnquiryStaff',
  components: {
    BTable,
    FilterTime,
    customStyle,
    SelectTree,
  },
  mixins: [loadCustomStyle, mixinPermission, loadPreferences, enquiryComponentProxy],
  data() {
    return {
      ss_code: 'ac.enquiry.staff',
      pf_code: 'ac.enquiry.staff',
      p_code: 'ac.enquiry.staff',
      p_isComponent: true,

      loading: false,
      tableColumns: [
        'vc_date', // 日期
        'vc_no', // 傳票編號
        'ac_code', // 會計賬號
        'ac_name_', // 賬目名稱
        'descr', // 內容描述
        'tx_type', // IE
        'amount_dr', // 借方金額
        'amount_cr', // 貸方金額
        'net_amount', // 淨金額
        'bal', // 結餘
        'vc_payee', // 收付款
        'ref', // 參考號
        'budget_code', // 預算名稱
        'vc_dept', // 部門編號
        'dept_name_', // 部門名稱
        'st_code', // 職員編號
        'staff_name_', // 職員名稱
        'vc_contra', // 對沖編號
      ],
      amountColumns: ['amount_dr', 'amount_cr', 'net_amount', 'bal'],
      langKey: 'enquiry.staff.label.',

      showDialog: false,

      tableData: [],
      footerData: [],

      preferences: {
        filters: {
          type: 'A',
          fy_code: '',
          fy_id: '',
          pd_code: '',
          pd_id: '',
          st_code: '',
          st_type_id: '',
          st_selectGroup: false,
          ac_B: 'Y',
          ac_I: 'Y',
          ac_E: 'Y',
        },
      },
      childPreferences: ['pd_code', 'pd_id'],
      customDateFormat: 'dd/MM/yy',
      lastDay: new Date(),
      firstDay: new Date(),
      treeData: [],
      year_list: [], // 會計週期（年份）
      month_list: [], // 月份

      // 窗口適應
      resizeListen: {},
      filtersHeight: 30,
      pageResizeListen: {},
      pageHeight: 500,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear', 'user_id']),
    allStaff() {
      return {
        active_year: '',
        children: null,
        code: '',
        level: 1,
        name_cn: this.$t('assistance.staff.label.parent_default'),
        name_en: this.$t('assistance.staff.label.parent_default'),
        parent_st_type_id: null,
        seq: 0,
        st_grade: null,
        st_type_id: null,
        staff_id: null,
        username: null,
      }
    },
    natureClass() {
      return this.preferences.filters.nature ? ' selected' : ''
    },
    iClass() {
      return (
        'edac-icon action-icon edac-icon-I' +
        (this.preferences.filters.ac_I === 'Y' ? '' : ' disable')
      )
    },
    eClass() {
      return (
        'edac-icon action-icon edac-icon-E' +
        (this.preferences.filters.ac_E === 'Y' ? '' : ' disable')
      )
    },
    bClass() {
      return (
        'edac-icon action-icon edac-icon-B' +
        (this.preferences.filters.ac_B === 'Y' ? '' : ' disable')
      )
    },
    columnList() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    tableHeight() {
      const h = this.pageHeight - this.filtersHeight - 25
      return h < 100 ? 100 : h
    },
  },
  watch: {
    language: {
      handler() {
        this.footerData = []
        this.$nextTick(() => {
          this.footerData = [this.summaryMethod({ columns: this.columnList, data: this.tableData })]
          this.$refs.table.updateFooter()
        })
      },
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.resizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
    this.initData()
  },
  methods: {
    onSetting() {
      this.showDialog = true
    },
    normalizer(node) {
      return {
        id: node.code,
        label:
          (node.st_type_id ? '' : node.code ? `[${node.code}]` : '') +
          (this.language === 'en' ? node.name_en : node.name_cn),
        children: node.children,
      }
    },
    changeFilterTime(data) {
      Object.assign(this.preferences.filters, data)
      if (this.loading) return
      this.$nextTick(() => {
        this.fetchData()
      })
    },
    fetchData() {
      this.loading = true
      const type = this.preferences.filters.type
      const fy_code = this.preferences.filters.fy_code || undefined
      const pd_code = this.preferences.filters.pd_code || undefined
      const st_code = this.preferences.filters.st_type_id
        ? undefined
        : this.preferences.filters.st_code
          ? this.preferences.filters.st_code
          : undefined
      const st_type_id = this.preferences.filters.st_type_id
        ? this.preferences.filters.st_type_id
        : undefined

      const ac_B = this.preferences.filters.ac_B
      const ac_I = this.preferences.filters.ac_I
      const ac_E = this.preferences.filters.ac_E

      searchTheDate({ fy_code, pd_code })
        .then(res => {
          this.lastDay = new Date(res.the_last_day)
          this.firstDay = new Date(res.the_first_day)
        })
        .then(() =>
          enquiryStaff({
            type,
            fy_code,
            pd_code,
            st_code,
            st_type_id,
            ac_B,
            ac_I,
            ac_E,
          }),
        )
        .then(res => {
          this.tableData = this.formatData(res)
          this.footerData = [this.summaryMethod({ columns: this.columnList, data: this.tableData })]
        })
        .finally(() => {
          this.loading = false
        })
    },
    initData() {
      this.loading = true
      let isError = false
      fetchYears().then(res => {
        this.year_list = res
      })
      this.loadUserPreference()
        .then(() => {
          const filters = this.preferences.filters
          return new Promise(resolve => {
            this.$nextTick(() => {
              if ('YTM'.includes(filters.type)) {
                if (this.year_list.length) {
                  const year = this.year_list.find(
                    i => i.fy_code === this.preferences.filters.fy_code,
                  )
                  if (year) {
                    resolve()
                    return
                  }
                  this.preferences.filters.fy_id = this.year_list[0].fy_id
                  this.preferences.filters.fy_code = this.year_list[0].fy_code
                } else {
                  isError = true
                }
              }
              resolve()
            })
          })
        })
        .then(() => {
          const filters = this.preferences.filters
          if ('TM'.includes(filters.type) && filters.fy_id) {
            return this.loadPeriods(filters.fy_id)
          } else {
            return Promise.resolve()
          }
        })
        .then(this.loadTree)
        .then(this.updateChildPreference)
        .then(() => {
          if (isError) {
            return Promise.reject()
          }
        })
        .then(this.fetchData)
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 載入會計科目樹
    loadTree() {
      return new Promise((resolve, reject) => {
        getStaffsTree()
          .then(res => {
            const top = Object.assign({}, this.allStaff)
            top.children = res
            this.treeData = [top]
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    onTreeSelect(node, instanceId) {
      this.preferences.filters.st_type_id = node.st_type_id
      this.preferences.filters.st_code = node.code
      this.fetchData()
    },
    onSelectStaff({ value, selectGroup, group_id, nature_code }) {
      this.preferences.filters.st_code = value
      this.preferences.filters.st_selectGroup = selectGroup
      this.preferences.filters.st_type_id = group_id
      this.fetchData()
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // if (rowIndex === 0) {
      //   switch (columnIndex) {
      //     case 0:
      //       return { rowspan: 1, colspan: 3 }
      //     case 1:
      //     case 2:
      //       return { rowspan: 0, colspan: 0 }
      //     default:
      //       break
      //   }
      // }
    },
    summaryMethod({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        switch (column.ss_key) {
          case 'descr':
            sums.push(this.$t('enquiry.staff.label.sumText'))
            return
          case 'amount_cr':
          case 'amount_dr':
          case 'net_amount': {
            let sum = 0
            data.forEach(item => {
              const n = toDecimal(Number(item.amount_cr))
              if (!isNaN(n) && Math.abs(n) !== 0) {
                sum = toDecimal(sum + n)
              }
            })
            sums.push(amountFormat(sum))
            return
          }
          case 'bal': {
            sums.push('')
            return
          }
          case 'vc_date': {
            sums.push(dateUtil.format(this.lastDay, 'dd/MM/yy'))
            break
          }
          default:
            sums.push('')
            return
        }
      })

      return sums
    },
    formatData({ result: data, totalBal }) {
      const newData = []
      let bal = toDecimal(totalBal) || 0
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        const newItem = Object.assign({}, item)
        // 淨金額
        let netAmount = 0
        switch (this.preferences.filters.nature_code) {
          case NATURE_CODE_DR:
          case NATURE_CODE_E:
            netAmount = toDecimal(Number(item.amount_dr) - Number(item.amount_cr))
            break
          case NATURE_CODE_CR:
          case NATURE_CODE_I:
            netAmount = toDecimal(Number(item.amount_cr) - Number(item.amount_dr))
            break
          case NATURE_CODE_G:
          case NATURE_CODE_IE:
            netAmount = toDecimal(Number(item.amount_cr))
            break
          default:
            netAmount = toDecimal(Number(item.amount_cr) - Number(item.amount_dr))
          // this.$message.error('系統錯誤，請聯繫管理員')
          // return []
        }

        // 結餘
        bal = toDecimal(bal + netAmount)
        newItem.bal = toDecimal(bal)
        newItem.net_amount = toDecimal(netAmount)
        newData.push(newItem)
      }
      // one
      // newData.splice(0, 0, {
      //
      //   vc_date: data[0] && data[0].vc_date,
      //   vc_no: '',
      //   ac_code: '',
      //   descr: '上年結餘',
      //   tx_type: '',
      //   amount_dr: '',
      //   amount_cr: '',
      //   bal: totalBal,
      //   net_amount: '',
      //   // 淨金額
      //   // 結餘
      //   vc_payee: '',
      //   // 收付款簡稱
      //   // 參考號
      //   budget_code: '',
      //   // 預算名稱
      //   st_code: '',
      //   // 職員名稱
      //   vc_contra: '',
      //   vc_dept: ''
      // })
      return newData
    },
    changeNature() {
      this.preferences.filters.nature = !this.preferences.filters.nature
    },
    onRowClick({ row }) {
      if (!row.lg_id) return
      let routerName = ''
      switch (row.vt_category) {
        case 'P':
          routerName = 'dailyPaymentView'
          break
        case 'C':
          routerName = 'dailyPettyCashView'
          break
        case 'R':
          routerName = 'dailyReceiptView'
          break
        case 'T':
          routerName = 'dailyBankTransferView'
          break
        case 'J':
          routerName = 'dailyJournalView'
          break
        default:
          this.$message.error(this.$t('message.pleaseContactTheAdministrator'))
          return
      }

      this.$router.replace({
        name: routerName,
        force: true,
        params: {
          action: 'view',
          isView: true,
          parentFyCode: row.fy_code,
          parentFyId: row.fy_id,
          parentPdCode: row.pd_code,
          editObject: row,
          vt_category: row.vt_category,
          lg_id: row.lg_id,
          time: new Date().getTime(),
        },
        query: {
          t: new Date().getTime(),
        },
      })
      // 取消其他頁面選中
      this.handleThisPageClickRow()
    },
    changeACI() {
      this.$set(this.preferences.filters, 'ac_I', this.preferences.filters.ac_I === 'Y' ? 'N' : 'Y')
      this.fetchData()
    },
    changeACE() {
      this.$set(this.preferences.filters, 'ac_E', this.preferences.filters.ac_E === 'Y' ? 'N' : 'Y')
      this.fetchData()
    },
    changeACB() {
      this.$set(this.preferences.filters, 'ac_B', this.preferences.filters.ac_B === 'Y' ? 'N' : 'Y')
      this.fetchData()
    },
    async onChangeTimeType(val) {
      const filters = this.preferences.filters
      switch (val) {
        case 'A':
          filters.fy_id = ''
          filters.fy_code = ''
          filters.pd_code = ''
          filters.pd_id = ''
          break
        case 'Y':
        case 'T':
        case 'M':
          {
            if (!this.year_list && this.year_list.length === 0) {
              filters.fy_id = ''
              filters.fy_code = ''
              filters.pd_code = ''
              filters.pd_id = ''
              break
            }
            const year = this.year_list.find(
              i => i.fy_id === (filters.fy_id ? filters.fy_id : this.currentYear.fy_id),
            )
            if (year) {
              filters.fy_id = year.fy_id
              filters.fy_code = year.fy_code
            } else {
              filters.fy_id = this.year_list[0].fy_id
              filters.fy_code = this.year_list[0].fy_code
            }
            if (val !== 'Y') {
              await this.loadPeriods(filters.fy_id)
            } else {
              filters.pd_code = ''
              filters.pd_id = ''
            }
          }
          break
      }
      this.fetchData()
    },
    loadPeriods(fy_id) {
      return new Promise((resolve, reject) => {
        const filters = this.preferences.filters
        getYear(filters.fy_id)
          .then(res => {
            const periods = res.periods
            this.month_list = periods

            if (periods.length > 0) {
              let month = periods.find(item => item.pd_code === filters.pd_code)
              if (!month) {
                month = periods[0]
              }
              filters.pd_code = month.pd_code
              filters.pd_id = month.pd_id
            } else {
              filters.pd_code = ''
              filters.pd_id = ''
              filters.type = 'Y'
            }
            resolve(periods)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onChangeYear(fy_id) {
      const year = this.year_list.find(i => i.fy_id === fy_id)
      if (year) {
        this.preferences.filters.fy_code = year.fy_code
        this.loadPeriods(fy_id).then(this.fetchData)
      } else {
        this.preferences.filters.fy_id = ''
        this.preferences.filters.fy_code = ''
      }
    },
    onExport(export_type) {
      const user_id = this.user_id
      const type = this.preferences.filters.type
      const fy_code = this.preferences.filters.fy_code || undefined
      const pd_code = this.preferences.filters.pd_code || undefined
      const st_code = this.preferences.filters.st_type_id
        ? undefined
        : this.preferences.filters.st_code
          ? this.preferences.filters.st_code
          : undefined
      const st_type_id = this.preferences.filters.st_type_id || undefined

      const ac_B = this.preferences.filters.ac_B
      const ac_I = this.preferences.filters.ac_I
      const ac_E = this.preferences.filters.ac_E
      this.loading = true
      enquiryStaffExport({
        user_id,
        export_type,
        type,
        fy_code,
        pd_code,
        st_code,
        st_type_id,
        ac_B,
        ac_I,
        ac_E,
      })
        .then(res => exportExcel(res))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    footerMethod() {
      return this.footerData
    },
  },
}
</script>

<style lang="scss" scoped>
$actionIconColor: #68afff;
$disableColor: #b9b6b6;
$settingColor: #b9b6b6;

.enquiry-staff {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: scroll;
  position: relative;
  min-height: 100px;

  .filter {
  }
  .staff-filter {
    margin-bottom: 5px;
    /deep/ {
      .el-input__inner,
      .el-input__icon {
        height: 25px !important;
        line-height: 25px !important;
      }
    }
    i.edac-icon {
      color: $actionIconColor;
      vertical-align: middle;
      font-size: 18px;

      &.disable {
        color: $disableColor;
      }
    }
    .tree-select {
      display: inline-block;
      position: relative;
      width: 300px;
      vertical-align: middle;
      line-height: 25px;
      height: 25px;
      /deep/ {
        .vue-treeselect__control {
          height: 23px;
          .vue-treeselect__value-container {
            height: 23px;
            .vue-treeselect__single-value {
              line-height: 23px;
              height: 23px;
            }
          }
        }
      }
    }
  }

  .view-icon {
    vertical-align: middle;
  }
  .edac-icon {
    color: $actionIconColor;
    font-size: 20px;
  }
  .edac-icon-setting1 {
    color: $settingColor;
  }
  .actions-icon {
    float: right;
    padding: 0 10px;
    .edac-icon {
      vertical-align: middle;
      line-height: 25px;
    }
  }
  .enquiry-title {
    line-height: 25px;
    display: inline-flex;
    .view-title {
      vertical-align: middle;
      padding: 0 5px;
      color: #606266;
    }
  }
  .table-row {
    min-height: 110px;
    flex: 1;
    /*height: calc(100% - 50px);*/

    /deep/ {
      .e-table {
        height: 100%;

        .el-table__body-wrapper {
          /*height: auto;*/
          /*height: calc(100% - 85px);*/
        }
        .el-table__empty-block {
          min-height: 30px;
        }
      }
    }
  }
}
</style>
