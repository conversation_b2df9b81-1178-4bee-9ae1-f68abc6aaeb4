import request from '@/utils/request'

/**
 * 新增購貨記錄
 * @param {string} fy_code 會計週期編號(如果是期初結餘,則不填)
 * @param {string} pi_no 發票編號
 * @param {string} date 日期
 * @param {string} pi_payee 供應商編號 (comp_code)
 * @param {string} chq_no 支票編號
 * @param {string} voucher_no 傳票編號
 * @param {integer} stocks_json 貨品數量價格,格式: [{"sk_code":"001","qty":"1","price":"1.00"},...]
 */
export function createPurchase({
  fy_code,
  pi_no,
  date,
  pi_payee,
  chq_no,
  voucher_no,
  stocks_json,
}) {
  return request({
    url: '/purchase-invoices/actions/create',
    method: 'post',
    data: {
      fy_code,
      pi_no,
      date,
      pi_payee,
      chq_no,
      voucher_no,
      stocks_json,
    },
  })
}

/**
 * 更新購貨記錄
 * @param {integer} purchase_invoice_id 購貨記錄id
 * @param {string} pi_no 發票編號
 * @param {string} date 日期
 * @param {string} pi_payee 供應商編號 (comp_code)
 * @param {string} chq_no 支票編號
 * @param {string} voucher_no 傳票編號
 * @param {integer} stocks_json 貨品數量價格,格式: [{"sk_code":"001","qty":"1","price":"1.00"},...]
 */
export function updatePurchase({
  purchase_invoice_id,
  pi_no,
  date,
  pi_payee,
  chq_no,
  voucher_no,
  stocks_json,
}) {
  return request({
    url: '/purchase-invoices/actions/update',
    method: 'post',
    data: {
      purchase_invoice_id,
      pi_no,
      date,
      pi_payee,
      chq_no,
      voucher_no,
      stocks_json,
    },
  })
}

/**
 * 刪除某購貨記錄詳情
 * @param {integer} purchase_invoice_id 購貨記錄id
 */
export function deletePurchase(purchase_invoice_id) {
  return request({
    url: '/purchase-invoices/actions/delete',
    method: 'post',
    data: {
      purchase_invoice_id,
    },
  })
}

/**
 * 獲取購貨記錄列表
 * @param {string} fy_code 會計週期編號(如果是期初結餘,則不填)
 */
export function fetchPurchases({ fy_code }) {
  return request({
    url: '/purchase-invoices',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 獲取某購貨記錄詳情
 * @param {integer} purchase_invoice_id 賬目類別id
 */
export function getPurchase(purchase_invoice_id) {
  return request({
    url: '/purchase-invoices/actions/inquire',
    method: 'get',
    params: {
      purchase_invoice_id,
    },
  })
}

export default { createPurchase, updatePurchase, deletePurchase, fetchPurchases, getPurchase }
