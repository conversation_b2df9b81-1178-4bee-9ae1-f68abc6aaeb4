import request from '@/utils/request'

/**
 * 下載當前備份
 * @return {Promise}
 */
export function backupsDownload() {
  return request({
    url: '/backups/actions/download',
    method: 'get',
    responseType: 'blob',
  })
}

/**
 * 還原備份文件
 * @return {Promise}
 */
export function backupsRestore(file) {
  const data = new FormData()
  data.append('backup_file', file)
  return request({
    url: '/backups/actions/restore',
    method: 'post',
    data,
  })
}
