<template>
  <div class="app-content">
    <RoleView system="BG" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import RoleView from '@/views/master/role'
import LRPane from '@/views/layout/components/pane.vue'
import customStyle from '@/views/customStyle/index.vue'
import { deleteRole, fetchRoles } from '@/api/master/role'

export default {
  name: 'MasterBudgetRoleIndex',
  components: {
    LRPane,
    customStyle,
    RoleView,
  },
  data() {
    return {
      showDialog: false,
      isInit: false,
      leftView: '',
      roles: [],
      editRole: null,
      system: 'BG',
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onAddRole() {
      this.editRole = null
      this.leftView = 'add'
    },
    onEditRole(scope) {
      this.editRole = scope.row
      this.leftView = 'edit'
    },
    onDeleteRole(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.role_name_en : scope.row.role_name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const role_id = scope.row.role_id
          return new Promise((resolve, reject) => {
            deleteRole(role_id)
              .then(res => {
                if (this.editRole && this.editRole.role_id === role_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchRoles(this.system)
        .then(res => {
          this.roles = res
        })
        .catch(() => {})
        .finally(() => {
          this.isInit = true
        })
    },
    onViewCancel(update) {
      this.editRole = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
