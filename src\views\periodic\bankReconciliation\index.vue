<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div style="height: 100%">
      <VBreadCrumb class="breadcrumb" />
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 銀行 -->
          <el-form-item :label="$t('filters.bank')">
            <el-select
              v-model="preferences.filters.selectedAcCode"
              class="bank"
              style="width: 250px"
            >
              <el-option
                v-for="item in bankList"
                :key="item.ac_code"
                :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
                :value="item.ac_code"
              />
            </el-select>
          </el-form-item>

          <!-- 時期 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              :value-format="dateValueFormat"-->
            <!--              type="daterange"-->
            <!--              style="width: 250px"-->
            <!--              @change="onChangeDateRange"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>

          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <!--              @change="onChangeYear"-->
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <!--              @change="onChangeMonth"-->
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button v-if="hasPermission_Edit" size="mini" type="primary" @click="onApplyReceipt">
              {{ $t('periodic.bankReconciliation.button.applyReceipt') }}
            </el-button>
            <el-button size="mini" type="primary" @click="onClearAll">
              {{ t('clearAll') }}
            </el-button>
          </el-form-item>
          <el-form-item>
            <!--<el-select-->
            <!--  v-model="com_vc_rdate"-->
            <!--  style="width: 130px;"-->
            <!--&gt;-->
            <!--  <el-option-->
            <!--    label="N/A"-->
            <!--    value="E"-->
            <!--  />-->
            <!--  <el-option-->
            <!--    v-for="item in dateList"-->
            <!--    :key="dateByFormat(item)"-->
            <!--    :label="dateByFormat(item,styles.dateFormat)"-->
            <!--    :value="dateByFormat(item)"-->
            <!--  />-->
            <!--  <el-option-->
            <!--    label="Future"-->
            <!--    value="F"-->
            <!--  />-->
            <!--  <el-option-->
            <!--    label="Unpaid"-->
            <!--    value="U"-->
            <!--  />-->
            <!--</el-select>-->
            <big-select v-model="com_vc_rdate" :list="dateList" style="width: 130px" />
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('PAGE')"
          />
        </div>
      </div>
      <div class="bank-reconciliation-table">
        <BTable
          ref="table"
          v-loading="!loading && tableLoading"
          :data="tableData"
          :style-columns="styleColumns"
          :amount-columns="amountColumns"
          :lang-key="langKey"
          :show-index="true"
          :show-actions="true"
          :actions-min-width="5"
          :show-checkbox="false"
          :default-top="230"
          :height="pageHeight - filtersHeight - summaryHeight - 55"
          :action-label="' '"
          border
          :sort-config="{ trigger: 'cell', orders: ['asc', 'desc', null] }"
          @changeWidth="changeColumnWidth"
        >
          <template slot="columns">
            <vxe-table-column
              v-for="item in filterVcRdateColumns"
              :key="item.ss_key"
              :title="$t(langKey + item.ss_key)"
              :align="item.alignment"
              :class-name="item.ss_key + ' mini-form'"
              :width="item.width"
              :property="$refs.table.column_property(item)"
              :column-key="item.ss_key"
              :params="{ key: item.ss_key }"
              :field="$refs.table.column_property(item)"
              :sort-method="$refs.table.sortAmountMethod(item.ss_key)"
              sortable
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <div v-if="item.ss_key === 'vc_method'">
                  <span>{{ t(scope.row.vc_method) }}</span>
                </div>
                <span v-else>{{
                  $refs.table.customFormatter(
                    item.ss_key,
                    scope.row[$refs.table.column_property(item)]
                  )
                }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column
              v-for="item in vcRdateColumns"
              :key="item.ss_key"
              :title="$t(langKey + item.ss_key)"
              :align="item.alignment"
              :class-name="item.ss_key + ' mini-form'"
              :width="item.width"
              :property="$refs.table.column_property(item)"
              :column-key="item.ss_key"
              :params="{ key: item.ss_key }"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <div v-if="item.ss_key === 'vc_rdate'">
                  <div v-if="hasPermission_Edit">
                    <!--<el-select-->
                    <!--  v-model="scope.row.vc_rdate"-->
                    <!--&gt;-->
                    <!--  <el-option-->
                    <!--    label="N/A"-->
                    <!--    value="E"-->
                    <!--  />-->
                    <!--  <el-option-->
                    <!--    v-for="item in dateList"-->
                    <!--    :key="dateByFormat(item)"-->
                    <!--    :label="dateByFormat(item,styles.dateFormat)"-->
                    <!--    :value="dateByFormat(item)"-->
                    <!--  />-->
                    <!--  <el-option-->
                    <!--    label="Future"-->
                    <!--    value="F"-->
                    <!--  />-->
                    <!--  <el-option-->
                    <!--    label="Unpaid"-->
                    <!--    value="U"-->
                    <!--  />-->
                    <!--</el-select>-->
                    <el-tooltip
                      popper-class="vc_rdate-tooltip"
                      effect="dark"
                      :content="scope.row.old_vc_rdate"
                      :disabled="scope.row.vc_rstatus !== 'F'"
                      placement="top"
                    >
                      <big-select
                        v-model="scope.row.vc_rdate"
                        :disabled="scope.row.vc_rstatus === 'F'"
                        :list="getDateList(scope.row.vc_date, scope.row.vc_rdate)"
                        class="select"
                        @change="onChangeRdate"
                      />
                    </el-tooltip>
                    <svg-icon
                      v-if="canSetDate(scope.row.vc_date)"
                      icon-class="left"
                      class="action-icon"
                      @click="onSetComDate(scope)"
                    />
                    <svg-icon v-else icon-class=" " class="action-icon" />
                  </div>
                  <span v-else style="padding: 0 10px">
                    <span v-if="scope.row.vc_rstatus === 'D'">
                      {{ $refs.table.customFormatter(item.ss_key, scope.row[item.ss_key]) }}
                    </span>
                    <span v-else-if="scope.row.vc_rstatus === 'U'">{{
                      $t('periodic.bankReconciliation.status.unpaid')
                    }}</span>
                    <span v-else-if="scope.row.vc_rstatus === 'F'">{{
                      $t('periodic.bankReconciliation.status.future')
                    }}</span>
                  </span>
                </div>
              </template>
            </vxe-table-column>
          </template>
          <template
            v-if="haveAi && scope && scope.row && scope.row.ai_document_file_ledger_id"
            slot="actions"
            slot-scope="{ scope }"
          >
            <div>
              <img
                v-if="scope.row.manual !== 'N'"
                class="picture-img"
                src="../../../assets/picture.png"
              >
              <img
                v-else
                class="picture-img"
                src="../../../assets/pictureA.png"
                @click="onViewImage(scope)"
              >
            </div>
          </template>
        </BTable>
      </div>
      <div ref="summary" class="summary">
        <el-form :inline="true" class="mini-form">
          <el-form-item :label="$t('periodic.bankReconciliation.label.ledgerBalance')">
            <el-input :value="summary.bf" readonly />
          </el-form-item>
          <span>{{ $t('periodic.balanceTransfer.symbol.add') }}</span>
          <el-form-item :label="$t('periodic.bankReconciliation.label.unpresentedPayment')">
            <el-input :value="summary.un_check_cr" readonly />
          </el-form-item>
          <span>{{ $t('periodic.balanceTransfer.symbol.subtract') }}</span>
          <el-form-item :label="$t('periodic.bankReconciliation.label.unpresentedReceipt')">
            <el-input :value="summary.un_check_dr" readonly />
          </el-form-item>
          <span>{{ $t('periodic.balanceTransfer.symbol.equals') }}</span>
          <el-form-item :label="$t('periodic.bankReconciliation.label.statementBalance')">
            <el-input :value="summary.end" readonly />
          </el-form-item>
          <span style="padding-left: 20px">
            <!-- <el-button v-if="hasPermission_Edit" size="mini" type="primary" @click="onEdit">{{ $t('button.update') }}</el-button> -->
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              size="mini"
              type="primary"
              @click="onPagePrint"
            >{{ $t('button.print') }}</el-button>
          </span>
        </el-form>
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
    <viewImage
      ref="viewImage"
      :view-image-data="viewImageData"
      :view-image-show="showImagePop"
      @close="showImagePop = false"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'
import { editBankLedgers, fetchBankLedgers } from '@/api/periodic/bankReconciliation'
import BTable from '@/components/BTable'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import handlePDF from './handlePDF'

import { amountFormat, dateIsValid } from '@/utils'

import ENumeric from '@/components/ENumeric'
import dateUtil from '@/utils/date'
import { bankReconciliationExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'
import { listenTo } from '@/utils/resizeListen'
import DateRange from '@/components/DateRange/index'
import BigSelect from '@/components/BigSelect/index'
import ViewImage from './components/viewPdf.vue'
import View from '../../daily/components/view.vue'
export default {
  name: 'BankReconciliationIndex',
  components: {
    BigSelect,
    DateRange,
    BTable,
    customStyle,
    ENumeric,
    ViewImage,
    VBreadCrumb,
    View,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      typeOption: {
        X: this.$t('daily.cheque.typeOption.type_X'),
        N: this.$t('daily.cheque.typeOption.type_N'),
        C: this.$t('daily.cheque.typeOption.type_C'),
      },
      bankList: [],
      chequeBooks: [],
      years: '',
      selectedYearId: '',
      tableData: [],
      summary: {
        bf: 0,
        un_check_cr: 0,
        un_check_dr: 0,
        end: 0,
      },
      data: [],
      dateList: [{ label: 'N/A', value: 'E' }],
      monthList: [],
      langKey: 'periodic.bankReconciliation.label.',
      tableColumns: [
        'vc_date',
        'vc_no',
        'descr',
        'vc_payee',
        'vc_method',
        'ref',
        'amount_dr',
        'amount_cr',
        'vc_rdate',
      ],
      amountColumns: ['amount_dr', 'amount_cr'],
      preferences: {
        filters: {
          selectedAcCode: '',
          selectedYearCode: '',
          selectedMonth: '',
          // date_range: [],
          begin_date: '',
          end_date: '',
        },
      },
      childPreferences: ['selectedMonth'],
      showYearPicker: false,
      periods: [],

      yearLastDate: new Date(),
      yearStartDate: new Date(),
      dateValueFormat: 'yyyy-MM-dd',
      // dataShowFormat: 'yyyy-MM-dd'

      com_vc_rdate: 'E',
      viewImageData: null,

      filtersResizeListen: {},
      filtersHeight: 40,
      summaryResizeListen: {},
      summaryHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,

      list: [],
      showLabel: '',
      showOption: false,
      value1: '',
      btnLoading: false,
      showImagePop: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'user_id', 'permissions', 'user_type']),
    columns() {
      const arr = this.styleColumns.filter(i => i.ss_key !== '_index')
      console.log(arr, 'arr')

      return arr
    },
    filterVcRdateColumns() {
      return this.columns.filter(i => i.ss_key !== 'vc_rdate')
    },
    vcRdateColumns() {
      return this.columns.filter(i => i.ss_key === 'vc_rdate')
    },
    haveAi() {
      const data = this.permissions['ac.periodic.bank_ai_reconciliation']
      return data || this.user_type === 'S'
    },
  },
  watch: {
    // selectedYearId: function(currentVal) {
    //   this.years.forEach(ele => {
    //     if (currentVal === ele.fy_id) {
    //       this.preferences.filters.selectedYearCode = ele.fy_code
    //     }
    //   })
    //   this.getMonth().then(this.reloadData)
    // },
    // 'preferences.filters.selectedAcCode': function(currentVal) {
    // this.getChequeBooks().then(() => this.reloadData())
    // }
  },
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
      this.summaryResizeListen = listenTo(this.$refs.summary, ({ width, height, ele }) => {
        this.summaryHeight = height
      })
    })
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({ ac_bank: 'C,S,F,P' }))
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.preferences.filters.selectedAcCode === '') {
            this.preferences.filters.selectedAcCode =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].ac_code : ''
          } else {
            if (this.bankList && this.bankList.length > 0) {
              let bool = false
              this.bankList.forEach(i => {
                if (i.ac_code === this.preferences.filters.selectedAcCode) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
              }
            }
          }
          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const ac_code = this.preferences.filters.selectedAcCode
        // let begin_date, end_date
        // const begin_date = this.preferences.filters.begin_date
        // const end_date = this.preferences.filters.end_date
        // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
        //   begin_date = '2019-04-01'
        //   end_date = '2019-04-30'
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        //   this.onChangeDateRange([begin_date, end_date])
        // } else {
        //   const b = this.preferences.filters.date_range[0]
        //   const e = this.preferences.filters.date_range[1]
        //   begin_date = b instanceof Date ? dateUtil.format(b, this.dateValueFormat) : b
        //   end_date = e instanceof Date ? dateUtil.format(e, this.dateValueFormat) : e
        // }
        // if (!begin_date || !end_date) {
        //   // reject()
        //   begin_date = '2019-04-01'
        //   end_date = '2019-04-30'
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        // }
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, 1)
        const lastDateObj = new Date(year, month + 1, 0)

        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        if (!this.preferences.filters.begin_date) {
          begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
          this.preferences.filters.begin_date = begin_date
        }
        if (!this.preferences.filters.end_date) {
          end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
          this.preferences.filters.end_date = end_date
        }
        // this.onChangeDateRange([begin_date, end_date])
        this.dateList = this.getAllDate(begin_date, end_date)
        this.loading = true
        this.tableLoading = true
        this.$forceUpdate()
        fetchBankLedgers({
          ac_code,
          begin_date,
          end_date,
        })
          .then(res => {
            this.tableData = this.formatData(res.ledgers)
            this.summary = {
              bf: amountFormat(res.summary.bf),
              un_check_cr: amountFormat(res.summary.un_check_cr),
              un_check_dr: amountFormat(res.summary.un_check_dr),
              end: amountFormat(res.summary.end),
            }
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
            this.tableLoading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      data.forEach(item => {
        const newItem = Object.assign({}, item)
        // switch (newItem.vc_rstatus) {
        //   case 'E':
        //     break
        //   case 'D':
        //     newItem.vc_rdate = newItem.vc_rstatus
        //     break
        // }
        newItem.old_vc_rstatus = newItem.vc_rstatus
        newItem.old_vc_rdate = newItem.vc_rdate
        if (newItem.vc_rstatus !== 'D') {
          console.log(newItem.vc_rstatus)
          newItem.vc_rdate = newItem.vc_rstatus
        }
        newData.push(newItem)
      })
      return newData
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    dateByFormat(date, format = 'yyyy-MM-dd') {
      return dateUtil.format(date, format)
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    getAllDate(begin, end) {
      const arr = []
      arr.push({
        label: 'N/A',
        value: 'E',
      })
      const ab = begin.split('-')
      const ae = end.split('-')
      const db = new Date()
      db.setUTCFullYear(ab[0], ab[1] - 1, ab[2])
      const de = new Date()
      de.setUTCFullYear(ae[0], ae[1] - 1, ae[2])
      const unixDb = db.getTime() - 24 * 60 * 60 * 1000
      const unixDe = de.getTime() - 24 * 60 * 60 * 1000
      for (let k = unixDb; k <= unixDe;) {
        k = k + 24 * 60 * 60 * 1000
        const d = new Date(parseInt(k))
        arr.push({
          label: this.dateByFormat(d, this.styles.dateFormat),
          value: this.dateByFormat(d),
        })
      }

      // arr.push({
      //   label: 'Future',
      //   value: 'F',
      // })
      arr.push({
        label: 'Unpaid',
        value: 'U',
      })
      return arr
    },
    onChangeDateRange(val) {
      // this.dateList = this.getAllDate(val[0], val[1])
    },
    onEdit() {
      console.log(this.tableData)

      const postDate = this.tableData.map(item => {
        let vc_rstatus
        let vc_rdate = item.vc_rdate

        switch (item.vc_rdate) {
          case 'F':
          case 'E':
            vc_rstatus = item.vc_rdate
            break
          case 'U':
            vc_rstatus = item.vc_rdate
            if (vc_rstatus !== item.old_vc_rstatus) {
              console.log(vc_rstatus, item.old_vc_rstatus, this.preferences.filters.end_date, 9999)
              vc_rdate = this.preferences.filters.end_date
            } else {
              vc_rdate = item.old_vc_rdate
            }
            break
          default:
            vc_rstatus = 'D'
            break
        }
        const fy_code = item.fy_code
        const lg_id = item.lg_id

        return {
          fy_code,
          lg_id,
          vc_rstatus,
          vc_rdate,
        }
      })
      console.log('postDate', postDate)

      editBankLedgers(postDate).then(res => {
        console.log(res)
        this.reloadData()
      })
    },
    onApplyReceipt() {
      this.tableData = this.tableData.map(item => {
        const newItem = Object.assign({}, item)

        if (
          newItem.amount_dr &&
          typeof Number(newItem.amount_dr) === 'number' &&
          (!newItem.vc_rdate || newItem.vc_rdate === 'E')
        ) {
          newItem.vc_rdate = newItem.vc_date
        }
        return newItem
      })
    },
    onChangeMonth(val) {
      if (val === '') {
        // 全年
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateValueFormat)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateValueFormat)
        // this.preferences.filters.date_range = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      // beginDate
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, this.dateValueFormat)

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, this.dateValueFormat)
      // this.preferences.filters.date_range = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
      this.onChangeDateRange([beginDate, endDate])
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateValueFormat,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateValueFormat,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const ac_code = this.preferences.filters.selectedAcCode

      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, 1)
      const lastDateObj = new Date(year, month + 1, 0)

      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!this.preferences.filters.begin_date) {
        begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        this.preferences.filters.begin_date = begin_date
      }
      if (!this.preferences.filters.end_date) {
        end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        this.preferences.filters.end_date = end_date
      }

      // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
      //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
      //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      // } else {
      //   begin_date = this.preferences.filters.date_range[0]
      //   end_date = this.preferences.filters.date_range[1]
      // }
      // if (!begin_date || !end_date) {
      //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
      //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      // }
      // if (!user_id || !begin_date || !end_date) {
      //   // this.$message.error('')
      //   return
      // }
      this.loading = true
      bankReconciliationExport({ user_id, export_type, ac_code, begin_date, end_date })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onSetComDate(scope) {
      scope.row.vc_rdate = this.com_vc_rdate
      this.onEdit()
    },
    getDateList(vc_date, vc_rdate) {
      const data = this.dateList.filter(i => {
        if ('EFU'.includes(i.value)) {
          return true
        }
        const iDate = new Date(i.value)
        const vDate = new Date(vc_date)
        return iDate >= vDate
      })
      if (vc_rdate === 'F') {
        data.push({
          label: 'Future',
          value: 'F',
        })
      }
      return data
    },
    canSetDate(vc_date) {
      const iDate = new Date(vc_date)
      let vDate = this.com_vc_rdate
      if ('EFU'.includes(vDate)) {
        return true
      }
      if (!vDate) {
        return true
      }
      vDate = new Date(this.com_vc_rdate)
      if (dateIsValid(vDate)) {
        return iDate <= vDate
      } else {
        return false
      }
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
    onViewImage(scope) {
      this.viewImageData = scope.row
      this.showImagePop = true
    },
    onChangeRdate() {
      this.onEdit()
    },
    onClearAll() {
      this.$confirm(`${this.t('isClearAll')}` + '?', this.$t('confirm.warningTitle'), {
        confirmButtonText: this.$t('confirm.confirmButtonText'),
        cancelButtonText: this.$t('confirm.cancelButtonText'),
        type: 'warning',
      }).then(() => {
        const that = this
        const paramsArr = this.tableData
          .filter(item => item.vc_rdate !== 'E' && item.vc_rdate)
          .map(item => {
            console.log(item, 'item')

            const params = {
              fy_code: item.fy_code,
              lg_id: item.lg_id,
              vc_rstatus: 'E',
              vc_rdate: '',
            }
            return params
          })
        console.log(paramsArr, 'paramsArr')
        editBankLedgers(paramsArr)
          .then(res => {
            that.$message.success(this.$t('message.success'))
            this.lastAutoSelectData = []
            that.reloadData()
          })
          .catch(err => {
            this.$message.error(err.message)
          })
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
  }
  .bank-reconciliation-table {
    /*height: calc(100vh - 270px);*/

    .b-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .vxe-cell {
            max-height: unset;
            height: 27px;
            line-height: 25px;
          }
          .cell,
          .vxe-cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .select {
              width: calc(100% - 30px);
              min-width: 80px;
            }
            svg {
              width: 19px;
              height: 19px;
              vertical-align: middle;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
  .summary {
    margin: 5px 0;
    /deep/ {
      .el-form-item.el-form-item--medium {
        margin-right: 0;
      }
      .el-input {
        width: 120px;
        input {
          text-align: right;
        }
      }

      span {
        line-height: 25px !important;
        height: 25px !important;
      }
      .el-button {
        padding: 0px 15px;
        vertical-align: middle;
      }
    }
  }
}
.el-icon-picture {
  font-size: 14px;
}
.picture-img {
  width: 14px;
  height: 14px;
  vertical-align: middle;
  cursor: pointer;
}
</style>
<style lang="scss">
.vc_rdate-tooltip {
  transform: translateY(12px) !important;
}
</style>
