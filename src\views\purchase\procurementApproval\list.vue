<template>
  <div class="page-procurement-approval-list">
    <div ref="header" class="page-header">
      <div class="filters">
        <el-form :inline="true" class="mini-form">
          <el-form-item>
            <el-radio-group
              v-model="preferences.filters.list"
              size="mini"
              style="display: flex; position: relative"
              @change="onChangeList"
            >
              <el-radio-button label="unhandled">
                {{ t('unhandled') }}
              </el-radio-button>
              <el-radio-button label="handled">
                {{ t('handled') }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="t('year')">
            <el-select
              v-model="preferences.filters.year"
              class="input input-select"
              @change="fetchData"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="preferences.filters.list === 'unhandled'" :label="t('level')">
            <!--            <el-input class="input input-select"/>-->
            <el-select
              v-model="preferences.filters.levelCode"
              class="input input-select"
              clearable
              @change="fetchData"
            >
              <el-option value="" label="-" />
              <!--              <el-option-->
              <!--                v-for="item in categoryList"-->
              <!--                :key="item.value"-->
              <!--                :label="item.label"-->
              <!--                :value="item.value"-->
              <!--              />-->
              <el-option
                v-for="item in levels"
                :key="item.pro_level_id"
                :label="item[isEnglish ? 'pro_level_name_en' : 'pro_level_name_cn']"
                :value="item.pro_level_code"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('remark')">
            <el-input
              v-model="preferences.filters.title_or_remark"
              class="input input-select"
              clearable
              @keyup.native.enter="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="actions-icon">
        <!--        <i class="edac-icon action-icon edac-icon-add1" @click="onAdd"/>-->
        <i class="edac-icon action-icon edac-icon-setting1" @click="onSetting" />
      </div>
    </div>
    <div :style="tableHeight" class="content">
      <ETable
        v-if="preferences.filters.list === 'unhandled'"
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-actions="true"
        :actions-min-width="5"
        :show-checkbox="false"
        :default-top="230"
        :filter-class-function="filterClassFunction"
        :auto-height="false"
        height="100%"
        action-label=" "
        border
        @changeWidth="changeColumnWidth"
      >
        <template slot="columns">
          <el-table-column
            v-for="item in columns"
            :key="item.ss_key"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :class-name="item.ss_key + ' mini-form'"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :column-key="item.ss_key"
          >
            <template slot-scope="scope">
              <span v-if="item.ss_key === 'step'">
                {{ stepObj[scope.row.step] }}
              </span>
              <span v-else>
                {{
                  $refs.table.customFormatter(
                    item.ss_key,
                    scope.row[$refs.table.column_property(item)]
                  )
                }}
              </span>
            </template>
          </el-table-column>
        </template>
        <template slot="actions" slot-scope="{ scope }">
          <div class="operation_icon">
            <i class="el-icon-view" @click="onReview(scope.row)" />
            <!--            <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope.row)"/>-->
            <!--            <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope.row)"/>-->
          </div>
        </template>
      </ETable>
      <ETable
        v-else
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-actions="true"
        :actions-min-width="5"
        :show-checkbox="false"
        :default-top="230"
        :filter-class-function="filterClassFunction"
        :auto-height="false"
        height="100%"
        action-label=" "
        border
        @changeWidth="changeColumnWidth"
      >
        <template slot="columns">
          <el-table-column
            v-for="item in columns"
            :key="item.ss_key"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :class-name="item.ss_key + ' mini-form'"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :column-key="item.ss_key"
          >
            <template slot-scope="scope">
              <span v-if="item.ss_key === 'step'">
                {{ stepObj[scope.row.step] }}
              </span>
              <span v-else-if="item.ss_key === 'handler_status'">
                <el-tag :type="tagNames[scope.row.handler_status]" size="mini">
                  {{ statusFormat(scope.row.handler_status) }}
                </el-tag>
              </span>
              <span v-else-if="item.ss_key === 'handle_time'">
                <span>{{ getDate(scope.row.handle_time) }}</span>
              </span>
              <span v-else>
                <span v-if="item.ss_key === 'apply_status'">
                  <el-tag :type="ATagNames[scope.row.apply_status]" size="mini">
                    {{ applyStatusList[scope.row.apply_status] }}
                  </el-tag>
                </span>
                <span v-else>
                  {{
                    $refs.table.customFormatter(
                      item.ss_key,
                      scope.row[$refs.table.column_property(item)]
                    )
                  }}
                </span>
              </span>
            </template>
          </el-table-column>
        </template>
        <template slot="actions" slot-scope="{ scope }">
          <div class="operation_icon">
            <i class="el-icon-view" @click="onView(scope.row)" />
            <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope.row)" />
            <!--            <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope.row)"/>-->
          </div>
        </template>
      </ETable>
      <div class="pagination">
        <el-pagination
          :current-page.sync="page"
          :page-sizes="[30, 50, 100]"
          :page-size.sync="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </div>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      :ss_code="ss_code"
      table-type="full-screen-without-first-field"
      @reloadStyleSheets="loadUserStyle"
    />
    <customStyle
      :dialog-visible.sync="showDialog2"
      :columns="tableColumns2"
      :lang-key="langKey2"
      :title="$t('style.defaultTitle')"
      :ss_code="ss_code"
      table-type="full-screen-without-first-field"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears } from '@/api/master/years'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import mixinPermission from '@/views/mixins/permission'
import { fetchProcurementLevels } from '@/api/purchase/procurementLevels'
import ETable from '@/components/ETable/index'
import {
  fetchProcurementApplicationshandledList,
  fetchProcurementApplicationsUnhandledList,
} from '@/api/purchase/procurementApplications'
import BudgetSelectTreeVue from '@/views/budget/common/BudgetSelectTreeVue'
import { listenTo } from '@/utils/resizeListen'

let groupCount = 1

export default {
  name: 'ProcurementApprovalList',
  components: { BudgetSelectTreeVue, ETable, customStyle },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission],
  props: {
    isBack: {
      type: [String],
      default: '',
    },
  },
  data() {
    return {
      loading: true,
      tableLoading: true,
      showDialog: false,
      showDialog2: false,
      langKey: 'purchase.daily.procurementApproval.',
      langKey2: 'purchase.daily.procurementApproval.',
      tableData: [],
      preferences: {
        filters: {
          year: '',
          beginDate: '',
          endDate: '',
          budgetId: '',
          budgetCode: '',
          levelCode: '',
          status: '',
          staff: '',
          pro_no: '',
          title_or_remark: '',
          list: 'unhandled',
          ss_code: 'pc.daily.approval.unhandled',
        },
      },
      page: 1,
      pageSize: 30,
      total: 0,

      years: [],
      staffs: [],
      levels: [],
      budgetGroupList: [],
      tableColumns: [
        'step',
        'g_budget_name_',
        'pro_no',
        'pro_title',
        'apply_date',
        'invite_date',
        'applicant_name_',
        'handler_name_',
      ],
      tableColumns2: [
        'step',
        'g_budget_name_',
        'pro_no',
        'pro_title',
        'apply_date',
        'invite_date',
        'applicant_name_',
        'handler_name_',
        'handler_status',
        'apply_status',
        'handle_time',
        'comment',
      ],
      amountColumns: [],

      resizeListen: {},
      headerHeight: 70,
    }
  },
  computed: {
    ...mapGetters([]),
    statusList() {
      return [
        { value: 'W', label: this.$t('purchase.approvalStatus.W') },
        { value: 'A', label: this.$t('purchase.approvalStatus.A') },
        { value: 'B', label: this.$t('purchase.approvalStatus.B') },
        { value: 'C', label: this.$t('purchase.approvalStatus.C') },
        { value: 'R', label: this.$t('purchase.approvalStatus.R') },
      ]
    },
    applyStatusList() {
      return {
        A: this.$t('purchase.status.A'),
        G: this.$t('purchase.status.G'),
        GR: this.$t('purchase.status.GR'),
        GA: this.$t('purchase.status.GA'),
        I: this.$t('purchase.status.I'),
        K: this.$t('purchase.status.K'),
        M: this.$t('purchase.status.M'),
        MR: this.$t('purchase.status.MR'),
        MI: this.$t('purchase.status.MI'),
        MA: this.$t('purchase.status.MA'),
        O: this.$t('purchase.status.O'),
      }
    },
    tagNames() {
      return {
        W: '',
        A: 'success',
        B: 'warning',
        C: 'warning',
        R: 'danger',
      }
    },
    ATagNames() {
      return {
        A: 'info',
        G: 'info',
        GR: 'danger',
        GA: 'warning',
        I: 'info',
        K: 'info',
        M: 'info',
        MR: 'danger',
        MI: 'warning',
        MA: 'warning',
        O: 'success',
      }
    },
    stepObj() {
      return {
        R: this.$t('purchase.reviewStep.R'),
        A: this.$t('purchase.reviewStep.A'),
      }
    },
    categoryList() {
      return [
        { value: 'P', label: this.$t('purchase.category.P') },
        { value: 'S', label: this.$t('purchase.category.S') },
      ]
    },
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    columns2() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    isUnList() {
      return this.preferences.filters.list === 'unhandled'
    },
    ss_code() {
      return this.preferences.filters.ss_code
    },
    tableHeight() {
      return `height: calc(100% - ${this.headerHeight}px - 30px);`
    },
  },
  created() {
    this.saveUserLastPage()
    this.init()
  },
  mounted() {
    this.$nextTick(() => {
      this.resizeListen = listenTo(this.$refs.header, ({ width, height, ele }) => {
        this.headerHeight = height + 10
      })
    })
  },
  methods: {
    t(key) {
      return this.$t(this.langKey + key)
    },
    async init() {
      try {
        this.years = await fetchYears()
        await this.loadUserPreference()
        if (!this.isBack) {
          this.preferences.filters.list = 'unhandled'
        }
        if (!this.preferences.filters.year) {
          if (this.years.length > 0) {
            this.preferences.filters.year = this.years[0].fy_code
          } else {
            return
          }
        }
        this.onChangeList(this.preferences.filters.list)
        // 獲取年度
        // const fy_code = this.preferences.filters.year
        // const st_grade = 'S'
        // 獲取職員
        // this.staffs = await fetchStaffs({ fy_code, st_grade })
        // 獲取採購等級
        this.levels = await fetchProcurementLevels()

        // const budget_types = 'F,G'
        // this.budgetGroupList = await fetchBudgetGroupList({ fy_code, budget_types })
      } catch (e) {
        console.log(e)
        return
      }
      try {
        //
        await this.fetchData()
      } catch (e) {
        return
      }
    },
    async fetchData() {
      try {
        const filters = this.preferences.filters
        const fy_code = filters.year
        const pro_no = undefined
        const pro_level_code = filters.levelCode
        const title_or_remark = filters.title_or_remark
        const page_size = this.pageSize
        const page = this.page
        const handle_staff_id = this.isAdmin ? undefined : this.userId

        let api = fetchProcurementApplicationsUnhandledList
        if (!this.isUnList) {
          api = fetchProcurementApplicationshandledList
        }
        const res = await api({
          fy_code,
          pro_no,
          pro_level_code,
          title_or_remark,
          handle_staff_id,
          page_size,
          page,
        })
        this.tableData = res.data
        this.total = res.total
        console.log(res)
      } catch (e) {
        console.log(e)
      }
    },

    onSetting() {
      if (this.isUnList) {
        this.showDialog = true
      } else {
        this.showDialog2 = true
      }
    },
    filterClassFunction(row) {
      row.row.tx_num === 1 ? groupCount++ : ''
      if (groupCount % 2 === 0) {
        return 'table-stripe'
      }
    },
    onChangeBudgetGroup(budget) {
      console.log(budget)
    },
    budgetDisabledMethod(item) {
      return false
    },
    onAdd() {
      this.$router.push({
        name: 'purchaseDailyProcurementAdd',
        params: {
          view: 'add',
        },
      })
    },
    onReview(row) {
      this.$router.push({
        name: 'purchaseDailyApprovalView',
        // path: '/daily/procurement/view/' + row.pro_application_id,
        params: {
          view: 'review',
          id: row.pro_application_id,
          handleId: row.pro_apply_handler_id,
        },
      })
    },
    onView(row) {
      this.$router.push({
        name: 'purchaseDailyApprovalView',
        // path: '/daily/procurement/view/' + row.pro_application_id,
        params: {
          view: 'view',
          id: row.pro_application_id,
          handleId: row.pro_apply_handler_id,
        },
      })
    },
    onEdit(row) {
      this.$router.push({
        name: 'purchaseDailyProcurementEdit',
        // path: '/daily/procurement/edit/' + row.pro_application_id,
        params: {
          view: 'edit',
          id: row.pro_application_id,
        },
      })
    },
    onDelete(row) {
      // this.$confirm(
      //   `${this.$t('confirm.deleteConfirm')}: ${row.pro_title} ?`,
      //   this.$t('confirm.warningTitle'),
      //   {
      //     confirmButtonText: this.$t('confirm.confirmButtonText'),
      //     cancelButtonText: this.$t('confirm.cancelButtonText'),
      //     type: 'warning'
      //   })
      //   .then(() => {
      //     const paper_set_id = scope.row.paper_set_id
      //     return new Promise((resolve, reject) => {
      //       delete(paper_set_id).then(res => {
      //         if (this.editUser && this.editUser.paper_set_id === paper_set_id) {
      //           this.onViewCancel()
      //         }
      //         resolve(res)
      //       }).catch(err => {
      //         reject(err)
      //       })
      //     })
      //   })
      //   .then(() => {
      //     this.fetchData()
      //     this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
      //   })
    },
    statusFormat(status) {
      const s = this.statusList.find(item => item.value === status)
      if (s) {
        return s.label
      } else {
        return status
      }
    },
    async onChangeList(val) {
      if (val === 'unhandled') {
        this.preferences.filters.ss_code = 'pc.daily.approval.unhandled'
      } else {
        this.preferences.filters.ss_code = 'pc.daily.approval.handled'
      }
      await this.loadUserStyle()
      this.$nextTick(() => {
        this.fetchData()
      })
    },
    getDate(str) {
      if (!str) return ''
      const arr = str.split(' ')
      if (arr.length > 1) {
        return arr[0]
      }
      return str
    },
  },
}
</script>

<style lang="scss" scoped>
.page-procurement-approval-list {
  height: 100%;
}
.mini-form {
  .input {
    width: 150px;
    &.input-select {
      width: 150px;
    }
    &.input-date {
      width: 130px;
    }
  }
}
.actions-icon {
  display: inline-block;
  vertical-align: bottom;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
.page-header {
  .filters {
    display: inline-block;
  }
}
.content {
  .pagination {
    padding: 5px 10px;
    text-align: right;
  }
}

.operation_icon i {
  margin: 0 10px;
  cursor: pointer;
}
</style>
