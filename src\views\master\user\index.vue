<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <!--<div slot="pane-right-filters"/>-->
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="icon add"
            @click="onAddUser"
          >
            <svg-icon icon-class="add" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :style-columns="styleColumns"
          :lang-key="langKey"
          :data="users"
          border
          @changeWidth="changeColumnWidth"
        >
          <!--          <template slot="columns">-->
          <!--            <el-table-column-->
          <!--              v-for="item in styleColumns"-->
          <!--              :key="item.ss_id"-->
          <!--              :label="$t(langKey + item.ss_key)"-->
          <!--              :align="item.alignment"-->
          <!--              :width="item.width"-->
          <!--              :property="item.ss_key"-->
          <!--              :column-key="item.ss_key"-->
          <!--            />-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditUser(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDeleteUser(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-user="editUser"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import addPage from './add'
import { deleteUser, fetchUsers } from '@/api/master/users'
import { mapGetters } from 'vuex'

import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import mixinPermission from '@/views/mixins/permission'

export default {
  name: 'MasterUserIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    addPage,
  },
  mixins: [mixinPermission, loadCustomStyle],
  data() {
    return {
      leftView: '',
      users: [],
      editUser: null,
      langKey: 'master.user.label.',
      tableColumns: ['name_cn', 'name_en', 'username', 'password'],
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      // console.log(row)
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onAddUser() {
      this.editUser = null
      this.leftView = 'add'
    },
    onEditUser(scope) {
      this.editUser = scope.row
      this.leftView = 'edit'
    },
    onDeleteUser(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const user_id = scope.row.user_id
          return new Promise((resolve, reject) => {
            deleteUser(user_id)
              .then(res => {
                if (this.editUser && this.editUser.user_id === user_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchUsers()
        .then(res => {
          this.users = res
        })
        .catch(() => {})
    },
    onViewCancel(update) {
      this.editUser = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
