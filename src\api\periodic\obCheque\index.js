import request from '@/utils/request'

/**
 * 新增期初支票
 * @param {string} vc_date 期初支票日期(只能選整個系統的會計週期第一天之前的日子)
 * @param {string} account_id 銀行會計賬號id  (ac_bank=C或者F)
 * @param {string} vc_payee 收款人
 * @param {string} amount 金額(可以是負數,不能為0)
 * @param {string} ref 支票號碼
 * @return {Promise}
 */
export function createObCheque({ vc_date, account_id, vc_payee, amount, ref }) {
  return request({
    url: '/cycle/ob-cheques/actions/create',
    method: 'post',
    data: {
      vc_date,
      account_id,
      vc_payee,
      amount,
      ref,
    },
  })
}

/**
 * 更新期初支票
 * @param {string} lg_id lg_id
 * @param {string} vc_date 期初支票日期(只能選整個系統的會計週期第一天之前的日子)
 * @param {string} account_id 銀行會計賬號id  (ac_bank=C或者F)
 * @param {string} vc_payee 收款人
 * @param {string} amount 金額(可以是負數,不能為0)
 * @param {string} ref 支票號碼
 * @return {Promise}
 */
export function editObCheque({ lg_id, vc_date, account_id, vc_payee, amount, ref }) {
  return request({
    url: '/cycle/ob-cheques/actions/update',
    method: 'post',
    data: {
      lg_id,
      vc_date,
      account_id,
      vc_payee,
      amount,
      ref,
    },
  })
}

/**
 * 刪除期初支票
 * @param {string} lg_id lg_id
 * @return {Promise}
 */
export function deleteObCheque(lg_id) {
  return request({
    url: '/cycle/ob-cheques/actions/delete',
    method: 'post',
    data: {
      lg_id,
    },
  })
}

/**
 * 返回期初支票列表
 * @param {string} account_id 銀行會計賬號id  (ac_bank=C或者F)
 * @return {Promise}
 */
export function fetchObCheques(account_id) {
  return request({
    url: '/cycle/ob-cheques',
    method: 'get',
    params: {
      account_id,
    },
  })
}

/**
 * 返回期初支票詳情
 * @param {string} lg_id lg_id
 * @return {Promise}
 */
export function getObCheque(lg_id) {
  return request({
    url: '/cycle/ob-cheques/actions/inquire',
    method: 'get',
    params: {
      lg_id,
    },
  })
}

export default {
  fetchObCheques,
  createObCheque,
  editObCheque,
  deleteObCheque,
  getObCheque,
}
