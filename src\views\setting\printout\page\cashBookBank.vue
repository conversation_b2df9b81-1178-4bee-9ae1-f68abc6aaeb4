<template>
  <div v-if="!hookVisible">
    <right v-loading="loading">
      <el-main slot="content">
        <el-form ref="form" :model="form" class="form mini-form" label-width="120px">
          <el-row>
            <el-col :lg="14" :md="24">
              <!-- 樣式 -->
              <el-form-item :label="$t('setting.printout.label.style')">
                <el-select
                  v-model="psg_code"
                  :placeholder="$t('setting.printout.label.style')"
                  @change="onFetch(0)"
                >
                  <el-option
                    v-for="item in styleList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <!-- 樣式名稱 -->
              <el-form-item
                :label="$t('setting.printout.label.styleName')"
                :rules="rules"
                prop="style_name"
              >
                <el-input v-model="form.style_name" />
              </el-form-item>
              <!-- 紙樣式 -->
              <pageSizeForm
                :page-style.sync="form.page_style"
                :page-width.sync="form.page_width"
                :page-height.sync="form.page_height"
                :page-orientation.sync="form.page_orientation"
              />
              <!-- 邊界 -->
              <margin-form
                :margin-top.sync="form.margin_top"
                :margin-bottom.sync="form.margin_bottom"
                :margin-left.sync="form.margin_left"
                :margin-right.sync="form.margin_right"
                :page-width="form.page_width"
                :page-height="form.page_height"
                :page-orientation="form.page_orientation"
                style="height: 100%"
              />
              <!-- 語言 -->
              <el-form-item :label="$t('setting.printout.label.language')">
                <el-select
                  v-model="form.language"
                  :placeholder="$t('setting.printout.label.language')"
                >
                  <el-option :label="$t('setting.printout.content.chinese')" value="zh-hk" />
                  <el-option :label="$t('setting.printout.content.english')" value="en" />
                </el-select>
              </el-form-item>
              <!-- 表頭高度 -->
              <el-form-item :label="$t('setting.printout.label.tableHeaderHeight')">
                <el-input-number v-model="form.table_header_height" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 表腳高度 -->
              <el-form-item :label="$t('setting.printout.label.tableFooterHeight')">
                <el-input-number v-model="form.table_footer_height" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 群組分頁 -->
              <el-form-item :label="$t('setting.printout.label.groupBreak')">
                <el-select
                  v-model="form.group_break"
                  :placeholder="$t('setting.printout.label.groupBreak')"
                >
                  <el-option :label="$t('setting.printout.content.noPageBreak')" value="0" />
                  <el-option :label="$t('setting.printout.content.pageBreak')" value="1" />
                </el-select>
              </el-form-item>
              <!-- 標題闊度 -->
              <el-form-item :label="$t('setting.printout.label.titleWidth')">
                <el-input-number v-model="form.title_width" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 字體大小 -->
              <fontSizeForm
                :font-size-school-name-cn.sync="form.font_size_school_name_cn"
                :font-size-school-name-en.sync="form.font_size_school_name_en"
                :font-size-title.sync="form.font_size_title"
                :font-size-content.sync="form.font_size_content"
                :font-size-signature.sync="form.font_size_signature"
                :font-size-page-num.sync="form.font_size_page_num"
              />
              <!-- 簽名 -->
              <signForm
                :sign-data.sync="form.sign_data"
                :sign-style.sync="form.sign_style"
                :sign-num.sync="form.sign_num"
                :sign-line.sync="form.sign_line"
                :sign-height.sync="form.sign_height"
                :sign-space.sync="form.sign_space"
                @setDefault="onSetSignDefault"
              />
            </el-col>
            <el-col :lg="10" :md="24">
              <!-- 分割線 -->
              <divider class="hidden-lg-only" width="80%" />
              <!-- 列位置與屬性 -->
              <columnsPositions
                v-if="!loading"
                :data.sync="form.columnData"
                :column-field-list="columnFieldList"
                :lang-key="langKey"
              />
            </el-col>
          </el-row>
          <!-- 操作欄 -->
          <actions
            :style-list="styleList"
            :on-fetch="onFetch"
            :on-save="onSave"
            :on-delete="onDelete"
            :on-copy="onCopy"
          />
        </el-form>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'
import signTable from '../components/signTable'
import signForm from '../components/signForm'
import marginForm from '../components/marginForm'
import pageSizeForm from '../components/pageSizeForm'
import fontSizeForm from '../components/fontSizeForm'
import columnsPositions from '../components/columnsPositions'
import actions from '../components/actions'
import Divider from '@/components/Divider'

export default {
  name: 'SettingPrintoutPrintoutCashBookBank',
  components: {
    right,
    signTable,
    signForm,
    marginForm,
    pageSizeForm,
    fontSizeForm,
    columnsPositions,
    actions,
    Divider,
  },
  mixins: [common],
  data() {
    return {
      form: {},
      defaultForm: {
        style_name: this.$t('print.cashBookBank'),
        page_style: '',
        page_width: 297,
        page_height: 210,
        page_orientation: 1,
        language: 'zh-hk',
        margin_top: 5,
        margin_bottom: 5,
        margin_left: 5,
        margin_right: 5,
        table_header_height: 10,
        title_width: 10,
        sign_style: '1',
        sign_num: 3,
        sign_line: 1,
        sign_data: [
          {
            step_cn: this.$t('print.prepared'),
            step_en: 'Prepared',
            post_cn: this.$t('print.staff'),
            post_en: 'Staff',
          },
          {
            step_cn: this.$t('print.approved'),
            step_en: 'Approved',
            post_cn: this.$t('print.principal'),
            post_en: 'Principal',
          },
          {
            step_cn: this.$t('print.checked'),
            step_en: 'Checked',
            post_cn: this.$t('print.supervisor'),
            post_en: 'Supervisor',
          },
          {
            step_cn: this.$t('print.received'),
            step_en: 'Received',
            post_cn: '',
            post_en: '',
          },
          {
            step_cn: '',
            step_en: '',
            post_cn: '',
            post_en: '',
          },
          {
            step_cn: '',
            step_en: '',
            post_cn: '',
            post_en: '',
          },
        ],
        sign_height: 20,
        sign_space: 3,
        font_size_school_name_cn: 15,
        font_size_school_name_en: 14,
        font_size_title: 13,
        font_size_content: 11,
        font_size_signature: 12,
        font_size_page_num: 11,
        table_footer_height: 10,
        group_break: '0',
        fund_abbr: [...Array(8)].map(() => ''),
        columnData: [],
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      position: [],
      langKey: 'setting.printout.report.cashBookBank.label.',
      columnFieldList: [
        'vc_date',
        'vc_no',
        'ref',
        'mark',
        'vc_payee',
        'descr',
        'vc_method',
        'ac_name_',
        'break_down',
        'amount_dr',
        'amount_cr',
        'amount_balance',
      ],
      ps_code: 'pdfcashbookbank',
      psg_code: '',
    }
  },
  computed: {},
  created() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
/deep/ {
  .sign-table-box {
    .sign-table {
      .sign-cell {
      }
    }
  }
  .fund-abbr-cell {
    margin-right: 1px;
  }
  .el-form-item {
    margin-bottom: 5px !important;
    height: 25px;
    line-height: 25px;
  }
}
</style>
