<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'
export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  data() {
    return {
      ps_code: 'pdfbankrec',
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo', 'school']),
  },
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(this.formatPrintData)
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              printSetting,
            }).then(() => resolve())
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const $t = this.$t.bind(this)
        const rawData = this.tableData
        const summary = this.summary
        const language = printSetting.language
        const langKey = 'setting.printout.periodic.bankReconciliation.label.'

        const columnData = printSetting.columnData
          .filter(a => a.position)
          .sort((a, b) => a.position - b.position)

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // ---------------- 學校數據 ----------------
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        // ---------------- 右邊表格 ----------------
        const title = $t('setting.printout.periodic.label.bankReconciliation', language)
        const bank = this.bankList.find(i => i.ac_code === this.preferences.filters.selectedAcCode)
        const bankName = bank
          ? language === 'en'
            ? bank.ac_name_en
            : bank.ac_name_cn
          : $t('periodic.autopayList.label.all', language)
        let dateStr = ''
        let startDate = this.preferences.filters.begin_date
        let endDate = this.preferences.filters.end_date
        if (startDate && endDate) {
          startDate = dateUtil.format(new Date(startDate), 'dd/MM/yyyy')
          endDate = dateUtil.format(new Date(endDate), 'dd/MM/yyyy')
          dateStr = startDate + ' - ' + endDate
        }
        const pageInfo = {
          title: title,
          filename: `${title} - ${dateStr}`,
          data: [
            {
              label: $t('setting.printout.periodic.label.bank', language) + ':',
              value: bankName,
            },
            {
              label: $t('setting.printout.periodic.label.period', language) + ':',
              value: dateStr,
            },
          ],
        }

        // ---------------- 表頭 ----------------
        const columns = [[]]
        let totalColumnIndex = -1
        columnData.forEach((item, index) => {
          if (item.position === 0) {
            return
          }
          const key = item.name
          columns[0].push({
            text: $t(langKey + key, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: item.alignment,
            width: item.width,
            border: [false, true, false, true],
          })
          if (item.name === 'total') {
            totalColumnIndex = index
          }
        })
        const hasTotal = totalColumnIndex !== -1
        const totalAlignment = columnData[totalColumnIndex].alignment || 'left'
        // 主數據

        const tableData = []

        function newRow() {
          return [...Array(columnData.length)].map(() => ({ text: '' }))
        }
        if (hasTotal) {
          // 截至 dd/MM/yyyy 銀行賬戶結餘金額
          const r1 = newRow()
          r1[totalColumnIndex].text = summary.bf
          r1[totalColumnIndex].bold = true
          r1[totalColumnIndex].alignment = totalAlignment
          r1[totalColumnIndex].style = 'tableFooter'
          if (totalColumnIndex > 0) {
            r1[0].colSpan = totalColumnIndex
            r1[0].text = $t(
              'setting.printout.periodic.bankReconciliation.label.bf_amount',
              language,
              { date: endDate },
            )
            r1[0].bold = true
          }
          tableData.push(r1)
          const r2 = newRow()
          r2[0].text = $t(
            'setting.printout.periodic.bankReconciliation.label.unpresentedPayment',
            language,
          )
          r2[0].colSpan = columnData.length
          r2[0].bold = true
          tableData.push(r2)
        }

        const crData = rawData.filter(
          i => i.amount_cr && (i.vc_rdate === 'E' || i.vc_rdate === 'F'),
        )
        const drData = rawData.filter(
          i => i.amount_dr && (i.vc_rdate === 'E' || i.vc_rdate === 'F'),
        )

        // CR 支出
        crData.forEach((item, rowIndex) => {
          const row = []
          columnData.forEach((col, colIndex) => {
            const currentCellValue = item[col.name]
            const cell = {
              text: '',
              name: col.name,
              alignment: col.alignment,
              style: 'tableContent',
            }
            switch (col.name) {
              case 'vc_date':
                cell.text = dateUtil.format(new Date(currentCellValue), 'dd/MM/yyyy')
                break
              case 'amount':
                cell.text = amountFormat(item.amount_cr)
                if (rowIndex === crData.length - 1) {
                  cell.border = [false, false, false, true]
                }
                break
              case 'total':
                break
              default:
                cell.text = currentCellValue
                break
            }
            row.push(cell)
          })
          tableData.push(row)
        })
        // 未兌現付款 - Row
        const r3 = newRow()
        if (hasTotal) {
          r3[totalColumnIndex].text = summary.un_check_cr
          r3[totalColumnIndex].bold = true
          r3[totalColumnIndex].alignment = totalAlignment
          r3[totalColumnIndex].style = 'tableFooter'
        }
        tableData.push(r3)
        const r4 = newRow()
        r4[0].text = $t(
          'setting.printout.periodic.bankReconciliation.label.unpresentedReceipt',
          language,
        )
        r4[0].colSpan = columnData.length
        r4[0].bold = true
        tableData.push(r4)

        // DR
        drData.forEach((item, rowIndex) => {
          const row = []
          columnData.forEach((col, colIndex) => {
            const currentCellValue = item[col.name]
            const cell = {
              text: '',
              name: col.name,
              alignment: col.alignment,
              style: 'tableContent',
            }
            switch (col.name) {
              case 'vc_date':
                cell.text = dateUtil.format(new Date(currentCellValue), 'dd/MM/yyyy')
                break
              case 'amount':
                cell.text = amountFormat(item.amount_dr)
                if (rowIndex === drData.length - 1) {
                  cell.border = [false, false, false, true]
                }
                break
              case 'total':
                break
              default:
                cell.text = currentCellValue
                break
            }
            row.push(cell)
          })
          tableData.push(row)
        })

        // 未兌現收款 - Row
        const r5 = newRow()
        if (hasTotal) {
          r5[totalColumnIndex].bold = true
          r5[totalColumnIndex].text = summary.un_check_dr
          r5[totalColumnIndex].border = [false, false, false, true]
          r5[totalColumnIndex].alignment = totalAlignment
          r5[totalColumnIndex].style = 'tableFooter'
        }
        tableData.push(r5)
        // 截至 31/08/2024 銀行月結單結餘金額： - Row
        const r6 = newRow()
        r6[0].colSpan = totalColumnIndex
        r6[0].bold = true
        r6[0].text = $t('setting.printout.periodic.bankReconciliation.label.end_amount', language, {
          date: endDate,
        })
        if (hasTotal) {
          r6[totalColumnIndex].text = summary.end
          r6[totalColumnIndex].border = [false, false, false, true]
          r6[totalColumnIndex].bold = true
          r6[totalColumnIndex].alignment = totalAlignment
          r6[totalColumnIndex].style = 'tableFooter'
        }
        tableData.push(r6)

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 表格寬度
      const widths = generateWidths(columns[0])

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }
      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              heights: tableData.map((e, i) =>
                i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
              ),
              headerRows: columns.length + 1,
              body: [
                pageHeader, // 頁頭
                ...columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              defaultBorder: false,
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      // console.log(JSON.stringify(docDefinition))
      await openPdf(docDefinition)
    },
  },
}
</script>
