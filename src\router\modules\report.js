import Layout from '@/views/layout/Layout'

const reportRouter = {
  path: '/report',
  component: Layout,
  redirect: 'noredirect',
  name: 'report',
  meta: {
    title: 'router.report',
    p_code: 'ac.report',
    icon: 'report',
  },
  children: [
    {
      path: 'trial_balance',
      component: () => import('@/views/report/trialBalance/index.vue'),
      name: 'reportTrialBalance',
      needAPrint: 'P',
      meta: {
        title: 'router.reportTrialBalance',
        p_code: 'ac.report.trial_balance',
        noCache: true,
      },
    },
    {
      path: 'ledger',
      component: () => import('@/views/report/ledger/index.vue'),
      name: 'reportLedger',
      needAPrint: 'P',
      meta: {
        title: 'router.reportLedger',
        p_code: 'ac.report.ledger',
        noCache: true,
      },
    },
    {
      path: 'cash_book',
      component: () => import('@/views/report/cashBook/index.vue'),
      name: 'reportCashBook',
      needAPrint: 'P',
      meta: {
        title: 'router.reportCashBook',
        p_code: 'ac.report.cash_book',
        noCache: true,
      },
    },
    {
      path: 'budget',
      component: () => import('@/views/report/budget/index.vue'),
      name: 'reportBudget',
      needAPrint: 'P',
      meta: {
        title: 'router.reportBudget',
        p_code: 'ac.report.budget',
        noCache: true,
      },
    },
    {
      path: 'staff',
      component: () => import('@/views/report/staff/index.vue'),
      name: 'reportStaff',
      needAPrint: 'P',
      meta: {
        title: 'router.reportStaff',
        p_code: 'ac.report.staff',
        noCache: true,
      },
    },
    {
      path: 'department',
      component: () => import('@/views/report/department/index.vue'),
      name: 'reportDepartment',
      needAPrint: 'P',
      meta: {
        title: 'router.reportDepartment',
        p_code: 'ac.report.department',
        noCache: true,
      },
    },
    {
      path: 'voucher',
      component: () => import('@/views/report/voucher/index.vue'),
      name: 'reportVoucher',
      needAPrint: 'P',
      meta: {
        title: 'router.reportVoucher',
        p_code: 'ac.report.voucher',
        noCache: true,
      },
    },
    {
      path: 'excel_report',
      component: () => import('@/views/report/excelReport/index.vue'),
      name: 'reportExcelReport',
      meta: {
        title: 'router.reportExcelReport',
        p_code: 'ac.report.excel_report',
        noCache: true,
      },
    },
  ],
}

export default reportRouter
