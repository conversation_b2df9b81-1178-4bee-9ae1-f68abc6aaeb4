<template>
  <div class="fundInfo">
    <el-form ref="form" :model="form" label-position="right" label-width="100px">
      <!-- 編號 -->
      <el-form-item
        :rules="codeRules"
        :class="{ 'code-rules-error': haveExist }"
        :label="$t('assistance.contra.label.contra_code')"
        prop="contra_code"
      >
        <el-input v-model="form.contra_code" />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.contra.label.contra_name_cn')"
        prop="contra_name_cn"
      >
        <el-input v-model="form.contra_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.contra.label.contra_name_en')"
        prop="contra_name_en"
      >
        <el-input v-model="form.contra_name_en" clearable />
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('master.fund.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_code"
            :value="item.fy_code"
          >
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editContra ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createContra, updateContra, getContra } from '@/api/assistance/contra'
import { fetchYears } from '@/api/master/years'
// import { Promise, resolve, reject } from 'q'

export default {
  name: 'AssistanceContraAdd',
  props: {
    editContra: {
      type: Object,
      default: null,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      system: 'AC',
      years: [],
      active_year_arr: [],
      form: {
        contra_id: '',
        contra_code: '',
        contra_name_cn: '',
        contra_name_en: '',
        active_year: '',
      },
      defaultForm: {
        contra_id: '',
        contra_code: '',
        contra_name_cn: '',
        contra_name_en: '',
        active_year: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    codeRules() {
      return [{ required: true, validator: this.checkCode, trigger: 'blur' }]
    },
  },
  watch: {
    editContra() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      console.log('watch editContra', this.editContra)
      this.initData()
    },
  },
  created() {
    console.log('editContra', this.editContra)
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editContra && this.editContra.contra_code === i.contra_code) {
          return
        }
        codeArr.push(i.contra_code)
      })
      return codeArr
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    checkRequired(rule, value, callback) {
      console.log(rule, value, callback)
    },
    initData() {
      fetchYears().then(res => {
        console.log('fetchyear', res)
        this.years = res
        if (!this.editContra && res.length && this.currentYear && this.currentYear.fy_code) {
          const year = res.find(i => i.fy_code === this.currentYear.fy_code)
          if (year) this.active_year_arr.push(year.fy_code)
        }
      })
      if (this.editContra) {
        // 編輯
        this.form = Object.assign({}, this.editContra) // init活躍年份
        this.form.contra_id = this.editContra.contra_id
        console.log('form', this.form)
        getContra(this.editContra.contra_id).then(res => {
          console.log('getcontra', res)
          this.active_year_arr = res.active_year ? res.active_year.split(',') : []
        })
      } else {
        // 新增
        this.form = Object.assign({}, this.defaultForm)
      }
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }

        console.log('user add onSave')
        console.log('contra_id', this.form.contra_id)
        const contra_id = this.form.contra_id
        const contra_code = this.form.contra_code ? this.form.contra_code : null
        const contra_name_cn = this.form.contra_name_cn
        const contra_name_en = this.form.contra_name_en
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const data = Object.assign({}, this.form)
        console.log(data)
        if (this.editContra) {
          // 編輯
          updateContra(contra_id, contra_code, contra_name_cn, contra_name_en, active_year)
            .then(res => {
              console.log('Success: updateContra', res)
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              console.log('ERR: updateContra', err)
              // this.$message.err(err)
            })
        } else {
          // 新增
          createContra(contra_code, contra_name_cn, contra_name_en, active_year)
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              console.log('Success: createContra', res)
              this.$emit('onCancel', true)
            })
            .catch(err => {
              console.log('ERR: createContra', err)
            })
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
