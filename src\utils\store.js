function get(key) {
  try {
    return sessionStorage.getItem(key)
  } catch (e) {
    console.error(e)
    return ''
  }
}
function set(key, val) {
  try {
    return sessionStorage.setItem(key, val)
  } catch (e) {
    console.error(e)
    return ''
  }
}

function remove(key) {
  try {
    return sessionStorage.removeItem(key)
  } catch (e) {
    console.error(e)
    return ''
  }
}

function setLocation(key, val) {
  try {
    return localStorage.setItem(key, val)
  } catch (e) {
    console.error(e)
    return ''
  }
}

function getLocation(key) {
  try {
    return localStorage.getItem(key)
  } catch (e) {
    console.error(e)
    return ''
  }
}
export default { get, set, remove, setLocation, getLocation }
