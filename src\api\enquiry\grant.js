import request from '@/utils/request'

/**
 * 返回相關類型查詢結果
 * @param {string,required} type A=所有,Y=全年,T=年度截至,M=當月 required
 * @param {string} fy_code 週期編號
 * @param {string}  pd_code 週期月份編號
 * @param {string} code 歸屬會計科目樹code
 * @param {string} fund_id 賬目類別id
 * @return {Promise}
 */
export function enquiryGrant({ type, fy_code, pd_code, ac_code, fund_id }) {
  return request({
    url: '/enquiry/grant',
    method: 'get',
    params: {
      type,
      fy_code,
      pd_code,
      ac_code,
      fund_id,
    },
  })
}

export default {
  enquiryGrant,
}
