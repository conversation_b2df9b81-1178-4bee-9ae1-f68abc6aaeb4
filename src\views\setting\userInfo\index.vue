<template>
  <div class="app-container">
    <header>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ $t('router.settingScreenBasicSetting') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t($route.meta.title) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </header>

    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="130px"
    >
      <!-- 登入名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('setting.user_info.username')"
        class="username"
        prop="username"
      >
        <el-input v-model="form.username" :disabled="true" />
      </el-form-item>
      <!-- 原有密碼 -->
      <el-form-item
        :rules="rules"
        :label="$t('setting.user_info.password')"
        class="password"
        prop="password"
      >
        <el-input v-model="form.password" type="password" clearable />
      </el-form-item>
      <!-- 更新密碼 -->
      <el-form-item
        :rules="rules"
        :label="$t('setting.user_info.new_password')"
        class="new-password"
        prop="new_password"
      >
        <el-input v-model="form.new_password" type="password" clearable />
      </el-form-item>
      <!-- 重入密碼 -->
      <el-form-item
        :rules="rules"
        :label="$t('setting.user_info.confirm_password')"
        class="confirm-password"
        prop="confirm_password"
      >
        <el-input v-model="form.confirm_password" type="password" clearable />
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ $t('button.update') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getMe, changePsw } from '@/api/settings/userInfo'
import loadPreferences from '@/views/mixins/loadPreferences'
import { staffChangePsw } from '@/api/assistance/staff'

export default {
  name: 'SettingUserInfo',
  mixins: [loadPreferences],
  data() {
    return {
      form: {
        username: '',
        password: '',
        new_password: '',
        confirm_password: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['system']),
  },
  created() {
    console.log('initData')
    this.initData()
    this.saveUserLastPage()
  },
  methods: {
    initData() {
      this.loading = false
      getMe().then(res => {
        console.log('getMe', res)
        this.form.username = res.username
      })
      this.loading = false
    },
    clearPassword() {
      this.form.password = ''
      this.form.new_password = ''
      this.form.confirm_password = ''
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        console.log('Update')
        // const username = this.form.username
        const password = this.$md5(this.form.password)
        const new_password = this.$md5(this.form.new_password)
        const confirm_password = this.$md5(this.form.confirm_password)
        if (new_password !== confirm_password) {
          this.$message.error(this.$t('message.confirmPasswordError'))
        } else {
          let api = staffChangePsw
          if (this.system === 'AC') {
            api = changePsw
          }
          // 更改
          api({ password, new_password })
            .then(res => {
              this.$message.success(this.$t('message.success'))
              this.clearPassword()
              console.log('Success: changePsw', res)
            })
            .catch(err => {
              console.log('ERR:  changePsw', err)
            })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.username,
.password,
.new-password,
.confirm-password {
  width: 400px;
}
</style>
