import request from '@/utils/request'

/**
 * 更新用戶偏好設置
 * @param {integer} user_id 用戶id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} pf_code 偏好編號
 * @param {string} module_code 模塊編號
 * @param {object} content 偏好內容(json)
 */
export function editUserPreference(user_id, system, pf_code, module_code, content) {
  return request({
    url: '/users/preferences/actions/update',
    method: 'post',
    data: {
      user_id,
      system,
      pf_code,
      module_code,
      content: JSON.stringify(content),
    },
  })
}

/**
 * 更新職員偏好設置
 * @param {integer} staff_id 職員id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} pf_code 偏好編號
 * @param {string} module_code 模塊編號
 * @param {string} content 偏好內容(json)
 */
export function editStaffPreference(staff_id, system, pf_code, module_code, content) {
  return request({
    url: '/staffs/preferences/actions/update',
    method: 'post',
    data: {
      staff_id,
      system,
      pf_code,
      module_code,
      content: JSON.stringify(content),
    },
  })
}

/**
 * 返回用戶偏好設置
 * @param {integer} user_id 用戶id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} pf_code 偏好編號
 * @param {string} module_code 模塊編號
 * @param ignore_err
 */
export function getUserPreference(user_id, system, pf_code, module_code, ignore_err = false) {
  return request({
    url: '/users/preferences/actions/inquire',
    method: 'get',
    ignore_err,
    params: {
      user_id,
      system,
      pf_code,
      module_code,
    },
  })
}

/**
 * 返回職員偏好設置
 * @param {integer} staff_id 職員id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} pf_code 偏好編號
 * @param {string} module_code 模塊編號
 */
export function getStaffPreference(staff_id, system, pf_code, module_code) {
  return request({
    url: '/staffs/preferences/actions/inquire',
    method: 'get',
    params: {
      staff_id,
      system,
      pf_code,
      module_code,
    },
  })
}
