import request from '@/utils/request'

/**
 * 返回部門報表數據
 * @param {string} parent_dept_type_id 部門組id
 * @param {string} dept_code 部門編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} BIE  可傳:B,I,E,IE
 * @return {Promise}
 */
export function fetchDepartmentLedgers({
  parent_dept_type_id,
  dept_code,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/departments/ledgers',
    method: 'get',
    params: {
      parent_dept_type_id,
      dept_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 返回部門報表數據 - 打印用
 * @param {string} parent_dept_type_id 部門組id
 * @param {string} dept_code 部門編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} BIE  可傳:B,I,E,IE
 * @return {Promise}
 */
export function fetchDepartmentLedgersForPrint({
  parent_dept_type_id,
  dept_code,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/departments/ledgers-print',
    method: 'get',
    params: {
      parent_dept_type_id,
      dept_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

export default {
  fetchDepartmentLedgers,
  fetchDepartmentLedgersForPrint,
}
