<script>
import { mapGetters } from 'vuex'
import { fetchFunds } from '@/api/master/funds'
import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'
export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  data() {
    return {
      ps_code: 'pdfdailycollect',
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo', 'school']),
  },
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(this.formatPrintData)
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              printSetting,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const $t = this.$t.bind(this)
        const rawData = this.tableData
        const langKey = 'setting.printout.periodic.dailyCollection.label.'
        const language = printSetting.language

        const columnData = printSetting.columnData
          .filter(a => a.position)
          .sort((a, b) => a.position - b.position)

        const selected = '√'
        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const title = $t('setting.printout.periodic.label.dailyCollection', language)
        const fund = this.funds.find(i => i.fund_id === this.preferences.filters.selectedFund)
        const type = fund
          ? language === 'en'
            ? fund.fund_name_en
            : fund.fund_name_cn
          : $t('setting.printout.periodic.label.all', language)

        const begin_date = this.preferences.filters.begin_date
        const end_date = this.preferences.filters.end_date
        let dateStr = ''
        if (begin_date && end_date) {
          dateStr =
            dateUtil.format(new Date(begin_date), 'dd/MM/yyyy') +
            ' - ' +
            dateUtil.format(new Date(end_date), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${type} - ${dateStr}`,
          data: [
            {
              label: $t('setting.printout.periodic.label.fund', language) + ':',
              value: type,
            },
            {
              label: $t('setting.printout.periodic.label.period', language) + ':',
              value: dateStr,
            },
          ],
        }

        const borderAll = [true, true, true, true]
        // const borderBottom = [false, false, false, true]
        const borderTop = [false, true, false, false]
        const borderX = [false, true, false, true]
        const borderNone = [false, false, false, false]
        const columns = [[]]
        const fundRow = []

        let hasFund = false
        // 表頭
        const fundNameList = printSetting.fund_abbr // ['GF', 'SF', 'ES']

        const funds = await fetchFunds({ fund_type: 'F' }) // funds
        columnData.forEach(item => {
          if (item.position === 0) {
            return
          }
          if (item.name === 'fund_name_') {
            funds.forEach((fund, i) => {
              columns[0].push({
                text: $t(langKey + item.name, language),
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: i === 0 ? funds.length : 1,
                alignment: 'center',
                width: item.width,
                border: borderAll,
              })
              fundRow.push({
                text: fundNameList[i],
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 1,
                alignment: item.alignment,
                width: item.width,
                border: borderAll,
              })
            })
            hasFund = true
          } else {
            const key = item.name
            columns[0].push({
              text: $t(langKey + key, language),
              style: 'tableHeader',
              rowSpan: 2,
              alignment: item.alignment,
              width: item.width,
              border: borderAll,
            })
            fundRow.push({ text: '', border: borderAll })
          }
        })
        if (hasFund) {
          columns.push(fundRow)
        } else {
          columns[0].forEach(item => {
            item.rowSpan = 1
          })
        }

        // 主數據
        const tableData = []
        let prevItem = {}
        const sumRow = []
        let sumIndex = 0
        // 初始化行
        columnData.forEach((col, colIndex) => {
          if (col.position === 0) {
            // 即不顯示列
            return
          }
          switch (col.name) {
            case 'fund_name_':
              funds.forEach(fund => {
                sumRow.push({ name: col.name, text: '', style: 'tableFooter', border: borderX })
              })
              funds.length > 1 && (sumIndex += funds.length - 1)
              break
            case 'vc_amount': {
              sumRow.push({
                name: col.name,
                text: '0.00',
                value: 0,
                style: 'tableFooter',
                border: borderX,
              })
              break
            }
            default: {
              sumRow.push({ name: col.name, text: '', style: 'tableFooter', border: borderX })
            }
          }
          sumIndex++
        })
        rawData.forEach((item, rowIndex) => {
          const row = []
          sumIndex = 0
          columnData.forEach((col, colIndex) => {
            if (col.position === 0) {
              // 即不顯示列
              return
            }
            const isNewVoucher = prevItem.vc_no !== item.vc_no
            const border = isNewVoucher ? borderTop : borderNone
            const cell = {
              name: col.name,
              text: '',
              alignment: col.alignment,
              style: 'tableContent',
              border,
            }
            switch (col.name) {
              case 'fund_name_':
                funds.forEach(fund => {
                  row.push({
                    text: fund.fund_name_cn === item.fund_name_cn ? selected : '',
                    alignment: col.alignment,
                    style: 'tableContent',
                    border,
                  })
                })
                funds.length > 1 && (sumIndex += funds.length - 1)
                return
              case 'vc_date':
              case 'vc_no':
              case 'ref':
              case 'vc_receipt':
              case 'vc_receipt_date':
              case 'vc_payee':
                if (isNewVoucher) {
                  let key = col.name
                  if (key[key.length - 1] === '_') {
                    key = language === 'en' ? key + 'en' : key + 'cn'
                  }
                  const v = item[key]
                  let text
                  if (col.name === 'vc_date') {
                    const d = new Date(v)
                    if (d.getTime()) {
                      text = dateUtil.format(d, 'dd/MM/yyyy')
                    }
                  }
                  if (text === undefined) {
                    text = v
                  }
                  cell.text = text
                }
                break
              case 'ac_name':
                cell.text = item[language === 'en' ? 'ac_name_en' : 'ac_name_cn']
                break
              case 'lg_amount': {
                let v = Number(item[col.name])
                isNaN(v) && (v = 0)
                cell.text = amountFormat(v)
                break
              }
              case 'vc_amount': {
                if (isNewVoucher) {
                  let v = Number(item[col.name])
                  isNaN(v) && (v = 0)
                  cell.text = isNewVoucher ? amountFormat(v) : ''
                  sumRow[sumIndex].alignment = col.alignment
                  sumRow[sumIndex].value = toDecimal(Number(sumRow[sumIndex].value) + v)
                  sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)
                }
                break
              }
              default: {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = language === 'en' ? key + 'en' : key + 'cn'
                }
                const v = item[key]
                cell.text = v === undefined ? '' : v
              }
            }
            row.push(cell)
            sumIndex++
          })
          tableData.push(row)
          prevItem = item
        })

        if (sumRow.length > 0) {
          const sumPosition = sumRow.findIndex(i => i.name === 'vc_amount')
          if (sumPosition > 0) {
            sumRow[0].text = $t('print.total', language)
            sumRow[0].colSpan = sumPosition
            sumRow[0].alignment = 'right'
            sumRow[0].bold = true
            sumRow[0].style = 'tableFooter'
          }

          tableData.push(sumRow)
        }
        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 表格寬度
      const widths = generateWidths(columns[0])

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }
      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              heights: tableData.map((e, i) =>
                i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
              ),
              headerRows: columns.length + 1,
              body: [
                pageHeader, // 頁頭
                ...columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              defaultBorder: false,
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1
      // console.log(JSON.stringify(docDefinition))
      await openPdf(docDefinition)
    },
  },
}
</script>
