import Vue from 'vue'
import Router from 'vue-router'
/* Router Modules */
// import enquiryRouter from './modules/enquiry'
import dailyRouter from './modules/daily'
import periodicRouter from './modules/periodic'
import stockRouter from './modules/stock'
import reportRouter from './modules/report'
import masterRouter from './modules/master'
import assistanceRouter from './modules/assistance'
import settingRouter from './modules/setting'
import bgSettingRouter from './modules/bgSetting'
import purchaseSettingRouter from './modules/purchaseSetting'
import budgetRouter from './modules/budget' // 預算
import purchaseRouter from './modules/purchase' // 採購
import Layout from '@/views/layout/Layout'
/* Layout */
// import Layout from '@/views/layout/Layout'

Vue.use(Router)

/** note: sub-menu only appear when children.length>=1
 *  detail see  https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 **/

/**
 * hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
 *                                if not set alwaysShow, only more than one route under the children
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    will control the page roles (you can set multiple roles)
    title: 'title'               the name show in sub-menu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if true, the page will no be cached(default is false)
    breadcrumb: false            if false, the item will hidden in breadcrumb(default is true)
    affix: true                  if true, the tag will affix in the tags-view
  }
 **/
export const constantRouterMap = [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: 'login',
    },
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/401',
    name: '401',
    meta: {
      title: '401',
      p_code: '*',
      noCache: false,
    },
    component: () => import('@/views/errorPage/401.vue'),
    hidden: true,
  },
  {
    path: '/tips',
    component: Layout,
    redirect: 'noredirect',
    name: 'tips',
    meta: {
      title: 'tips',
      p_code: '*',
    },
    children: [
      {
        path: '/coding',
        component: () => import('@/views/errorPage/coding/index.vue'),
        name: 'coding',
        meta: {
          title: 'coding',
          p_code: '*',
        },
        hidden: true,
      },
    ],
    hidden: true,
  },
  {
    path: '/tutorial',
    name: 'tutorial',
    component: () => import('@/views/tutorial.vue'),
    hidden: true,
    meta: {
      p_code: '*',
    },
  },
]

export default new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap,
})

export const asyncRouterMap = [
  {
    path: '/',
    name: 'index',
    hidden: true,
    meta: {
      p_code: 'index',
    },
    redirect: '/master/user', // 默認路由
  },
  // enquiryRouter, // 查詢
  dailyRouter, // 日常
  periodicRouter, // 週期
  stockRouter, // 庫存
  reportRouter, // 報表
  masterRouter, // 主檔案
  assistanceRouter, // 輔助檔案
  settingRouter, // 設定
  ...budgetRouter,
  ...purchaseRouter,
  purchaseSettingRouter,
  bgSettingRouter, // 設定

  { path: '/401', redirect: '/401', hidden: true },
  { path: '*', redirect: '/404', hidden: true },
]
