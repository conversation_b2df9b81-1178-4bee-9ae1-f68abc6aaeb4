<template>
  <div class="quotes-select-step">
    <el-form ref="form" :model="formModel">
      <el-table
        :data="form"
        :expand-row-keys="expandRows"
        :cell-class-name="handleCellClassName"
        row-key="pro_apply_supplier_id"
        border
      >
        <el-table-column type="index" />
        <el-table-column type="expand">
          <template v-if="props && props.row" slot-scope="props">
            <el-table
              v-if="props.row.supplier_status === 'Q'"
              :data="props.row.quotes"
              style="padding-left: 120px"
              size="mini"
              class="expand-table"
            >
              <el-table-column type="index" />
              <el-table-column :label="t('itemName')" prop="item_name" width="300" />
              <el-table-column :label="t('qty')" prop="item_qty" width="80" />
              <el-table-column :label="t('price')" prop="price" align="right" width="180">
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ props.row.quotes[scope.$index].price | amount }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="t('amount')" align="right" width="180">
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ props.row.quotes[scope.$index].amount | amount }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column :label="t('supplierName')" prop="supplier_name" />
        <el-table-column :label="t('supplierTel')" prop="supplier_tel" />
        <el-table-column :label="t('supplierFax')" prop="supplier_fax" />
        <el-table-column :label="t('supplierContact')" prop="supplier_contact" />
        <el-table-column :label="t('status')" prop="supplier_status">
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>{{ getStatus(scope.row.supplier_status) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('total')" prop="supplier_status">
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="scope.row.supplier_status === 'Q'">{{
              scope.row.quotes.map(item => Number(item.amount)).reduce((a, b) => a + b, 0) | amount
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('select')" width="100">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-radio
              v-model="selectId"
              :disabled="isReadonly || scope.row.supplier_status !== 'Q'"
              :label="scope.row.pro_apply_supplier_id"
              @change="onChangeSelect"
            >
              <span />
            </el-radio>
          </template>
        </el-table-column>
        <div slot="append" class="not-enough-suppliers-reason">
          <el-form-item
            v-if="showReason"
            :rules="requiredRulesM"
            :label="t('notSelectLowestReason')"
            prop="notSelectLowestReason"
          >
            <el-input
              v-model="notSelectLowestReason"
              :disabled="isReadonly"
              type="textarea"
              autosize
              style="width: 500px"
              resize="none"
            />
          </el-form-item>
        </div>
      </el-table>
      <el-form-item class="actions">
        <el-button size="mini" type="info" @click="onBack">
          {{ $t('button.back') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onPrev">
          {{ $t('button.prev') }}
        </el-button>
        <el-button
          v-if="!isReadonly"
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="success"
          @click="onSave"
        >
          {{ $t('button.saveFile') }}
        </el-button>
        <el-button
          v-if="(!isReadonly && stage === 2) || (isReadonly && stage > 2)"
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="primary"
          @click="onNext"
        >
          {{ $t(status === 'K' ? 'button.submit' : 'button.next') }}
        </el-button>
      </el-form-item>
    </el-form>
    <!--    <svg-icon v-if="!isReadonly" icon-class="add" class-name="add-icon add-row" @click="onAddRow"/>-->
  </div>
</template>
<script>
import { selectProcurementApplicationsQuotes } from '@/api/purchase/procurementApplications'
import { uniqueArr, toDecimal } from '@/utils'
import ElRadio from 'element-ui/packages/radio/src/radio'

export default {
  name: 'QuotesSelectStep',
  components: { ElRadio },
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
    stage: {
      type: [String, Number],
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    suppliers_at_least: {
      type: Number,
      required: true,
    },
    not_select_lowest_reason: {
      type: String,
      required: true,
    },
    reason_for_low_price: {
      type: String,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: true,
    },
    status: {
      type: String,
      required: true,
    },
    reason_for_suppliers: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      langKey: 'purchase.daily.procurementApplications.',
      expandRows: [],
      // form: {
      //
      // }
      selectId: '',
    }
  },
  computed: {
    formModel() {
      return { form: this.form, notSelectLowestReason: this.notSelectLowestReason }
    },
    form: {
      get() {
        return this.data.filter(i => i.supplier_name.length > 0)
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
    notSelectLowestReason: {
      get() {
        return this.not_select_lowest_reason
      },
      set(val) {
        this.$emit('update:not_select_lowest_reason', val)
      },
    },
    showReason() {
      const arr = this.form.filter(i => i.pro_apply_supplier_id === this.selectId) // i.supplier_selected === 'Y')
      if (arr.length > 0) {
        return !this.isLowest(arr[0].pro_apply_supplier_id)
      }
      return false
    },
    statusList() {
      return [
        {
          value: 'W',
          label: this.$t('purchase.supplierStatus.W'),
        },
        {
          value: 'Q',
          label: this.$t('purchase.supplierStatus.Q'),
        },
        {
          value: 'R',
          label: this.$t('purchase.supplierStatus.R'),
        },
        {
          value: 'N',
          label: this.$t('purchase.supplierStatus.N'),
        },
      ]
    },
    typeList() {
      return [
        {
          value: 'COM',
          label: this.t('com'),
        },
        {
          value: 'ORG',
          label: this.t('org'),
        },
      ]
    },
    reasonforlowPrice() {
      return this.reason_for_suppliers === 'Y'
    },
    requiredRulesM() {
      return this.isReadonly
        ? []
        : [{ required: this.reasonforlowPrice, message: ' ', trigger: ['blur', 'change'] }]
    },
  },
  created() {
    for (let i = 0; i < this.form.length; i++) {
      const item = this.form[i]
      if (item.supplier_selected === 'Y') {
        this.selectId = item.pro_apply_supplier_id
        break
      }
    }
    this.initExpandRows()
  },
  methods: {
    initExpandRows() {
      const arr = [...this.expandRows]
      arr.push(
        ...this.data
          .filter(item => item.supplier_status === 'Q')
          .map(item => item.pro_apply_supplier_id),
      )
      this.expandRows = uniqueArr(arr)
    },
    onAddRow() {
      this.form.push({
        supplier_name: '',
        supplier_index: this.form.length,
        supplier_type: 'COM',
        supplier_tel: '',
        supplier_fax: '',
        supplier_contact: '',
      })
    },
    onDelete(scope) {
      this.form.splice(scope.$index, 1)
    },
    onSave(showTips = true) {
      return new Promise(async(resolve, reject) => {
        this.$refs.form.validate(async(valid, a) => {
          if (!valid) {
            reject()
            return false
          }
          const val = this.selectId
          if (!val) {
            this.$message.error(this.t('quotesSelectEmpty'))
            reject()
            return
          }
          let notSelectLowestReason = ''
          if (!this.isLowest(val)) {
            try {
              notSelectLowestReason = this.notSelectLowestReason.replace(/ /g, '')
              if (notSelectLowestReason.length === 0) {
                reject()
                return
              }
            } catch (e) {
              reject()
              return
            }
          }

          try {
            await selectProcurementApplicationsQuotes({
              pro_apply_supplier_id: val,
              not_select_lowest_reason: notSelectLowestReason,
            })
            if (showTips) {
              this.$message.success(this.$t('message.success'))
              // this.$emit('reshow')
              this.onBack()
            }
            resolve()
          } catch (e) {
            console.log(e)
            reject(e)
          }
        })
      })
    },
    onNext() {
      this.$emit('next')
    },
    onPrev() {
      this.$emit('prev')
    },
    onBack() {
      this.$emit('back')
    },
    onChangeStatus(item) {
      if (item.supplier_status === 'Q') {
        this.expandRows.push(item.pro_apply_supplier_id)
        const l = this.expandRows.length
        const na = uniqueArr(this.expandRows).length
        const nl = na.length
        if (nl < l) {
          this.expandRows = na
        }
      } else {
        const i = this.expandRows.findIndex(i => i === item.pro_apply_supplier_id)
        if (i !== -1) {
          this.expandRows.splice(i, 1)
        }
      }
    },
    handleCellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.type === 'expand' && row.supplier_status === 'Q') {
        return 'has-quotes'
      }
      return ''
    },
    getStatus(status) {
      const item = this.statusList.find(i => i.value === status)
      if (item) {
        return item.label
      } else {
        return status
      }
    },
    getTotal(list) {
      return list.reduce((total, item) => {
        const num = Number(item.amount)
        return toDecimal(total + (isNaN(num) ? 0 : num))
      }, 0)
    },
    isLowest(id) {
      const item = this.form.find(i => i.pro_apply_supplier_id === id)
      let amount = 0
      if (item.hasOwnProperty('quotes')) {
        amount = this.getTotal(item.quotes)
      }
      return this.form
        .filter(i => i.supplier_status === 'Q')
        .every(i => {
          if (i.pro_apply_supplier_id === id) {
            return true
          } else {
            const a = this.getTotal(i.quotes)
            return amount <= a
          }
        })
    },
    async onChangeSelect(val) {
      // let not_select_lowest_reason = ''
      // if (!this.isLowest(val)) {
      //   try {
      //     not_select_lowest_reason = await this.getReason()
      //   } catch (e) {
      //     return
      //   }
      // }
      //
      // try {
      //   await selectProcurementApplicationsQuotes({
      //     pro_apply_supplier_id: val,
      //     not_select_lowest_reason
      //   })
      //   this.$message.success(this.$t('message.success'))
      //   this.$emit('reshow')
      // } catch (e) {
      //   console.log(e)
      // }
    },
    getReason() {
      return new Promise((resolve, reject) => {
        this.$prompt('', this.t('backComment'), {
          confirmButtonText: this.$t('button.submit'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          inputType: 'textarea',
          inputValidator: this.commentValidator,
          // inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
          // inputErrorMessage: '邮箱格式不正确'
        })
          .then(({ comment }) => {
            resolve(comment)
          })
          .catch(() => {
            reject()
          })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.add-icon {
  cursor: pointer;
  &.add-row {
    position: absolute;
    right: -30px;
    top: 3px;
  }
}
.quotes-select-step {
  /deep/ {
    .el-input-number .el-input-number__increase,
    .el-input-number .el-input-number__decrease {
      top: 4px;
    }
    .el-form-item__error {
      display: none;
    }
    .el-table__expanded-cell {
      background-color: #3e97dd1f;
      &:hover {
        background-color: #3e97dd1f !important;
      }
    }

    .el-table__expand-column {
      // 禁止手動展開
      pointer-events: none;
      cursor: not-allowed;
      vertical-align: middle !important;
      &.has-quotes {
        cursor: auto;
        pointer-events: auto;
        i {
          cursor: pointer;
        }
      }
    }
    .expand-table {
      background-color: transparent;
      table thead tr th,
      .el-transfer-panel .el-transfer-panel__header {
        /*color: #ffffff;*/
        color: #909399;
        background-color: transparent !important;
      }
      th,
      tr {
        background-color: transparent !important;
      }
      div.el-table__body-wrapper table > tbody > tr > td .cell {
        line-height: 28px;
      }
      .el-table__body tr:hover > td {
        background-color: transparent !important;
      }
    }
    .el-radio__input.is-disabled.is-checked .el-radio__inner {
      border-color: #409eff;
      background: #409eff;
    }
  }
  .actions {
    text-align: right;
    width: 1000px;
    padding-top: 10px;
  }
  .not-enough-suppliers-reason {
    padding: 10px 0;
    /deep/ {
      .el-form-item__label {
        line-height: 30px;
        vertical-align: middle;
      }
      .el-form-item__content {
        line-height: inherit;
        .el-textarea {
          line-height: inherit;
        }
      }
    }
  }
}
</style>
