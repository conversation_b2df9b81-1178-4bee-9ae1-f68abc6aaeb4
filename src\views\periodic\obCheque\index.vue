<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <el-row>
          <el-form :inline="true" label-width="60px" class="mini-form">
            <!-- 銀行 -->
            <el-form-item :label="$t('filters.bank')">
              <el-select
                v-model="preferences.filters.selectedAcId"
                class="bank"
                style="width: 250px"
                @change="reloadData"
              >
                <el-option
                  v-for="item in bankList"
                  :key="item.account_id"
                  :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
                  :value="item.account_id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-row>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div v-if="hasPermission_Add" :title="$t('btnTitle.add')" class="icon add" @click="onAdd">
            <svg-icon icon-class="add" class="action-icon" />
          </div>
          <!-- 導出按鈕-頁面 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class=""
            @click="onExport('PAGE')"
          >
            <svg-icon icon-class="excel" class="action-icon" />
          </div>
          <!-- 導出按鈕-全部 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class=""
            @click="onExport('ALL')"
          >
            <svg-icon icon-class="excel_add" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <div class="bank-reconciliation-table">
          <ETable
            v-if="!paneLoading"
            ref="table"
            v-loading="!loading && tableLoading"
            :data="tableData"
            :style-columns="styleColumns"
            :amount-columns="amountColumns"
            :lang-key="langKey"
            :show-index="true"
            :show-actions="true"
            :show-checkbox="false"
            :default-top="230"
            border
            @changeWidth="changeColumnWidth"
          >
            <!--            <template slot="columns">-->
            <!--              <el-table-column-->
            <!--                v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"-->
            <!--                v-if="!paneLoading"-->
            <!--                :key="item.ss_key"-->
            <!--                :label="$t(langKey + item.ss_key)"-->
            <!--                :align="item.alignment"-->
            <!--                :class-name="item.ss_key + ' mini-form'"-->
            <!--                :width="item.width"-->
            <!--                :property="$refs.table.column_property(item)"-->
            <!--                :column-key="item.ss_key"-->
            <!--              >-->
            <!--                <template v-if="scope && scope.row" slot-scope="scope">-->
            <!--                  <div v-if="scope.row.edit" class="edit-row">-->
            <!--                    <span v-if="item.ss_key === 'vc_date'">-->
            <!--                      <el-date-picker-->
            <!--                        v-model="scope.row[item.ss_key+'_new']"-->
            <!--                        value-format="yyyy-MM-dd"-->
            <!--                        format="yyyy-MM-dd"-->
            <!--                        type="date"-->
            <!--                        placeholder="选择日期"/>-->
            <!--                    </span>-->
            <!--                    <span v-else-if="item.ss_key === 'amount'">-->
            <!--                      <ENumeric v-model="scope.row[item.ss_key+'_new']"/>-->
            <!--                    </span>-->
            <!--                    <span v-else-if="item.ss_key === 'vc_rdate'" class="show-row">-->
            <!--                      {{ scope.row.vc_rdate ? scope.row.vc_rdate : 'N/A' }}-->
            <!--                    </span>-->
            <!--                    <span v-else>-->
            <!--                      <el-input v-model="scope.row[item.ss_key+'_new']"/>-->
            <!--                    </span>-->
            <!--                  </div>-->
            <!--                  <div v-else class="show-row">-->
            <!--                    <span>-->
            <!--                      {{ scope.row[item.ss_key] }}-->
            <!--                    </span>-->
            <!--                  </div>-->
            <!--                </template>-->
            <!--              </el-table-column>-->
            <!--            </template>-->
            <template slot="columns">
              <el-table-column
                v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
                :key="item.ss_key"
                :label="$t(langKey + item.ss_key)"
                :align="item.alignment"
                :width="item.width"
                :property="column_property(item)"
                :column-key="item.ss_key"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span v-if="item.ss_key === 'vc_rdate'">
                    {{ scope.row.vc_rstatus === 'U' ? t('Unpaid') : scope.row.vc_rdate }}
                  </span>
                  <span v-else>
                    {{ scope.row[column_property(item)] }}
                  </span>
                </template>
              </el-table-column>
            </template>
            <!-- 操作列 -->
            <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
              <div class="operation_icon">
                <div class="operation_icon">
                  <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
                  <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
                </div>
              </div>
            </template>
          </ETable>
        </div>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :account-id="preferences.filters.selectedAcId"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import addPage from './add'

import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'

import { deleteObCheque, fetchObCheques } from '@/api/periodic/obCheque'
import { fetchAccounts } from '@/api/master/account'
import { exportExcel } from '@/utils/excel'
import { oBChequesExport } from '@/api/report/excel'

export default {
  name: 'BankReconciliationIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    addPage,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      tableLoading: false,
      loading: false,
      leftView: '',
      tableData: [],
      editObject: null,
      langKey: 'periodic.obCheque.label.',
      tableColumns: ['amount', 'ref', 'vc_date', 'vc_payee', 'vc_rdate'],
      amountColumns: ['amount'],
      preferences: {
        filters: {
          selectedAcId: '',
        },
      },
      bankList: [],
    }
  },
  computed: {
    ...mapGetters(['language', 'user_id']),
  },
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  methods: {
    onAdd() {
      this.editObject = null
      this.leftView = 'add'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    onDelete(scope) {
      const name = scope.row.ref
        ? ': ' + scope.row.ref
        : this.$t('periodic.obCheque.label.currentCheque')
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}${name}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const lg_id = scope.row.lg_id
          return new Promise((resolve, reject) => {
            deleteObCheque(lg_id)
              .then(res => {
                if (this.editObject && this.editObject.lg_id === lg_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      this.loading = true
      fetchAccounts({ ac_bank: 'C' })
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedAcId === '') {
            this.preferences.filters.selectedAcId =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].account_id : ''
          } else {
            let bool = false
            this.bankList.forEach(ele => {
              if (this.preferences.filters.selectedAcId === ele.account_id) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedAcId = this.bankList[0].account_id
            }
          }
        })
        .then(this.reloadData)
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const account_id = this.preferences.filters.selectedAcId
        if (account_id === '') {
          return Promise.reject()
        }
        this.loading = true
        fetchObCheques(account_id)
          .then(res => {
            this.tableData = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const account_id = this.preferences.filters.selectedAcId
      if (!user_id || !account_id) {
        // this.$message.error('')
        return
      }
      this.loading = true
      oBChequesExport({ user_id, account_id, export_type })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    column_property(item) {
      if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key + (this.language === 'en' ? 'en' : 'cn')
      } else {
        return item.ss_key
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  /deep/ {
    .el-form-item__label {
      width: auto !important;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
