import Vue from 'vue'
const v = new Vue()
const h = v.$createElement
import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import i18n from '@/lang/index.js'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 60 * 1000, // request timeout
})

function blob2Str(blob) {
  return new Promise(resolve => {
    const reader = new FileReader()
    reader.onload = function() {
      resolve(this.result)
    }
    reader.readAsBinaryString(blob)
  })
}

// request interceptor
service.interceptors.request.use(
  config => {
    // Do something before request is sent
    if (store.getters.token) {
      // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
      config.headers['Authorization'] = 'Bearer ' + getToken()
      // config.headers['Content-Type'] = 'multipart/form-data'
      // config.headers['accept'] = '*/*'
    }
    config.headers['sch-code'] = store.getters.schCode
    config.headers['lang'] = store.getters.language

    if (config.responseType === 'blob') {
      config.headers['Cache-Control'] = 'no-cache'
    }

    if (JSON.stringify(config).match(/\d{4}-\d{1,2}-\d{1,2}T/)) {
      // do nothing
    }

    config.headers['channel'] = store.getters.system
    return config
  },
  error => {
    // Do something with request error
    Promise.reject(error)
  },
)

// response interceptor
service.interceptors.response.use(
  response => {
    /**
     * code为非20000是抛错 可结合自己业务进行修改
     */
    const res = response.data
    // const lang = Cookies.get('language')
    // 處理下載文件的 link
    if (response.request.responseType === 'arraybuffer') {
      if (
        response.headers['content-type'] ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        let filename = 'data.xlsx'
        let r = response.headers['content-disposition'].match(/ filename=\"(.*)\"/)
        if (r === null) {
          r = response.headers['content-disposition'].match(/ filename=(.*) ?/)
        }
        if (r) {
          filename = r[1]
        }
        const blob = new Blob([res], { type: response.headers['content-type'] })
        return { url: URL.createObjectURL(blob), filename }
        // return response.data
      } else {
        return response.data
      }
    }
    if (
      response.request.responseType === 'blob' &&
      response.headers['content-type'] ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      let filename = 'data.xlsx'
      let r = response.headers['content-disposition'].match(/ filename=\"(.*)\"/)
      if (r === null) {
        r = response.headers['content-disposition'].match(/ filename=(.*) ?/)
      }
      if (r) {
        filename = r[1]
      }
      // const blob = new Blob([res], { type: response.headers['content-type'] })
      return { url: URL.createObjectURL(res), filename }
      // return response.data
    } else if (response.request.responseType === 'blob') {
      let filename = 'download'
      let r = response.headers['content-disposition'].match(/ filename=\"(.*)\"/)
      if (r === null) {
        r = response.headers['content-disposition'].match(/ filename=(.*) ?/)
      }
      if (r) {
        filename = r[1]
      }
      // const blob = new Blob([res], { type: response.headers['content-type'] })
      return { url: URL.createObjectURL(res), filename }
      // return response.data
    }
    if (res.error) {
      // debugger
      Message({
        message: res.error.message,
        type: 'error',
        duration: 3 * 1000,
      })
      return Promise.reject(res)
    } else {
      // console.log('res')
      // console.log(response.data)
      return response.data
    }
  },
  async error => {
    if (
      error.config &&
      ((error.config.params && error.config.params.ignore_err) ||
        (error.config && error.config.ignore_err))
    ) {
      return Promise.reject(error)
    }
    if (error.code === 'ECONNABORTED') {
      Message({
        message: i18n.t('message.networkError'),
        type: 'error',
        dangerouslyUseHTMLString: false,
        duration: 3 * 1000,
      })
      return Promise.reject(error)
    }
    const status = error.response.status
    let msg = error.message
    switch (status) {
      case 401:
        // 需要重新登錄
        store.dispatch('FedLogOut').then(() => {
          MessageBox.alert(i18n.t('message.loginAgainMsg'), i18n.t('message.loginAgain'), {
            confirmButtonText: i18n.t('message.loginAgainBtn'),
            type: 'warning',
          })
            .then(() => {
              store.dispatch('FedLogOut').then(() => {
                location.reload() // 为了重新实例化vue-router对象 避免bug
              })
            })
            .catch(() => {
              store.dispatch('FedLogOut').then(() => {
                location.reload() // 为了重新实例化vue-router对象 避免bug
              })
            })
        })
        break
      case 422: {
        let data = error.response.data
        if (data instanceof Blob) {
          const str = await blob2Str(data)
          data = JSON.parse(str)
        }
        if (data && data.error && data.error.errors) {
          const errors = data.error.errors
          // 單獨處理Import錯誤提示
          if (error.request.responseURL.match(/\/import$/)) {
            return Promise.reject(errors)
          } else if (
            error.config.responseType === 'blob' &&
            error.request.responseURL.match(/export/)
          ) {
            const newData = []
            for (const item in errors) {
              for (const i in errors[item]) {
                newData.push(h('p', null, errors[item][i]))
              }
            }
            MessageBox.alert(i18n.t('message.tips'), {
              title: i18n.t('message.tips'),
              customClass: 'import-error-dialog',
              message: h('div', { class: 'import-error-dialog-content' }, newData),
              confirmButtonText: i18n.t('button.confirm'),
              type: 'warning',
              showCancelButton: false,
            })
            return Promise.reject(errors)
          } else {
            let errmsg = ''
            for (const item in errors) {
              errmsg += errors[item].join('<br/>') + '<br/>'
            }
            Message({
              message: errmsg,
              type: 'error',
              dangerouslyUseHTMLString: true,
              duration: 3 * 1000,
            })
          }
        }
        // msg = error.response && error.response.data && error.response.data.error && error.response.data.error.errors ? Object.values(error.response.data.error.errors)[0][0] : error.message

        // Message({
        //   message: msg,
        //   type: 'error',
        //   dangerouslyUseHTMLString: true,
        //   duration: 5 * 1000
        // })

        break
      }
      case 500:
        Message({
          message: i18n.t('message.responseError'),
          type: 'error',
          dangerouslyUseHTMLString: true,
          duration: 3 * 1000,
        })
        break
      default:
        // console.log('err' + error) // for debug
        msg =
          error.response && error.response.data && error.response.data.error
            ? error.response.data.error.message
            : error.message

        Message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          duration: 3 * 1000,
        })
        break
    }
    return Promise.reject(error)
  },
)

export default service
