<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div>
      <header v-if="false">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('router.settingScreenBasicSetting') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t($route.meta.title) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </header>
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 賬目 -->
          <el-form-item :label="$t('filters.fund')">
            <el-select v-model="preferences.filters.selectedFund" class="year" style="width: 130px">
              <el-option :label="allFund.label" :value="allFund.value" />
              <el-option
                v-for="item in funds"
                :key="item.fund_id"
                :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
                :value="item.fund_id"
              />
            </el-select>
          </el-form-item>
          <!-- 時段 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.selectedPeriod"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :clearable="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :value-format="dateValueFormat"-->
            <!--              type="daterange"-->
            <!--              align="right"-->
            <!--              unlink-panels-->
            <!--              style="width: 250px"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>
          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <!--              @change="onChangeMonth"-->
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" class="action-button" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              type="primary"
              size="mini"
              class="action-button"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
            <el-button
              v-if="hasPermission_Edit"
              type="primary"
              size="mini"
              class="action-button"
              @click="onSave"
            >
              {{ $t('periodic.dailyCollection.button.updateReceipt') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('PAGE')"
          />
        </div>
      </div>
      <b-table
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-actions="true"
        :actions-min-width="5"
        :show-checkbox="false"
        :default-top="230"
        :filter-class-function="filterClassFunction"
        :height="pageHeight - filtersHeight - 55"
        action-label=" "
        class="b-table"
        border
        @changeWidth="changeColumnWidth"
      >
        <template slot="columns">
          <vxe-table-column
            v-for="item in columns"
            :key="item.ss_key"
            :title="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :class-name="item.ss_key + ' mini-form'"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :field="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :params="{ key: item.ss_key }"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <el-input
                v-if="hasPermission_Edit && item.ss_key === 'vc_receipt' && scope.row.tx_num === 1"
                v-model="scope.row.vc_receipt"
                clearable
              />
              <el-date-picker
                v-else-if="
                  hasPermission_Edit && item.ss_key === 'vc_receipt_date' && scope.row.tx_num === 1
                "
                v-model="scope.row.vc_receipt_date"
                :format="styles.dateFormat"
                :placeholder="$t('placeholder.selectDate')"
                value-format="yyyy-MM-dd"
                type="date"
              />
              <span v-else-if="checkCanShow(scope.row, item)">{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)]
                )
              }}</span>
            </template>
          </vxe-table-column>
        </template>
      </b-table>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { updateDailyCollections, fetchDailyCollections } from '@/api/periodic/dailyCollection'
import { fetchFunds } from '@/api/master/funds'
import { editUserPreference } from '@/api/settings/user/preference'
import BTable from '@/components/BTable'
import DateRange from '@/components/DateRange/index'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import handlePDF from './handlePDF'
import dateUtil from '@/utils/date'
import { dailyCollectionExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'
import { listenTo } from '@/utils/resizeListen'
// import { amountFormat } from '@/utils'

export default {
  name: 'PeriodicDailyCollectionIndex',
  components: {
    BTable,
    DateRange,
    customStyle,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      funds: [],
      tableData: [],
      data: [],
      langKey: 'periodic.dailyCollection.label.',
      tableColumns: [
        'vc_date',
        'vc_no',
        'vc_method',
        'ref',
        'ac_code',
        'ac_name_',
        'descr',
        'lg_amount',
        'vc_amount',
        'vc_receipt',
        'vc_receipt_date',
      ],
      amountColumns: ['lg_amount', 'vc_amount'],
      preferences: {
        filters: {
          selectedFund: '',
          // selectedPeriod: [],
          begin_date: '',
          end_date: '',
          selectedYearCode: '',
          selectedMonth: '',
        },
      },
      childPreferences: ['selectedMonth'],
      years: [],
      monthList: [],
      selectedYearId: '',
      showYearPicker: false,
      periods: [],

      yearLastDate: new Date(),
      yearStartDate: new Date(),
      dateValueFormat: 'yyyy-MM-dd',

      filtersResizeListen: {},
      filtersHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'user_id']),
    allFund() {
      return {
        label: this.$t('filters.all'),
        value: '',
      }
    },
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
      // this.summaryResizeListen = listenTo(this.$refs.summary, ({ width, height, ele }) => {
      //   this.summaryHeight = height
      // })
    })
  },
  methods: {
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.funds && this.funds.length > 0 && this.preferences.filters.selectedFund !== '') {
            let bool = false
            this.funds.forEach(i => {
              if (i.fund_id === this.preferences.filters.selectedFund) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedFund = this.funds[0].fund_id
            }
          }
          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        this.loading = true
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, '1')
        const lastDateObj = new Date(year, month + 1, '0')

        const filter_data = {
          fund_id: '',
          begin_date: '',
          end_date: '',
        }

        filter_data.fund_id = this.preferences.filters.selectedFund
        filter_data.begin_date = this.preferences.filters.begin_date
        filter_data.end_date = this.preferences.filters.end_date
        // filter_data.begin_date = this.preferences.filters.selectedPeriod === null ||
        // this.preferences.filters.selectedPeriod[0] === undefined
        //   ? (dateUtil.format(beginDateObj, this.dateValueFormat))
        //   : this.preferences.filters.selectedPeriod[0]
        // filter_data.end_date = this.preferences.filters.selectedPeriod === null ||
        // this.preferences.filters.selectedPeriod[1] === undefined ?
        //   (dateUtil.format(lastDateObj, this.dateValueFormat)) :
        //   this.preferences.filters.selectedPeriod[1]
        // this.preferences.filters.selectedPeriod = [filter_data.begin_date, filter_data.end_date]
        if (!this.preferences.filters.begin_date) {
          filter_data.begin_date = dateUtil.format(beginDateObj, this.dateValueFormat)
          this.preferences.filters.begin_date = filter_data.begin_date
        }
        if (!this.preferences.filters.end_date) {
          filter_data.end_date = dateUtil.format(lastDateObj, this.dateValueFormat)
          this.preferences.filters.end_date = filter_data.end_date
        }

        fetchDailyCollections(filter_data)
          .then(res => {
            let group = 0
            res.forEach((item, index) => {
              if (item.tx_num === 1) {
                group++
              }
              item._group = group
            })
            this.tableData = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    onSave() {
      const dataArray = []
      this.tableData.forEach(ele => {
        const fy_code = ele.fy_code
        const vc_id = ele.vc_id
        const vc_receipt = ele.vc_receipt
        const vc_receipt_date = ele.vc_receipt_date

        const isExist = dataArray.findIndex(i => i.vc_id === vc_id) !== -1
        if (isExist) {
          return
        }
        dataArray.push({
          fy_code,
          vc_id,
          vc_receipt,
          vc_receipt_date,
        })
      })
      const receipt_date_json = JSON.stringify(dataArray)
      updateDailyCollections(receipt_date_json)
        .then(res => {
          this.$message.success(this.$t('message.success'))
          this.fetchData()
        })
        .catch(err => {
          this.$message.err(err)
        })
    },
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.thisSystem
      const content = {
        page: this.$route.name,
      }
      const pf_code = system.toLowerCase() + '.home'
      editUserPreference(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    onChangeMonth(val) {
      if (val === '') {
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateValueFormat)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateValueFormat)
        // this.preferences.filters.selectedPeriod = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, this.dateValueFormat)

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, this.dateValueFormat)
      // this.preferences.filters.selectedPeriod = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
    },
    filterClassFunction(row) {
      if (row.row._group % 2 === 0) {
        return 'table-stripe'
      }
    },
    checkCanShow(row, item) {
      if (row.tx_num === 1) {
        if (item.ss_key === 'vc_receipt' && (row.vc_receipt === 'X' || row.vc_receipt === 'Y')) {
          return false
        } else {
          return true
        }
      } else if (
        item.ss_key !== 'vc_date' &&
        item.ss_key !== 'vc_no' &&
        item.ss_key !== 'ref' &&
        item.ss_key !== 'vc_receipt' &&
        item.ss_key !== 'vc_receipt_date' &&
        item.ss_key !== 'vc_method' &&
        item.ss_key !== 'vc_amount'
      ) {
        return true
      }
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateValueFormat,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateValueFormat,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const fund_id = this.preferences.filters.selectedFund

      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, '1')
      const lastDateObj = new Date(year, month + 1, '0')

      // const begin_date = this.preferences.filters.selectedPeriod === null || this.preferences.filters.selectedPeriod[0] === undefined ? (dateUtil.format(beginDateObj, this.dateValueFormat)) : this.preferences.filters.selectedPeriod[0]
      // const end_date = this.preferences.filters.selectedPeriod === null || this.preferences.filters.selectedPeriod[1] === undefined ? (dateUtil.format(lastDateObj, this.dateValueFormat)) : this.preferences.filters.selectedPeriod[1]
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!this.preferences.filters.begin_date) {
        begin_date = dateUtil.format(beginDateObj, this.dateValueFormat)
        this.preferences.filters.begin_date = begin_date
      }
      if (!this.preferences.filters.end_date) {
        end_date = dateUtil.format(lastDateObj, this.dateValueFormat)
        this.preferences.filters.end_date = end_date
      }
      if (!user_id || !begin_date || !end_date) {
        // this.$message.error('')
        return
      }
      this.loading = true
      dailyCollectionExport({ user_id, export_type, fund_id, begin_date, end_date })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  .b-table {
    /*height: calc(100vh - 195px);*/
    /deep/ {
      .el-table__body-wrapper {
        /*height: calc(100vh - 195px);*/
      }

      .el-input--medium .el-input__inner {
        height: 23px;
        line-height: 23px;
      }

      .vc_receipt .el-input--medium {
        line-height: 23px;
      }

      .vc_receipt_date .el-input--medium {
        line-height: 23px;
      }

      .vxe-table--main-wrapper {
        .vxe-table--body-wrapper {
          .vxe-body--row {
            .vxe-body--column {
              .vxe-cell {
              }

              &.vc_receipt {
                padding: 0;
                .vxe-cell {
                  line-height: inherit;
                  height: 100%;
                  min-height: 25px;
                  padding: 0;
                  .el-input {
                    display: block;
                    line-height: initial;
                    height: 100%;
                    input {
                      height: 100%;
                      line-height: 100%;
                      vertical-align: middle;
                    }
                  }
                }
              }
              &.vc_receipt_date {
                padding: 0;
                .vxe-cell {
                  line-height: inherit;
                  height: 100%;
                  min-height: 25px;
                  padding: 0;
                  .el-input {
                    display: block;
                    line-height: initial;
                    height: 100%;
                    width: 100%;
                    min-width: 120px;
                    input {
                      height: 100%;
                      line-height: 100%;
                      vertical-align: middle;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
        .el-date-editor.el-input {
          width: 150px;
        }
      }
    }
  }

  .el-button {
    padding: 5px 15px;
    vertical-align: middle;
  }
}
</style>
