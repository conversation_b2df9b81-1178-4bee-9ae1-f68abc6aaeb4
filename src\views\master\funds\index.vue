<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <!-- <div slot="pane-right-filters" class="filter">
      </div>  -->
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="icon add"
            @click="onAddFund"
          >
            <svg-icon icon-class="add" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :data="funds"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <!--          <template slot="columns">-->
          <!--            <el-table-column-->
          <!--              v-for="item in styleColumns"-->
          <!--              :key="item.ss_id"-->
          <!--              :type="item.ss_key === '_index' ? 'index' : ''"-->
          <!--              :label="$t(langKey + item.ss_key)"-->
          <!--              :align="item.alignment"-->
          <!--              :width="item.width"-->
          <!--              :property="column_label(item)"-->
          <!--              :column-key="item.ss_key"-->
          <!--            />-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditFund(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDeleteFund(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!--左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-fund="editFund"
        :table-data="funds"
        @onCancel="onViewCancel"
      />
      <router-view />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'
import addPage from './add'
import { deleteFund, fetchFunds } from '@/api/master/funds'
import { mapGetters } from 'vuex'

import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

export default {
  name: 'MasterFundIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    addPage,
  },
  mixins: [mixinPermission, loadCustomStyle],
  data() {
    return {
      showDialog: false,
      leftView: '',
      funds: [],
      editFund: null,
      langKey: 'master.fund.label.',
      tableColumns: ['fund_name_', 'fund_abbr_'],
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    onAddFund() {
      this.editFund = null
      this.leftView = 'add'
    },
    onEditFund(scope) {
      this.editFund = scope.row
      this.leftView = 'edit'
    },
    onDeleteFund(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.fund_name_en : scope.row.fund_name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const fund_id = scope.row.fund_id
          return new Promise((resolve, reject) => {
            deleteFund(fund_id)
              .then(res => {
                if (this.editFund && this.editFund.fund_id === fund_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchFunds({ fund_type: 'F' })
        .then(res => {
          this.funds = res
        })
        .catch(() => {})
    },
    onViewCancel(update) {
      this.editFund = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
    column_label(item) {
      if (item.ss_key === '_index') {
        return '#'
      } else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key + (this.language === 'en' ? 'en' : 'cn')
      } else {
        return item.ss_key
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
