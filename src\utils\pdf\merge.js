import { PDFDocument } from 'pdf-lib'
// import fontkit from '@pdf-lib/fontkit'
import axios from 'axios'

export default class PdfMerge {
  constructor() {
    this._isInit = false
  }
  get isInit() {
    return this._isInit
  }
  checkInit() {
    if (!this.isInit) throw new Error('PDF Merge Not Init')
  }

  /**
   * 初始化
   * @return {Promise<void>}
   */
  async init(pdf) {
    try {
      if (pdf) {
        this.pdf = await PDFDocument.load(pdf)
      } else {
        this.pdf = await PDFDocument.create()
      }
      // this.pdf.registerFontkit(fontkit)
      this._isInit = true
    } catch (e) {
      //
    }
  }
  static async loadUrlPdf(url) {
    try {
      const res = await axios.get(url, { responseType: 'arraybuffer' })
      return res.data
    } catch (e) {
      //
    }
  }
  async addUrlPdf(url) {
    this.checkInit()
    try {
      const data = await PdfMerge.loadUrlPdf(url)
      const pdfDoc = await PDFDocument.load(data)
      const copiedPagesUrl = await this.pdf.copyPages(pdfDoc, pdfDoc.getPageIndices())
      copiedPagesUrl.forEach((page) => {
        this.pdf.addPage(page)
      })
    } catch (e) {
      //
    }
  }

  async addPdf(pdfArrayBuffer) {
    this.checkInit()
    const pdfFile = await PDFDocument.load(pdfArrayBuffer)
    const copiedPages1 = await this.pdf.copyPages(pdfFile, pdfFile.getPageIndices())
    copiedPages1.forEach((page) => {
      this.pdf.addPage(page)
    })
  }

  async getBlob() {
    this.checkInit()
    return new Blob([await this.pdf.save()], { type: 'application/pdf' })
  }
  async getBlobUrl() {
    this.checkInit()
    return URL.createObjectURL(
      new Blob([await this.pdf.save()], { type: 'application/pdf' }),
    )
  }
  async open(title, win) {
    this.checkInit()
    const urlCreator = window.URL || window.webkitURL
    const url = urlCreator.createObjectURL(
      new Blob([await this.pdf.save()], { type: 'application/pdf' }),
    )
    if (win) {
      win.location = url
      win.document.title = title
      win.onload = function() {
        win.location = url
        win.document.title = title
      }
    } else {
      const win = window.open('#/blank', '_blank')
      win.onload = function() {
        win.location = url
        win.document.title = title
      }
    }

    // win.location = 'about:blank'
    return win
  }
}
