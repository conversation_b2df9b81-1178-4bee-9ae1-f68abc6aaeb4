<template>
  <div class="setting-basic">
    <right>
      <el-main slot="content">
        <el-form ref="form" :model="form" class="form" label-width="120px">
          <el-form-item :label="$t('setting.basic.font_color')">
            <el-color-picker v-model="form.table_header_font_color" />
          </el-form-item>
          <el-form-item :label="$t('setting.basic.background_color')">
            <el-color-picker v-model="form.table_header_background_color" />
          </el-form-item>
          <el-row class="actions">
            <el-button size="mini" type="info" @click="onFetch(0)">
              {{ $t('button.loadDefault') }}
            </el-button>
            <el-button size="mini" type="info" @click="onSave(0)">
              {{ $t('button.saveDefault') }}
            </el-button>
            <el-button size="mini" type="danger" @click="onFetch(1)">
              {{ $t('button.reset') }}
            </el-button>
            <el-button size="mini" type="primary" @click="onSave(1)">
              {{ $t('button.update') }}
            </el-button>
          </el-row>
        </el-form>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'

export default {
  name: 'SettingScreenTableHeader',
  components: {
    right,
  },
  mixins: [common],
  data() {
    return {
      form: {
        table_header_font_color: '#909399',
        table_header_background_color: '#ffffff',
      },

      ss_code: 'screen_basic_table_header',
      ss_type: 'table_header',
    }
  },
  computed: {},
  created() {},
  methods: {},
}
</script>
