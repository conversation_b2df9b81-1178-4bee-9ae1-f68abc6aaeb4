<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="428px"
  >
    <div class="content-area">
      <div>
        <span>{{ $t('customReport.areaType') }}</span>
        <el-radio-group v-model="radio">
          <el-radio :label="`ACCOUNT`">
            {{ $t('customReport.accountSituation') }}
          </el-radio>
          <el-radio :label="`BANK`">
            {{ $t('customReport.bankSituation') }}
          </el-radio>
          <el-radio :label="`SIGNATURE`">
            {{ $t('customReport.signature') }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" type="primary" @click="onConfirm">{{ $t('button.confirm') }}</el-button>
      <el-button size="mini" @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddContentArea',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      radio: '',
    }
  },
  computed: {
    title() {
      return this.$t('customReport.addContentArea')
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      },
    },
  },
  methods: {
    onConfirm() {
      this.$emit('addContentArea', this.radio)
      this.dialogVisible = false
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    height: 50px !important;
  }
  .el-dialog__footer {
    text-align: right;
  }
}
.content-area {
  display: flex;
  align-items: center;
  // justify-content: center;
  margin: 0 20px;
  height: 100%;
  span {
    margin-right: 10px;
  }
}
</style>
