<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="150px"
    >
      <!-- 編號 -->
      <el-form-item
        :rules="codeRules"
        :label="$t('master.voucher_type.label.vt_code')"
        :class="{ 'code-rules-error': haveExist }"
        prop="vt_code"
      >
        <el-input v-model="form.vt_code" clearable />
      </el-form-item>
      <!-- 撥款 -->
      <el-form-item :rules="rules" :label="$t('master.voucher_type.label.fund_id')" prop="fund_id">
        <el-select v-model="form.fund_id" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in funds"
            :key="item.fund_id"
            :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
            :value="item.fund_id"
          />
        </el-select>
      </el-form-item>
      <!-- 類別 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.voucher_type.label.vt_category')"
        prop="vt_category"
      >
        <el-select v-model="form.vt_category">
          <el-option
            v-for="item in vt_category_list"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 描述（中） -->
      <el-form-item
        :rules="rules"
        :label="$t('master.voucher_type.label.vt_description_cn')"
        prop="vt_description_cn"
      >
        <el-input v-model="form.vt_description_cn" clearable />
      </el-form-item>
      <!-- 描述（英） -->
      <el-form-item
        :rules="rules"
        :label="$t('master.voucher_type.label.vt_description_en')"
        prop="vt_description_en"
      >
        <el-input v-model="form.vt_description_en" clearable />
      </el-form-item>
      <!-- 銀行賬號 -->
      <el-form-item
        v-if="'PRCA'.includes(form.vt_category)"
        :rules="{
          required: 'PRCA'.includes(form.vt_category),
          trigger: 'blur',
          message: ' ',
        }"
        :label="$t('master.voucher_type.label.vt_ac_code')"
        prop="vt_ac_code"
      >
        <el-select v-model="form.vt_ac_code" style="width: 100%">
          <el-option
            v-for="item in accounts"
            :key="item.account_id"
            :label="`[${item.ac_code}] ${language === 'en' ? item.ac_name_en : item.ac_name_cn}`"
            :value="item.ac_code"
          >
            {{ `[${item.ac_code}] ${language === 'en' ? item.ac_name_en : item.ac_name_cn}` }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 支票款式 -->
      <el-form-item
        v-if="'P' === form.vt_category"
        :rules="{
          required: 'P' === form.vt_category,
          trigger: 'blur',
          message: ' ',
        }"
        :label="$t('master.voucher_type.label.chq_template_code')"
        prop="chq_template_code"
      >
        <el-select v-model="form.chq_template_code">
          <el-option
            v-for="item in cheques"
            :key="item.chq_id"
            :label="language === 'en' ? item.chq_name_en : item.chq_name_cn"
            :value="item.chq_template_code"
          />
        </el-select>
      </el-form-item>
      <!-- 格式 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.voucher_type.label.vt_format')"
        prop="vt_format"
      >
        <el-input v-model="form.vt_format" clearable />
      </el-form-item>
      <!-- 組別 -->
      <el-form-item :label="$t('master.voucher_type.label.vt_group')" prop="vt_group">
        <el-input v-model="form.vt_group" clearable />
      </el-form-item>

      <!-- 活躍年度 -->
      <el-form-item :label="$t('master.voucher_type.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getVoucherType, editVoucherType, createVoucherType } from '@/api/master/voucherType'
import { fetchFunds } from '@/api/master/funds'
import { fetchYears } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'
import { fetchCheques } from '@/api/master/cheques'

export default {
  name: 'MasterVoucherTypeAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        voucher_type_id: '',
        fund_id: '',
        vt_category: '',
        vt_code: '',
        vt_description_cn: '',
        vt_description_en: '', // 父職員類別
        vt_description_: '',
        vt_format: '',
        vt_ac_code: '',
        chq_template_code: '',
        vt_group: '',
        active_year: '',
      },
      defaultForm: {
        voucher_type_id: '',
        fund_id: '',
        vt_category: '',
        vt_code: '',
        vt_description_cn: '',
        vt_description_en: '', // 父職員類別
        vt_description_: '',
        vt_format: '',
        vt_ac_code: '',
        chq_template_code: '',
        vt_group: '',
        active_year: '',
      },
      rules: [
        {
          // 必填
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      funds: [],
      accounts: [],
      cheques: [],
      staffTypes: [],
      seqStartLevel: 1,
      active_year_arr: [],
      loading: true,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    vt_category_list() {
      return ['P', 'R', 'C', 'T', 'J', 'A'].map(i => {
        return {
          label: this.$t('voucher_type_category.' + i),
          value: i,
        }
      })
    },
    allVTCategory() {
      return {
        label: this.$t('voucher_type_category.ALL'),
        value: '',
      }
    },
    allFund() {
      return {
        label: this.$t('master.fund.all_fund'),
        value: '',
      }
    },
    codeRules() {
      return [
        {
          required: true,
          validator: this.checkCode,
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    changePosition() {
      this.forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentStaffType(staffType, html, startLevel = 1) {
      let text = this.language === 'en' ? staffType.st_type_en : staffType.st_type_cn
      if (html) {
        text = '&nbsp;'.repeat((staffType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    checkRequired(rule, value, callback) {},
    checkCode(rule, value, callback) {
      if (!value) {
        console.log('checkCode', !value)
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      let check = this.tableData.some(i => i.vt_code === value)
      if (this.editObject) {
        check = this.tableData.some(
          i => i.vt_code === value && i.voucher_type_id !== this.editObject.voucher_type_id,
        )
      }
      if (check) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getVoucherType(this.editObject.voucher_type_id)
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.form.vt_category = this.vt_category_list.length ? this.vt_category_list[0].value : ''
          this.active_year_arr = []
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject && res.length) {
            if (this.fyCode) {
              const selected = res.find(i => i.fy_code === this.fyCode)
              if (selected) {
                this.active_year_arr.push(selected.fy_code)
              }
            } else {
              if (this.currentYear && this.currentYear.fy_code) {
                const item = res.find(i => i.fy_code === this.currentYear.fy_code)
                if (item) {
                  this.active_year_arr.push(item.fy_code)
                }
              }
            }
          }
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
          !this.editObject && res.length && (this.form.fund_id = res[0].fund_id)
        })
        .then(() => fetchAccounts({ ac_bank: 'P,C,S,F' }))
        .then(res => {
          this.accounts = res
          !this.editObject && res.length && (this.form.vt_ac_code = res[0].ac_code)
          if (res.length > 0) {
            if (this.form.vt_ac_code) {
              const bank = res.find(i => i.ac_code === this.form.vt_ac_code)
              if (!bank) {
                this.form.vt_ac_code = ''
              }
            } else {
              this.form.vt_ac_code = res[0].ac_code
            }
          }
        })
        .then(fetchCheques)
        .then(res => {
          this.cheques = res

          !this.editObject && res.length && (this.form.chq_template_code = res[0].chq_template_code)
        })
        .finally(() => {
          this.loading = false
        })
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const voucher_type_id = this.form.voucher_type_id
        const fund_id = this.form.fund_id
        const vt_category = this.form.vt_category
        const vt_code = this.form.vt_code
        const vt_description_cn = this.form.vt_description_cn
        const vt_description_en = this.form.vt_description_en
        const vt_format = this.form.vt_format
        const vt_ac_code = 'PRCA'.includes(this.form.vt_category) ? this.form.vt_ac_code : undefined
        const chq_template_code = vt_category === 'P' ? this.form.chq_template_code : undefined
        const vt_group = this.form.vt_group
        const active_year = this.active_year_arr.filter(i => i).join(',')

        if (this.editObject) {
          // 編輯
          editVoucherType({
            voucher_type_id,
            fund_id,
            vt_category,
            vt_code,
            vt_description_cn,
            vt_description_en,
            vt_format,
            vt_ac_code,
            chq_template_code,
            vt_group,
            active_year,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createVoucherType({
            voucher_type_id,
            fund_id,
            vt_category,
            vt_code,
            vt_description_cn,
            vt_description_en,
            vt_format,
            vt_ac_code,
            chq_template_code,
            vt_group,
            active_year,
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
