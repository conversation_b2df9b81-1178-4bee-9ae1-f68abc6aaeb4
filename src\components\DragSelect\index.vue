<template>
  <el-select
    ref="dragSelect"
    v-model="selectVal"
    v-bind="$attrs"
    class="drag-select native"
    multiple
    @change="onChange"
    v-on="$listeners"
  >
    <slot />
  </el-select>
</template>

<script>
import Sortable from 'sortablejs'

export default {
  name: 'DragSelect',
  props: {
    value: {
      type: Array,
      required: true,
    },
    filterText: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    selectVal: {
      get() {
        return [...this.value]
      },
      set(val) {
        this.$emit('input', [...val])
      },
    },
  },
  mounted() {
    this.setSort()
  },
  methods: {
    setSort() {
      const that = this
      const el = this.$refs.dragSelect.$el.querySelectorAll('.el-select__tags > span')[0]
      this.sortable = Sortable.create(el, {
        animation: 100,
        ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
          // to avoid Firefox bug
          // Detail see : https://github.com/RubaXa/Sortable/issues/1012
        },
        filter: evt => {
          return that.filterText.includes(evt.target.innerText)
        },
        onEnd: evt => {
          const targetRow = this.value.splice(evt.oldIndex, 1)[0]
          this.value.splice(evt.newIndex, 0, targetRow)
        },
      })
    },
    onChange(val) {
      this.$emit('change', val)
    },
  },
}
</script>

<style scoped>
.drag-select >>> .sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #389fff !important;
}

.drag-select >>> .el-tag {
  cursor: pointer;
}
.drag-select >>> .el-input {
  /*width: 500px!important;*/
}
.drag-select >>> .el-select__tags {
  width: 100%;
  max-width: 468px;
}
</style>
