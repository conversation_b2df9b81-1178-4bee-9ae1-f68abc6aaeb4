<template>
  <el-form-item class="form-table-box">
    <!-- 獲取簽名預設值 -->
    <div class="form-table" style="width: 120px">
      <el-button size="mini" type="info" @click="onSetDefault">
        {{ $t('setting.printout.button.defaultValue') }}
      </el-button>
    </div>

    <table class="form-table">
      <tbody>
        <tr>
          <td class="table-label">
            <span>{{ $t('setting.printout.label.stepCN') }}</span>
          </td>
          <td v-for="(item, index) in signTable" :key="index">
            <el-input
              v-model="item.step_cn"
              class="form-table-cell"
              @change="onChange(item.step_cn, index, 'step_cn')"
            />
          </td>
        </tr>
        <tr>
          <td class="table-label">
            <span>{{ $t('setting.printout.label.postCN') }}</span>
          </td>
          <td v-for="(item, index) in signTable" :key="index">
            <el-input
              v-model="item.post_cn"
              class="form-table-cell"
              @change="onChange(item.post_cn, index, 'post_cn')"
            />
          </td>
        </tr>
        <tr>
          <td class="table-label">
            <span>{{ $t('setting.printout.label.stepEN') }}</span>
          </td>
          <td v-for="(item, index) in signTable" :key="index">
            <el-input
              v-model="item.step_en"
              class="form-table-cell"
              @change="onChange(item.step_en, index, 'step_en')"
            />
          </td>
        </tr>
        <tr>
          <td class="table-label">
            <span>{{ $t('setting.printout.label.postEN') }}</span>
          </td>
          <td v-for="(item, index) in signTable" :key="index">
            <el-input
              v-model="item.post_en"
              class="form-table-cell"
              @change="onChange(item.post_en, index, 'post_en')"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </el-form-item>
</template>

<script>
export default {
  name: 'SignTable',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    colLength: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      //
    }
  },
  computed: {
    signTable: {
      get() {
        return this.data.filter((e, i) => i < this.colLength)
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  watch: {
    sign_data(newVal, oldVal) {
      this.$emit('update:data')
    },
    data(newVal, oldVal) {
      this.sign_data = newVal
    },
  },
  created() {},
  methods: {
    onChange(val, index, column) {
      this.$emit('change', val, index, column)
    },
    onSetDefault() {
      this.$emit('setDefault')
    },
  },
}
</script>

<style lang="scss" scoped>
.table-label {
  text-align: right !important;
  padding: 0 10px;
  span {
    text-align: right !important;
    width: 100px;
  }
}
.form-table-cell {
  animation: fadenum 0.5s infinite;
  animation-iteration-count: 1;
}
@-webkit-keyframes fadenum {
  /*设置内容由显示变为隐藏*/
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
