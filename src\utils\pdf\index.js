// import { pdfStyle } from '@/utils/pdf/generator'
import { asyncLoadJs } from '@/utils/asyncLoadJs'
import { Message, MessageBox } from 'element-ui'
import i18n from '@/lang/index.js'
import { generateReportList } from './workers/index.js'
import serialize from 'serialize-javascript'
export let isInitPDF = false
export let isError = false
let pdfMake
export function initPDFFont() {
  return new Promise((resolve, reject) => {
    if (pdfMake === undefined) {
      pdfMake = (() => require('pdfmake/build/pdfmake'))()
    }
    asyncLoadJs('./static/js/vfs_fonts_pf.js')
      .then(() => {
        isInitPDF = true
        pdfMake.vfs = window.pdfMake.vfs

        const normal = 'PingFangTC-Light.ttf'
        const bold = 'PingFangTC-Semibold.ttf'

        pdfMake.fonts = {
          cn: {
            normal: normal,
            bold: bold,
            italics: normal,
            bolditalics: bold,
          },
        }
        resolve()
      })
      .catch(() => {
        isError = true
        reject()
      })
  })
}

let pdfWinBlock = false
// 提示窗口被攔截
function showWindowBlock() {
  if (pdfWinBlock) {
    return
  }
  pdfWinBlock = true
  MessageBox.alert(i18n.t('message.pdfWinBlock'), i18n.t('message.pdfPrintError'), {
    type: 'warning',
    callback: () => {
      pdfWinBlock = false
    },
  })
}

export function initTips() {
  if (isError) {
    Message.error(i18n.t('message.pdfFailedToLoad'))
  } else {
    Message.warning(i18n.t('message.pdfLoading'))
  }
}

export async function generatePdf(doc) {
  if (pdfMake === undefined) {
    pdfMake = (() => require('pdfmake/build/pdfmake'))()
  }
  if (pdfMake.vfs === undefined) {
    Message.warning(i18n.t('message.pdfLoading'))
    return
    // const pdfFonts = (() => require('./vfs_fonts.min'))()
    // const pdfFonts = window.pdfFonts
    // const pdfFonts = () => require('./vfs_fonts.min')
    // const notice = Notification({
    //   // title: 'PDF',
    //   message: i18n.t('message.pdfLoading'),
    //   iconClass: 'el-icon-loading',
    //   offset: 40,
    //   duration: 0,
    //   showClose: false
    // })
    // try {
    //   await asyncLoadJs('./static/vfs_fonts.min.js')
    // } catch (e) {
    //   notice.close()
    //   Notification({
    //     message: i18n.t('message.pdfError'),
    //     iconClass: 'el-icon-error'
    //   })
    //   return
    // }
    // notice.close()
    //
    // pdfMake.vfs = window.pdfMake.vfs
    // pdfMake.fonts = {
    //   cn: {
    //     normal: 'msjh.ttf',
    //     bold: 'msjhbd.ttf',
    //     italics: 'msjh.ttf',
    //     bolditalics: 'msjhbd.ttf'
    //   }
    // }
  }

  const d = {
    // styles: {
    //   ...pdfStyle
    // },
    info: {
      title: 'PDF',
      author: 'Norray',
      subject: 'PDF',
    },
    defaultStyle: {
      font: 'cn',
      fontSize: 9,
    },
  }
  // debugger
  const docDefinition = Object.assign(d, doc)
  return pdfMake.createPdf(docDefinition)
}

// function openDoc(options, win) {
//   const that = this
//   try {
//     this.getBlob(function(result) {
//       if (!win) {
//         win = that._openWindow()
//       }
//       const urlCreator = window.URL || window.webkitURL
//       win.location.href = urlCreator.createObjectURL(result)
//     }, options)
//   } catch (e) {
//     Message.warning(i18n.t('message.pdfError') + '\n' + e.toString())
//     win && win.close()
//     throw e
//   }
// }

export function openPdf(doc, win) {
  doc.defaultStyle = { font: 'cn' }
  return new Promise(resolve => {
    let docs = []
    if (Array.isArray(doc)) {
      docs = doc.map(d => serialize(d))
    } else {
      docs = [serialize(doc)]
    }
    const onProgress = (completeCount, length) => {
      console.log(completeCount, length)
    }
    generateReportList(docs, onProgress).then(url => {
      if (!win) {
        win = window.open('', '_blank')
      }
      if (win === null) {
        showWindowBlock()
        resolve()
        return
      }
      win.location.href = url
      resolve()
    })
  })
  // generatePdf(doc).then(pdf => {
  //   const open =  pdf.open({}, win)
  // })
}

export function printPdf(doc, win) {
  generatePdf(doc).then(pdf => {
    pdf.print({}, win)
  })
}

export function downloadPdf(doc, filename) {
  return new Promise(resolve => {
    generatePdf(doc).then(pdf => {
      pdf.download(filename, function() {
        resolve()
      })
    })
  })
}

export function getDataUrlPdf(doc, filename) {
  return new Promise(resolve => {
    generatePdf(doc).then(pdf => {
      pdf.getDataUrl(dataUrl => {
        resolve(dataUrl)
      })
    })
  })
}

export function getBase64Pdf(doc, filename) {
  return new Promise(resolve => {
    generatePdf(doc).then(pdf => {
      pdf.getBase64(data => {
        resolve(data)
      })
    })
  })
}

export function getBufferPdf(doc, filename) {
  return new Promise(resolve => {
    generatePdf(doc).then(pdf => {
      pdf.getBuffer(buffer => {
        resolve(buffer)
      })
    })
  })
}

export function getBlobPdf(doc, filename) {
  return new Promise(resolve => {
    generatePdf(doc).then(pdf => {
      pdf.getBlob(buffer => {
        resolve(buffer)
      })
    })
  })
}

export function toDataUrl(file) {
  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    xhr.responseType = 'blob'
    xhr.onload = function() {
      var reader = new FileReader()
      reader.onloadend = function() {
        resolve(reader.result)
      }
      reader.readAsDataURL(xhr.response)
    }
    xhr.open('GET', file)
    xhr.send()
  })
}
/**
 *
 * @param url 图片路径
 * @param ext 图片格式
 */
export function getUrlBase64(url, ext) {
  return new Promise((resolve, reject) => {
    let canvas = document.createElement('canvas') // 创建canvas DOM元素
    const ctx = canvas.getContext('2d')
    const img = new Image()
    img.setAttribute('crossOrigin', 'anonymous')
    // if (process.env.NODE_ENV === 'production') {
    //   img.src = url
    // } else {
    //   img.src = require('@/assets/pdflogo.jpg')
    // }
    img.src = url
    img.onload = function(v) {
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0) // 参数可自定义
      const dataURL = canvas.toDataURL(ext ? 'image/' + ext : undefined)
      canvas = null
      resolve(dataURL)
    }
  })
}
const emptyImage =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAMAAAAoyzS7AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3FpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDAxODhmZC0xY2E2LTM3NGMtYjNhZS1lYTQ1NDUxODQ1Y2IiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDg4MEM2RDY4QjY2MTFFOUE3ODBBMjg5QTM0NTc4QjMiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDg4MEM2RDU4QjY2MTFFOUE3ODBBMjg5QTM0NTc4QjMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjRkMDE4OGZkLTFjYTYtMzc0Yy1iM2FlLWVhNDU0NTE4NDVjYiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDAxODhmZC0xY2E2LTM3NGMtYjNhZS1lYTQ1NDUxODQ1Y2IiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7Zr/D1AAAABlBMVEX///8AAABVwtN+AAAADElEQVR42mJgAAgwAAACAAFPbVnhAAAAAElFTkSuQmCC'
export function getEmptyImageBase64() {
  return emptyImage
}

export function mm2pt(mm) {
  var pointRatio = 2.8346456692913384
  return parseFloat(mm) * pointRatio
}
export function pt2mm(pt) {
  var x = 0.3527777777777778
  return parseFloat(pt) * x
}

export function px2pt(pt) {
  var x = 0.75292857248934
  return parseFloat(pt) * x
}

/**
 * 函數內部插入數據
 * @param funcStr 函數的字符串，必須是function(){}的形式
 * @param object 需要插入的數據
 * @returns {Promise<any>}
 */
export function insertData(funcStr, object) {
  const firstBracket = funcStr.indexOf('{')
  const funcArr = [funcStr.slice(0, firstBracket + 1), funcStr.slice(firstBracket + 1)]
  const objKeys = Object.keys(object)
  let str = ''
  for (let i = 0; i < objKeys.length; i++) {
    str += `const ${objKeys[i]} = ${serialize(object[objKeys[i]])};`
  }
  funcArr.splice(1, 0, str)
  const s = funcArr.join('\n')
  // eslint-disable-next-line no-eval
  return eval('(' + s + ')')
}
