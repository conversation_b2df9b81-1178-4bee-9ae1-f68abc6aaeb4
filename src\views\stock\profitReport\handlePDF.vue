<script>
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'

export default {
  name: 'HandlePDF',
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(this.formatPrintData)
          .then(({ schoolInfo, pageInfo, columns, tableData, content }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              printSetting,
              content,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const language = printSetting.language

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }
        const langKey = this.langKey
        const title = this.$t(langKey + 'title', language)
        const style = this.styleOption.find(i => i.value === this.preferences.filters.selectedStyle)
        const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
        const month = this.monthList.find(i => i.pd_code === this.preferences.filters.selectedMonth)

        // 右邊表格
        let pageInfo = {}
        if (this.preferences.filters.selectedStyle !== 'Y') {
          pageInfo = {
            title: title,
            filename: `${title} - ${style.label} - ${month.pd_name}`,
            data: [
              {
                label: this.$t('stock.style', language) + ':',
                value: this.$t(style.labelKey, language),
              },
              {
                label: this.$t('stock.years', language) + ':',
                value: year.fy_name,
              },
              {
                label: this.$t('stock.months', language) + ':',
                value: month.pd_name,
              },
            ],
          }
        } else {
          pageInfo = {
            title: title,
            filename: `${title} - ${style.label} - ${year.fy_name}`,
            data: [
              {
                label: this.$t('stock.style', language) + ':',
                value: this.$t(style.labelKey, language),
              },
              {
                label: this.$t('stock.years', language) + ':',
                value: year.fy_name,
              },
            ],
          }
        }

        // 表頭
        const tmpResult = []
        let tmpColumn = []

        //
        const itemNumPerPage = printSetting.item_num_per_page

        // 處理列，每頁只有(itemNumPerPage * 2 + 2)列
        this.tableData.forEach((item, index) => {
          if (tmpColumn.length === 0) {
            tmpColumn.push(
              {
                text: '',
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 2,
                alignment: 'center',
                width: 8,
                border: [true, true, true, true],
              },
              {
                text: '',
                width: 12,
              },
            )
          } else if (tmpColumn.length === itemNumPerPage * 2 + 2) {
            tmpResult.push(tmpColumn)
            tmpColumn = [
              {
                text: '',
                style: 'tableHeader',
                rowSpan: 1,
                colSpan: 2,
                alignment: 'center',
                width: 8,
                border: [true, true, true, true],
              },
              {
                text: '',
                width: 12,
              },
            ]
          }
          tmpColumn.push(
            {
              text: language === 'en' ? item.sk_name_en : item.sk_name_cn,
              style: 'tableHeader',
              rowSpan: 1,
              colSpan: 2,
              alignment: 'center',
              border: [true, true, true, true],
              itemData: item,
              width: 40 / itemNumPerPage,
            },
            {
              text: '',
              itemData: item,
              width: 40 / itemNumPerPage,
            },
          )
        })

        tmpResult.push(tmpColumn)

        const columnData = []
        let result = []

        tmpResult.forEach(item => {
          result = []
          item.map(column => {
            result.push(column)
          })
          columnData.push(result)
        })
        // console.log( this.rowData)
        const { content } = this.generateContent(
          schoolInfo,
          pageInfo,
          columnData,
          printSetting,
          this.rowData,
          this.tableData,
        )

        resolve({
          schoolInfo,
          pageInfo,
          printSetting,
          content,
        })
      })
    },

    generateContent(schoolInfo, pageInfo, columnData, printSetting, allRow, allData) {
      const langKey = this.langKey
      const language = printSetting.language

      const margin_right = mm2pt(printSetting.margin_right)
      const title_width = mm2pt(printSetting.title_width)

      const content = []

      columnData.forEach((groupColumn, groupIndex) => {
        const columns = []
        const columns_data = []

        groupColumn.forEach((col, index) => {
          columns_data[index] = col.itemData === undefined ? {} : col.itemData
          col.itemData = null
          columns.push(col)
        })

        // 學校信息
        const schoolTable = generateSchoolInfo(
          schoolInfo.name_cn,
          schoolInfo.name_en,
          schoolInfo.logo,
        )
        // 頁面信息
        const pageTable = generatePageInfo(
          pageInfo.title,
          pageInfo.filename,
          pageInfo.data,
          title_width,
          margin_right,
        )

        // 頁頭，包含LOGO，頁面信息
        const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

        const tableData = []

        const border = [false, false, false, false]
        allRow.forEach((rowData, colIndex) => {
          const row = []
          let l =
            rowData.l !== undefined && rowData.l_langKey !== undefined && rowData.l_langKey !== ''
              ? this.$t(langKey + rowData.l_langKey, language)
              : ''
          if (language === 'en') {
            if (rowData.l_langKey === 'cost') {
              l = 'COGS'
            } else if (rowData.l_langKey === 'percentageProfit') {
              l = 'Percentage'
            }
          }
          row.push({
            text: l,
            alignment: 'right',
            rowSpan: 1,
            colSpan: 1,
            style: 'tableContent',
            border: border,
          })

          row.push({
            text:
              rowData.r !== undefined && rowData.r_langKey !== undefined && rowData.r_langKey !== ''
                ? this.$t(langKey + rowData.r_langKey, language)
                : '',
            alignment: 'right',
            rowSpan: 1,
            colSpan: 1,
            style: 'tableContent',
            border: border,
          })

          columns.forEach((col, colIndex) => {
            if (colIndex < 2) {
              return
            }
            if (colIndex % 2 === 0) {
              row.push({
                text:
                  rowData.l_key === 'profits_percent'
                    ? rowData.l_brackets
                      ? '(' + this.formatPercent(columns_data[colIndex][rowData.l_key]) + ')'
                      : this.formatPercent(columns_data[colIndex][rowData.l_key])
                    : rowData.l_brackets
                      ? '(' + this.formatAmount(columns_data[colIndex][rowData.l_key]) + ')'
                      : this.formatAmount(columns_data[colIndex][rowData.l_key]),
                alignment: 'right',
                rowSpan: 1,
                colSpan: 1,
                style: 'tableContent',
                border: [false, false, false, !!rowData.l_underline],
              })
            } else {
              row.push({
                text:
                  rowData.r_key === 'profits_percent'
                    ? rowData.r_brackets
                      ? '(' + this.formatPercent(columns_data[colIndex][rowData.r_key]) + ')'
                      : this.formatPercent(columns_data[colIndex][rowData.r_key])
                    : rowData.r_brackets
                      ? '(' + this.formatAmount(columns_data[colIndex][rowData.r_key]) + ')'
                      : this.formatAmount(columns_data[colIndex][rowData.r_key]),
                alignment: 'right',
                rowSpan: 1,
                colSpan: 1,
                style: 'tableContent',
                border: [false, false, false, !!rowData.r_underline],
              })
            }
          })
          tableData.push(row)
        })

        // 表格寬度
        const widths = generateWidths(columns)

        content.push(this.generateContentObject(groupIndex, widths, pageHeader, columns, tableData))

        if (printSetting.sign_style.toString() === '1') {
          // 浮動
          const sign_height = mm2pt(printSetting.sign_height)
          const sign_space = mm2pt(printSetting.sign_space)
          const margin_left = mm2pt(printSetting.margin_left)
          const bottom_sign = printSetting.sign_style.toString() === '2'

          // 簽名設置
          const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
          const signTable = generateSign(
            printSetting.sign_line,
            signColumn,
            printSetting.language,
            sign_height,
            sign_space,
            margin_left,
            margin_right,
            printSetting.font_size_signature,
            bottom_sign,
          )

          content.push(signTable)
        }
      })
      return { content }
    },
    generateContentObject(groupIndex, widths, pageHeader, columns, tableData) {
      return [
        {
          // Content
          headlineLevel: groupIndex > 0 ? 1 : 0,
          pageBreak: groupIndex > 0 ? 'before' : undefined,
          width: '100%',
          style: 'tableExample',
          table: {
            dontBreakRows: true,
            keepWithHeaderRows: 1,
            // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
            widths: widths,
            // heights: tableData.map((e, i) => i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto'),
            headerRows: 2, // columns.length + 1,
            body: [
              pageHeader, // 頁頭
              columns, // 數據表頭
              ...tableData, // 數據
            ],
          },
          layout: {
            vLineWidth: lineWidth,
            hLineWidth: lineWidth,
            hLineColor: lineColor,
            vLineColor: lineColor,
          },
        },
      ]
    },
    async showPDF({ schoolInfo, pageInfo, printSetting, content }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: content,
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        pageBreakBefore: function(
          currentNode,
          followingNodesOnPage,
          nodesOnNextPage,
          previousNodesOnPage,
        ) {
          return currentNode.headlineLevel === 1
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      // console.log(JSON.stringify(docDefinition))
      await openPdf(docDefinition)
    },
  },
}
</script>
