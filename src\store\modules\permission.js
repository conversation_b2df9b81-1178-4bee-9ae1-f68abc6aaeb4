import { asyncRouterMap, constantRouterMap } from '@/router'
// import { getUserInfoByStaff } from '@/api/login'
// import { fetchPermissions } from '@/api/master/role'

/**
 * 通过meta.p_code判断是否与当前用户权限匹配
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.p_code) {
    const p_code = route.meta.p_code
    if (p_code === '*') return true
    if (roles[p_code] && roles[p_code].p_selected) {
      if (roles[p_code].p_selected.includes('Y')) {
        return true
      }
    }
  }

  return false
}

function hasPrintPermission(roles, route) {
  if (route.needAPrint && route.meta && route.meta.p_code) {
    const p_code = route.meta.p_code
    if (roles[p_code] && roles[p_code].p_selected) {
      const np = route.needAPrint.split(',')
      for (let i = 0; i < np.length; i++) {
        if (roles[p_code].p_selected.includes(np[i])) {
          return true
        }
      }
    }
  }
  return false
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param routes asyncRouterMap
 * @param roles
 */
function filterAsyncRouter(routes, roles) {
  const res = []
  let hasPDFRouter = false
  routes.forEach(route => {
    const tmp = { ...route }
    if (!hasPDFRouter && hasPrintPermission(roles, tmp)) {
      hasPDFRouter = true
    }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        const { data: cData, hasPDFRouter: hasPdf } = filterAsyncRouter(tmp.children, roles)
        tmp.children = cData
        if (hasPdf) {
          hasPDFRouter = true
        }
      }
      res.push(tmp)
    }
  })

  return { data: res, hasPDFRouter }
}

const permission = {
  state: {
    routers: [],
    addRouters: [],
    allPermissions: {
      AC: [],
      BG: [],
      PC: [],
    },
    hasPDFRouter: false,
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    },
    SET_ALL_PERMISSIONS: (state, permissions, system) => {
      state.allPermissions[system] = permissions
    },
    SET_HAS_PDF_ROUTER: (state, hasPDFRouter) => {
      state.hasPDFRouter = hasPDFRouter
    },
  },
  actions: {
    setAllPermissions({ commit }, { data, system }) {
      return new Promise((resolve, reject) => {
        commit('SET_ALL_PERMISSIONS', data, system)
        resolve(data)
      })
    },
    GenerateRoutes({ commit, getters }, data) {
      return new Promise(resolve => {
        const { permissions } = data
        //
        for (const p in permissions) {
          // console.log(`[${p}]`)
          const arr = p.split('.')
          if (arr.length > 2) {
            const parentName = arr[0] + '.' + arr[1]
            if (!permissions[parentName]) {
              permissions[parentName] = {
                p_code: parentName,
                p_selected: 'Y',
              }
            }
          }
        }
        permissions.dashboard = {
          p_code: 'dashboard',
          p_selected: 'Y',
        }
        permissions.index = {
          p_code: 'index',
          p_selected: 'Y',
        }

        if (getters.system === 'AC' && getters.user_type === 'S') {
          // 輔助檔案-財務報表設定
          permissions['ac.assistance.fund_summary_types_relation'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 輔助檔案-EDB導入
          permissions['ac.assistance.edb'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 報表-中學EDB設定
          permissions['ac.assistance.edb_secondary_school'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 輔助檔案-EDB導入
          permissions['ac.assistance.fix_wrong_voucher'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 設定-資料導入
          permissions['ac.setting.data_import'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 設定-還原Demo數據
          permissions['ac.setting.restore_demo_data'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 輔助檔案-Entry數量
          permissions['ac.assistance.entry_summary'] = {
            p_code: '*',
            p_selected: 'Y',
          }
          // 週期-銀行ai對帳
          permissions['ac.periodic.bank_ai_reconciliation'] = {
            p_code: '*',
            p_selected: 'Y,A,D,E,F,O,I,V,L,C,R,M,N,P,T,W,U,Z,B',
          }
        }

        // let accessedRouters
        // if (roles.includes('admin')) {
        //   accessedRouters = asyncRouterMap
        // } else {
        const { data: accessedRouters, hasPDFRouter } = filterAsyncRouter(
          asyncRouterMap,
          permissions,
        )
        // }

        commit('SET_HAS_PDF_ROUTER', hasPDFRouter)
        commit('SET_ROUTERS', accessedRouters)
        resolve()
      })
    },
  },
}

export default permission
