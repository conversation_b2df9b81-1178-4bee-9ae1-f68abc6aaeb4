<script>
import { mapGetters } from 'vuex'
import {
  editStaffPreference,
  editUserPreference,
  getStaffPreference,
  getUserPreference,
} from '@/api/settings/user/preference'
export default {
  props: {},
  data() {
    return {
      loadingPreferences: true,
      tmpPrefernces: {},
    }
  },
  computed: {
    ...mapGetters({ thisSystem: 'system', user_id: 'user_id' }),
  },
  watch: {
    preferences: {
      deep: true,
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.saveUserPreference()
        })
      },
    },
  },
  created() {},
  mounted() {
    // this.loadUserPreference()
  },
  updated() {},
  methods: {
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.thisSystem
      const content = {
        page: this.$route.name,
      }
      let api = editStaffPreference
      if (system === 'AC') {
        api = editUserPreference
      }
      const pf_code = system.toLowerCase() + '.home'
      api(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
    loadUserPreference() {
      return new Promise((resolve, reject) => {
        if (!this.preferences) {
          reject()
        }
        this.loadingPreferences = true
        const user_id = this.user_id
        const system = this.thisSystem
        const pf_code = this.pf_code ? this.pf_code : this.$route.meta.p_code

        const preferences = this.preferences

        let api = getStaffPreference
        if (system === 'AC') {
          api = getUserPreference
        }

        if (preferences.filters) {
          api(user_id, system, pf_code, 'filters')
            .then(res => {
              this.tmpPrefernces = res
              console.log(this.tmpPrefernces)
              if (this.childPreferences) {
                const tmpFilters = Object.assign(preferences.filters, this.tmpPrefernces)
                this.childPreferences.forEach(item => {
                  tmpFilters[item] = ''
                })
                preferences.filters = tmpFilters
              } else {
                preferences.filters = Object.assign(preferences.filters, this.tmpPrefernces)
              }
            })
            .catch(() => {})
            .finally(() => {
              this.loadingPreferences = false
              resolve()
            })
        } else {
          this.loadingPreferences = false
          resolve()
        }
      })
    },
    updateChildPreference() {
      return new Promise((resolve, reject) => {
        if (!this.preferences) {
          resolve()
        }
        if (!this.childPreferences) {
          resolve()
        }
        this.childPreferences.forEach(item => {
          this.preferences.filters[item] = this.tmpPrefernces[item]
        })
        resolve()
      })
    },
    updateChildPreferenceByName(name) {
      return new Promise((resolve, reject) => {
        if (!this.preferences) {
          resolve()
        }
        if (!this.childPreferences) {
          resolve()
        }
        if (!this.preferences.filters[name]) {
          resolve()
        }
        this.preferences.filters[name] = this.tmpPrefernces[name]
        resolve()
      })
    },
    saveUserPreference() {
      if (this.loadingPreferences) {
        return
      }
      if (!this.preferences) {
        return
      }
      const user_id = this.user_id
      const system = this.thisSystem
      const pf_code = this.pf_code ? this.pf_code : this.$route.meta.p_code

      const preferences = this.preferences
      const filters = preferences.filters

      let api = editStaffPreference
      if (system === 'AC') {
        api = editUserPreference
      }
      if (filters) {
        api(user_id, system, pf_code, 'filters', filters)
          .then(res => {})
          .catch(() => {})
      }
    },
  },
}
</script>
