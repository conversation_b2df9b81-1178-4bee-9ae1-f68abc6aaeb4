import request from '@/utils/request'

/**
 * 修改系統設定
 * @param {string} [setting_json] 選擇的權限,格式: {"system_setting_id":"陳大文"}
 * @return {Promise}
 */
export function editSystemSettings({ settings }) {
  return request({
    url: '/system-settings/actions/update',
    method: 'post',
    data: {
      setting_json: JSON.stringify(settings),
    },
  })
}

/**
 * 獲取系統設定
 * @param {string} system AC,BG,PC
 * @return {Promise}
 */
export function fetchSystemSettings(system) {
  return request({
    url: '/system-settings',
    method: 'get',
    params: {
      system,
    },
  })
}
