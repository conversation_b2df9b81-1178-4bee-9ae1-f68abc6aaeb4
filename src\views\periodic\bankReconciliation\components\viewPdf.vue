<template>
  <el-dialog
    v-if="show"
    :title="$t('bankAiReconciliation.bankMonth')"
    :visible.sync="show"
    class="bank-monthly-dialog"
    width="50%"
  >
    <div class="image-box">
      <div class="wrapper">
        <div class="wrapper-box" style="background: #4a4a4a">
          <template>
            <!--<div v-if="imgVisible" v-viewer.static="options" class="image-wrapper">-->
            <!--  <img ref="image" :src="currentImageUrl" alt="">-->
            <!--</div>-->
            <panZoom
              ref="panZoom"
              :options="{
                autocenter: true,
                transformOrigin: { x: 0.5, y: 0.5 },
                minZoom: 0.05,
                bounds: true,
                boundsPadding: 0.2,
                initialZoom: 0.05,
              }"
              selector=".right-content"
              @init="onZoomInit"
              @panstart="onPanStart"
              @panend="onPanEnd"
            >
              <div
                :style="{
                  visibility: currentImageWidth > 0 ? '' : 'hidden',
                  width: currentImageWidth + 'px',
                  height: currentImageHeight + 'px',
                }"
                :class="{
                  'pan-ing': panIng,
                }"
                class="right-content"
              >
                <img
                  :src="currentImageUrl"
                  @dragstart="
                    e => {
                      e.preventDefault()
                      return false
                    }
                  "
                >
                <template v-if="ocrSplitResults.length > 0">
                  <div
                    v-for="block in ocrSplitResults"
                    :key="block.index"
                    :class="{
                      'ocr-div': true,
                      'current-select': selectBlock.includes(block.index),
                      // 'recommended-div': checkOcrDivStyle(block.index, 'recommend'),
                      //'is-hit': true,
                    }"
                    :style="{
                      top: block.location.top + 'px',
                      left: block.location.left + 'px',
                      width: block.location.width + 'px',
                      height: block.location.height + 'px',
                      fontSize:
                        (block.location.height < 22 ? block.location.height - 2 : 20) + 'px',
                    }"
                  />
                </template>
              </div>
            </panZoom>
          </template>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getDocumentFileLedger, getFileActions } from '@/api/periodic/bankReconciliation'
import panZoom from '@/components/pan-zoom/component'
import { mapGetters } from 'vuex'

export default {
  components: {
    panZoom,
  },
  props: {
    viewImageShow: {
      type: Boolean,
      default: false,
    },
    viewImageData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      show: false,
      imgLoading: false,
      imgList: [],
      ocrResults: [],
      currentImageUrl: '',
      imageIndex: -1,
      panIng: false,
      currentImageWidth: 0,
      hoverGroup: '',
      currentImageHeight: 0,
      selectBlock: [],
    }
  },
  watch: {
    viewImageShow(val) {
      this.show = val
      if (val) {
        this.getImage(this.viewImageData)
      }
    },
    show() {
      if (!this.show) {
        this.$emit('close', false)
      }
    },
  },
  computed: {
    ...mapGetters(['language', 'styles', 'user_id', 'remoteServerInfo']),
    ocrSplitResults() {
      const ocrResultsCopy = JSON.parse(JSON.stringify(this.ocrResults))
      if (ocrResultsCopy[this.imageIndex] && ocrResultsCopy[this.imageIndex].length > 0) {
        return ocrResultsCopy[this.imageIndex]
      } else {
        return []
      }
    },
  },
  created() {
    this.showSecondaryWindow = true
    this.channel = new BroadcastChannel('EDAC_BroadcastChannelBankStatementPreview')
    this.channel.addEventListener('message', this.omChannelMessage)
  },
  methods: {
    async getImage(row) {
      if (this.imgLoading) return
      this.imgLoading = true
      try {
        this.imgList = []
        const res = await getDocumentFileLedger(row.ai_document_file_ledger_id)
        const fileRes = await getFileActions(res.aiDocumentFileLedger.ai_document_file_id)
        const { remoteFilePath } = this.remoteServerInfo
        this.imgList = fileRes.imgs.map(i => {
          return {
            tempUrl: `${remoteFilePath}${i.file_path}`,
          }
        })
        const blockData = fileRes.imgs.map(item => {
          return JSON.parse(item.block_data)
        })
        this.ocrResults = blockData
        this.$nextTick(() => {
          this.loadImages()
        })
        this.imageIndex = res.aiDocumentFileLedger.page_in_group - 1
        this.handleMouseenter(res)
      } catch (e) {
        console.log(e)
      }
      this.imgLoading = false
    },
    onZoomInit() {
      console.log('onZoomInit')
    },
    handleMouseenter(row) {
      if (!row.aiDocumentFileLedger) return
      row = row.aiDocumentFileLedger
      const arr = row.block_indexes ? (row.block_indexes + '').split(',').map(i => Number(i)) : []
      this.selectBlock = arr // row.blockIndexes ? (row.blockIndexes + '').split(',').map(i => Number(i)) : []
      this.postMsg('selectBlock', arr)
      this.postMsg('switchImage', this.imageIndex)
    },
    onPanStart() {
      this.panIng = true
    },
    onPanEnd() {
      this.panIng = false
    },
    async loadImages() {
      const imageLoadId = new Date().getTime() + Math.random()
      this.imageLoadId = imageLoadId
      for (let i = 0; i < this.imgList.length; i++) {
        if (this.imageLoadId !== imageLoadId) break
        const url = this.imgList[i].tempUrl
        try {
          const img = await this.toDataURL(url)
          if (this.imageLoadId !== imageLoadId) break
          this.imgList[i].url = img.url
          this.imgList[i].width = img.width
          this.imgList[i].height = img.height
          this.imgList[i].status = 'done'
          if (i === this.imageIndex) {
            this.$nextTick(() => {
              this.currentImageUrl = this.imgList[this.imageIndex].url
              this.refreshImg()
            })
          }
        } catch (e) {
          this.imageLinks[i].status = 'error'
        }
      }
    },
    toDataURL(src, outputFormat) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'Anonymous'
        // img.setAttribute('crossOrigin', 'anonymous')
        img.onload = function() {
          const canvas = document.createElement('CANVAS')
          const ctx = canvas.getContext('2d')
          canvas.height = this.naturalHeight
          canvas.width = this.naturalWidth
          ctx.drawImage(this, 0, 0)
          // const dataURL = canvas.toDataURL(outputFormat)
          // resolve({ url: dataURL, width: this.naturalWidth, height: this.naturalHeight })
          canvas.toBlob(blobObj => {
            const imgSrc = window.URL.createObjectURL(blobObj)
            resolve({ url: imgSrc, width: this.naturalWidth, height: this.naturalHeight })
          })
        }
        img.οnerrοr = function(e) {
          console.log(e)
          reject(e)
        }
        img.src = src
        // if (img.complete || img.complete === undefined) {
        //   img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw=='
        //   img.src = src
        // }
      })
    },
    refreshImg() {
      this.currentImageStatus = ''
      this.$nextTick(() => {
        this.imgInit = false
        this.imgVisible = false
        this.currentImageUrl = this.imgList[this.imageIndex].url
        this.currentImageWidth = this.imgList[this.imageIndex].width
        this.currentImageHeight = this.imgList[this.imageIndex].height
        this.$nextTick(() => {
          this.imgVisible = true
          this.onChangeZoomInit()
          this.currentImageStatus = this.imgList[this.imageIndex].status
          this.$nextTick(() => {
            this.onChangeZoomInit()
          })
        })
      })
    },
    onChangeZoomInit() {
      console.log('onChangeZoomInit')
      this.imgInit = true
      this.$nextTick(() => {
        this.waitPanElement()
      })
    },
    async waitPanElement() {
      let i = 0
      while (!this.$refs.panZoom && i < 50) {
        i++
        await this.sleep(50)
      }
      this.$refs.panZoom && this.$refs.panZoom.autoZoom(false)
      await this.sleep(50)
      this.$refs.panZoom && this.$refs.panZoom.autoZoom(false)
    },
    sleep(time) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve()
        }, time)
      })
    },
    omChannelMessage(ev) {
      const { type } = ev // data
      switch (type) {
        case 'keepAlive':
          !this.showSecondaryWindow && (this.showSecondaryWindow = true)
          break
        case 'close':
          this.showSecondaryWindow && (this.showSecondaryWindow = false)
          break
        case 'ready':
          this.postMsg('init', {
            isFilePreview: this.isFilePreview,
            previewType: this.previewType,
            companyId: this.companyId,
            bankStatementFileId: this.bankStatementFileId,
            bankStatementGroupId: this.bankStatementGroupId,
          })
          this.postAlwaysIndexes()
          break
      }
    },
    postMsg(type, data, channel) {
      const c = channel || this.channel
      if (c) {
        c.postMessage(
          {
            type,
            from: 'page',
            data,
          },
          'page',
        )
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.bank-monthly-dialog {
  top: -50px !important;
  ::v-deep {
    .el-dialog .el-dialog__body {
      max-height: 80vh !important;
      height: 80vh !important;
    }
  }
}
.image-box {
  width: 100%;
  height: 100%;
}
.wrapper {
  padding: 4px 20px 20px;
  overflow: hidden;
  width: 100%;
  height: 100%;
  text-align: center;
  .wrapper-box {
    //max-height: 70vh;
    height: calc(90vh - 101px);
    //overflow-y: auto;
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;
    .example {
      position: absolute;
      top: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .left-icon,
    .right-icon {
      position: absolute;
      top: 45%;
      width: 34px;
      height: 34px;
      display: flex;
      // background-color: #FAFAFA;
      background-color: rgba(250, 250, 250, 0.8);
      justify-content: center;
      cursor: pointer;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
    }
    .left-icon {
      left: 10px;
    }
    .right-icon {
      right: 10px;
    }
    > div {
      width: 100%;
      img {
        // width: 50%;
        //visibility: hidden;
      }
    }

    .image-info-wrapper {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      text-align: left;
      padding: 10px 40px;
      //background: #333333;
      //opacity: 0.8;
      background: rgba(51, 51, 51, 0.8);
      color: #ffffff;
      //height: 68px;
      div {
        line-height: 19px;
        margin-bottom: 10px;
        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
  }
}
.ocr-div {
  position: absolute;
  // opacity: 0.2;

  background: rgba(255, 193, 5, 0.2);
  border: 1px solid #ffc105;
  color: #4c5362;
  //visibility: hidden;
  line-height: 1;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  &.current-select {
    // visibility: unset;
    opacity: 1;
  }
  &:hover {
    opacity: 1;
  }
}

.ocr-div.recommended-div {
  background-color: #d100f5;
}

.ocr-div.selected-div {
  background-color: #48c760;
}

.ocr-div.show-words {
  background-color: #ff980033;
  color: #4c5362;
  opacity: 1;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  visibility: unset;
}

.ocr-div.recommended-div.show-words {
  background-color: #d100f533;
  color: #4c5362;
}

.ocr-div.selected-div.show-words {
  //background-color: #48C76033;
  background-color: #ff980033;
  color: #4c5362;
}

.ocr-div.is-hit.show-words {
  background-color: #48c76033;
}
</style>
