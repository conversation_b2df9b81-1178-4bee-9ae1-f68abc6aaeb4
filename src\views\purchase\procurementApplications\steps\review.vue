<template>
  <div class="review-step">
    <div v-if="showItems" class="handlers">
      <el-table :data="data" :show-header="false" :stripe="false">
        <el-table-column type="index" />

        <el-table-column>
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-button
              v-for="item in scope.row"
              :key="item.pro_apply_handler_id"
              :class="{
                success: item.status === 'A',
                reject: item.status === 'R',
                back: item.status === 'B',
                c: item.status === 'C',
              }"
              size="mini"
              class="st-btn"
            >
              {{ item[isEnglish ? 'st_name_en' : 'st_name_cn'] }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="log">
      <el-table :data="logs" border>
        <el-table-column type="index" />
        <!--        <el-table-column-->
        <!--          label="階段"-->
        <!--          prop=""-->
        <!--          width="100"-->
        <!--        >-->
        <!--          <template v-if="scope && scope.row" slot-scope="scope">-->
        <!--            {{ stepObj[scope.row.step] }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column :label="t('auto')" prop="auto" width="80">
          <!--          <template v-if="scope && scope.row" slot-scope="scope">-->
          <!--            <el-form-item :prop="`form[${scope.$index}].supplier_name`" required>-->
          <!--              <el-input v-model="form[scope.$index].supplier_name" :disabled="isReadonly" type="textarea" autosize resize="none"/>-->
          <!--            </el-form-item>-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column :prop="isEnglish ? 'st_name_en' : 'st_name_cn'" :label="t('stName')" />
        <el-table-column :label="t('updatedAt')" prop="">
          <template v-if="scope && scope.row" slot-scope="scope">
            {{ scope.row.updated_at }}
          </template>
        </el-table-column>
        <el-table-column :label="t('reviewStatus')" prop="status">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-tag :type="tagNames[scope.row.status]">
              {{ statusObj[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('reviewComment')" prop="comment" />
      </el-table>
      <div class="actions">
        <el-button size="mini" type="info" @click="onBack">
          {{ $t('button.back') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onPrev">
          {{ $t('button.prev') }}
        </el-button>
        <el-button v-if="canNext" size="mini" type="primary" @click="onNext">
          {{ $t('button.next') }}
        </el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ReviewStep',
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    logs: {
      type: Array,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: true,
    },
    showItems: {
      type: Boolean,
      default: true,
    },
    canNext: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      langKey: 'purchase.daily.procurementApplications.',
      // form: {
      //
      // }
      tagNames: {
        W: '',
        A: 'success',
        B: 'warning',
        C: 'warning',
        R: 'danger',
      },
    }
  },
  computed: {
    form: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
    // tableData() {
    //   return this.review_logs.filter(i => {
    //     if (this.type === 'review') {
    //       return i.step === 'R'
    //     } else {
    //       return i.step === 'A'
    //     }
    //   })
    // },
    typeList() {
      return [
        {
          value: 'COM',
          label: this.t('com'),
        },
        {
          value: 'ORG',
          label: this.t('org'),
        },
      ]
    },
    statusObj() {
      return {
        W: this.$t('purchase.approvalStatus.W'),
        A: this.$t('purchase.approvalStatus.A'),
        B: this.$t('purchase.approvalStatus.B'),
        C: this.$t('purchase.approvalStatus.C'),
        R: this.$t('purchase.approvalStatus.R'),
      }
    },
    stepObj() {
      return {
        R: this.$t('purchase.reviewStep.R'),
        A: this.$t('purchase.reviewStep.A'),
      }
    },
  },
  created() {},
  methods: {
    onBack() {
      this.$emit('back')
    },
    onPrev() {
      this.$emit('prev')
    },
    onNext() {
      this.$emit('next')
    },
  },
}
</script>

<style lang="scss" scoped>
.add-icon {
  cursor: pointer;
  &.add-row {
    position: absolute;
    right: -30px;
    top: 3px;
  }
}
.review-step {
  /deep/ {
    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: #ffffff;
    }
  }
  .actions {
    text-align: right;
    width: 1000px;
    padding-top: 10px;
  }
  .handlers {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin: 10px 0;
    .st-btn {
      min-width: 100px;
      &.success {
        color: #67c23a;
      }
      &.reject {
        color: #f56c6c;
      }
      &.back {
        color: #e6a23c;
      }
      &.c {
        color: #e6a23c;
      }
    }
  }
}
</style>
