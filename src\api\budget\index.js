import request from '@/utils/request'

/**
 * 返回新增后的會計科目id
 * @param {string} fy_code 會計週期編號
 * @param {string} budget_type 預算類型(F=預算組,G=預算群組,C=預算事項,D=預算事項詳細)
 * @param {string} budget_code 預算編號
 * @param {string} budget_IE 收支類型,I=收入,E=支出,B=收支,如果budget_type=D只能傳I或E
 * @param {string} name_cn 中文名稱
 * @param {string} name_en 英文名稱
 * @param {string} budget_account 當budget_type=D時可填寫逗號拼接多個account_id
 * @param {string} manager_staffs_json 當budget_type=G時可填寫,E=額外,N=正常,L=限制,格式:[{"type":"N","staff_id":"1"},...]
 * @param {string} member_staffs_json 當budget_type=G時可填寫,固定傳M,格式:[{"type":"M","staff_id":"1"},...]
 * @param {string} approve_staff_id 當budget_type=G時可填寫單個staff_id
 * @param {string} budget_stage 當budget_type=G必須傳預算進度,未遞交(X)=>已遞交(S)=>已審批(A)=>已接納(O)=>已確認(C)=>修訂中(R)=>已修訂(T)=>已重批(K)=>已重接納(B)=>已重確認(D)
 * @param {float} proposed_amount 建議金額
 * @param {float} approved_amount 審批金額
 * @param {float} last_proposed_amount 上次建議金額
 * @param {float} last_approved_amount 上次審批金額
 * @param {string} budget_active 是否活躍,Y=活躍,N=非活躍
 * @param {integer} parent_budget_id 父預算id
 * @param {integer} seq 所處層級里的位置
 * @return {Promise}
 */
export function createBudget({
  fy_code,
  budget_type,
  budget_code,
  budget_IE,
  name_cn,
  name_en,
  budget_account,
  manager_staffs_json,
  member_staffs_json,
  approve_staff_id,
  budget_stage,
  proposed_amount,
  approved_amount,
  last_proposed_amount,
  last_approved_amount,
  budget_active,
  parent_budget_id,
  seq,
}) {
  return request({
    url: '/budgets/actions/create',
    method: 'post',
    data: {
      fy_code,
      budget_type,
      budget_code,
      budget_IE,
      name_cn,
      name_en,
      budget_account,
      manager_staffs_json,
      member_staffs_json,
      approve_staff_id,
      budget_stage,
      proposed_amount,
      approved_amount,
      last_proposed_amount,
      last_approved_amount,
      budget_active,
      parent_budget_id,
      seq,
    },
  })
}

/**
 *
 * @param {string} fy_code 會計週期編號
 * @param {string} budget_id 預算id
 * @param {string} budget_code 預算編號
 * @param {string} budget_IE 收支類型,I=收入,E=支出,B=收支
 * @param {string} name_cn 中文名稱
 * @param {string} name_en 英文名稱
 * @param {string} budget_account 當budget_type=D時可填寫逗號拼接多個account_id
 * @param {string} manager_staffs_json 當budget_type=G時可填寫,E=額外,N=正常,L=限制,格式:[{"type":"N","staff_id":"1"},...]
 * @param {string} member_staffs_json 當budget_type=G時可填寫,固定傳M,格式:[{"type":"M","staff_id":"1"},...]
 * @param {string} approve_staff_id 當budget_type=G時可填寫單個staff_id
 * @param {string} budget_stage 當budget_type=G必須傳預算進度,未遞交(X)=>已遞交(S)=>已審批(A)=>已接納(O)=>已確認(C)=>修訂中(R)=>已修訂(T)=>已重批(K)=>已重接納(B)=>已重確認(D)
 * @param {float} proposed_amount 建議金額
 * @param {float} approved_amount 審批金額
 * @param {float} last_proposed_amount 上次建議金額
 * @param {float} last_approved_amount 上次審批金額
 * @param {string} budget_active 是否活躍,Y=活躍,N=非活躍
 * @param {integer} parent_budget_id 父預算id
 * @param {integer} seq 所處層級里的位置
 * @return {Promise}
 */
export function editBudget({
  fy_code,
  budget_id,
  budget_code,
  budget_IE,
  name_cn,
  name_en,
  budget_account,
  manager_staffs_json,
  member_staffs_json,
  approve_staff_id,
  budget_stage,
  proposed_amount,
  approved_amount,
  last_proposed_amount,
  last_approved_amount,
  budget_active,
  parent_budget_id,
  budget_amount,
  seq,
}) {
  return request({
    url: '/budgets/actions/update',
    method: 'post',
    data: {
      fy_code,
      budget_id,
      budget_code,
      budget_IE,
      name_cn,
      name_en,
      budget_account,
      manager_staffs_json,
      member_staffs_json,
      approve_staff_id,
      budget_stage,
      proposed_amount,
      approved_amount,
      last_proposed_amount,
      last_approved_amount,
      budget_active,
      parent_budget_id,
      budget_amount,
      seq,
    },
  })
}

/**
 *
 * @param {string} fy_code 會計週期編號
 * @param {string} budget_account_json 格式:[{"budget_id":"M","account_ids":"1,2,3"},...]
 * @return {Promise}
 */
export function editBudgetAccounts({ fy_code, budget_account_json }) {
  return request({
    url: '/budgets/actions/update-accounts',
    method: 'post',
    data: {
      fy_code,
      budget_account_json,
    },
  })
}

/**
 * 返回會計科目樹
 * @param {integer} parent_budget_id 父預算id
 * @param {string} fy_code 選擇的會計週期
 * @param {string} type 預算組或預算群組選擇上級時需傳 F,預算事項或預算事項詳細選擇上級時需傳 C,不傳則不篩選
 * @param {string} only_node 傳0或不傳=返回全部層數,1=只返回parent_budget_id的下一層
 * @param {string} budget_id 如傳入此參數,結果將會排除此budget_id(用於預算編輯)
 * @param {string} budget_active 是否活躍, Y=活躍,N=不活躍,不傳返回全部
 * @return {Promise}
 */
export function fetchBudgetTree({
  parent_budget_id,
  fy_code,
  type,
  only_node,
  budget_id,
  budget_active,
}) {
  return request({
    url: '/budgets/tree',
    method: 'get',
    params: {
      parent_budget_id,
      fy_code,
      type,
      only_node,
      budget_id,
      budget_active,
    },
  })
}

/**
 * 返回預算群組以及預算事項的樹狀列表
 * @param {string} fy_code 選擇的會計週期
 * @param {integer} parent_budget_id 父預算id
 * @param {string} budget_types [分類]下拉框 = F ; [組別]下拉框 = G,C ; [預算]下拉框 = F,G
 * @return {Promise}
 */
export function fetchBudgetGroupList({ fy_code, parent_budget_id, budget_types }) {
  return request({
    url: '/budgets/actions/group-list',
    method: 'get',
    params: {
      fy_code,
      parent_budget_id,
      budget_types,
    },
  })
}

/**
 * 返回預算項目詳情列表
 * @param {integer} parent_budget_id 預算群組id或預算事項id
 * @param {string} fy_code 選擇的會計週期
 * @param {string} budget_active 是否活躍, Y=活躍,N=不活躍,不傳返回全部
 * @return {Promise}
 */
export function searchBudget({
  parent_budget_id,
  fy_code,
  budget_name,
  budget_active,
  account_id,
}) {
  return request({
    url: '/budgets/actions/search',
    method: 'get',
    params: {
      parent_budget_id,
      fy_code,
      budget_name,
      budget_active,
      account_id,
    },
  })
}

/**
 * 獲取某預算詳情
 * @param {string} fy_code 會計週期編號
 * @param {integer} budget_id 預算id
 * @return {Promise}
 */
export function getBudget({ fy_code, budget_id }) {
  return request({
    url: '/budgets/actions/inquire',
    method: 'get',
    params: {
      fy_code,
      budget_id,
    },
  })
}

/**
 * 刪除某預算詳情
 * @param {string} fy_code 會計週期編號
 * @param {integer} budget_id 預算id
 * @return {Promise}
 */
export function deleteBudget({ fy_code, budget_id }) {
  return request({
    url: '/budgets/actions/delete',
    method: 'post',
    data: {
      fy_code,
      budget_id,
    },
  })
}

/**
 * 返回預算列表
 * @param {string} fy_code 選擇的會計週期
 * @param {integer} parent_budget_id 父預算id(只能傳budget_type = F的預算id)
 * @param {string} budget_stage 預算群組狀態,未遞交(X)=>已遞交(S)=>已審批(A)=>已接納(O)=>已確認(C)=>修訂中(R)=>已修訂(T)=>已重批(K)=>已重接納(B)=>已重確認(D)
 * @param {string} manager_staff_id 負責的職員id
 * @param {string} approve_staff_id 負責審批的職員id
 * @return {Promise}
 */
export function fetchBudgetSummary({
  fy_code,
  parent_budget_id,
  budget_stage,
  manager_staff_id,
  approve_staff_id,
}) {
  return request({
    url: '/budgets/summary',
    method: 'get',
    params: {
      fy_code,
      parent_budget_id,
      budget_stage,
      manager_staff_id,
      approve_staff_id,
    },
  })
}

/**
 * 獲取某預算詳情
 * @param {string} fy_code 會計週期編號
 * @param {string} budget_code 預算budget_code
 * @return {Promise}
 */
export function getBudgetByCode({ fy_code, budget_code }) {
  return request({
    url: '/budgets/actions/inquire-by-code',
    method: 'get',
    params: {
      fy_code,
      budget_code,
      ignore_err: true,
    },
  })
}

/**
 * 返回預算相關的賬目列表
 * @param {string} fy_code 會計週期編號
 * @param {integer} budget_id 歸屬預算的budget_id)
 * @return {Promise}
 */
export function fetchBudgetLedgers({ fy_code, budget_id }) {
  return request({
    url: '/budget-ledgers',
    method: 'get',
    params: {
      fy_code,
      budget_id,
    },
  })
}

/**
 * 更新預算審批進度
 * @param {string} fy_code 會計週期編號
 * @param {integer} budget_id 預算id (budget_type = G的預算)
 * @param {string} budget_stage 要更改成的進度
 * @return {Promise}
 */
export function editBudgetStage({ fy_code, budget_id, budget_stage }) {
  return request({
    url: '/budgets/actions/update-stage',
    method: 'post',
    data: {
      fy_code,
      budget_id,
      budget_stage,
    },
  })
}

/**
 * 複製去年預算樹
 * @param {string} channel 指定驗證token通道,可传AC或BG,不傳默認AC
 * @param {string} fy_code 會計週期編號(今年)
 * @param {integer} budget_id 預算群組id(今年),要budget_type=G和budget_stage=X
 * @param {string} mode STRUCTURE=僅結構,ALL=結構和內容
 * @return {Promise}
 */
export function copyBudgetTree({ channel, fy_code, budget_id, mode }) {
  return request({
    url: '/budgets/actions/copy-tree',
    method: 'post',
    data: {
      channel,
      fy_code,
      budget_id,
      mode,
    },
  })
}

/**
 * 清空預算群組下結構或金額
 * @param {string} fy_code 會計週期編號
 * @param {integer} budget_id 預算群組id,要budget_type=G和budget_stage=X
 * @param {string} mode CONTENT=僅金額,ALL=結構和內容
 * @return {Promise}
 */
export function cleanBudgetTree({ fy_code, budget_id, mode }) {
  return request({
    url: '/budgets/actions/clean-tree',
    method: 'post',
    data: {
      fy_code,
      budget_id,
      mode,
    },
  })
}

/**
 * 修改預算的順序
 * @param {string} fy_code 會計週期編號
 * @param {integer} budget_id 預算id
 * @param {integer} seq 序號
 * @return {Promise}
 */
export function editBudgetSeq({ fy_code, budget_id, seq }) {
  return request({
    url: '/budgets/actions/update-seq',
    method: 'post',
    data: {
      fy_code,
      budget_id,
      seq,
    },
  })
}

/**
 * 返回預算excel
 * @param {number} fy_code 會計週期編號
 * @return {Promise}
 */
export function exportBudgets(fy_code) {
  return request({
    url: '/budgets/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
    },
  })
}

/**
 * 匯入預算excel
 * @param {number} fy_code 會計週期編號
 * @param {object} data 匯入的數據
 * @return {Promise}
 */
export function importBudgets(data, fy_code) {
  return request({
    url: '/budgets/actions/import',
    method: 'post',
    data: {
      fy_code,
      data_json: JSON.stringify(data),
    },
  })
}

/**
 * 預算項目詳情Full List

 */
export function exportBudgetLedgers(params) {
  return request({
    url: '/budget-ledgers/actions/export',
    responseType: 'blob',
    method: 'get',
    params,
  })
}
