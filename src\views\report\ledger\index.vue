<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div style="height: 100%">
      <VBreadCrumb class="breadcrumb" />
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 範圍 -->
          <el-form-item :label="$t('filters.range')">
            <accountTreeSelect
              :fund-id.sync="preferences.filters.fund_id"
              :ac-fund-id.sync="preferences.filters.ac_fund_id"
              :ac-code.sync="preferences.filters.ac_code"
              :select-group.sync="preferences.filters.select_group"
              :nature-code.sync="preferences.filters.nature_code"
              :ac-name-en.sync="currentAccountNameEN"
              :ac-name-cn.sync="currentAccountNameCN"
              account-width="300"
              @changeAccountItem="onChangeFilter"
            />
          </el-form-item>
          <!-- 樣式 -->
          <el-form-item :label="$t('report.ledger.label.style')">
            <el-select
              v-model="preferences.filters.style"
              style="width: 150px"
              @change="onChangeFilter"
            >
              <el-option
                v-for="item in styleList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- 顯示 -->
          <el-form-item :label="$t('report.ledger.label.Show')">
            <el-select
              v-model="preferences.filters.mode"
              style="width: 200px"
              @change="onChangeFilter"
            >
              <el-option :label="$t('report.ledger.content.normalEntry')" value="N" />
              <el-option :label="$t('report.ledger.content.entryAndAdjustment')" value="A" />
              <el-option
                :label="$t('report.ledger.content.entryAndAdjustmentAndYearEnd')"
                value="B"
              />
            </el-select>
          </el-form-item>
          <!-- 排序 -->
          <el-form-item :label="$t('report.ledger.label.order')">
            <el-select
              v-model="preferences.filters.order"
              style="width: 130px"
              @change="onChangeFilter"
            >
              <el-option :label="$t('report.ledger.content.byDate')" value="D" />
              <el-option :label="$t('report.ledger.content.byAcCode')" value="A" />
              <el-option :label="$t('report.ledger.content.byVcNo')" value="V" />
            </el-select>
          </el-form-item>
          <br>
          <!-- 時期 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              type="daterange"-->
            <!--              value-format="yyyy-MM-dd"-->
            <!--              style="width: 250px"-->
            <!--              @change="onChangeDateRange"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
              @change="onChangeFilter"
            />
          </el-form-item>

          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <!--              @change="onChangeMonth"-->
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              type="primary"
              size="mini"
              class="action-button"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
        </div>
      </div>
      <div class="cash-book-table">
        <b-table
          ref="table"
          v-loading="!loading && tableLoading"
          :style-columns="styleColumns"
          :amount-columns="amountColumns"
          :lang-key="langKey"
          :show-index="true"
          :show-actions="true"
          :actions-min-width="5"
          :show-checkbox="false"
          :show-summary="true"
          :highlight-current-row="true"
          :sum-text="$t('enquiry.contra.label.sumText')"
          :height="pageHeight - filtersHeight - 80"
          :footer-method="footerMethod"
          action-label=" "
          show-footer
          show-footer-overflow
          border
          :sort-config="{
            trigger: 'cell',
            orders: ['asc', 'desc', null],
            sortMethod: sortAmountMethod,
          }"
          @changeWidth="changeColumnWidth"
          @sort-change="sortChangeEvent"
        >
          <template v-if="$refs.table" slot="columns">
            <vxe-table-column
              v-for="(item, index) in filterAmountBalance"
              :key="item.ss_key + index"
              :title="columnLabel(item.ss_key)"
              :align="item.alignment"
              :class-name="item.ss_key + ' mini-form'"
              :width="item.width"
              :property="$refs.table.column_property(item)"
              :column-key="item.ss_key"
              :params="{ key: item.ss_key }"
              :field="$refs.table.column_property(item)"
              sortable
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span
                  v-if="
                    scope.rowIndex === 0 &&
                      (item.ss_key === 'amount_dr' ||
                        item.ss_key === 'amount_cr' ||
                        item.ss_key === 'net_amount')
                  "
                />
                <span
                  v-else-if="
                    ['amount_cr', 'amount_dr'].includes(item.ss_key) &&
                      preferences.filters.style === 'C' &&
                      ['Dr', 'Cr', 'I', 'E'].includes(preferences.filters.nature_code)
                  "
                >
                  {{ $refs.table.customFormatter(item.ss_key, makeFinCr(scope.row)) }}
                </span>
                <span v-else>{{
                  $refs.table.customFormatter(
                    item.ss_key,
                    scope.row[$refs.table.column_property(item)]
                  )
                }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column
              v-for="(item1, index1) in amountBalanceColumns"
              :key="item1.ss_key + filterAmountBalance.length + index1"
              :title="columnLabel(item1.ss_key)"
              :align="item1.alignment"
              :width="item1.width"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span>{{
                  $refs.table.customFormatter(
                    item1.ss_key,
                    scope.row[$refs.table.column_property(item1)]
                  )
                }}</span>
              </template>
            </vxe-table-column>
          </template>
        </b-table>
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'
import { getReportLedgers } from '@/api/report/index'
import { ledgersExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'
import BTable from '@/components/BTable/index'
import ElSlPanel from 'element-ui/packages/color-picker/src/components/sv-panel'
import ENumeric from '@/components/ENumeric'
import DateRange from '@/components/DateRange/index'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import accountTreeSelect from '@/views/components/accountTreeSelect'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'

import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { listenTo } from '@/utils/resizeListen'
import BigJs from 'big.js'
export default {
  name: 'ReportLedgerIndex',
  components: {
    BTable,
    DateRange,
    ElSlPanel,
    customStyle,
    ENumeric,
    VBreadCrumb,
    accountTreeSelect,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, loadPrintoutSetting, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      bankList: [],
      chequeBooks: [],
      years: '',
      selectedYearId: '',
      tableData: [],
      currentData: [],
      footerData: [],
      summary: {
        bf: 0,
        un_check_cr: 0,
        un_check_dr: 0,
        end: 0,
      },
      data: [],
      monthList: [],
      langKey: 'report.ledger.label.',
      tableColumns: [
        'ac_code',
        'ac_name_',
        'vc_date',
        'vc_no',
        'descr',
        'vc_payee',
        'st_code',
        'st_name_',
        'dept_code',
        'dept_name_',
        'budget_code',
        'amount_dr',
        'amount_cr',
        'net_amount',
        'amount_balance',
        'ref',
        'chq_no',
      ],
      amountColumns: ['amount_dr', 'amount_cr', 'amount_balance', 'net_amount', 'cr_sum'],
      preferences: {
        filters: {
          selectedYearCode: '',
          selectedMonth: '',
          // date_range: [],
          begin_date: '',
          end_date: '',
          parent_fund_id: '',
          mode: 'N',

          fund_id: '',
          ac_fund_id: '',
          ac_code: '',
          select_group: false,
          nature_code: '',

          style: 'A',
          order: 'D',
        },
      },
      currentFilters: {},
      childPreferences: ['selectedMonth'],
      showYearPicker: false,
      periods: [],
      ps_code: 'pdfcashbookcash',

      yearLastDate: new Date(),
      yearStartDate: new Date(),

      currentAccountNameEN: '',
      currentAccountNameCN: '',
      tableShow: true,

      filtersResizeListen: {},
      filtersHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,
      btnLoading: false,
      order: '',
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school', 'remoteServerInfo', 'user_id']),
    columns() {
      const nc = this.currentFilters.nature_code || 'G'
      const style = this.preferences.filters.style
      // const igList = []
      // if (
      //   ((!this.currentFilters.ac_fund_id ||
      //   !this.currentFilters.ac_code)) &&
      //   (
      //     this.currentFilters.order === 'A' ||
      //   this.currentFilters.order === 'V'
      //   )
      // ) {
      //   igList.push('amount_balance')
      // }
      const noCr = ['_index', 'amount_cr']
      const noDr = ['_index', 'amount_dr']
      const empty = ['_index']
      const igMap = {
        'A|Dr': empty,
        'A|E': empty,
        'A|I': empty,
        'A|Cr': empty,

        'B|Dr': empty,
        'B|E': empty,
        'B|I': empty,
        'B|Cr': empty,

        'C|Cr': noDr,
        'C|E': noDr,
        'C|Dr': noCr,
        'C|I': noCr,
      }

      console.log('key', style + '|' + nc)

      const igList = igMap[style + '|' + nc] || []

      // if (style === 'C') {
      //   switch (nc) {
      //     case 'Dr':
      //     case 'E':
      //       igList.push('_index', 'amount_cr')
      //       // return !igList.includes(i.ss_key)
      //       break
      //     case 'I/E':
      //     case 'G':
      //       break
      //     case 'I':
      //     case 'Cr':
      //     default:
      //       igList.push('_index', 'amount_dr')
      //       break
      //   }
      // }
      return this.styleColumns.filter(i => {
        return !igList.includes(i.ss_key)
        // return i.ss_key !== '_index'
      })
      // .map(col => {
      //   const newCol = Object.assign({}, col)
      //
      //   if (newCol.ss_key === 'amount_balance' &&
      //     (this.preferences.filters.style === 'B' || this.preferences.filters.style === 'C')) {
      //     newCol.ss_key = 'cr_sum'
      //   }
      //   return newCol
      // })
    },
    filterAmountBalance() {
      return this.columns.filter(i => i.ss_key !== 'amount_balance')
    },
    amountBalanceColumns() {
      return this.columns.filter(i => i.ss_key === 'amount_balance')
    },
    styleList() {
      const nc = this.currentFilters.nature_code || 'G'
      const list = [
        {
          label: this.$t('report.ledger.content.drCrBalance'),
          value: 'A',
        },
        {
          label: '',
          value: 'B',
        },
        {
          label: '',
          value: 'C',
        },
      ]
      const b = list[1]
      const c = list[2]
      // 津貼=G,收入/支出=I/E,收入=I,支出=E,借方結餘=Dr,貸方結餘=Cr
      switch (nc) {
        case 'E': // 支出=E
        case 'Dr': // 借方結餘=Dr
          b.label = this.$t('report.ledger.content.drBalance')
          break
        case 'G': // 津貼=G
        case 'I/E': // 收入/支出=I/E
        case 'I': // 收入=I
        case 'Cr': // 貸方結餘=Cr
        default:
          b.label = this.$t('report.ledger.content.crBalance')
          break
      }
      switch (nc) {
        case 'G': // 津貼=G
          c.label = this.$t('report.ledger.content.grantBalance') // 津貼結餘
          break
        case 'Dr': // 借方結餘=Dr
          c.label = this.$t('report.ledger.content.netDr') // 借方淨值
          break
        case 'E': // 支出=E
          c.label = this.$t('report.ledger.content.netExpenditure') // 淨支出
          break
        case 'I': // 收入=I
          c.label = this.$t('report.ledger.content.netIncome') // 淨收入
          break
        case 'I/E': // 收入/支出=I/E
          c.label = this.$t('report.ledger.content.NetIncomeAndExpenditure') // 淨收支
          break
        case 'Cr': // 貸方結餘=Cr
        default:
          c.label = this.$t('report.ledger.content.netCr') // 貸方淨值
          break
      }
      return list
    },
  },
  watch: {
    tableData: {
      deep: true,
      handler() {
        this.tableReshow()
      },
    },
    language: {
      handler() {
        this.footerData = []
        this.$nextTick(() => {
          this.footerData = [this.summaryMethod({ columns: this.columns, data: this.tableData })]
        })
      },
    },
  },
  created() {
    this.onChangeFilter = debounce(this.onChangeFilter, 100)
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    add(a, b) {
      const aa = Number(a.toFixed(2))
      const bb = Number(b.toFixed(2))
      return toDecimal(aa + bb)
    },
    tableReshow() {
      this.tableShow = false
      this.$nextTick(() => {
        this.$nextTick(() => {
          this.tableShow = true
        })
      })
    },
    onSetting() {
      this.showDialog = true
    },
    columnLabel(key, language) {
      const style = this.preferences.filters.style
      const nc = this.currentFilters.nature_code || 'G'

      const labelMap = {
        drSum: 'report.ledger.content.drSum', // 借方累計
        crSum: 'report.ledger.content.crSum', // 貸方累計
        cumulativeExpenditure: 'report.ledger.content.cumulativeExpenditure', // 累計支出
        cumulativeIncome: 'report.ledger.content.cumulativeIncome', // 累計收入
        accumulatedRevenueAndExpenditure: 'report.ledger.content.accumulatedRevenueAndExpenditure', // 累計收支
        grantBalance: 'report.ledger.content.grantBalance', // 津貼結餘
        balance: 'report.ledger.content.balance', // 結餘
        expenditure: 'report.ledger.label.expenditure', // 支出
        amount_dr: 'report.ledger.label.amount_dr', // 借方金額
        income: 'report.ledger.label.income', // 收入
        amount_cr: 'report.ledger.label.amount_cr', // 貸方金額
      }
      // style|nc|column
      const columnLabelMap = {
        // amount_balance
        'A|G|amount_balance': labelMap['balance'], // 結餘
        'A|I/E|amount_balance': labelMap['balance'], // 結餘
        'A|I|amount_balance': labelMap['balance'], // 結餘
        'A|E|amount_balance': labelMap['balance'], // 結餘
        'A|Dr|amount_balance': labelMap['balance'], // 結餘
        'A|Cr|amount_balance': labelMap['balance'], // 結餘

        'B|G|amount_balance': labelMap['crSum'], // 貸方累計
        'B|I/E|amount_balance': labelMap['crSum'], // 貸方累計
        'B|I|amount_balance': labelMap['crSum'], // 貸方累計
        'B|E|amount_balance': labelMap['drSum'], // 借方累計
        'B|Dr|amount_balance': labelMap['drSum'], // 借方累計
        'B|Cr|amount_balance': labelMap['crSum'], // 貸方累計

        'C|G|amount_balance': labelMap['grantBalance'], // 津貼結餘
        'C|I/E|amount_balance': labelMap['accumulatedRevenueAndExpenditure'], // 累計收支
        'C|I|amount_balance': labelMap['cumulativeIncome'], // 累計收入
        'C|E|amount_balance': labelMap['cumulativeExpenditure'], // 累計支出
        'C|Dr|amount_balance': labelMap['drSum'], // 借方累計
        'C|Cr|amount_balance': labelMap['crSum'], // 貸方累計

        // amount_dr 借方金額
        'A|G|amount_dr': labelMap['amount_dr'], // 借方金額
        'A|I/E|amount_dr': labelMap['amount_dr'], // 借方金額
        'A|I|amount_dr': labelMap['amount_dr'], // 借方金額
        'A|E|amount_dr': labelMap['amount_dr'], // 借方金額
        'A|Dr|amount_dr': labelMap['amount_dr'], // 借方金額
        'A|Cr|amount_dr': labelMap['amount_dr'], // 借方金額

        'B|G|amount_dr': labelMap['amount_dr'], // 借方金額
        'B|I/E|amount_dr': labelMap['amount_dr'], // 借方金額
        'B|I|amount_dr': labelMap['amount_dr'], // 借方金額
        'B|E|amount_dr': labelMap['amount_dr'], // 借方金額
        'B|Dr|amount_dr': labelMap['amount_dr'], // 借方金額
        'B|Cr|amount_dr': labelMap['amount_dr'], // 借方金額

        'C|G|amount_dr': labelMap['income'], // 收入
        'C|I/E|amount_dr': labelMap['income'], // 收入
        'C|I|amount_dr': labelMap['income'], // 借方金額
        // 'C|E|amount_dr': labelMap['amount_dr'], // 借方金額
        'C|Dr|amount_dr': labelMap['amount_dr'], // 借方金額
        // 'C|Cr|amount_dr': labelMap['amount_dr'], // 借方金額

        // amount_cr 貸方金額
        'A|G|amount_cr': labelMap['amount_cr'], // 貸方金額
        'A|I/E|amount_cr': labelMap['amount_cr'], // 貸方金額
        'A|I|amount_cr': labelMap['amount_cr'], // 貸方金額
        'A|E|amount_cr': labelMap['amount_cr'], // 貸方金額
        'A|Dr|amount_cr': labelMap['amount_cr'], // 貸方金額
        'A|Cr|amount_cr': labelMap['amount_cr'], // 貸方金額

        'B|G|amount_cr': labelMap['amount_cr'], // 貸方金額
        'B|I/E|amount_cr': labelMap['amount_cr'], // 貸方金額
        'B|I|amount_cr': labelMap['amount_cr'], // 貸方金額
        'B|E|amount_cr': labelMap['amount_cr'], // 貸方金額
        'B|Dr|amount_cr': labelMap['amount_cr'], // 貸方金額
        'B|Cr|amount_cr': labelMap['amount_cr'], // 貸方金額

        'C|G|amount_cr': labelMap['expenditure'], // 支出
        'C|I/E|amount_cr': labelMap['expenditure'], // 支出
        // 'C|I|amount_cr': labelMap['income'], // 支出
        'C|E|amount_cr': labelMap['expenditure'], // 支出
        // 'C|Dr|amount_cr': labelMap['amount_dr'], // 借方金額
        'C|Cr|amount_cr': labelMap['amount_cr'], // 貸方金額
      }

      const labelKey = style + '|' + nc + '|' + key
      if (columnLabelMap[labelKey]) {
        return this.$t(columnLabelMap[labelKey], language)
      }
      return this.$t(this.langKey + key, language)
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({ ac_bank: 'C,S,F,P' }))
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }

          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    loadData() {
      const nc = this.currentFilters.nature_code || 'G'
      const style = this.preferences.filters.style
      const newData = []
      let sum = 0
      if (this.tableData.length === 0) {
        this.loadOrders([])
        return newData
      }
      const itemFormat = oldItem => {
        const newItem = Object.assign({}, oldItem)
        // if (i > 0) {
        switch (style) {
          case 'A':
            newItem.net_amount = toDecimal(Number(newItem.amount_dr) - Number(newItem.amount_cr))
            break
          case 'B':
          case 'C':
            switch (nc) {
              case 'Dr':
              case 'E':
                newItem.net_amount = toDecimal(
                  Number(newItem.amount_dr) - Number(newItem.amount_cr),
                )
                break
              case 'I':
              case 'I/E':
              case 'G':
              case 'Cr':
              default:
                newItem.net_amount = toDecimal(
                  Number(newItem.amount_cr) - Number(newItem.amount_dr),
                )
                break
            }
            break
        }
        // sum += newItem.net_amount
        sum = this.add(sum, newItem.net_amount)
        newItem.amount_balance = sum

        if (style === 'C') {
          let amount = 0
          if (Number(newItem.amount_cr) > 0) {
            amount = Number(newItem.amount_cr)
          } else if (Number(newItem.amount_dr) > 0) {
            amount = Number(newItem.amount_dr)
          }
          if (oldItem.tx_type === 'E') {
            newItem.amount_dr = 0
            newItem.amount_cr = amount
          } else {
            newItem.amount_dr = amount
            newItem.amount_cr = 0
          }
        }

        return newItem
      }

      // 第一行
      const bf = itemFormat(this.tableData[0])

      const orders = this.tableData
        .filter((item, i) => i > 0)
        .map((item, i) => {
          const newItem = Object.assign({}, item)
          // const newItem = Object.assign({}, item)
          // // if (i > 0) {
          // switch (style) {
          //   case 'A':
          //     newItem.net_amount = Number(newItem.amount_dr) - Number(newItem.amount_cr)
          //     break
          //   case 'B':
          //   case 'C':
          //     switch (nc) {
          //       case 'Dr':
          //       case 'E':
          //         newItem.net_amount = Number(newItem.amount_dr) - Number(newItem.amount_cr)
          //         break
          //       case 'I':
          //       case 'I/E':
          //       case 'G':
          //       case 'Cr':
          //       default:
          //         newItem.net_amount = Number(newItem.amount_cr) - Number(newItem.amount_dr)
          //         break
          //     }
          //     break
          // }
          // sum += newItem.net_amount
          // newItem.amount_balance = sum
          // } else {
          //   newItem.amount_balance = 0
          // }
          return itemFormat(newItem)
        })

      // vc_list.sort((a, b) => {
      //   return ('' + a.vc_no).localeCompare(('' + b.vc_no))
      // }).sort((a, b) => {
      //   const a_no = ('' + a.vc_no).replace(/[a-zA-Z]/g, '')
      //   const b_no = ('' + b.vc_no).replace(/[a-zA-Z]/g, '')
      //   return a_no.localeCompare(b_no)
      // })
      const order = this.preferences.filters.order
      if (order === 'A') {
        // tx_num
        orders.sort((a, b) => {
          return a.tx_num - b.tx_num
        })
        // 日期
        orders.sort((a, b) => {
          return ('' + a.vc_date).localeCompare('' + b.vc_date)
        })
        // CS排最後
        orders.sort((a, b) => {
          if (a.vc_no.substring(0, 2) === 'CS') {
            return 1
          } else if (b.vc_no.substring(0, 2) === 'CS') {
            return -1
          }
          return 0
        })
        // 會計編號
        orders.sort((a, b) => {
          return ('' + b.ac_code).localeCompare('' + a.ac_code)
        })
      } else if (order === 'V') {
        // tx_num
        orders.sort((a, b) => {
          return a.tx_num - b.tx_num
        })
        // 傳票編號
        orders.sort((a, b) => {
          return ('' + a.vc_no).localeCompare('' + b.vc_no)
        })
        // CS排最後
        orders.sort((a, b) => {
          if (a.vc_no.substring(0, 2) === 'CS') {
            return 1
          } else if (b.vc_no.substring(0, 2) === 'CS') {
            return -1
          }
          return 0
        })
      }
      sum = bf.amount_balance
      orders.forEach(item => {
        // switch (style) {
        //   case 'A':
        //     item.net_amount = Number(item.amount_dr) - Number(item.amount_cr)
        //     break
        //   case 'B':
        //   case 'C':
        //     switch (nc) {
        //       case 'Dr':
        //       case 'E':
        //         item.net_amount = Number(item.amount_dr) - Number(item.amount_cr)
        //         break
        //       case 'I':
        //       case 'I/E':
        //       case 'G':
        //       case 'Cr':
        //       default:
        //         item.net_amount = Number(item.amount_cr) - Number(item.amount_dr)
        //         break
        //     }
        //     break
        // }
        // console.log(sum, item.net_amount)
        // sum += item.net_amount
        sum = this.add(sum, item.net_amount)
        item.amount_balance = sum
      })
      orders.unshift(bf) // 第一行
      // this.currentData = orders
      this.loadOrders(orders)
    },
    loadOrders(orders) {
      this.footerData = [this.summaryMethod({ columns: this.columns, data: orders })]
      this.$refs.table && this.$refs.table.methods('loadData', orders)
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const filters = this.preferences.filters

        const parent_fund_id = filters.select_group ? filters.ac_fund_id : filters.fund_id
        const ac_code = filters.select_group ? undefined : filters.ac_code
        const mode = filters.mode

        const dateFormat = 'yyyy-MM-dd'
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, 1)
        const lastDateObj = new Date(year, month + 1, 0)
        // let begin_date, end_date
        // if (!filters.date_range || !filters.date_range.length) {
        //   begin_date = dateUtil.format(beginDateObj, dateFormat)
        //   end_date = dateUtil.format(lastDateObj, dateFormat)
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        //   this.onChangeDateRange([begin_date, end_date])
        // } else {
        //   begin_date = dateUtil.format(new Date(filters.date_range[0]), dateFormat)
        //   end_date = dateUtil.format(new Date(filters.date_range[1]), dateFormat)
        // }
        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        if (!begin_date || !end_date) {
          begin_date = dateUtil.format(beginDateObj, dateFormat)
          end_date = dateUtil.format(lastDateObj, dateFormat)
          // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
          this.preferences.filters.begin_date = begin_date
          this.preferences.filters.end_date = end_date
        }

        this.onChangeDateRange([begin_date, end_date])
        this.loading = true
        getReportLedgers({
          parent_fund_id,
          ac_code,
          begin_date,
          end_date,
          mode,
        })
          .then(res => {
            this.keepFilter()
            this.tableData = res // this.formatData(res)
            this.$refs.table && this.$refs.table.methods('loadData', this.tableData)
            this.loadData()
            this.tableReshow()
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      let sum = 0
      data.forEach((item, i) => {
        const newItem = Object.assign({}, item)
        if (i > 0) {
          switch (this.preferences.filters.style) {
            case 'A':
              newItem.net_amount = toDecimal(Number(newItem.amount_cr) - Number(newItem.amount_dr))
              break
            case 'B':
            case 'C':
              newItem.net_amount = toDecimal(Number(newItem.amount_dr) - Number(newItem.amount_cr))
              // sum += newItem.net_amount
              sum = this.add(sum, newItem.net_amount)
              newItem.amount_balance = sum
              break
          }
        } else {
          if (this.preferences.filters.style === 'B' || this.preferences.filters.style === 'C') {
            sum = newItem.amount_balance
            newItem.amount_balance = sum
          }
        }
        newData.push(newItem)
      })
      return newData
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    onChangeDateRange(val) {},
    onChangeMonth(val) {
      if (val === '') {
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateFormatStr)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateFormatStr)
        // this.preferences.filters.date_range = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }

      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, 'yyyy-MM-dd')

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, 'yyyy-MM-dd')
      // this.preferences.filters.date_range = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
      this.onChangeDateRange([beginDate, endDate])
    },
    summaryMethod({ columns, data }) {
      const sums = []
      columns.forEach(async(column, index) => {
        const key = column.ss_key || ''
        switch (key) {
          case 'descr': {
            sums.push('C/F')
            break
          }
          case 'amount_balance': {
            let sum = 0
            if (data.length) sum = Number(data[data.length - 1][key])
            sums.push(amountFormat(sum))
            break
          }
          case 'amount_dr':
          case 'amount_cr':
          case 'net_amount': {
            let sum = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[key])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum = toDecimal(sum + n)
                }
              }
            })
            sums.push(amountFormat(sum))
            break
          }
          default:
            sums.push('')
            break
        }
      })
      return sums
    },
    keepFilter() {
      this.currentFilters = Object.assign({}, this.preferences.filters)
      // this.currentFilters.date_range = this.preferences.filters.date_range.map(i => i)
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateFormatStr,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateFormatStr,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id

      const filters = this.preferences.filters

      const parent_fund_id = filters.select_group ? filters.ac_fund_id : filters.fund_id
      const ac_code = filters.select_group ? undefined : filters.ac_code
      const mode = filters.mode
      const style = filters.style

      const dateFormat = 'yyyy-MM-dd'
      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, 1)
      const lastDateObj = new Date(year, month + 1, 0)
      // let begin_date, end_date
      // if (!filters.date_range || !filters.date_range.length) {
      //   begin_date = dateUtil.format(beginDateObj, dateFormat)
      //   end_date = dateUtil.format(lastDateObj, dateFormat)
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      //   this.onChangeDateRange([begin_date, end_date])
      // } else {
      //   begin_date = filters.date_range[0]
      //   end_date = filters.date_range[1]
      // }
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!begin_date || !end_date) {
        begin_date = dateUtil.format(beginDateObj, dateFormat)
        end_date = dateUtil.format(lastDateObj, dateFormat)
        // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        this.preferences.filters.begin_date = begin_date
        this.preferences.filters.end_date = end_date
      }

      if (!user_id || !begin_date || !end_date || !style || !mode) {
        // this.$message.error('')
        return
      }
      this.loading = true
      ledgersExport({
        user_id,
        export_type,
        parent_fund_id,
        ac_code,
        begin_date,
        end_date,
        style,
        mode,
      })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    sortMethod(a, b) {
      return 1
    },
    onChangeFilter() {
      // this.$refs.table.methods('loadData', this.tableData)
      console.log('onChangeFilter')
      this.tableReshow()
      this.reloadData()
    },
    footerMethod() {
      return this.footerData
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
    sortAmountMethod({ data, property, order }) {
      const key = property

      let list = []
      const bfList = data.filter(i => i.descr === 'B/F')
      if (this.amountColumns.includes(key)) {
        list = data.filter(i => i.descr !== 'B/F')
        if (order === 'asc') {
          list = list.sort((a, b) => {
            const n1 = Number(a[key].replace(/,/g, ''))
            const n2 = Number(b[key].replace(/,/g, ''))
            return n1 - n2
          })
        } else {
          list = list.sort((a, b) => {
            const n1 = Number(a[key].replace(/,/g, ''))
            const n2 = Number(b[key].replace(/,/g, ''))
            return n2 - n1
          })
        }
      } else {
        list = data.filter(i => i.descr !== 'B/F')
        if (order === 'asc') {
          list = list.sort((a, b) => {
            console.log(a[key], b[key], a[key] < b[key], 'asc')
            return a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0
          })
        } else {
          list = list.sort((a, b) => {
            console.log(a[key], b[key], a[key] < b[key], 'desc')
            return a[key] < b[key] ? 1 : a[key] > b[key] ? -1 : 0
          })
        }
      }
      list = [...bfList, ...list]
      return list
    },
    sortChangeEvent({ column, property, order }) {
      console.info(column, property, order)
      this.order = order
    },
    makeFinCr(row) {
      const tx_type = row.tx_type
      console.log(
        'cr',
        row.amount_cr,
        'dr',
        row.amount_dr,
        new BigJs(row.amount_cr).minus(row.amount_dr).toFixed(2),
      )
      // if (tx_type === 'E') {
      return new BigJs(row.amount_dr).minus(row.amount_cr)
      // } else {
      //   return new BigJs(row.amount_cr).minus(row.amount_dr)
      // }
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.el-icon-date {
  cursor: pointer;
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
  }
  .cash-book-table {
    /*height: calc(100vh - 220px);*/

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
</style>
