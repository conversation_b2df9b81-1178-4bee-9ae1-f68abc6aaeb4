import request from '@/utils/request'

/**
 * 返回新增后的對沖編號id
 * @param {string} contra_code 賬目類別編號
 * @param {string} contra_name_cn 賬目類別中文
 * @param {string} contra_name_en 賬目類別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 */
export function createContra(contra_code, contra_name_cn, contra_name_en, active_year) {
  return request({
    url: '/contras/actions/create',
    method: 'post',
    data: {
      contra_code,
      contra_name_cn,
      contra_name_en,
      active_year,
    },
  })
}

/**
 * 修改對沖編號
 * @param {integer} contra_id 對沖編號id
 * @param {string} contra_code 對沖編號編號
 * @param {string} contra_name_cn 對沖編號中文
 * @param {string} contra_name_en 對沖編號英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...

 */
export function updateContra(contra_id, contra_code, contra_name_cn, contra_name_en, active_year) {
  return request({
    url: '/contras/actions/update',
    method: 'post',
    data: {
      contra_id,
      contra_code,
      contra_name_cn,
      contra_name_en,
      active_year,
    },
  })
}

/**
 * 刪除對沖編號
 * @param {integer} contra_id 對沖編號id
 */
export function deleteContra(contra_id) {
  return request({
    url: '/contras/actions/delete',
    method: 'post',
    data: {
      contra_id,
    },
  })
}

/**
 * 返回所有對沖編號
 * @param {integer} fy_code 會計週期編號
 * @param {string} name 模糊搜索名字
 * @return {Promise}
 */
export function fetchContras({ fy_code, name } = {}) {
  return request({
    url: '/contras',
    method: 'get',
    params: {
      fy_code,
      name,
    },
  })
}

/**
 * 獲取某對沖編號詳情
 * @param {integer} contra_id 對沖編號id
 */
export function getContra(contra_id) {
  return request({
    url: '/contras/actions/inquire',
    method: 'get',
    params: {
      contra_id,
    },
  })
}

export default { createContra, updateContra, deleteContra, fetchContras, getContra }
