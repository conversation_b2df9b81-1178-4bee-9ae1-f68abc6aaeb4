<template>
  <el-dialog :title="t('view')" :visible.sync="dialogVisible" class="cheque-dialog" width="60%">
    <div ref="paperBox" class="paper-box">
      <div
        ref="paperBoxContainer"
        :style="{
          width: boxWidth + 'px',
          height: boxHeight + 'px',
        }"
        class="paper-box-container"
      >
        <div
          ref="paper"
          class="paper"
          :style="{
            width: paperWidth + 'px',
            height: paperHeight + 'px',
          }"
        >
          <img
            ref="cheque"
            :src="chequeImg"
            :style="{
              width: chequeWidth + 'px',
              height: chequeHeight + 'px',
              transform: `translate(${offsetX}px , ${offsetY}px)`,
            }"
            alt=""
            class="cheque-img"
          >
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    chequeImg: {
      type: String,
      default: '',
    },
    form: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // dialogVisible: false,
      langKey: 'paperPreview.',
      paperSize: {
        width: 0,
        height: 0,
        chequeWidth: 0,
        chequeHeight: 0,
      },
      scale: {
        x: 1,
        y: 1,
      },

      boxWidth: 0, // 纸盒宽度
      boxHeight: 0, // 纸盒高度
      paperWidth: 0, // 纸张宽度
      paperHeight: 0, // 纸张高度
      chequeWidth: 0, // 支票宽度
      chequeHeight: 0, // 支票高度
      offsetX: 0, // 偏移量X
      offsetY: 0, // 偏移量Y
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.paperSize.width = this.form.page_width
      this.paperSize.height = this.form.page_height
      this.paperSize.chequeWidth = this.form.cheque_width
      this.paperSize.chequeHeight = this.form.cheque_height

      this.$nextTick(() => {
        this.refreshPaper()
      })
    },
    refreshPaper() {
      const paperBox = this.$refs.paperBox

      // 計算紙張和支票的實際所需空間（包含偏移量）
      const totalWidth = Math.max(
        this.paperSize.width,
        this.paperSize.chequeWidth + this.form.offset_x,
      )
      const totalHeight = Math.max(
        this.paperSize.height,
        this.paperSize.chequeHeight + this.form.offset_y,
      )

      // 計算縮放比例（保持原比例，縮放到容器的90%）
      const widthScale = (paperBox.clientWidth / totalWidth) * 0.9
      const heightScale = (paperBox.clientHeight / totalHeight) * 0.9
      const scale = Math.min(widthScale, heightScale)

      // 設置容器尺寸
      this.boxWidth = totalWidth * scale
      this.boxHeight = totalHeight * scale

      // 計算紙張尺寸（保持原比例）
      this.paperWidth = this.paperSize.width * scale
      this.paperHeight = this.paperSize.height * scale

      // 計算支票尺寸（保持原比例）
      this.chequeWidth = this.paperSize.chequeWidth * scale - 2 // 減去2px的邊框
      this.chequeHeight = this.paperSize.chequeHeight * scale - 2 // 減去2px的邊框

      // 計算偏移量（保持原比例）
      this.offsetX = this.form.offset_x * scale
      this.offsetY = this.form.offset_y * scale
    },
  },
}
</script>

<style lang="scss" scoped>
.paper-box {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  min-height: 100%;
  padding: 0px;

  .paper-box-container {
    position: relative;
    border-radius: 4px;
  }

  .paper {
    position: relative;
    border: 1px solid #dcdfe6;
    background: white;
    transition: opacity 0.3s; // 添加透明度過渡效果
    border-color: black;

    &:hover {
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.2); // 懸停時加深陰影
    }
  }

  .cheque-img {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0.7;
    transition: opacity 0.3s; // 添加透明度過渡效果

    &:hover {
      opacity: 1; // 懸停時顯示完整支票
    }
  }
}

.cheque-dialog {
  top: -50px !important;

  ::v-deep {
    .el-dialog {
      border-radius: 4px; // 對話框添加圓角
      overflow: hidden; // 確保圓角效果

      .el-dialog__header {
        background: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
      }

      .el-dialog__body {
        max-height: 80vh !important;
        height: 80vh !important;
        padding: 20px;
      }
    }
  }
}
</style>
