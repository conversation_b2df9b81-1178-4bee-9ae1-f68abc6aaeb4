<template>
  <div class="app-content">
    <el-row class="screen-setting">
      <el-col :xs="8" :sm="6" :md="4" :lg="4" class="left">
        <el-menu
          :unique-opened="true"
          :router="true"
          :default-active="$route.name"
          class="left-menu"
          background-color="#fff"
          text-color="#707070"
          active-text-color="#707070"
          @open="handleOpen"
          @close="handleClose"
        >
          <el-submenu v-for="subject in menus" :key="subject.name" :index="subject.name">
            <template slot="title">
              <span>{{ subject.title }}</span>
            </template>
            <el-menu-item-group>
              <el-menu-item
                v-for="item in subject.children"
                :key="item.name"
                :index="item.name"
                :route="{ name: item.name }"
              >
                {{ item.title }}
              </el-menu-item>
            </el-menu-item-group>
          </el-submenu>
        </el-menu>
      </el-col>
      <el-col :xs="16" :sm="18" :md="20" :lg="20" class="right">
        <router-view />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'

export default {
  name: 'SettingScreenIndex',
  components: {
    pageBasic: resolve => {
      require(['./page/basic'], resolve)
    },
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['language']),
    menus() {
      return [
        {
          title: this.$t('setting.basic.name'),
          name: 'settingScreen',
          children: [
            {
              title: this.$t('setting.basic.font'),
              name: 'settingScreenBasic',
            },
            {
              title: this.$t('setting.basic.tableHeader'),
              name: 'settingScreenTableHeader',
            },
            {
              title: this.$t('setting.basic.menu'),
              name: 'settingScreenMenu',
            },
            {
              title: this.$t('setting.basic.content'),
              name: 'settingScreenContent',
            },
          ],
        },
        // {
        //   title: '1111111111111111',
        //   name: '',
        //   children: []
        // }
      ]
    },
  },
  created() {
    this.saveUserLastPage()
  },
  methods: {
    handleOpen(key, keyPath) {},
    handleClose(key, keyPath) {},
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
  .screen-setting {
    height: 100%;
    .left {
      height: 100%;
      .left-menu {
        height: 100%;
        .el-menu-item {
          &.is-active,
          &:hover {
            background-color: #e8f5fe !important;
          }
        }
      }
    }
  }
}

.app-content {
  .screen-setting {
    height: 100%;
    /deep/ {
      .left {
        height: 100%;
        .left-menu {
          background: #ffffff !important;
          height: 100%;

          .el-submenu__title {
            line-height: 30px;
            height: 30px;
            vertical-align: inherit;
            * {
              vertical-align: inherit;
            }
            i {
              /*color: #fff;*/
            }
          }

          .el-submenu .el-menu-item {
            line-height: 30px;
            height: 30px;
            min-width: unset;
          }

          .el-menu-item-group__title {
            padding: 0;
          }

          .el-submenu .el-submenu__title:hover {
            background-color: #3e97dd94 !important;
          }
        }
      }
      .right {
        .el-input-number--medium,
        .el-input {
          width: 150px;
        }
      }
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss"></style>
