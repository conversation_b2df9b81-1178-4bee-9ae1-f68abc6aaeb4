import Layout from '@/views/layout/Layout'

const settingRouter = {
  path: '/setting',
  component: Layout,
  redirect: 'noredirect',
  name: 'setting',
  meta: {
    title: 'router.setting',
    p_code: 'ac.setting',
    icon: 'setting',
  },
  children: [
    {
      path: 'user_info',
      component: () => import('@/views/setting/userInfo/index.vue'),
      name: 'settingUserInfo',
      meta: {
        title: 'router.settingUserInfo',
        p_code: 'ac.setting.user_info',
        noCache: true,
      },
    },
    {
      path: 'screen',
      component: () => import('@/views/setting/screen/index.vue'),
      name: 'settingScreen',
      redirect: 'screen/basic', // 基本設定
      meta: {
        title: 'router.settingScreen',
        p_code: 'ac.setting.screen',
        noCache: true,
      },
      children: [
        {
          path: 'basic',
          component: () => import('@/views/setting/screen/page/basic.vue'),
          name: 'settingScreenBasic',
          meta: {
            title: 'router.settingScreenBasic',
            p_code: 'ac.setting.screen',
            noCache: true,
          },
        },
        {
          path: 'table_header',
          component: () => import('@/views/setting/screen/page/tableHeader.vue'),
          name: 'settingScreenTableHeader',
          meta: {
            title: 'router.settingScreenTableHeader',
            p_code: 'ac.setting.screen',
            noCache: true,
          },
        },
        {
          path: 'menu',
          component: () => import('@/views/setting/screen/page/menu.vue'),
          name: 'settingScreenMenu',
          meta: {
            title: 'router.settingScreenMenu',
            p_code: 'ac.setting.screen',
            noCache: true,
          },
        },
        {
          path: 'content',
          component: () => import('@/views/setting/screen/page/content.vue'),
          name: 'settingScreenContent',
          meta: {
            title: 'router.settingScreenContent',
            p_code: 'ac.setting.screen',
            noCache: true,
          },
        },
      ],
    },
    {
      path: 'printout',
      component: () => import('@/views/setting/printout/index.vue'),
      name: 'settingPrintout',
      redirect: 'printout/printout_redirect', // 自動轉賬列表
      meta: {
        title: 'router.settingPrintout',
        p_code: 'ac.setting.printout',
        noCache: true,
      },
      children: [
        // 定位
        {
          path: 'printout_redirect',
          component: () => import('@/views/setting/printout/redirect.vue'),
          name: 'settingPrintout.printout_redirect',
          meta: {
            title: '',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 日常
        // 付款傳票
        {
          path: 'daily_payment',
          component: () => import('@/views/setting/printout/page/paymentVoucher.vue'),
          name: 'settingPrintout.daily.pdfvoucherp',
          meta: {
            title: 'setting.printout.navMenu.daily_payment',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 收款傳票
        {
          path: 'daily_receipt',
          component: () => import('@/views/setting/printout/page/receiptVoucher.vue'),
          name: 'settingPrintout.daily.pdfvoucherr',
          meta: {
            title: 'setting.printout.navMenu.daily_receipt',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 零用現金傳票
        {
          path: 'daily_petty_cash',
          component: () => import('@/views/setting/printout/page/cashVoucher.vue'),
          name: 'settingPrintout.daily.pdfvoucherc',
          meta: {
            title: 'setting.printout.navMenu.daily_petty_cash',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 轉賬傳票
        {
          path: 'daily_bank_transfer',
          component: () => import('@/views/setting/printout/page/transferVoucher.vue'),
          name: 'settingPrintout.daily.pdfvouchert',
          meta: {
            title: 'setting.printout.navMenu.daily_bank_transfer',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 一般傳票
        {
          path: 'daily_journal',
          component: () => import('@/views/setting/printout/page/journalVoucher.vue'),
          name: 'settingPrintout.daily.pdfvoucherj',
          meta: {
            title: 'setting.printout.navMenu.daily_journal',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 傳票列表
        {
          path: 'daily_voucher_list',
          component: () => import('@/views/setting/printout/page/voucherList.vue'),
          name: 'settingPrintout.daily.pdfvcchecklist',
          meta: {
            title: 'setting.printout.navMenu.daily_voucher_list',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 綜合傳票
        {
          path: 'daily_voucher_consolidate',
          component: () => import('@/views/setting/printout/page/consolidateVoucher.vue'),
          name: 'settingPrintout.daily.pdfconsolidate',
          meta: {
            title: 'setting.printout.navMenu.daily_voucher_consolidate',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 付款收據
        {
          path: 'daily_payment_receipt',
          component: () => import('@/views/setting/printout/page/paymentReceipt.vue'),
          name: 'settingPrintout.daily.pdfreceipt',
          meta: {
            title: 'setting.printout.navMenu.daily_payment_receipt',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 收款收據
        {
          path: 'daily_receipt_receipt',
          component: () => import('@/views/setting/printout/page/receiptReceipt.vue'),
          name: 'settingPrintout.daily.pdfreceipt2',
          meta: {
            title: 'setting.printout.navMenu.daily_receipt_receipt',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 郵寄標籤
        {
          path: 'daily_mailing_label',
          component: () => import('@/views/setting/printout/page/mailingLabel.vue'),
          name: 'settingPrintout.daily.pdlabel',
          meta: {
            title: 'setting.printout.navMenu.daily_mailing_label',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 郵寄標籤
        {
          path: 'daily_envelope',
          component: () => import('@/views/setting/printout/page/envelope.vue'),
          name: 'settingPrintout.daily.pdfenvelope',
          meta: {
            title: 'setting.printout.navMenu.daily_envelope',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 支票
        {
          path: 'daily_cheque',
          component: () => import('@/views/setting/printout/page/cheque.vue'),
          name: 'settingPrintout.daily.pdfcheque',
          meta: {
            title: 'setting.printout.navMenu.daily_cheque',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        /**
         * 週期
         */
        // 自動轉賬
        {
          path: 'periodic_auto_pay_list',
          component: () => import('@/views/setting/printout/page/autoPayList.vue'),
          name: 'settingPrintout.periodic.pdfautopaylist',
          meta: {
            title: 'setting.printout.navMenu.periodic_autopay_list',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 銀行轉賬
        {
          path: 'periodic_bank_reconciliation',
          component: () => import('@/views/setting/printout/page/bankReconciliation.vue'),
          name: 'settingPrintout.periodic.pdfbankrec',
          meta: {
            title: 'setting.printout.navMenu.periodic_bank_reconciliation',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 每日收款
        {
          path: 'periodic_daily_collection',
          component: () => import('@/views/setting/printout/page/dailyCollection.vue'),
          name: 'settingPrintout.periodic.pdfdailycollect',
          meta: {
            title: 'setting.printout.navMenu.periodic_daily_collection',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 結餘轉賬
        {
          path: 'periodic_balance_transfer',
          component: () => import('@/views/setting/printout/page/balanceTransfer.vue'),
          name: 'settingPrintout.periodic.pdfobcsreport',
          meta: {
            title: 'setting.printout.navMenu.periodic_balance_transfer',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        /**
         * 報表
         */
        // 試算表
        {
          path: 'report_trial_balance',
          component: () => import('@/views/setting/printout/page/trialBalance.vue'),
          name: 'settingPrintout.report.pdftrialbalance',
          meta: {
            title: 'setting.printout.navMenu.report_trial_balance',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 會計賬目(總賬賬目列表)
        {
          path: 'report_ledger',
          component: () => import('@/views/setting/printout/page/reportLedger.vue'),
          name: 'settingPrintout.report.pdfledger',
          meta: {
            title: 'setting.printout.navMenu.report_ledger',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 現金賬簿
        {
          path: 'report_cash_book',
          component: () => import('@/views/setting/printout/page/cashBook.vue'),
          name: 'settingPrintout.report.pdfcashbookcash',
          meta: {
            title: 'setting.printout.navMenu.report_cash_book',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 銀行賬簿
        {
          path: 'report_cash_book_bank',
          component: () => import('@/views/setting/printout/page/cashBookBank.vue'),
          name: 'settingPrintout.report.pdfcashbookbank',
          meta: {
            title: 'setting.printout.navMenu.report_cash_book_bank',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 預算
        {
          path: 'report_budget',
          component: () => import('@/views/setting/printout/page/budget.vue'),
          name: 'settingPrintout.report.pdfbudgetledger',
          meta: {
            title: 'setting.printout.navMenu.report_budget',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 職員
        {
          path: 'report_staff',
          component: () => import('@/views/setting/printout/page/staff.vue'),
          name: 'settingPrintout.report.pdfstaffledger',
          meta: {
            title: 'setting.printout.navMenu.report_staff',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 部門
        {
          path: 'report_department',
          component: () => import('@/views/setting/printout/page/department.vue'),
          name: 'settingPrintout.report.pdfdeptledger',
          meta: {
            title: 'setting.printout.navMenu.report_department',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        // 傳票列表
        {
          path: 'report_voucher',
          component: () => import('@/views/setting/printout/page/reportVoucher.vue'),
          name: 'settingPrintout.report.pdfvoucherlist',
          meta: {
            title: 'setting.printout.navMenu.report_voucher',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
        /**
         * 庫存
         */
        // 利潤報表
        {
          path: 'stock_sales_proffit',
          component: () => import('@/views/setting/printout/page/salesProfit.vue'),
          name: 'settingPrintout.stock.pdfsalesprofit',
          meta: {
            title: 'setting.printout.navMenu.stock_sales_proffit',
            p_code: 'ac.setting.printout',
            noCache: true,
          },
        },
      ],
    },
    // {
    //   path: 'checking',
    //   component: () => import('@/views/errorPage/coding/index.vue'),
    //   name: 'settingChecking',
    //   meta: {
    //     title: 'router.settingChecking',
    //     p_code: 'ac.setting.checking',
    //     noCache: true
    //   }
    // },
    // {
    //   path: 'data_export',
    //   component: () => import('@/views/errorPage/coding/index.vue'),
    //   name: 'settingDataExport',
    //   meta: {
    //     title: 'router.settingDataExport',
    //     p_code: 'ac.setting.data_export',
    //     noCache: true
    //   }
    // },
    {
      path: 'page_set',
      component: () => import('@/views/setting/pageSet/index.vue'),
      name: 'settingPageSet',
      meta: {
        title: 'router.settingPageSet',
        p_code: 'ac.setting.page_set',
        noCache: true,
      },
    },
    {
      path: 'data_import',
      component: () => import('@/views/setting/dataImport/index.vue'),
      name: 'settingDataImport',
      meta: {
        title: 'router.settingDataImport',
        p_code: 'ac.setting.data_import',
        noCache: true,
      },
    },

    {
      path: 'backup',
      component: () => import('@/views/setting/backup/index.vue'),
      name: 'settingBackup',
      meta: {
        title: 'router.settingBackup',
        p_code: 'ac.setting.user_info',
        noCache: true,
      },
    },
    {
      path: 'restore_demo_data',
      component: () => import('@/views/setting/restoreDemoData/index.vue'),
      name: 'restoreDemoData',
      meta: {
        title: 'router.restoreDemoData',
        p_code: 'ac.setting.restore_demo_data',
        noCache: true,
      },
    },
  ],
}

export default settingRouter
