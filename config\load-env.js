/**
 * 環境變量加載模塊
 * 統一處理不同環境的 .env 文件加載
 */
'use strict'

/**
 * 加載對應環境的 .env 文件
 * @param {boolean} force - 是否強制重新加載
 */
function loadEnvironment(force = false) {
  // 如果已經加載過且不強制重新加載，則跳過
  if (process.env._ENV_LOADED && !force) {
    return
  }

  // 根據環境加載對應的 .env 文件
  let envFile = '.env'
  if (process.env.NODE_ENV === 'development') {
    envFile = '.env.development'
  }

  // 加載環境變量文件
  const path = require('path')
  const result = require('dotenv').config({
    path: path.resolve(process.cwd(), envFile)
  })

  if (result.error) {
    console.warn(`Warning: Could not load ${envFile}:`, result.error.message)
  } else {
    console.log(`Environment loaded from: ${envFile}`)
  }

  // 標記已加載
  process.env._ENV_LOADED = 'true'
  
  return result
}

/**
 * 檢查必需的環境變量
 * @param {string[]} requiredVars - 必需的環境變量列表
 * @param {string} context - 上下文描述（用於錯誤信息）
 */
function checkRequiredEnvs(requiredVars, context = 'application') {
  const missing = requiredVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    console.error(`Missing required environment variables for ${context}:`)
    missing.forEach(varName => {
      console.error(`  ${varName}`)
    })
    
    // 根據當前環境提示檢查的文件
    let envFile = '.env'
    if (process.env.NODE_ENV === 'development') {
      envFile = '.env.development'
    }
    
    console.error(`Please check your ${envFile} file`)
    console.error('Reference .env.example for required environment variable format')
    
    process.exit(1)
  }
}

/**
 * 獲取當前環境信息
 */
function getEnvironmentInfo() {
  return {
    nodeEnv: process.env.NODE_ENV || 'development',
    vueAppEnv: process.env.VUE_APP_ENV,
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production'
  }
}

module.exports = {
  loadEnvironment,
  checkRequiredEnvs,
  getEnvironmentInfo
}
