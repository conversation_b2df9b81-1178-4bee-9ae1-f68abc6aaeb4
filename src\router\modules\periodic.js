import Layout from '@/views/layout/Layout'

const periodicRouter = {
  path: '/periodic',
  component: Layout,
  redirect: 'noredirect',
  name: 'periodic',
  meta: {
    title: 'router.periodic',
    p_code: 'ac.periodic',
    icon: 'cycle',
  },
  children: [
    {
      path: 'bank_ai_reconciliation',
      component: () => import('@/views/periodic/bankAiReconciliation/index.vue'),
      name: 'periodicBankAiReconciliation',
      needAPrint: 'P',
      meta: {
        title: 'router.periodicBankAiReconciliation',
        p_code: 'ac.periodic.bank_ai_reconciliation',
        noCache: true,
      },
    },
    {
      path: 'bank_reconciliation',
      component: () => import('@/views/periodic/bankReconciliation/index.vue'),
      name: 'periodicBankReconciliation',
      needAPrint: 'P',
      meta: {
        title: 'router.periodicBankReconciliation',
        p_code: 'ac.periodic.bank_reconciliation',
        noCache: true,
      },
    },
    {
      path: 'daily_collection',
      component: () => import('@/views/periodic/dailyCollection/index.vue'),
      name: 'periodicDailyCollection',
      needAPrint: 'P',
      meta: {
        title: 'router.periodicDailyCollection',
        p_code: 'ac.periodic.daily_collection',
        noCache: true,
      },
    },
    {
      path: 'autopay_list',
      component: () => import('@/views/periodic/autopayList/index.vue'),
      name: 'periodicAutopayList',
      needAPrint: 'P',
      meta: {
        title: 'router.periodicAutopayList',
        p_code: 'ac.periodic.autopay_list',
        noCache: true,
      },
    },
    {
      path: 'balance_transfer',
      component: () => import('@/views/periodic/balanceTransfer/index.vue'),
      name: 'periodicBalanceTransfer',
      needAPrint: 'P',
      meta: {
        title: 'router.periodicBalanceTransfer',
        p_code: 'ac.periodic.balance_transfer',
        noCache: true,
      },
    },
    {
      path: 'ob_cheque',
      component: () => import('@/views/periodic/obCheque/index.vue'),
      name: 'periodicObCheque',
      needAPrint: 'P',
      meta: {
        title: 'router.periodicObCheque',
        p_code: 'ac.periodic.ob_cheque',
        noCache: true,
      },
    },
    {
      path: 'edb_input',
      component: () => import('@/views/periodic/edbInput/index.vue'),
      name: 'periodicEDBInput',
      meta: {
        title: 'router.periodicEDBInput',
        p_code: 'ac.periodic.edb_input',
        noCache: true,
      },
    },
    // {
    //   path: 'data_backup',
    //   component: () => import('@/views/errorPage/coding/index.vue'),
    //   name: 'periodicDataBackup',
    //   meta: {
    //     title: 'router.periodicDataBackup',
    //     p_code: 'ac.periodic.data_backup',
    //     noCache: true
    //   }
    // }
  ],
}

export default periodicRouter
