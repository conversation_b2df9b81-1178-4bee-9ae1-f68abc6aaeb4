<template>
  <div v-loading="loading" class="app-container">
    <VBreadcrumb />
    <div class="filter mini-form">
      <el-form :inline="true">
        <el-row>
          <el-col :span="16" class="left">
            <el-form-item :label="$t('daily.address.label.payeeGroup')" class="range">
              <el-select v-model="preferences.filters.company_group_id" style="width: 150px">
                <el-option :label="$t('daily.address.label.allPayee')" value="" />
                <el-option
                  v-for="item in companyGroups"
                  :key="item.company_group_id"
                  :label="item[language === 'en' ? 'cg_name_en' : 'cg_name_cn']"
                  :value="item.company_group_id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('daily.address.label.search')" class="search">
              <el-input
                v-model="search_text"
                style="width: 150px"
                @keyup.enter.native="fetchData"
              />
            </el-form-item>
            <el-form-item>
              <el-button size="mini" type="primary" class="" @click="fetchData">
                {{ $t('daily.address.button.fetch') }}
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="right">
            <el-form-item>
              <el-button :loading="btnLoading1" size="mini" type="primary" @click="onPrintLabel">
                {{ $t('daily.address.button.printLabel') }}
              </el-button>
              <el-button :loading="btnLoading2" size="mini" type="primary" @click="onPrintEnvelope">
                {{ $t('daily.address.button.printEnvelope') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="content">
      <el-transfer
        v-model="value"
        :data="data"
        :props="{
          key: 'company_id',
          label: 'comp_name',
        }"
        :titles="titles"
        @change="onChange"
      >
        <span slot-scope="{ option }">{{ option.comp_code }} - {{ option.comp_name }}</span>
      </el-transfer>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import VBreadcrumb from '@/views/layout/components/VBreadcrumb'
import ETable from '@/components/ETable'
import { fetchCompanyGroups } from '@/api/assistance/payeePayer/payeePayerGroup'
import { searchCompanies } from '@/api/assistance/payeePayer'

import loadPreferences from '@/views/mixins/loadPreferences'
import handlePDF from './handlePDF'

export default {
  name: 'DailyAddress',
  components: {
    VBreadcrumb,
    ETable,
  },
  mixins: [loadPreferences, handlePDF],
  data() {
    return {
      loading: true,

      data: [],
      value: [], // 選擇的key
      selectItems: [], // 選擇的對象

      companyGroups: [],
      preferences: {
        filters: {
          company_group_id: '',
        },
      },
      search_text: '',
      btnLoading1: false,
      btnLoading2: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    titles() {
      return [this.$t('daily.address.label.optional'), this.$t('daily.address.label.selected')]
    },
  },
  mounted() {
    this.initData()
    this.saveUserLastPage()
  },
  methods: {
    initData() {
      this.loading = true
      this.loadUserPreference()
        .then(() => fetchCompanyGroups({}))
        .then(res => {
          this.companyGroups = res
        })
        .then(this.fetchData)
    },
    fetchData() {
      this.loading = true
      if (
        this.companyGroups &&
        this.companyGroups.length > 0 &&
        this.preferences.filters.company_group_id !== ''
      ) {
        let bool = false
        this.companyGroups.forEach(i => {
          if (i.company_group_id === this.preferences.filters.company_group_id) {
            bool = true
            return
          }
        })
        if (!bool) {
          this.preferences.filters.company_group_id = this.companyGroups[0].company_group_id
        }
      }
      const parent_company_group_id = this.preferences.filters.company_group_id
      const name = this.search_text
      searchCompanies({ parent_company_group_id, name })
        .then(res => {
          const newData = []
          this.selectItems.forEach(item => {
            if (res.findIndex(i => i.company_id === item.company_id) === -1) {
              newData.push(item)
            }
          })
          newData.push(...res)
          this.data = newData
        })
        .finally(() => {
          this.loading = false
        })
    },
    onChange(val, type, key) {
      console.table({ val, type, key })
      this.selectItems = val.map(v => {
        return this.data.find(item => item.company_id === v)
      })
    },
    onPrintLabel() {
      this.btnLoading1 = true
      this.handlerPrintMailingLabel(this.selectItems).finally(() => {
        this.btnLoading1 = false
      })
    },
    onPrintEnvelope() {
      this.btnLoading2 = true
      this.handlerPrintEnvelope(this.selectItems).finally(() => {
        this.btnLoading2 = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  vbreadcrumb {
  }
  .filter {
    padding-right: 10px;
    margin: 10px 10px 5px 10px;
    .left {
      .range {
      }
      .search {
      }
      .actions {
      }
    }
    .right {
      & > div {
        float: right;
      }
    }
  }
  .content {
    height: calc(100vh - 210px);
    margin: 0 10px;
    /deep/ {
      .el-transfer {
        height: 100%;
        text-align: center;
        .el-transfer-panel {
          text-align: left;
          width: calc((100vw - 190px) / 2);
          height: inherit;

          .el-transfer-panel__body {
            height: calc(100% - 25px);
            position: relative;
            display: block;
            .el-transfer-panel__list {
              height: 100%;
              .el-transfer-panel__item {
                width: 100%;
                height: 25px;
                line-height: 25px;
                .el-checkbox__label {
                }
              }
            }
          }
          .el-transfer-panel__header {
            height: 28px;
            line-height: 28px;
            .el-checkbox {
              line-height: inherit;
              .el-checkbox__input.is-checked .el-checkbox__inner {
                box-shadow: 0 0 4px 0px white;
              }
            }
          }
        }
      }
    }
  }
}
</style>
