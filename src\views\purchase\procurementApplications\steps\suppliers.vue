<template>
  <div class="items-step">
    <el-form ref="form" :model="{ form, notEnoughSuppliersReason }">
      <el-table :data="form">
        <el-table-column type="index" />
        <!--    供應商名稱    -->
        <el-table-column :label="t('supplierName')" prop="supplier_name">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item
              v-if="show"
              :prop="`form[${scope.$index}].supplier_name`"
              style="width: 100%"
            >
              <!--              <el-input-->
              <!--                v-model="form[scope.$index].supplier_name"-->
              <!--                :disabled="isReadonly"-->
              <!--                type="textarea"-->
              <!--                autosize-->
              <!--                resize="none"/>-->
              <div class="supplier-name-container">
                <el-autocomplete
                  v-model="form[scope.$index].supplier_name"
                  :fetch-suggestions="querySearchAsync"
                  :disabled="isReadonly"
                  autosize
                  clearable
                  style="width: 100%"
                  @focus="handleFocus(form[scope.$index])"
                  @select="handleSelect"
                />
                <i
                  v-if="!isReadonly"
                  class="edac-icon action-icon edac-icon-search"
                  @click="onSearch(scope.$index)"
                />
              </div>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('supplierType')" prop="supplier_type" width="90">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item :rules="itemRequiredRules" :prop="`form[${scope.$index}].supplier_type`">
              <el-select
                v-model="form[scope.$index].supplier_type"
                :disabled="isReadonly"
                class="input input-select"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('supplierTel')" prop="supplier_tel" width="120">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item :rules="itemRequiredRules" :prop="`form[${scope.$index}].supplier_tel`">
              <el-input v-model="form[scope.$index].supplier_tel" :disabled="isReadonly" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('supplierFax')" prop="supplier_fax" width="120">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item :prop="`form[${scope.$index}].supplier_fax`">
              <el-input v-model="form[scope.$index].supplier_fax" :disabled="isReadonly" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('supplierContact')" prop="supplier_contact" width="120">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item
              :rules="itemRequiredRules"
              :prop="`form[${scope.$index}].supplier_contact`"
            >
              <el-input v-model="form[scope.$index].supplier_contact" :disabled="isReadonly" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="$t('table.action')" width="100">
          <template v-if="scope && scope.row" slot-scope="scope">
            <i
              v-if="!isReadonly && canDelete"
              class="el-icon-delete action-icon"
              @click="onDelete(scope)"
            />
          </template>
        </el-table-column>
        <div slot="append" class="not-enough-suppliers-reason">
          <el-form-item
            v-if="show && showReason"
            :rules="reasonForSuppliers ? [] : requiredRules"
            :label="t('notEnoughSuppliersReason')"
            prop="notEnoughSuppliersReason"
          >
            <el-input
              v-model="notEnoughSuppliersReason"
              :disabled="isReadonly"
              type="textarea"
              autosize
              style="width: 500px"
              resize="none"
            />
          </el-form-item>
        </div>
      </el-table>
      <el-form-item class="actions">
        <el-button size="mini" type="info" @click="onBack">
          {{ $t('button.back') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onPrev">
          {{ $t('button.prev') }}
        </el-button>
        <el-button
          v-if="!isReadonly"
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="success"
          @click="onSave"
        >
          {{ $t('button.saveFile') }}
        </el-button>
        <el-button
          v-if="!(isReadonly && stage === 0 && status !== 'GA')"
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="primary"
          @click="onNext"
        >
          {{ $t(status === 'A' ? 'button.submit' : 'button.next') }}
        </el-button>
      </el-form-item>
    </el-form>

    <svg-icon v-if="!isReadonly" icon-class="add" class-name="add-icon add-row" @click="onAddRow" />
    <DialogSupplier
      :dialog-visible.sync="dialogSupplierVisible"
      :fy-code="fyCode"
      @selectRow="handleSupplierSelect"
    />
  </div>
</template>
<script>
import DialogSupplier from './DialogSupplier.vue'
import {
  editProcurementApplicationsSuppliers,
  fetchSupplierList,
} from '@/api/purchase/procurementApplications'

export default {
  name: 'SuppliersStep',
  components: {
    DialogSupplier,
  },
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    not_enough_suppliers_reason: {
      type: String,
      required: true,
    },
    reason_for_suppliers: {
      type: String,
      required: true,
    },
    suppliers_at_least: {
      type: Number,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: true,
    },
    stage: {
      type: [Number, String],
      required: true,
    },
    status: {
      type: [Number, String],
      required: true,
    },
    fyCode: {
      type: [String, Object],
    },
  },
  data() {
    return {
      langKey: 'purchase.daily.procurementApplications.',
      show: true,
      dialogSupplierVisible: false,
      rowIndex: -1,
      // form: {
      //
      // }
    }
  },
  computed: {
    itemRequiredRules() {
      return this.isReadonly
        ? []
        : [{ validator: this.itemValidate, message: ' ', trigger: ['blur'] }]
    },
    form: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
    notEnoughSuppliersReason: {
      get() {
        return this.not_enough_suppliers_reason
      },
      set(val) {
        this.$emit('update:not_enough_suppliers_reason', val)
      },
    },
    reasonForSuppliers() {
      return this.reason_for_suppliers === 'Y'
    },
    showReason() {
      return this.form.filter(i => i.supplier_name.length > 0).length < this.suppliers_at_least
    },
    canDelete() {
      return this.form.length > this.suppliers_at_least
    },
    lastRow() {
      return {
        notEnoughSuppliersReason: this.notEnoughSuppliersReason,
        supplier_name: '',
        supplier_index: -1,
        supplier_type: '',
        supplier_tel: '',
        supplier_fax: '',
        supplier_contact: '',
        currentRow: {},
      }
    },
    tableData() {
      return [...this.form, this.lastRow]
    },
    typeList() {
      return [
        {
          value: 'COM',
          label: this.t('com'),
        },
        {
          value: 'ORG',
          label: this.t('org'),
        },
      ]
    },
  },
  created() {
    for (let i = this.form.length; i < this.suppliers_at_least; i++) {
      this.onAddRow()
    }
  },
  mounted() {
    this.show = false
    this.$nextTick(() => {
      this.show = true
    })
  },
  methods: {
    itemValidate(rule, value, callback) {
      const reg = rule.field.match(/form\[(\d+)\]\..*/)
      let index = ''
      if (reg) {
        index = reg[1]
        const name = this.form[index].supplier_name
        if (name.length > 0) {
          if (value.length === 0) {
            callback(new Error(' '))
          }
        }
      }
      // this.$refs.ruleForm.validateField('checkPass')
      callback()
    },
    onAddRow() {
      this.form.push({
        supplier_name: '',
        supplier_index: this.form.length,
        supplier_type: 'COM',
        supplier_tel: '',
        supplier_fax: '',
        supplier_contact: '',
      })
    },
    onDelete(scope) {
      if (!this.canDelete) {
        return
      }
      this.form.splice(scope.$index, 1)
    },
    onSave(showTips = true) {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(async(valid, a) => {
          if (!valid) {
            reject()
            return false
          }
          const pro_application_id = this.id
          const not_enough_suppliers_reason = this.not_enough_suppliers_reason
          const list = this.form.filter(i => i.supplier_name.length > 0)

          if (list.length === 0) {
            this.$message.error(this.t('suppliersEmpty'))
            reject()
            return
          }
          const suppliers = list.map((item, index) => {
            return {
              supplier_name: item.supplier_name,
              supplier_index: index + 1,
              supplier_type: item.supplier_type,
              supplier_tel: item.supplier_tel,
              supplier_fax: item.supplier_fax,
              supplier_contact: item.supplier_contact,
            }
          })

          try {
            await editProcurementApplicationsSuppliers({
              pro_application_id,
              suppliers,
              not_enough_suppliers_reason,
            })
            if (showTips) {
              this.$message.success(this.t('saveSuccess'))
              this.onBack()
            }
            resolve()
          } catch (e) {
            console.log(e)
            reject(e)
          }
        })
      })
    },
    onNext() {
      this.$emit('next')
    },
    onPrev() {
      this.$emit('prev')
    },
    onBack() {
      this.$emit('back')
    },

    // 搜索歷史供應商名稱
    async querySearchAsync(queryString, cb) {
      try {
        const res = await fetchSupplierList(queryString)
        console.log(res)
        res.forEach(item => {
          item.value = item.comp_name
        })
        cb(res)
      } catch (e) {
        console.log(e)
      }
    },
    handleFocus(row) {
      this.currentRow = row
    },
    handleSelect(item, a, b) {
      console.log(item, a, b)
      this.currentRow.supplier_name = item.comp_name || ''
      this.currentRow.supplier_tel = item.comp_tel || ''
      this.currentRow.supplier_fax = item.comp_fax || ''
      this.currentRow.supplier_contact = item.comp_attention || ''
      this.currentRow.supplier_name = item.comp_name || ''
    },
    handleSupplierSelect(row) {
      // this.dialogSupplierVisible = true
      this.form[this.rowIndex].supplier_name = row.comp_name || ''
      this.form[this.rowIndex].supplier_tel = row.comp_tel || ''
      this.form[this.rowIndex].supplier_fax = row.comp_fax || ''
      this.form[this.rowIndex].supplier_contact = row.comp_attention || ''
      this.form[this.rowIndex].supplier_name = row.comp_name || ''
    },
    onSearch(rowIndex) {
      this.dialogSupplierVisible = true
      this.rowIndex = rowIndex
    },
  },
}
</script>

<style lang="scss" scoped>
.add-icon {
  cursor: pointer;
  &.add-row {
    position: absolute;
    right: -30px;
    top: 3px;
  }
}
.items-step {
  /deep/ {
    .el-input-number .el-input-number__increase,
    .el-input-number .el-input-number__decrease {
      top: 4px;
    }
    .el-form-item__error {
      display: none;
    }
  }
  .actions {
    text-align: right;
    width: 1000px;
    padding-top: 10px;
  }
  .not-enough-suppliers-reason {
    padding: 10px 0;
    /deep/ {
      .el-form-item__label {
        line-height: 30px;
        vertical-align: middle;
      }
      .el-form-item__content {
        line-height: inherit;
        .el-textarea {
          line-height: inherit;
        }
      }
    }
  }
}
.supplier-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
