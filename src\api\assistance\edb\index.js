import request from '@/utils/request'

/**
 * 匯出EDB報表(Norray版)
 * @param {string} fy_code 會計週期code
 * @param {string} pd_code 截止至會計週期月份編號
 * @return {Promise}
 */
export function exportNorrayEDB({ fy_code, pd_code }) {
  return request({
    url: '/reports/norray-edb/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      pd_code,
    },
  })
}

/**
 * 匯出EDB賬目關係報表(Norray版)
 * @param {string} fy_code 會計週期code
 * @return {Promise}
 */
export function exportNorrayEDBRelation({ fy_code }) {
  return request({
    url: '/reports/norray-edb-relation/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
    },
  })
}

/**
 * 匯入EDB賬目關係報表(Norray版)
 * @param {string} fy_code 會計週期code
 * @param {string} data_json 匯入的數據
 * @return {Promise}
 */
export function importNorrayEDBRelation(data, fy_code) {
  return request({
    url: '/reports/norray-edb-relation/actions/import',
    method: 'post',
    data: {
      fy_code,
      data_json: JSON.stringify(data),
    },
  })
}

/**
 * 匯出EDB學校人數
 * @param {string} fy_code 會計週期code
 * @return {Promise}
 */
export function exportNumberOfSchool({ fy_code }) {
  return request({
    url: '/reports/number-of-school/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
    },
  })
}

/**
 * 匯入學校人數
 * @param {string} fy_code 會計週期code
 * @param {string} data_json 匯入的數據
 * @return {Promise}
 */
export function importNumberOfSchool(data, fy_code) {
  return request({
    url: '/reports/number-of-school/actions/import',
    method: 'post',
    data: {
      fy_code,
      data_json: JSON.stringify(data),
    },
  })
}

/**
 * 匯出EDB收支賬的輸入(Norray版)
 * @param {string} fy_code 會計週期code
 * @return {Promise}
 */
export function exportNorrayEDBInput({ fy_code }) {
  return request({
    url: '/reports/norray-edb-input/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
    },
  })
}

/**
 * 匯入EDB收支賬的輸入(Norray版)
 * @param {string} fy_code 會計週期code
 * @param {object} data 匯入的數據
 * @return {Promise}
 */
export function importNorrayEDBInput(data, fy_code) {
  return request({
    url: '/reports/norray-edb-input/actions/import',
    method: 'post',
    data: {
      fy_code,
      data_json: JSON.stringify(data),
    },
  })
}

/**
 * 獲取EDB學校人數
 * @param {string} fy_code 會計週期code
 * @return {Promise}
 */
export function fetchNumberOfSchool(fy_code) {
  return request({
    url: '/reports/number-of-school',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 更新EDB學校人數
 * @param {string} fy_code 會計週期code
 * @param {string} numbers 人數內容(json),格式:[{"month":"01","fy_code":"10.00","sales_price":"10.00"},...]
 * @return {Promise}
 */
export function editNumberOfSchool({ fy_code, numbers }) {
  return request({
    url: '/reports/number-of-school/actions/update',
    method: 'post',
    data: {
      fy_code,
      number_json: JSON.stringify(numbers),
    },
  })
}
