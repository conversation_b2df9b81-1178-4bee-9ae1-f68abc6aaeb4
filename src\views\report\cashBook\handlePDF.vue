<script>
import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'

export default {
  name: 'HandlePDF',
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      const bank = this.bankList.find(i => i.ac_code === this.preferences.filters.selectedAcCode)

      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting(bank.ac_bank === 'P' ? 'pdfcashbookcash' : 'pdfcashbookbank', 1)
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(this.formatPrintData)
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              printSetting,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const language = printSetting.language

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }
        const bank = this.bankList.find(i => i.ac_code === this.preferences.filters.selectedAcCode)
        const langKey =
          bank.ac_bank === 'P' ? 'report.cashBook.label.' : 'report.cashBookBank.label.'
        const title = this.$t(langKey + 'title', language)
        const bankName = bank
          ? language === 'en'
            ? bank.ac_name_en
            : bank.ac_name_cn
          : this.$t('filters.all', language)
        const begin_date = this.preferences.filters.begin_date
        const end_date = this.preferences.filters.end_date
        let dateStr = ''
        if (begin_date && end_date) {
          dateStr =
            dateUtil.format(new Date(begin_date), 'dd/MM/yyyy') +
            ' - ' +
            dateUtil.format(new Date(end_date), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${bankName} - ${dateStr}`,
          data: [
            {
              label: this.$t('filters.fund', language) + ':',
              value: bankName,
            },
            {
              label: this.$t('filters.period', language) + ':',
              value: dateStr,
            },
          ],
        }

        // 表頭
        const columns = []
        const columnData = printSetting.columnData
          .filter(i => i.position > 0)
          .sort((a, b) => a.position - b.position)
        columnData.forEach(col => {
          columns.push({
            text: this.$t(langKey + col.name, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: col.alignment,
            width: col.width,
            border: [true, true, true, true],
            // ,
            // maxHeight: 25,
            // marginBottom: 25
          })
        })
        // 主數據
        //

        // tableData
        const tableData = []
        const sumRow = []
        let sumIndex = 0

        // 初始化行
        columnData.forEach((col, colIndex) => {
          if (col.position === 0) {
            // 即不顯示列
            return
          }
          switch (col.name) {
            case 'break_down': {
              sumRow.push({ name: col.name, text: '0.00', value: 0, style: 'tableFooter' })
              break
            }
            case 'amount_dr': {
              sumRow.push({ name: col.name, text: '0.00', value: 0, style: 'tableFooter' })
              break
            }
            case 'amount_cr': {
              sumRow.push({ name: col.name, text: '0.00', value: 0, style: 'tableFooter' })
              break
            }
            case 'amount_net': {
              sumRow.push({ name: col.name, text: '0.00', value: 0, style: 'tableFooter' })
              break
            }
            case 'amount_balance': {
              sumRow.push({ name: col.name, text: '0.00', value: 0, style: 'tableFooter' })
              break
            }
            default: {
              sumRow.push({ name: col.name, text: '', style: 'tableFooter' })
            }
          }
          sumIndex++
        })
        const totalDataCount = this.tableData.length
        this.tableData.forEach((item, rowIndex) => {
          const row = []
          sumIndex = 0
          const notBorder = [
            false,
            false,
            false,
            rowIndex === totalDataCount - 1 || this.tableData[rowIndex + 1].vc_no !== null,
          ]
          columnData.forEach((col, colIndex) => {
            if (col.position === 0) {
              // 即不顯示列
              return
            }
            const cell = {
              text: '',
              alignment: col.alignment,
              style: 'tableContent',
              width: col.width,
              border: notBorder,
            }
            switch (col.name) {
              case 'descr':
              case 'break_down':
              case 'ac_name_': {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = language === 'en' ? key + 'en' : key + 'cn'
                }
                cell.text = item[key]
                break
              }
              case 'vc_date': {
                if (item.vc_date !== null) {
                  let vc_date = ''
                  if (rowIndex === 0) {
                    vc_date = new Date(begin_date)
                  } else {
                    vc_date = new Date(item.vc_date)
                  }

                  cell.text = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
                }
                break
              }
              case 'amount_dr':
              case 'amount_cr': {
                // case 'amount_balance':
                if (item.vc_date !== null) {
                  let v = Number(item[col.name])
                  isNaN(v) && (v = 0)
                  sumRow[sumIndex].alignment = col.alignment
                  sumRow[sumIndex].value = toDecimal(Number(sumRow[sumIndex].value) + v)
                  sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)

                  cell.text = amountFormat(v)
                }
                break
              }
              case 'amount_balance':
                if (item.vc_date !== null || (item.ac_code === null && rowIndex === 0)) {
                  let v = Number(item[col.name])
                  isNaN(v) && (v = 0)
                  sumRow[sumIndex].alignment = col.alignment
                  sumRow[sumIndex].value = v
                  sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)

                  cell.text = amountFormat(v)
                }
                break
              default: {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = language === 'en' ? key + 'en' : key + 'cn'
                }
                const v = item[key]
                cell.text = v === undefined ? '' : v
                break
              }
            }
            row.push(cell)
            sumIndex++
          })
          tableData.push(row)
        })

        if (sumRow.length > 0) {
          const sumPosition = sumRow.findIndex(i => i.name === 'break_down')
          if (sumPosition > 0) {
            sumRow[0].text = this.$t('print.total', language)
            sumRow[0].colSpan = sumPosition
            sumRow[0].alignment = 'right'
            sumRow[0].bold = true
            sumRow[0].style = 'tableFooter'
          }

          const sumDrPosition = sumRow.findIndex(i => i.name === 'amount_dr')
          const sumCrPosition = sumRow.findIndex(i => i.name === 'amount_cr')
          const sumBalPosition = sumRow.findIndex(i => i.name === 'amount_balance')

          if (sumDrPosition !== -1) {
            sumRow[sumDrPosition].style = 'tableFooter'
            sumRow[sumDrPosition].alignment = columnData[sumDrPosition].alignment
          }
          if (sumCrPosition !== -1) {
            sumRow[sumCrPosition].style = 'tableFooter'
            sumRow[sumCrPosition].alignment = columnData[sumCrPosition].alignment
          }
          if (sumBalPosition !== -1) {
            sumRow[sumBalPosition].style = 'tableFooter'
            sumRow[sumBalPosition].alignment = columnData[sumBalPosition].alignment
          }

          tableData.push(sumRow)
        }

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 表格寬度
      const widths = generateWidths(columns)

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              heights: tableData.map((e, i) =>
                i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
              ),
              headerRows: 2,
              body: [
                pageHeader, // 頁頭
                columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              defaultBorder: false,
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      // console.log(JSON.stringify(docDefinition))
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      await openPdf(docDefinition)
    },
  },
}
</script>
