<template>
  <div class="app-container">
    <VBreadCrumb :show="true" class="breadcrumb" />

    <el-form
      ref="form"
      v-loading="loading"
      :model="preferences.filters"
      label-position="right"
      label-width="80px"
    >
      <el-form-item>
        <el-button size="mini" type="primary" @click="onBackup(true)">
          {{ $t('button.backup') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onRestore">
          {{ $t('button.restore') }}
        </el-button>
      </el-form-item>
    </el-form>
    <restore-dialog
      :visible.sync="importDialog"
      :status="backupStatus"
      :title="$t('setting.dataBackup.dataRestore')"
      :msg="$t('setting.dataBackup.restoreTips')"
      :loading="importLoading"
      :file-type-tips="' '"
      :on-backup="onBackup"
      :on-submit="handleRestore"
      :multiple="false"
      @change="onChangeFile"
    />
  </div>
</template>

<script>
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import { mapGetters } from 'vuex'
import loadPreferences from '@/views/mixins/loadPreferences'
import permission from '@/views/mixins/permission'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'
import { downloadObjectUrl, exportExcel, importExcel, importExcelMultiple } from '@/utils/excel'
import RestoreDialog from '@/components/restoreDialog/index'
import { exportNumberOfSchool, importNumberOfSchool } from '@/api/assistance/edb'
import { backupsDownload, backupsRestore } from '@/api/settings/backups'

export default {
  name: 'AssistanceEDBRelation',
  components: {
    VBreadCrumb,
    RestoreDialog,
    UploadExcel,
  },
  mixins: [loadPreferences, permission],
  data() {
    return {
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      preferences: {
        filters: {
          year: '',
        },
      },
      years: [],
      importLoading: false,
      importDialog: false,
      backupStatus: 1,
      currentFile: {},
    }
  },
  computed: {
    ...mapGetters(['system', 'currentYear']),
  },
  created() {
    console.log('initData')
    this.initData()
    this.saveUserLastPage()
  },
  methods: {
    async initData() {
      this.loading = false
      try {
        this.years = await fetchYears()
        await this.loadUserPreference()
        await this.$nextTick()
        if (!this.preferences.filters.year && this.currentYear) {
          this.preferences.filters.year = this.currentYear.fy_code
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    async onImport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return

      this.importDialog = true
    },
    async handleImport({ header, results }) {
      this.loading = true
      const fy_code = this.preferences.filters.year
      if (!fy_code) return
      importExcel(this, importNumberOfSchool, results, header, { fy_code })
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    async onExport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return
      try {
        this.loading = true
        const fy_code = this.preferences.filters.year
        const res = await exportNumberOfSchool({ fy_code })
        await exportExcel(res, '')
        this.$message.success(this.$t('file.exportSuccess'))
      } catch (e) {
        this.$message.error(this.$t('file.exportError'))
        console.log(e)
      }
      this.loading = false
    },
    async onBackup(backup) {
      try {
        this.loading = true
        const { url, filename } = await backupsDownload()
        downloadObjectUrl(url, filename)
        this.$message.success(this.$t('setting.dataBackup.backupSuccess'))

        if (backup) {
          this.backupStatus = 2
        }
      } catch (e) {
        // this.$message.error(this.$t('file.exportError'))
        console.error(e)
      }
      this.loading = false
    },
    onRestore() {
      this.backupStatus = 1
      this.currentFile = {}
      this.importDialog = true
    },
    onChangeFile(file) {
      this.currentFile = file
    },
    async handleRestore() {
      this.importLoading = true
      try {
        if (this.currentFile && this.currentFile instanceof File && this.currentFile.name) {
          await backupsRestore(this.currentFile)
          this.$message.success(this.$t('setting.dataBackup.restoreSuccess'))
          this.importDialog = false
        } else {
          this.$message.error(this.$t('setting.dataBackup.selectFile'))
        }
      } catch (e) {
        console.error(e)
      }
      this.importLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.year {
  width: 400px;
}
</style>
