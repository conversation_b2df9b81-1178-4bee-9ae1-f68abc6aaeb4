import request from '@/utils/request'

/**
 * 新增貨品
 * @param {string} sk_code 貨品編號
 * @param {string} sk_name_cn 貨品中文名
 * @param {string} sk_name_en 貨品英文名
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_stock_group_id 父貨品組別id((不傳則為根節點)
 * @param {integer} seq 在組別中的順序
 * @param {string} prices_json 選擇的權限,格式: [{"fy_code":"18","purchase_price":"10.00","sales_price":"10.00"},...]
 */
export function createStock({
  sk_code,
  sk_name_cn,
  sk_name_en,
  active_year,
  parent_stock_group_id,
  seq,
  prices_json,
}) {
  return request({
    url: '/stocks/actions/create',
    method: 'post',
    data: {
      sk_code,
      sk_name_cn,
      sk_name_en,
      active_year,
      parent_stock_group_id,
      seq,
      prices_json,
    },
  })
}

/**
 * 修改貨品
 * @param {string} stock_id 貨品id
 * @param {string} sk_code 貨品編號
 * @param {string} sk_name_cn 貨品中文名
 * @param {string} sk_name_en 貨品英文名
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_stock_group_id 父貨品組別id((不傳則為根節點)
 * @param {integer} seq 在組別中的順序
 * @param {string} prices_json 選擇的權限,格式: [{"fy_code":"18","purchase_price":"10.00","sales_price":"10.00"},...]
 */
export function updateStock({
  stock_id,
  sk_code,
  sk_name_cn,
  sk_name_en,
  active_year,
  parent_stock_group_id,
  seq,
  prices_json,
}) {
  return request({
    url: '/stocks/actions/update',
    method: 'post',
    data: {
      stock_id,
      sk_code,
      sk_name_cn,
      sk_name_en,
      active_year,
      parent_stock_group_id,
      seq,
      prices_json,
    },
  })
}

/**
 * 刪除貨品
 * @param {integer} stock_id 貨品id
 */
export function deleteStock(stock_id) {
  return request({
    url: '/stocks/actions/delete',
    method: 'post',
    data: {
      stock_id,
    },
  })
}

/**
 * 獲取貨品列表
 * @param {string} fy_code 選擇的會計週期
 */
export function getStocks(fy_code) {
  return request({
    url: '/stocks',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 查詢貨品樹
 * @param {string} fy_code 選擇的會計週期
 * @param {string} active Y=只返回活躍的,N=返回全部
 */
export function getStocksTree(fy_code, active) {
  return request({
    url: '/stocks/tree',
    method: 'get',
    params: {
      fy_code,
      active,
    },
  })
}

/**
 * 查詢貨品樹節點
 * @param {string} fy_code 選擇的會計週期
 * @param {integer} parent_stock_group_id 上級貨品組別id
 * @param {integer} except_stock_group_id 排除貨品組別id
 * @param {integer} except_stock_id 排除貨品id
 */
export function getStocksTreeNode(
  fy_code,
  parent_stock_group_id,
  except_stock_group_id,
  except_stock_id,
) {
  return request({
    url: '/stocks/tree/node',
    method: 'get',
    params: {
      fy_code,
      parent_stock_group_id,
      except_stock_group_id,
      except_stock_id,
    },
  })
}

/**
 * 獲取某貨品詳情
 * @param {integer} stock_id 商品id
 */
export function getStock(stock_id) {
  return request({
    url: '/stocks/actions/inquire',
    method: 'get',
    params: {
      stock_id,
    },
  })
}

/**
 * 匯出貨品及貨品組別excel數據
 */
export function exportStocks() {
  return request({
    url: '/stocks/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入貨品及貨品組別excel數據
 * @param {string} data_json 匯入的數據
 */
export function importStocks(data_json) {
  return request({
    url: '/stocks/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

/**
 * 匯出貨品價錢excel數據
 */
export function exportStocksPrice() {
  return request({
    url: '/stock-prices/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入貨品價錢excel數據
 * @param {string} data_json 匯入的數據
 */
export function importStocksPrice(data_json) {
  return request({
    url: '/stock-prices/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}
