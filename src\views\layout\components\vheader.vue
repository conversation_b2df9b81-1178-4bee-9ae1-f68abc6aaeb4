<template>
  <div class="EDAC_header">
    <div class="EDAC_header_top">
      <div class="EDAC_header_top_left">
        <div class="EDAC_header_Logo">
          <svg-icon :icon-class="systemLogo" />
        </div>
        <!-- 主菜單 -->
        <div class="mainMenu">
          <ul>
            <template v-for="(item, index) in permission_routers">
              <li
                v-if="!item.hidden"
                :key="index"
                :class="item.name === activateMenu ? 'activated' : ''"
                @click="showChildrenMenu(item)"
              >
                <div class="EDAC_header_title" style="text-align: center; padding: 0 15px 2px">
                  <svg-icon :icon-class="item.meta.icon" />
                </div>
                <div style="color: #fff; text-align: center; font-size: 12px">
                  {{ $t(item.meta.title) }}
                </div>
              </li>
            </template>
          </ul>
        </div>
      </div>
      <div style="clear: both" />
      <!-- 右上功能 -->
      <div class="EDAC_header_top_right">
        <div v-if="hasPDFRouter" class="pdf-status">
          <div>{{ $t('print.pdfLabel') }}:</div>
          <template v-if="printList.length">
            <i class="el-icon-printer" @click="openPrinterListDialog" />
          </template>
          <template v-else>
            <i v-if="pdfStatus === 1" class="el-icon-success" />
            <i v-else-if="pdfStatus === 2" class="el-icon-loading" />
            <i v-else class="el-icon-error" />
          </template>
        </div>
        <div class="pdf-status" style="width: 60px">
          <router-link target="_blank" :to="{ name: 'tutorial' }" class="inlineBlock">
            <i class="el-icon-question" /> {{ $t('navbar.help') }}
          </router-link>
        </div>
        <div v-if="isAC" class="EDAC_selectDate">
          <div class="EDAC_selectDate_introduce">
            {{ $t('navbar.inputDate') }}
          </div>
          <div>
            <el-date-picker
              v-model="date"
              :clearable="false"
              :format="styles.dateFormat"
              :placeholder="$t('date.dateTips')"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              type="date"
              @change="changeDate"
            />
          </div>
        </div>
        <div class="EDAC_dropdown">
          <el-dropdown v-if="showSystemSwitch" trigger="click" @command="handleSystemSwitchCommand">
            <span class="el-dropdown-link">
              {{ currentSystem }}
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="system !== 'BG'" command="switchBG">
                {{ $t('navbar.switchToBudgetSystem') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="system !== 'PC'" command="switchPC">
                {{ $t('navbar.switchToPurchaseSystem') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown trigger="click" @command="handleCommand">
            <span class="el-dropdown-link">
              {{ language === 'en' ? name_en : name_cn }}
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="switchCN">
                {{ $t('navbar.zhHK') }}
              </el-dropdown-item>
              <el-dropdown-item command="switchEN">
                {{ $t('navbar.english') }}
              </el-dropdown-item>
              <el-dropdown-item command="logout">
                {{ $t('login.logOut') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="EDAC_header_right_logo">
          <img
            :src="
              remoteServerInfo.protocol +
                '://' +
                remoteServerInfo.ip +
                ':' +
                remoteServerInfo.port +
                '/' +
                remoteServerInfo.remoteProjectName +
                '/' +
                remoteServerInfo.uri +
                '/' +
                school.sch_logo
            "
          >
        </div>
      </div>
    </div>
    <div class="EDAC_header_bottom">
      <ul v-if="!hackSecMenu">
        <template v-for="(item, index) in secMenu">
          <li
            v-if="!item.hidden"
            :key="index"
            :class="item.name == activateRouter ? 'hover' : ''"
            @click="toRouter(item)"
          >
            {{ $t(item.meta.title) }}
          </li>
        </template>
      </ul>
    </div>
    <printer-list-dialog
      v-if="printerListDialogVisible"
      :dialog-visible.sync="printerListDialogVisible"
    />
  </div>
</template>
<script>
import printerListDialog from './printerListDialog.vue'
import { mapGetters, mapActions } from 'vuex'
import { searchTheDate } from '@/api/master/years'
import { isError } from '@/utils/pdf'
import dateUtil from '@/utils/date'

export default {
  components: {
    printerListDialog,
  },
  data() {
    // const that = this
    return {
      hackSecMenu: false,
      date: dateUtil.format(new Date(), 'yyyy-MM-dd'),
      select: '0',
      activateRouter: '',
      activateMenu: '',
      secMenu: [],
      pdfStatus: 2,
      printerListDialogVisible: false,
    }
  },
  computed: {
    ...mapGetters([
      'permission_routers',
      'language',
      'name_cn',
      'name_en',
      'school',
      'remoteServerInfo',
      'styles',
      'currentDate',
      'system',
      'hasPDFRouter',
      'printList',
    ]),
    pickerOptions() {
      return {
        disabledDate: time => {
          if (this.first_day && this.last_day) {
            return (
              time.getTime() > this.last_day.getTime() || time.getTime() < this.first_day.getTime()
            )
          }
          return true
        },
        shortcuts: [
          {
            text: this.$t('navbar.today'),
            onClick(picker) {
              picker.$emit('pick', new Date())
            },
          },
          {
            text: this.$t('navbar.yesterday'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('navbar.oneWeekAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            },
          },
        ],
      }
    },
    currentSystem() {
      return this.$t('system.' + this.system)
    },
    showSystemSwitch() {
      const systems = this.school.systems
      return (
        systems &&
        systems.includes('BG') &&
        systems.includes('PC') &&
        (this.system === 'BG' || this.system === 'PC')
      )
    },
    systemLogo() {
      return this.system.toLowerCase()
    },
    isAC() {
      return this.system === 'AC'
    },
  },
  watch: {
    $route() {
      this.loadMenu()
    },
  },
  created() {
    this.loadMenu()
    if (this.isAC) {
      this.loadDate()
    }
    if (this.hasPDFRouter) {
      this.$bus.on('PDFLoading', this.pdfLoading)
      this.$bus.on('PDFLoadedSuccessfully', this.pdfLoadedSuccessfully)
      this.$bus.on('PDFFailedToLoad', this.pdfFailedToLoad)
      if (isError) {
        this.pdfStatus = 3
      }
    }
  },
  methods: {
    ...mapActions(['setLanguage', 'setCurrentDate', 'getCurrentDate']),
    loadMenu() {
      if (this.$route.matched && this.$route.matched.length >= 2) {
        const Menu1 = this.$route.matched[0]
        const Menu2 = this.$route.matched[1]
        for (let i = 0; i < this.permission_routers.length; i++) {
          const item = this.permission_routers[i]
          if (item.name === Menu1.name) {
            this.secMenu = item.children
            this.activateMenu = item.name
            break
          }
        }
        this.activateRouter = Menu2.name
      }
    },
    resetSecMenu() {
      this.hackSecMenu = true
      this.$nextTick(() => {
        this.hackSecMenu = false
      })
    },
    getBottomIndex(index, item) {
      // sessionStorage.setItem('index', index)
      // sessionStorage.setItem('url', item.url)
      this.select = index
      this.$router.push({ path: item.url })
    },
    handleCommand(command) {
      switch (command) {
        case 'logout':
          this.$store.dispatch('LogOut').then(() => {
            location.reload() // 刷新頁面
          })
          break
        case 'switchCN':
          {
            const lang = 'zh-hk'
            this.$i18n.locale = lang
            this.setLanguage(lang)
          }
          break
        case 'switchEN':
          {
            const lang = 'en'
            this.$i18n.locale = lang
            this.setLanguage(lang)
          }
          break
        default:
          break
      }
    },
    handleSystemSwitchCommand(command) {
      switch (command) {
        case 'switchBG':
          this.$store.dispatch('switchSystem', 'BG')
          break
        case 'switchPC':
          this.$store.dispatch('switchSystem', 'PC')
          break
      }
    },
    logOut() {},
    showChildrenMenu(item) {
      this.secMenu = item.children
      this.activateMenu = item.name
      this.resetSecMenu()
    },
    toRouter(item) {
      this.activateRouter = item.name
      this.$router.push({
        name: item.name,
      })
    },
    changeDate(date) {
      // const options = { year: 'numeric', month: '2-digit', day: '2-digit' }
      // const date = this.date.toLocaleDateString('zh', options)
      this.setCurrentDate(date).then(this.getCurrentDate)
    },
    loadDate() {
      searchTheDate({}).then(res => {
        this.first_day = new Date(res.the_first_day + ' 00:00')
        this.last_day = new Date(res.the_last_day + ' 00:00')
      })
      this.getCurrentDate()
        .then(date => {
          if (date) {
            this.date = new Date(date)
          }
        })
        .catch(() => {})
    },
    pdfLoading() {
      this.pdfStatus = 2
    },
    pdfLoadedSuccessfully() {
      this.pdfStatus = 1
    },
    pdfFailedToLoad() {
      this.pdfStatus = 3
    },
    openPrinterListDialog() {
      this.printerListDialogVisible = true
    },
  },
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
$hover_color: #2e9eff;

.EDAC_header {
  height: 100%;
  border-bottom: 1px solid #eaeaea;
  /* 一級導航 */
  .EDAC_header_top {
    background: rgb(62, 151, 221);
    height: 50px;
    color: #fff;
    display: flex;
    position: relative;
    overflow: hidden;
    /* 導航欄 - 左 */
    .EDAC_header_top_left {
      display: flex;
      /* 系統LOGO */
      .EDAC_header_Logo {
        pointer-events: none;
        .svg-icon {
          font-size: 17px !important;
          margin: 8px 10px 8px 20px;
          /*-webkit-box-shadow: 0 0px 9px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);*/
          /*box-shadow: 0 0px 9px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);*/
          box-shadow: 0 5px 5px -4px #0000004d, 0px 0px 6px 0 rgba(0, 0, 0, 0.04);
        }
      }
      /* 一級菜單 */
      .mainMenu {
        margin: 8px 0 0;

        ul {
          list-style: none;
          margin: 0;
          padding: 0px 10px 0;
          height: 100%;

          li {
            float: left;
            margin: 0 8px 0 0;
            cursor: pointer;
            height: 100%;
          }
          .activated:after {
            content: '';
            display: block;
            width: 0px;
            height: 0px;
            border: 14px solid #ec080800;
            border-bottom: 10px solid #fff;
            top: -7px;
            position: relative;
            left: 9px;
          }
        }
      }
    }

    /* 導航欄 - 右 */
    .EDAC_header_top_right {
      display: flex;
      color: #fff;
      position: absolute;
      right: 20px;
      top: 0px;
      > * {
        /*margin-top: 8px;*/
        line-height: 30px;
      }
      /* 右側時間 */
      .EDAC_selectDate {
        display: flex;
        margin-right: 20px;
        margin-top: 8px;
        /deep/ {
          .el-date-editor {
            .el-input__icon {
              line-height: 30px;
            }
          }
        }
      }

      /* 用戶下拉菜單 */
      .EDAC_dropdown {
        margin-top: 8px;
        margin-right: 20px;
        /deep/ {
          .el-dropdown-link {
            padding: 6px 0;
          }
        }
      }

      /* 右圖標 */
      .EDAC_header_right_logo img {
        width: 40px;
        height: 40px;
        margin-top: 4px;
      }

      .pdf-status {
        vertical-align: middle;
        height: 100%;
        display: inline-flex;
        width: 70px;
        line-height: 30px;
        margin-top: 8px;
        div {
          padding-right: 10px;
        }
        i {
          line-height: inherit;
        }
      }
    }
  }

  /* 二級導航 */
  .EDAC_header_bottom {
    height: 30px;
    line-height: 30px;
    overflow: hidden;

    ul {
      list-style: none;
      margin: 0;
      height: 100%;

      li {
        float: left;
        margin: 0 12px;
        cursor: pointer;
        height: 100%;
        color: rgb(162, 162, 162);
        font-size: 14px;

        &:hover,
        &.hover {
          color: $hover_color;
          border-bottom: 2px solid $hover_color;
        }
      }
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.EDAC_header {
  /* 一級導航 */
  .EDAC_header_top {
    /* 導航欄 - 左 */
    .EDAC_header_top_left {
      /* 系統LOGO */
      .EDAC_header_Logo {
        .svg-icon {
          width: 2em !important;
          height: 2em !important;
        }
      }
      /* 一級菜單 */
      .mainMenu {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        ul {
          li {
            .EDAC_header_title {
              .svg-icon {
                color: #fff;
                width: 18px !important;
                height: 18px !important;
              }
            }
            &:hover {
              opacity: 0.8;
            }
          }
          .activated:after {
          }
        }
      }
    }

    /* 導航欄 - 右 */
    .EDAC_header_top_right {
      /* 右側時間 */
      .EDAC_selectDate {
        .el-date-editor {
          .el-input__icon {
          }
          &.el-input {
            width: 120px;
          }
        }

        .EDAC_selectDate_introduce {
        }

        .el-input--suffix {
          .el-input__inner {
            padding-right: 0px;
          }
        }
      }

      /* 用戶下拉菜單 */
      .EDAC_dropdown {
        .el-dropdown {
          cursor: pointer;
          color: #fff;
          text-align: center;
          margin: 0 5px;
        }
      }

      /* 右圖標 */
      .EDAC_header_right_logo img {
      }
    }
  }

  /* 二級導航 */
  .EDAC_header_bottom {
    ul {
      li {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        &:hover,
        &.hover {
        }
      }
    }
  }
}

.el-dropdown-menu {
  li.el-dropdown-menu__item {
    text-align: center;
  }
}
.el-icon-printer {
  cursor: pointer;
}
</style>
<style lang="scss">
.EDAC.font-size-18,
.EDAC.font-size-17,
.EDAC.font-size-16,
.EDAC.font-size-15 {
  .EDAC_header_top_right {
    .el-date-editor--date {
      width: 130px !important;
    }
  }
}
</style>
