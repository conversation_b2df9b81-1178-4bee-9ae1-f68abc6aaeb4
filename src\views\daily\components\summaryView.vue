<template>
  <!--  傳票信息  -->
  <div :class="{ 'show-select': showSelect }" class="summary-view">
    <el-form
      ref="info-form"
      :inline="true"
      :model="info"
      :validate-on-rule-change="true"
      class="info-form"
    >
      <!--第一行-->
      <el-row>
        <!--類型-->
        <el-form-item :rules="rules" :label="$t('daily.label.type')" label-width="80px">
          <el-input
            :value="language === 'en' ? data.vt_description_en : data.vt_description_cn"
            class="link"
            readonly
            style="width: 300px"
            @click.native="onVoucherTypeClick"
          />
        </el-form-item>

        <!-- 借方總額 -->
        <el-form-item
          v-if="!amt_type"
          :label="$t('daily.label.drAmtSum')"
          label-width="80px"
          style="margin-left: 10px; margin-right: 0; float: right"
        >
          <ENumeric
            :value="dr_sum"
            :read-only="true"
            class="aaa"
            style="
              -webkit-appearance: none;
              background-color: #f4f4f4;
              background-image: none;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              color: #606266;
              display: inline-block;
              font-size: inherit;
              outline: 0;
              padding: 0 2px;
              -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              width: 120px;
              text-align: right;
            "
          />
        </el-form-item>

        <!-- 工具 -->
        <el-form-item
          v-if="amt_type && 'PCR'.includes(info.vt_category)"
          prop="vc_receipt"
          style="float: right"
        >
          <el-date-picker
            v-if="vc_rdate"
            v-model="vc_rdate"
            readonly
            :format="styles.dateFormat"
            value-format="yyyy-MM-dd"
            type="date"
            class="date-view-input"
            style="display: inline-block; width: 110px; margin-right: 10px"
          />
          <i
            :class="{ 'no-selected': data.vc_receipt === 'X' }"
            class="icon-receipt"
            style="cursor: unset"
          >
            <svg-icon icon-class="paid" />
          </i>
          <el-input
            v-if="data.vt_category === 'R' && data.vc_receipt !== 'X'"
            v-model="data.vc_receipt"
            :disabled="true"
            readonly
            style="display: inline-block; width: 80px"
          />
          <el-checkbox
            v-else-if="data.vt_category !== 'R' || data.vc_receipt !== 'X'"
            v-model="hasReceipt"
            :disabled="data.vc_receipt === 'X'"
            @change="onChangeReceipt"
          />
        </el-form-item>
      </el-row>
      <!--第二行-->
      <el-row>
        <!--傳票日期-->
        <el-form-item
          :rules="rules"
          :label="$t('daily.label.vc_date')"
          prop="vc_date"
          label-width="80px"
        >
          <el-date-picker
            v-model="data.vc_date"
            :clearable="false"
            :placeholder="$t('placeholder.selectDate')"
            :format="styles.dateFormat"
            value-format="yyyy-MM-dd"
            readonly
            type="date"
          />
        </el-form-item>

        <!--收款人-->
        <el-form-item
          v-if="amt_type"
          :label="payeeLabel"
          :class="'vc_payee' + data && data._payeeIsNew ? ' is-new-value' : ''"
          prop="vc_payee"
          label-width="80px"
          style="margin-right: 0"
        >
          <el-input :value="data.vc_payee" readonly style="width: 300px" />
          <i
            v-if="data && data._payeeIsNew"
            class="edac-icon action-icon edac-icon-add"
            @click="onSelectPayee"
          />
          <el-form-item v-if="amt_type" :rules="rules" prop="vc_extra" style="float: right">
            <i
              class="edac-icon action-icon edac-icon-buyer"
              style="cursor: unset; margin-left: 8px"
            />
            <el-checkbox v-model="data.vc_extra" :disabled="true" true-label="Y" false-label="N" />
          </el-form-item>
        </el-form-item>
        <!--職員-->
        <!--        <el-form-item-->
        <!--          v-if="!amt_type"-->
        <!--          :label="$t('daily.label.staff')"-->
        <!--          label-width="80px"-->
        <!--        >-->
        <!--          <el-input :value="language === 'en' ? data.st_name_en : data.st_name_cn" readonly style="width: 220px"/>-->
        <!--        </el-form-item>-->
        <!-- 貸方總額 -->
        <el-form-item
          v-if="!amt_type"
          :label="$t('daily.label.crAmtSum')"
          label-width="80px"
          style="margin-left: 10px; margin-right: 0; float: right"
        >
          <ENumeric
            :value="cr_sum"
            :read-only="true"
            class="aaa"
            style="
              -webkit-appearance: none;
              background-color: #f4f4f4;
              background-image: none;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              color: #606266;
              display: inline-block;
              font-size: inherit;
              outline: 0;
              padding: 0 2px;
              -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              width: 120px;
              text-align: right;
            "
          />
        </el-form-item>
        <el-select
          v-if="statusShow && !amt_type && info.vt_category == 'J'"
          v-model="statusTitle"
          :placeholder="statusTitle"
          class="downShow"
          disabled
        />
      </el-row>
      <!--第三行-->
      <el-row>
        <!--職員-->
        <el-form-item :label="$t('daily.label.staff')" label-width="80px">
          <el-input
            :value="language === 'en' ? data.st_name_en : data.st_name_cn"
            :class="{
              link: data.vc_st_code ? true : false,
            }"
            readonly
            style="width: 220px"
            @click.native="onClickStaff"
          />
        </el-form-item>
        <!--    內容描述    -->
        <el-form-item
          :label="$t('daily.label.desc')"
          :class="'vc_summary' + data && data._descIsNew ? ' is-new-value' : ''"
          label-width="80px"
          style="margin-right: 0"
        >
          <el-input :value="data.vc_summary" readonly style="width: 300px" />
        </el-form-item>
      </el-row>
      <!--第四行-->
      <el-row>
        <el-form-item
          v-if="amt_type"
          :rules="rules"
          :label="$t('daily.label.amt_type')"
          prop="vc_method"
          label-width="80px"
        >
          <el-radio-group :value="data.vc_method" :disabled="true">
            <el-radio label="TRANSF">
              {{ $t('voucher_method.transf') }}
            </el-radio>
            <el-radio label="CASH">
              {{ $t('voucher_method.cash') }}
            </el-radio>
            <el-radio label="CHEQUE" @click.native="onSelectedCheque">
              {{ $t('voucher_method.cheque') }}
            </el-radio>
            <el-radio label="AUTO">
              {{ $t('voucher_method.auto') }}
            </el-radio>
            <el-radio label="OTHER">
              {{ $t('voucher_method.other') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="amt_type && data.vc_method === 'CHEQUE'"
          :label="$t('daily.label.number')"
          label-width="40px"
          style="margin-right: 0"
        >
          <el-input v-model="data.ref" readonly style="width: 80px" />
        </el-form-item>
        <el-form-item
          v-else-if="amt_type && data.vc_method === 'OTHER'"
          label=""
          label-width="40px"
          style="margin-right: 0"
        >
          <el-input v-model="data.ref" readonly style="width: 120px" />
        </el-form-item>
        <el-form-item
          v-if="amt_type && data.vc_method === 'CHEQUE'"
          :label="$t('daily.label.date')"
          label-width="40px"
          prop="vc_chq_date"
          style="margin-right: 0"
        >
          <el-date-picker
            v-model="data.vc_chq_date"
            :format="styles.dateFormat"
            :clearable="false"
            placeholder=" "
            value-format="yyyy-MM-dd"
            type="date"
            readonly
            style="width: 110px"
          />
        </el-form-item>
        <!-- 總額 -->
        <el-form-item
          v-if="amt_type"
          :label="$t('daily.label.amtSum')"
          label-width="40px"
          style="margin-right: 0; float: right"
        >
          <ENumeric
            :value="total"
            :read-only="true"
            class="aaa"
            style="
              -webkit-appearance: none;
              background-color: #f4f4f4;
              background-image: none;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              -webkit-box-sizing: border-box;
              box-sizing: border-box;
              color: #606266;
              display: inline-block;
              font-size: inherit;
              outline: 0;
              padding: 0 2px;
              -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              text-align: right;
              width: 120px;
            "
          />
        </el-form-item>
      </el-row>
    </el-form>
    <!--選擇收款人 彈窗-->
    <dialogPayee
      v-if="fy_code"
      :dialog-visible.sync="dialogPayeeVisible"
      :fy_code="fy_code"
      :add-name="data.vc_payee"
      @close="handlePayeeClose"
    />
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import ENumeric from '@/components/ENumeric'

import dialogPayee from '@/views/daily/components/DialogPayeeShortcut'

// API

import { deepCloneByJSON, toDecimal } from '@/utils'
import { changeReceiptStatus } from '@/api/daily/payment'

export default {
  name: 'SummaryView',
  components: {
    VBreadCrumb,
    ENumeric,
    dialogPayee,
  },
  props: {
    isView: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    action: {
      type: String,
      default: 'add',
    },
    fy_code: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      info: deepCloneByJSON(this.data),
      voucherTypeList: [],
      hasReceipt: false,

      dialogPayeeVisible: false,
      dialogDescVisible: false,
      dialogStaffVisible: false,

      dr_sum: 0,
      cr_sum: 0,

      statusTitle: '',
      statusShow: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    //
    amt_type() {
      return 'PCR'.includes(this.info.vt_category)
    },
    showSelect() {
      console.log(this.$route.params.ac_bank, 'this.$route.params.ac_bank')

      return this.$route.params.ac_bank !== 'X'
    },
    payeeLabel() {
      switch (this.info.vt_category) {
        case 'P':
        case 'C':
          return this.$t('daily.label.payee_receipt')
        case 'R':
          return this.$t('daily.label.payee_payment')
        default:
          return this.$t('daily.label.payee')
      }
    },
    vc_rdate() {
      if (this.data && this.data.vc_rdate) {
        return this.data.vc_rdate
      }
      return ''
    },
    total() {
      let sum = 0
      for (let i = 0; i < this.tableData.length; i++) {
        const item = this.tableData[i]
        const vt_category = (this.info && this.info.vt_category) || ''
        let v = 0
        if (vt_category === 'P' || vt_category === 'C') {
          v = toDecimal(item.amount_dr)
        } else if (vt_category === 'R') {
          v = toDecimal(item.amount_cr)
        } else {
          if (item.select_amount_type === 'Dr') {
            v = toDecimal(item.amount_dr)
          } else {
            v = toDecimal(item.amount_cr)
          }
        }
        sum += v
      }
      return sum
    },
  },
  watch: {
    info: {
      deep: true,
      immediate: false,
      handler(newValue, oldVale) {
        if (newValue._update) {
          this.$emit('update:data', newValue)
        } else {
          newValue._update = true
        }
      },
    },
    tableData: {
      deep: true,
      immediate: true,
      handler(value) {
        this.tableSum()
      },
    },
    data(val) {
      this.info = deepCloneByJSON(val)
      this.info._update = false
      this.hasReceipt = val.vc_receipt === 'Y'
      this.getCycleDate(val.vc_date).then(res => {
        if (val.vc_date === res.the_last_day) {
          this.statusShow = true
          if (val.vc_status === 1) {
            this.statusTitle = this.$t('daily.label.normal')
          } else if (val.vc_status === 2) {
            this.statusTitle = this.$t('daily.label.correctOne')
          } else if (val.vc_status === 3) {
            this.statusTitle = this.$t('daily.label.correctTwo')
          } else if (val.vc_status === 4) {
            this.statusTitle = this.$t('daily.label.correctThree')
          }
        } else {
          this.statusShow = false
        }
      })
    },
  },
  created() {
    this.initData()
  },
  mounted() {},
  methods: {
    ...mapActions(['getCycleDate']),
    tableSum() {
      let dr = 0
      let cr = 0
      this.tableData.forEach(item => {
        dr = toDecimal(dr + parseFloat(item.amount_dr))
        cr = toDecimal(cr + parseFloat(item.amount_cr))
      })
      this.dr_sum = dr
      this.cr_sum = cr
      return dr === 0 ? cr : dr
    },
    initData() {},

    // dom
    handleTriggerReceipt() {
      if (this.isView) return
      if (this.data.vc_receipt === 'X') {
        this.data.vc_receipt = this.hasReceipt ? 'Y' : 'N'
      } else {
        this.data.vc_receipt = 'X'
      }
    },
    handleTriggerPayee() {
      if (this.isView) return
    },
    ChangeReceipt(val) {
      if (this.isView) return
      if (this.data.vc_receipt !== 'X') {
        this.data.vc_receipt = val ? 'Y' : 'N'
      }
    },
    handlePayeeSelect(data) {
      if (this.isView) return
      if (data && data.comp_name) {
        this.data.vc_payee = data.comp_name
      }
    },
    //
    onSelectPayee() {
      // if (this.isView) return
      this.dialogPayeeVisible = true
    },
    //
    onSelectStaff() {
      if (this.isView) return
      this.dialogStaffVisible = true
    },
    // 描述
    onSelectDesc() {
      if (this.isView) return
      this.dialogDescVisible = true
    },
    handleDescSelect(data) {
      if (this.isView) return
      if (data) {
        this.$set(this.info, 'vc_summary', data.desc)
      }
    },
    handleStaffSelect(data) {
      if (this.isView) return
      if (data) {
        this.$set(this.info, 'vc_st_code', data.st_code)
        this.$set(
          this.info,
          'vc_st_name',
          this.language === 'en' ? data.st_name_en : data.st_name_cn,
        )
      }
    },
    loadTableDesc() {
      if (this.isView) return
      const arr = []
      this.tableData.forEach(item => {
        if (arr.indexOf(item.descr) === -1) {
          arr.push(item.descr)
        }
      })
      this.$set(this.info, 'vc_summary', arr.filter(i => i).join(' / '))
    },
    setChildrenDesc() {
      if (this.isView) return
      const newData = this.tableData.map(item => (item.descr = this.data.vc_summary) && item)

      this.$emit('update:tableData', newData)
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs['info-form'].validate((valid, data) => {
          if (valid) {
            resolve(data)
          } else {
            reject(data)
          }
        })
      })
    },
    onVoucherTypeClick() {
      const fund_id = this.info.fund_id
      const ac_code = this.info.vt_ac_code
      if (ac_code && fund_id) {
        this.$bus.emit('enquiryGrantFetch', fund_id, ac_code)
      }
    },
    onClickStaff() {
      const st_code = this.info.vc_st_code
      if (st_code) {
        this.$bus.emit('enquiryStaffFetch', st_code)
      }
    },
    async onChangeReceipt(val) {
      try {
        const fy_code = this.fy_code
        const vc_id = this.info.vc_id
        const vc_receipt = val ? 'Y' : 'N'
        if (!fy_code || !vc_id) {
          console.error('無法獲取傳票信息')
          this.$message.error(this.$t('daily.voucher.message.dataLoadingFailed'))
          return
        }
        const res = await changeReceiptStatus({
          fy_code,
          vc_id,
          vc_receipt,
        })
        console.log(res)
        this.$message.success(this.$t('message.success'))
      } catch (e) {
        console.error(e)
      }
    },
    handlePayeeClose(update) {
      this.$emit('checkPayee')
    },
  },
}
</script>

<style lang="scss" scoped>
i {
  font-size: 20px;
  vertical-align: middle;
  line-height: 25px;
  &.icon-receipt {
    fill: #68afff;
    /* width: 30px; */
    height: 25px;
    line-height: 20px;
    display: inline-block;
    vertical-align: middle;
    cursor: unset;
    .svg-icon {
      width: 25px;
      height: 25px;
    }

    &.no-selected {
      .svg-icon {
        fill: #b9b7b8;
      }
    }
  }
}
.summary-view {
  background: #f4f4f4;
  margin: 5px 0;
  min-width: 555px;
  /deep/ {
    .el-form-item--medium .el-form-item__content,
    .el-form-item--medium .el-form-item__label,
    .el-input--medium .el-input__inner,
    .el-input--medium .el-input__icon {
      line-height: 25px !important;
      height: 25px !important;
    }
    .el-input {
      line-height: 25px;
      height: 25px;
      /*max-width: 150px;*/
    }
    /*[class^='el-input'],*/
    [class^='el-form'],
    .el-input__icon {
      vertical-align: middle;
    }
    .el-form-item__label {
      vertical-align: sub;
      padding: 0 5px 0 0;
    }
    .el-button--mini {
      padding: 5px 10px;
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
    .el-radio + .el-radio {
      margin-left: 5px;
    }
    .el-input--suffix .el-input__inner {
      padding-right: 3px;
    }
    .el-radio__label {
      padding-left: 5px;
    }
    .el-form--inline .el-form-item__content {
      vertical-align: middle;
    }

    .link input {
      cursor: pointer;
    }
    .none input {
      cursor: not-allowed;
    }

    .is-new-value {
      input {
        color: #169a55;
      }
    }
  }
  .info-form {
    padding: 5px;
    /deep/ {
      .el-input:not(.el-date-editor) {
        > input {
          padding: 0 5px;
        }
      }
    }
  }

  .edac-icon.action {
    cursor: pointer;
  }
  &.is-view {
    .edac-icon.action {
      cursor: not-allowed;
    }
  }
  .edac-icon {
    color: #68afff;
    &.no-selected {
      color: #b9b7b8;
    }
  }
  .sum-input {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  .edac-icon-add {
    width: 18px;
    height: 18px;
    background: #409eff;
    color: #ffffff;
    text-align: center;
    border-radius: 50%;
    line-height: 18px;
    vertical-align: middle;
    :hover {
      opacity: 1;
    }
  }
}

.downShow {
  margin-left: 75px;
  width: 100px;
}

.date-view-input {
  display: inline-block;
  width: 88px;
  margin-right: 10px;
  /deep/ {
    input {
      text-align: center;
    }
  }
}
.show-select {
  background-color: #fcbdc1;
}
</style>
