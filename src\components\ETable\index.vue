<template>
  <el-table
    ref="singleTable"
    :data="data"
    :height="height"
    v-bind="$attrs"
    class="e-table"
    v-on="$listeners"
    @header-dragend="onHeaderDragend"
  >
    <span v-html="styleSheets" />

    <el-table-column
      v-if="indexColumn && showIndex"
      :align="indexColumn.alignment"
      :width="indexColumn.width"
      :index="index"
      column-key="_index"
      type="index"
      label="#"
    />
    <el-table-column v-if="showCheckbox" :selectable="selectable" width="40" type="selection" />
    <slot name="prefixColumns" />
    <slot name="columns">
      <!--       && i.ss_key !== 'type'-->
      <el-table-column
        v-for="(item, index) in styleColumns.filter(i => i.ss_key !== '_index')"
        :key="item.ss_key"
        :label="$t(langKey + item.ss_key)"
        :align="item.alignment"
        :width="lastColumnAutoWidth && index === columnList.length - 1 ? '' : item.width"
        :min-width="lastColumnAutoWidth && index === columnList.length - 1 ? 100 : ''"
        :property="column_property(item)"
        :column-key="item.ss_key"
        :formatter="formatters[item.ss_key] || formatter"
        :sortable="sortable"
      />
      <!--      <el-table-column-->
      <!--        v-for="item in styleColumns.filter(i => i.ss_key == 'type')"-->
      <!--        :key="item.ss_key"-->
      <!--        :label="$t(langKey + item.ss_key)"-->
      <!--        :align="item.alignment"-->
      <!--        :width="item.width"-->
      <!--        :property="column_property(item)"-->
      <!--        :column-key="item.ss_key"-->
      <!--        :formatter="typeFormat"-->
      <!--        :sortable="sortable"-->
      <!--      />-->
    </slot>
    <el-table-column
      v-if="showActions && styleColumns && styleColumns.length > 0"
      :label="actionLabel ? actionLabel : defaultActionLabel"
      :min-width="actionsMinWidth"
      fixed="right"
      align="left"
      header-align="left"
    >
      <template slot-scope="scope">
        <slot :scope="scope" name="actions" />
      </template>
    </el-table-column>
    <el-table-column
      v-if="styleColumns.length && fullColumn"
      :min-width="fullColumnWidth"
      align="right"
      header-align="right"
    />
    <template slot="append">
      <slot name="append" />
    </template>
  </el-table>
</template>

<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
import dateUtil from '@/utils/date'

export default {
  name: 'ETable',
  props: {
    /* eslint-disable */
    data: {
      type: [Array, Object],
      required: true,
    },
    columns: {
      type: Array,
      default: () => [],
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    fullColumn: {
      type: Boolean,
      default: false,
    },
    fullColumnWidth: {
      type: Number,
      default: 1,
    },
    actionLabel: {
      type: String,
      default: '',
    },
    showIndex: {
      type: Boolean,
      default: true,
    },
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    actionsMinWidth: {
      type: Number,
      default: 100,
    },
    styleColumns: {
      type: Array,
      default: () => [],
    },
    amountColumns: {
      type: Array,
      default: () => [],
    },
    langKey: {
      type: String,
      default: '',
    },
    defaultTop: {
      type: Number,
      default: 140,
    },
    autoHeight: {
      type: Boolean,
      default: true,
    },
    lastColumnAutoWidth: {
      type: Boolean,
      default: false,
    },
    height: {
      type: [Number, String],
      default: ' ',
    },
    sortable: {
      type: Boolean,
      default: false,
    },
    filterClassFunction: {
      type: Function,
      default: function (row) {
        /**
         * Table斑馬紋
         */
        if (row.rowIndex % 2 === 0) {
          return 'table-stripe'
        }
      },
    },
    formatters: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Function,
      default: i => i + 1,
    },
    selectable: {
      type: Function,
      default: () => true,
    },
  },
  data() {
    return {
      // tableHeight: 400
      styleSheets: '',
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    indexColumn() {
      return this.styleColumns.find(item => item.ss_key === '_index')
    },
    columnList() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    defaultActionLabel() {
      return this.$t('table.action')
    },
  },
  watch: {
    // 'data'() {
    //   this.$nextTick(() => {
    //     this.updateHeight()
    //   })
    // }
  },
  updated() {
    this.$nextTick(() => {
      this.updateHeight()
    })
  },
  activated() {},
  mounted() {
    this.$nextTick(() => {
      this.updateHeight()
    })
  },
  methods: {
    getParentTop(el) {
      let top = el.offsetTop

      if (el.parentNode) {
        const pt = this.getParentTop(el.parentNode)
        if (pt) top = top + pt
      }
      console.log(top, el)
      return top
    },
    updateHeight() {
      if (!this.autoHeight) return
      // const t = this.getParentTop(this.$refs['singleTable'].$el)
      // debugger
      const top = this.$refs['singleTable'].$el.parentNode.parentNode.parentNode.offsetTop
      const headerHeight = this.$refs['singleTable'].$el.children[1].offsetHeight
      const defaultTop = this.defaultTop
      // console.log(t)
      this.styleSheets = `
<style>
  .e-table {
    height: calc(100vh - ${defaultTop}px - ${top}px);
  }
  .e-table .el-table__body-wrapper {
    height: calc(100vh - ${defaultTop}px - 2px - ${top}px - ${headerHeight}px);
  }
</style>

      `
    },
    /**
     * 修改列寬提交到父組件
     * @param newWidth
     * @param oldWidth
     * @param column
     * @param event
     */
    onHeaderDragend(newWidth, oldWidth, column, event) {
      const key = column.columnKey
      this.$emit('changeWidth', key, newWidth)

      this.$nextTick(() => {
        this.updateHeight()
      })
    },
    column_property(item) {
      if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key + (this.language === 'en' ? 'en' : 'cn')
      } else {
        return item.ss_key
      }
    },
    column_label(item) {
      if (item.ss_key === '_index') {
        return '#'
      }
      // else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
      //   return item.ss_key.substring(0, item.ss_key.length-1)
      // }
      else {
        return item.ss_key
      }
    },
    formatter(row, column, cellValue, index) {
      if (cellValue == null || cellValue === '') return ''
      if (this.amountColumns.includes(column.property)) {
        // 金額
        return amountFormat(cellValue)
      }
      if (column.property.includes('date')) {
        // 日期
        return dateUtil.format(new Date(cellValue), this.styles.dateFormat)
      }
      return cellValue
    },
    setCurrentRow(row) {
      if (this.$refs.singleTable) {
        this.$refs.singleTable.setCurrentRow(row)
      } else {
        this.$nextTick(() => {
          this.$refs.singleTable.setCurrentRow(row)
        })
      }
    },
    customFormatter(key, cellValue, customDateFormat) {
      if (cellValue == null || cellValue === '') return ''
      if (this.amountColumns.includes(key)) {
        // 金額
        return amountFormat(cellValue)
      }
      const date = new Date(cellValue)
      if (key.substr(key.length - 4) === 'date' && !isNaN(date.getTime())) {
        // 日期
        return dateUtil.format(
          date,
          customDateFormat !== undefined ? customDateFormat : this.styles.dateFormat
        )
      }
      return cellValue
    },
  },
}
</script>
<style rel="stylesheet/css"></style>

<style lang="scss" rel="stylesheet/scss" scoped>
.e-table {
  /*height: calc(100vh - 210px);*/

  /deep/ {
    .el-table__body-wrapper {
      /*height: calc(100vh - 240px);*/
      overflow-y: auto;
    }
    .el-table-column--selection .cell {
      text-overflow: unset !important;
    }
    /*.el-table--border{*/
    /*  border: 1px solid #ebeef5;*/
    /*}*/
  }
}

.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
