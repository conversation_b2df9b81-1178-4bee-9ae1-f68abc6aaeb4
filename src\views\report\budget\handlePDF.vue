<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'

export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  data() {
    return {
      ps_code: 'pdfbudgetledger',
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo', 'school', 'currentDate']),
  },
  methods: {
    handlePrint({ data, date_start, date_end, BIE, budget }) {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(() =>
            this.formatPrintData({ printSetting, data, date_start, date_end, BIE, budget }),
          )
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              budget,
              printSetting,
            }).then(() => resolve())
          })
          .catch(err => {
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData({ printSetting, data, date_start, date_end, BIE, budget }) {
      return new Promise(async(resolve, reject) => {
        const $t = this.$t.bind(this)
        const language = printSetting.language
        const langKey = 'setting.printout.report.budget.label.'

        const columnData = printSetting.columnData
          .filter(a => a.position)
          .sort((a, b) => a.position - b.position)

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // ---------------- 學校數據 ----------------
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        // ---------------- 右邊表格 ----------------
        // const title = $t('setting.printout.periodic.label.' + titleKey, language)
        const title = $t('setting.printout.report.budget.label.title', language)
        const dateFormat = 'dd/MM/yyyy'
        const dateStartStr = dateUtil.format(new Date(date_start), dateFormat)
        const dateEndStr = dateUtil.format(new Date(date_end), dateFormat)
        let category
        switch (BIE) {
          case 'IE':
          case 'I':
          case 'E':
            category = this.$t('setting.printout.report.budget.label.' + BIE, language)
            break
          default:
            category = this.$t('setting.printout.report.budget.label.all', language)
            break
        }

        const pageInfo = {
          title: title,
          filename: `${title}`,
          data: [
            {
              label: $t('setting.printout.periodic.label.period', language) + ':',
              value: `${dateStartStr} - ${dateEndStr}`,
            },
            {
              label: $t('setting.printout.periodic.label.category', language) + ':',
              value: category,
            },
          ],
        }

        // ---------------- 表頭 ----------------
        const columns = [[]]
        let drIndex = -1
        let crIndex = -1
        let balanceIndex = -1
        columnData.forEach((item, index) => {
          if (item.position === 0) {
            return
          }
          const key = item.name
          columns[0].push({
            text: $t(langKey + key, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: item.alignment,
            width: item.width,
            border: [true, true, true, true],
          })
          if (item.name === 'amount_dr') {
            drIndex = index
          }
          if (item.name === 'amount_cr') {
            crIndex = index
          }
          if (item.name === 'amount_balance') {
            balanceIndex = index
          }
        })
        // 主數據

        const tableData = []
        let drSum = 0
        let crSum = 0
        let balanceSum = 0
        const borderAll = [true, true, true, true]

        function newRow() {
          return [...Array(columnData.length)].map(() => ({ text: '', border: borderAll }))
        }
        data.forEach((item, rowIndex) => {
          const row = []
          columnData.forEach((col, colIndex) => {
            const currentCellValue = item[col.name]
            const cell = {
              text: '',
              name: col.name,
              alignment: col.alignment,
              style: 'tableContent',
              border: borderAll,
            }
            switch (col.name) {
              case 'index':
                cell.text = rowIndex + 1
                break
              case 'vc_date':
                cell.text = dateUtil.format(new Date(item.vc_date), dateFormat)
                break
              case 'ac_name':
                cell.text = language === 'en' ? item.ac_name_cn : item.ac_name_cn
                break
              case 'amount_dr':
                drSum += Number(item.amount_dr)
                cell.text = amountFormat(item.amount_dr)
                break
              case 'amount_cr':
                crSum += Number(item.amount_cr)
                cell.text = amountFormat(item.amount_cr)
                break
              case 'amount_balance':
                // balanceSum += Number(item.amount_balance)
                balanceSum = Number(item.amount_balance)
                cell.text = amountFormat(item.amount_balance)
                break
              default:
                cell.text = currentCellValue
                break
            }
            row.push(cell)
          })
          tableData.push(row)
        })
        const r6 = newRow()
        let totalLabel = 999
        if (crIndex >= 0) {
          r6[crIndex].text = amountFormat(crSum)
          r6[crIndex].border = borderAll
          r6[crIndex].bold = true
          r6[crIndex].alignment = columnData[crIndex].alignment
          r6[crIndex].style = 'tableFooter'
          if (totalLabel > crIndex) totalLabel = crIndex
        }
        if (drIndex >= 0) {
          r6[drIndex].text = amountFormat(drSum)
          r6[drIndex].border = borderAll
          r6[drIndex].bold = true
          r6[drIndex].alignment = columnData[crIndex].alignment
          r6[drIndex].style = 'tableFooter'
          if (totalLabel > drIndex) totalLabel = drIndex
        }
        if (balanceIndex >= 0) {
          r6[balanceIndex].text = amountFormat(balanceSum)
          r6[balanceIndex].border = borderAll
          r6[balanceIndex].bold = true
          r6[balanceIndex].alignment = columnData[balanceIndex].alignment
          r6[balanceIndex].style = 'tableFooter'
          if (totalLabel > balanceIndex) totalLabel = balanceIndex
        }
        if (totalLabel > 0) {
          r6[0].text = $t('setting.printout.report.budget.label.total', language)
          r6[0].border = borderAll
          r6[0].colSpan = totalLabel
          r6[0].bold = true
          r6[0].alignment = 'right'
          r6[0].style = 'tableFooter'
        }
        tableData.push(r6)

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    generateBudgetInfo(budgetGroup, language, tableColumnsLength) {
      const langKey = 'setting.printout.report.budget.label.'
      return [
        {
          // Header
          margin: [0, 0, 0, 0],
          colSpan: tableColumnsLength,
          border: [false, false, false, false],
          columns: [
            {
              style: 'tableExample',
              width: '*',
              table: {
                widths: [200, '*'],
                heights: [10, 10],
                headerRows: 1,
                body: [
                  [
                    {
                      text:
                        this.$t(langKey + 'budget_code', language) + ': ' + budgetGroup.budget_code,
                      alignment: 'left',
                      style: 'tableContent',
                      bold: false,
                      border: [false, false, false, false],
                    },
                    {
                      text:
                        this.$t(langKey + 'budget_name_', language) +
                        ': ' +
                        (language === 'en' ? budgetGroup.name_en : budgetGroup.name_cn),
                      alignment: 'left',
                      style: 'tableContent',
                      bold: false,
                      border: [false, false, false, false],
                    },
                  ],
                ],
              },
              layout: 'noBorders',
            },
          ],
        },
        ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
          text: '',
          border: [false, false, false, false],
        })),
      ]
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, budget, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 表格寬度
      const widths = generateWidths(columns[0])

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height
      }

      const budgetInfo = this.generateBudgetInfo(budget, printSetting.language, columns[0].length)
      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              widths: widths,
              heights: tableData.map((e, i) =>
                i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
              ),
              headerRows: columns.length + 1,
              body: [
                pageHeader, // 頁頭
                budgetInfo,
                ...columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              defaultBorder: false,
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(mm2pt(printSetting.table_header_height)),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(mm2pt(printSetting.table_footer_height)),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      await openPdf(docDefinition)
    },
  },
}
</script>
