<script>
// import clip from '@/utils/clipboard'
import { mapGetters, mapActions } from 'vuex'
import { getPrintSetting, editPrintSetting, delPrintSetting } from '@/api/settings/printSetting'
import { editStaffPreference, editUserPreference } from '@/api/settings/user/preference'

export default {
  name: 'SettingPrintoutMixin',
  data() {
    return {
      hookVisible: false,
      loading: true,
      allConfig: [],
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['language', 'user_id', 'system']),
    styleList() {
      return this.allConfig.map(item => {
        return {
          label: item.config_json.style_name,
          value: item.psg_code,
        }
      })
    },
  },
  watch: {
    'form.sign_data'(val) {
      if (Array.isArray(val) && val.length === 0) {
        this.initSignData()
      } else if (
        this.form &&
        this.form.hasOwnProperty('sign_data') &&
        (!this.form.sign_data || this.form.sign_data.length === 0)
      ) {
        this.initSignData()
      }
    },
    form(val) {
      if (val === undefined || val === null) {
        this.form = this.defaultForm
      }
    },
  },
  mounted() {
    this.onFetch(1)
    this.saveLastPrintoutPage()
  },
  methods: {
    ...mapActions(['fetchGlobalStyle']),
    reload() {
      this.hookVisible = true
      this.$nextTick(() => {
        this.hookVisible = false
      })
    },
    onSave(isNew) {
      if (!this.$refs.form) {
        throw new Error('form error')
      }
      this.$refs.form.validate(valid => {
        if (!valid) {
          return
        }
        const ps_code = this.ps_code
        let psg_code
        if (isNew) {
          let i = 1
          while (this.styleList.findIndex(item => item.value.toString() === i.toString()) !== -1) {
            i += 1
          }
          psg_code = i.toString()
        } else {
          psg_code = this.psg_code === '' ? '1' : this.psg_code
        }
        const config = this.form
        const using = 1
        this.loading = true
        return new Promise((resolve, reject) => {
          editPrintSetting({ ps_code, psg_code, config, using })
            .then(() => {
              this.$message.success(this.$t('message.success'))
              this.psg_code = psg_code
              this.onFetch(1)
            })
            .catch(err => {
              reject(err)
            })
            .finally(() => {
              this.loading = false
            })
        })
      })
    },
    onCopy() {
      const config = JSON.stringify(this.form)
      const psg_code = this.psg_code
      const ps_code = this.ps_code
      const text = `{config_json: ${config},
ps_code: "${ps_code}",
psg_code: "${psg_code}"}`

      this.$confirm(
        `<textarea style="width:100%;height:200px" id="printout-config-text">${text}</textarea>`,
        this.$t('checkConfiguration'),
        {
          dangerouslyUseHTMLString: true,
          closeOnClickModal: true,
          confirmButtonText: this.$t('button.copy'),
          confirmButtonClass: 'btn-copy',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              const ele = document.querySelector('#printout-config-text')
              if (ele) {
                ele.select()
                if (document.execCommand('Copy')) {
                  this.$message.success(this.$t('copySuccess'))
                  done()
                  return
                }
              }
              this.$message.error(this.$t('copyFailed'))
            } else {
              done()
            }
          },
        },
      )
        .then(() => {})
        .catch(() => {})
    },
    onFetch(using) {
      const ps_code = this.ps_code
      const psg_code = this.psg_code
      console.log('mixin', ps_code)
      this.loading = true
      return new Promise((resolve, reject) => {
        getPrintSetting({ ps_code })
          .then(res => {
            this.allConfig = res
            let data = null
            // // 空form
            if (!res.length) {
              this.form = null
              resolve(data)
              return
            }
            let item
            if (using && !psg_code) {
              item = res.find(i => i.using === 1)
            } else {
              item = res.find(i => i.psg_code === psg_code)
            }
            if (!item) item = res[0]
            data = Object.assign({}, item.config_json)
            this.psg_code = item.psg_code
            this.form = data
            resolve(data)
            return data
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    onDelete() {
      console.log('delete')
      const ps_code = this.ps_code
      const psg_code = this.psg_code
      delPrintSetting({ ps_code, psg_code }).then(res => {
        this.onFetch(1)
      })
    },
    initSignData() {
      if (!this.form) {
        this.form = {}
      }
      this.$set(this.form, 'sign_data', [
        {
          step_cn: this.$t('print.prepared'),
          step_en: 'Prepared',
          post_cn: this.$t('print.staff'),
          post_en: 'Staff',
        },
        {
          step_cn: this.$t('print.approved'),
          step_en: 'Approved',
          post_cn: this.$t('print.principal'),
          post_en: 'Principal',
        },
        {
          step_cn: this.$t('print.checked'),
          step_en: 'Checked',
          post_cn: this.$t('print.supervisor'),
          post_en: 'Supervisor',
        },
        {
          step_cn: this.$t('print.received'),
          step_en: 'Received',
          post_cn: '',
          post_en: '',
        },
        {
          step_cn: '',
          step_en: '',
          post_cn: '',
          post_en: '',
        },
        {
          step_cn: '',
          step_en: '',
          post_cn: '',
          post_en: '',
        },
      ])
    },
    onSetSignDefault() {
      this.form.sign_data = JSON.parse(JSON.stringify(this.defaultForm.sign_data))
    },
    saveLastPrintoutPage() {
      const user_id = this.user_id
      const system = this.system
      const content = {
        page: this.$route.name,
      }
      const pf_code = system.toLowerCase() + '.setting'
      const module_code = 'printoutPage'
      let api = editStaffPreference
      if (system === 'AC') {
        api = editUserPreference
      }
      api(user_id, system, pf_code, module_code, content)
        .then(res => {})
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.el-main {
  /*width: 500px;*/
}

.header > header {
  font-size: 18px !important;
  margin-left: 0;
}

.actions {
  margin-top: 30px;
}
</style>
<style lang="scss" rel="stylesheet/scss">
.unit-tips {
  font-size: 10px !important;
  vertical-align: bottom;
  margin-left: 5px;
  color: #818181;
}
</style>
