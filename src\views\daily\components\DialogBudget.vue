<template>
  <!--預算彈窗-->
  <el-dialog :visible.sync="showDialog" :title="$t('daily.dialog.budget')" @open="onOpenDialog">
    <div class="selectBudget">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.category')">
            <BudgetSelectTreeVue
              :data="budgetGroupTree"
              :budget-id.sync="searchFilter.group_id"
              :disabled-method="handleBudgetSelectDisabledMethod"
              :select-all="true"
              :language="language"
              @change="onChangeGroup"
            />
          </el-form-item>
          <el-form-item :label="$t('daily.label.group')">
            <BudgetSelectTreeVue
              :data="budgetItemTree"
              :budget-id.sync="searchFilter.item_id"
              :disabled-method="handleBudgetSelectDisabledMethod"
              :select-all="true"
              :language="language"
              @change="onChangeGroup"
            />
          </el-form-item>
          <el-form-item :label="$t('daily.label.budgetItem')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear(true)">
              {{ $t('button.clear') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="400"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="$t('daily.label.no')" property="budget_code" width="150" />
          <el-table-column
            :label="$t('daily.label.group')"
            property="g_budget_name_cn"
            width="250"
          />
          <el-table-column
            :label="$t('daily.label.budgetItem')"
            :property="language === 'en' ? 'name_en' : 'name_cn'"
          />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import BudgetSelectTreeVue from '@/views/budget/common/BudgetSelectTreeVue.vue'

import { fetchBudgetTree, searchBudget } from '@/api/budget'

export default {
  name: 'DialogBudget',
  components: {
    BudgetSelectTreeVue,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
    account_id: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      p_code: 'ac.assistance.budget',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      showDialog: this.dialogVisible,
      dialogView: 'list',
      tableData: [],
      searchFilter: {
        group_id: '',
        item_id: '',
        budget_name: '',
      },
      defaultFilter: {
        group_id: '',
        item_id: '',
        budget_name: '',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp

      // 預算
      budgetGroupTree: [],
      group_id: '', // 選擇的組
      budgetItemTree: [],
      item_id: '',
    }
  },
  computed: {
    ...mapGetters(['language']),
    allBudget() {
      return {
        budget_IE: 'B',
        budget_code: '',
        budget_default: '',
        budget_id: '',
        budget_type: 'F',
        level: 0,
        children: [],
        name_cn: this.$t('enquiry.timeType.all'),
        name_en: 'All',
        parent_budget_id: null,
      }
    },
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  created() {},
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    onClear(update) {
      this.searchFilter = Object.assign({}, this.defaultFilter)
      if (update) this.fetchData()
    },
    async onOpenDialog() {
      // 清空
      this.tableData = []
      this.onClear()

      // 初始化預算組
      await this.initBudgetGroup()
      // 初始化預算事項
      await this.initBudgetItem()

      this.fetchData()
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$emit('selectRow', currentRow)
      this.showDialog = false
    },
    /* 預算 */
    async initBudgetGroup(val) {
      const fy_code = this.fy_code
      const parent_budget_id = val
      const only_node = 1
      const type = 'F'
      this.budgetGroupTree = await fetchBudgetTree({
        parent_budget_id,
        fy_code,
        type,
        only_node,
      })
    },
    onChangeGroup(val) {
      console.log(val)
      this.initBudgetItem().then(this.fetchData)
    },
    budgetLabel(item) {
      const code = item.budget_code ? `[${item.budget_code}] ` : ''
      const label = item[this.language === 'en' ? 'name_en' : 'name_cn']
      return code + label
    },
    async initBudgetItem() {
      const fy_code = this.fy_code
      const parent_budget_id = this.searchFilter.group_id
      const type = 'C'
      this.budgetItemTree = await fetchBudgetTree({
        parent_budget_id,
        fy_code,
        type,
      })
    },
    async fetchData() {
      this.tableLoading = true
      const fy_code = this.fy_code
      const parent_budget_id = this.searchFilter.item_id || this.searchFilter.group_id || undefined
      const budget_name = this.searchFilter.budget_name || undefined
      const budget_active = 'Y'
      try {
        this.tableData = await searchBudget({
          parent_budget_id,
          fy_code,
          budget_name,
          budget_active,
          account_id: this.account_id,
        })
      } catch (e) {
        console.error(e)
        //
      }
      this.tableLoading = false
    },
    handleBudgetSelectDisabledMethod() {
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectBudget {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
</style>
