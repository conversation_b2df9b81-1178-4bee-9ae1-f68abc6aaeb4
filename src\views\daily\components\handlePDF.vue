<script>
import { mapGetters } from 'vuex'
import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt, insertData } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  paddingLeft,
  paddingRight,
  paddingTop,
  paddingBottom,
  generateGeneralHeader,
  generateWidths,
  generateVoucherAccountCell,
  generateVoucherDescCell,
  generateVoucherInfo,
  font_size_fotter,
  margin_bottom_offset,
  mergeTotalRow,
  generateConsolidateVoucherInfo,
  generateConsolidateDesc,
} from '@/utils/pdf/generator'
import { getVoucher, fetchLedgers } from '@/api/daily/payment'
import { fetchSystemSettings } from '@/api/settings/system'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import { companiesAddressPrintList } from '@/api/assistance/payeePayer'
import { chequeConvertCN, chequeConvertEnglish } from '@/utils/amount'
import { getEmptyImageBase64 } from '@/utils/pdf'
import dayjs from 'dayjs'
export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  computed: {
    ...mapGetters(['school', 'remoteServerInfo']),
  },
  data() {
    return {
      logoCache: null,
    }
  },
  methods: {
    async generateSchoolLogo() {
      if (this.logoCache) {
        return this.logoCache
      }
      const rsi = this.remoteServerInfo
      let logoURL
      if (process.env.NODE_ENV === 'production') {
        logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`
      } else {
        logoURL = require('@/assets/pdflogo.jpg')
      }
      try {
        this.logoCache = await getUrlBase64(logoURL)
        return this.logoCache
      } catch (e) {
        return null
      }
    },
    onPrintVoucherByView() {
      if (!isInitPDF) {
        return initTips()
      }
      this.handlePrint(this.form, this.tableData)
    },
    handlePrint(info, tableData) {
      switch (info.vt_category) {
        case 'P':
        case 'R':
        case 'C':
        case 'T':
        case 'J':
          return this.handlerPrintPaymentVoucherByData(info, tableData)
        default:
          return null
      }
    },
    onPrintVoucherById(fy_code, vc_id) {
      if (!isInitPDF) {
        return initTips()
      }
      let info = {}
      let tableData = []
      return getVoucher({ fy_code, vc_id }).then(res => {
        info = res
        tableData = res.ledgers
        return this.handlePrint(info, tableData)
      })
    },
    handlerPrintPaymentVoucherByData(voucher, voucherLedgers) {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      let t_ps_code
      switch (voucher.vt_category) {
        case 'P':
          t_ps_code = 'pdfvoucherp'
          break
        case 'R':
          t_ps_code = 'pdfvoucherr'
          break
        case 'C':
          t_ps_code = 'pdfvoucherc'
          break
        case 'T':
          t_ps_code = 'pdfvouchert'
          break
        case 'J':
          t_ps_code = 'pdfvoucherj'
          break
        default:
          return false
      }
      this.loadPrintoutSetting(t_ps_code)
        .then(ps => {
          printSetting = ps
          return ps
        })
        .then(ps => {
          switch (voucher.vt_category) {
            case 'P':
            case 'R':
            case 'C':
              return this.formatPrintDataByPayment(ps, voucher, voucherLedgers)
            case 'T':
            case 'J':
              return this.formatPrintDataByTransfer(ps, voucher, voucherLedgers)
            default:
              return Promise.reject()
          }
        })
        .then(data =>
          this.generatePDF({
            ...data,
            printSetting,
          }),
        )
        .then(doc => openPdf(doc))
        .catch(err => {
          this.$message.error(this.$t('message.printSettingEmpty'))
          console.error('onPrint', err)
        })
    },
    // Format page header
    formatHeader(printSetting, voucher) {
      return new Promise(async(resolve, reject) => {
        const language = printSetting.language
        const _t = this.$t.bind(this)
        let logo
        try {
          logo = await this.generateSchoolLogo()
        } catch (e) {
          reject('logo load error')
          return null
        }
        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: logo,
        }
        let title = ''
        switch (voucher.vt_category) {
          case 'P':
            title = this.$t('daily.voucher.label.paymentVoucher')
            break
          case 'R':
            title = this.$t('daily.voucher.label.receiptVoucher')
            break
          case 'C':
            title = this.$t('daily.voucher.label.cashVoucher')
            break
          case 'T':
            title = this.$t('daily.voucher.label.transferVoucher')
            break
          case 'J':
            title = this.$t('daily.voucher.label.journalVoucher')
            break
        }
        const vc_no = voucher.vc_no
        const vc_date = voucher.vc_date
        let dateStr = ''
        if (vc_date) {
          dateStr = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${vc_no} - ${dateStr}`,
          data: [
            {
              label: this.$t('daily.voucher.label.voucherNo') + ':',
              value: vc_no,
            },
            {
              label: this.$t('daily.voucher.label.voucherDate') + ':',
              value: dateStr,
            },
          ],
        }

        // 頁頭，包含LOGO，頁面信�?        resolve({ schoolInfo, pageInfo })
        return { schoolInfo, pageInfo }
      })
    },
    // Format print data by payment
    formatPrintDataByPayment(printSetting, voucher, voucherLedgers) {
      return new Promise(async(resolve, reject) => {
        const language = printSetting.language
        const _t = this.$t.bind(this)

        const columns = [[]]

        // 表頭
        columns[0] = [
          {
            text: '#',
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_1,
          },
          {
            text: this.$t('daily.voucher.label.account'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_2,
          },
          {
            text: this.$t('daily.voucher.label.desc'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_3,
          },
          {
            text: this.$t('daily.voucher.label.amount'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_4,
          },
        ]
        // 表頭信息
        let schoolInfo, pageInfo
        try {
          const data = await this.formatHeader(printSetting, voucher)
          schoolInfo = data.schoolInfo
          pageInfo = data.pageInfo
        } catch (e) {
          resolve(e)
          return
        }

        const voucherInfo = generateVoucherInfo(voucher, language, _t, columns[0].length)
        // 主數�?        //

        // tableData
        const tableData = []
        const sumRow = [
          {
            text: this.$t('print.total'),
            colSpan: 3,
            alignment: 'right',
            bold: true,
            style: 'tableFooter',
          },
          {},
          {},
          {
            value: 0,
            text: '0.00',
            alignment: 'right',
          },
        ]

        let amountField = 'amount_dr'
        switch (voucher.vt_category) {
          case 'P':
          case 'C':
            amountField = 'amount_dr'
            break
          case 'R':
            amountField = 'amount_cr'
            break
        }
        // 初始化行
        voucherLedgers.forEach((item, rowIndex) => {
          const row = []
          // 序號
          row.push({
            text: (rowIndex + 1).toString(),
            alignment: 'center',
            style: 'tableContent',
            width: printSetting.column_width_1,
          })
          // 會計帳目
          row.push(
            generateVoucherAccountCell(
              item.ac_code,
              item.tx_type,
              language === 'en' ? item.ac_name_en : item.ac_name_cn,
              printSetting.column_width_2,
              printSetting.font_size_content,
            ),
          )
          // 內容描述
          row.push(generateVoucherDescCell(item, language, _t, printSetting.column_width_3))

          // 金額
          row.push({
            text: amountFormat(item[amountField]),
            alignment: 'right',
            style: 'tableContent',
            width: printSetting.column_width_4,
          })
          sumRow[3].value = toDecimal(Number(sumRow[3].value) + Number(item[amountField]))
          sumRow[3].text = amountFormat(sumRow[3].value)
          tableData.push(row)
        })

        tableData.push(sumRow)
        resolve({
          schoolInfo,
          pageInfo,
          voucherInfo,
          columns,
          tableData,
        })
      })
    },
    // Format print data by transfer
    formatPrintDataByTransfer(printSetting, voucher, voucherLedgers) {
      return new Promise(async(resolve, reject) => {
        const language = printSetting.language
        const _t = this.$t.bind(this)

        const columns = [[]]

        // 表頭
        columns[0] = [
          {
            text: '#',
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_1,
          },
          {
            text: this.$t('daily.voucher.label.account'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_2,
          },
          {
            text: this.$t('daily.voucher.label.desc'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_3,
          },
          {
            text: this.$t('daily.voucher.label.amount_dr'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_4,
          },
          {
            text: this.$t('daily.voucher.label.amount_cr'),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: printSetting.column_width_5,
          },
        ]
        // 表頭信息
        let schoolInfo, pageInfo
        try {
          const data = await this.formatHeader(printSetting, voucher)
          schoolInfo = data.schoolInfo
          pageInfo = data.pageInfo
        } catch (e) {
          resolve(e)
          return
        }

        const voucherInfo = generateVoucherInfo(voucher, language, _t, columns[0].length)
        // 主數�?        //

        // tableData
        const tableData = []
        const sumRow = [
          {
            text: this.$t('print.total'),
            colSpan: 3,
            alignment: 'right',
            bold: true,
            style: 'tableFooter',
          },
          {},
          {},
          {
            value: 0,
            text: '0.00',
            alignment: 'right',
          },
          {
            value: 0,
            text: '0.00',
            alignment: 'right',
          },
        ]

        // 初始化行
        voucherLedgers.forEach((item, rowIndex) => {
          const row = []
          // 序號
          row.push({
            text: (rowIndex + 1).toString(),
            alignment: 'center',
            style: 'tableContent',
            width: printSetting.column_width_1,
          })
          // 會計帳目
          row.push(
            generateVoucherAccountCell(
              item.ac_code,
              item.tx_type,
              language === 'en' ? item.ac_name_en : item.ac_name_cn,
              printSetting.column_width_2,
              printSetting.font_size_content,
            ),
          )
          // 內容描述
          row.push(generateVoucherDescCell(item, language, _t, printSetting.column_width_3))

          // 金額
          row.push({
            text: amountFormat(item.amount_dr),
            alignment: 'right',
            style: 'tableContent',
            width: printSetting.column_width_4,
          })
          // 金額
          row.push({
            text: amountFormat(item.amount_cr),
            alignment: 'right',
            style: 'tableContent',
            width: printSetting.column_width_5,
          })
          sumRow[3].value = toDecimal(Number(sumRow[3].value) + Number(item.amount_dr))
          sumRow[3].text = amountFormat(sumRow[3].value)
          sumRow[4].value = toDecimal(Number(sumRow[4].value) + Number(item.amount_cr))
          sumRow[4].text = amountFormat(sumRow[4].value)
          tableData.push(row)
        })

        tableData.push(sumRow)
        resolve({
          schoolInfo,
          pageInfo,
          voucherInfo,
          columns,
          tableData,
        })
      })
    },
    // 生成PDF，單張傳
    generatePDF({ schoolInfo, pageInfo, voucherInfo, columns, tableData, printSetting }) {
      return new Promise((resolve, reject) => {
        // const $t = this.$t.bind(this)
        //
        // const font_size_footer = 11

        const margin_left = mm2pt(printSetting.margin_left)
        const margin_top = mm2pt(printSetting.margin_top)
        const margin_right = mm2pt(printSetting.margin_right)
        let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const sign_height = mm2pt(printSetting.sign_height)
        const sign_space = mm2pt(printSetting.sign_space)

        const header_width_1 = printSetting.header_width_1 + '%'
        const header_width_2 = printSetting.header_width_2 + '%'

        const bottom_sign = printSetting.sign_style.toString() === '2'

        // 表格寬度
        const widths = generateWidths(columns[0])

        // 學校信息
        const schoolTable = generateSchoolInfo(
          schoolInfo.name_cn,
          schoolInfo.name_en,
          schoolInfo.logo,
          header_width_1,
        )
        // 頁面信息
        const pageTable = generatePageInfo(
          pageInfo.title,
          pageInfo.filename,
          pageInfo.data,
          header_width_2,
          margin_right,
        )
        // 頁頭，包含LOGO，頁面信�?        const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

        // 簽名設置
        const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
        const signTable = generateSign(
          printSetting.sign_line,
          signColumn,
          printSetting.language,
          sign_height,
          sign_space,
          margin_left,
          margin_right,
          printSetting.font_size_signature,
          bottom_sign,
        )
        if (bottom_sign) {
          // 簽名固定底部時，需預留簽名位置
          margin_bottom += signTable.height // + 20// +50
        }
        const data = []
        if (bottom_sign) {
          data.push(signTable)
        }
        const docDefinition = {
          info: {
            title: pageInfo.filename,
            author: 'Norray',
            subject: pageInfo.filename,
          },
          content: [
            {
              // Content
              width: '100%',
              style: 'tableExample',
              table: {
                dontBreakRows: true,
                keepWithHeaderRows: 1,
                // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
                widths: widths,
                heights: tableData.map((e, i) =>
                  i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
                ),
                headerRows: columns.length + 1,
                body: [
                  pageHeader, // 頁頭
                  voucherInfo, // 傳票信息
                  ...columns, // 數據表頭
                  ...tableData, // 數據
                ],
              },
              layout: {
                vLineWidth: lineWidth,
                hLineWidth: lineWidth,
                hLineColor: lineColor,
                vLineColor: lineColor,
              },
            },
          ],
          footer: data,
          styles: {
            tableExample: {
              fontSize: Number(printSetting.font_size_content),
              margin: [0, 0, 0, 0],
            },
            tableHeader: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
              height: Number(printSetting.table_header_height),
              color: 'black',
              // fillColor: '#CCCCCC',
              alignment: 'center',
            },
            tableFooter: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
              height: Number(printSetting.table_footer_height),
              color: 'black',
              // fillColor: '#CCCCCC'
            },
            schoolNameCN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_cn),
              color: 'black',
            },
            schoolNameEN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_en),
              color: 'black',
            },
            titleCell: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
            },
            tableContent: {
              bold: false,
              fontSize: Number(printSetting.font_size_content),
            },
            signCell: {
              fontSize: Number(printSetting.font_size_signature),
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
        }
        if (printSetting.sign_style.toString() === '1') {
          // 浮動
          docDefinition.content.push(signTable)
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    // 生成表格，批次
    generatePDFContent({
      index,
      schoolInfo,
      pageInfo,
      voucherInfo,
      columns,
      tableData,
      printSetting,
    }) {
      return new Promise((resolve, reject) => {
        const margin_right = mm2pt(printSetting.margin_right)
        const header_width_1 = printSetting.header_width_1 + '%'
        const header_width_2 = printSetting.header_width_2 + '%'

        const bottom_sign = printSetting.sign_style.toString() === '2'

        // 學校信息
        const schoolTable = generateSchoolInfo(
          schoolInfo.name_cn,
          schoolInfo.name_en,
          schoolInfo.logo,
          header_width_1,
        )
        // 頁面信息
        const pageTable = generatePageInfo(
          pageInfo.title,
          pageInfo.filename,
          pageInfo.data,
          header_width_2,
          margin_right,
        )
        // 頁頭，包含LOGO，頁面信�?        const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

        const content = []

        // 表格寬度
        const widths = generateWidths(columns[0])

        content.push([
          {
            // Content
            // absolutePosition: { x: 0, y: (index % 2 ? 200 : 0) },
            headlineLevel: index > 0 ? 1 : 0,
            pageBreak: index > 0 ? 'before' : undefined,
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              // heights: tableData.map((e, i) => i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto'),
              headerRows: columns.length + 1,
              body: [
                pageHeader, // 頁頭
                voucherInfo, // 傳票信息
                ...columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ])

        if (printSetting.sign_style.toString() === '1') {
          // 浮動
          const sign_height = mm2pt(printSetting.sign_height)
          const sign_space = mm2pt(printSetting.sign_space)
          const margin_left = mm2pt(printSetting.margin_left)
          // 簽名設置
          const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
          const signTable = generateSign(
            printSetting.sign_line,
            signColumn,
            printSetting.language,
            sign_height,
            sign_space,
            margin_left,
            margin_right,
            printSetting.font_size_signature,
            bottom_sign,
          )
          content.push(signTable)
        }
        resolve(content)
      })
    },
    // 生成PDF，批量
    generatePDF2({ title, columns, content, printSetting }) {
      return new Promise((resolve, reject) => {
        const margin_left = mm2pt(printSetting.margin_left)
        const margin_top = mm2pt(printSetting.margin_top)
        const margin_right = mm2pt(printSetting.margin_right)
        let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const sign_height = mm2pt(printSetting.sign_height)
        const sign_space = mm2pt(printSetting.sign_space)

        const bottom_sign = printSetting.sign_style.toString() === '2'

        // 簽名設置
        const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
        const signTable = generateSign(
          printSetting.sign_line,
          signColumn,
          printSetting.language,
          sign_height,
          sign_space,
          margin_left,
          margin_right,
          printSetting.font_size_signature,
          bottom_sign,
        )
        if (bottom_sign) {
          // 簽名固定底部時，需預留簽名位置
          margin_bottom += signTable.height // + 20// +50
        }
        const data = []
        if (bottom_sign) {
          data.push(signTable)
        }
        const docDefinition = {
          info: {
            title: title,
            author: 'Norray',
            subject: title,
          },
          content: content,
          footer: data,
          pageBreakBefore: function(
            currentNode,
            followingNodesOnPage,
            nodesOnNextPage,
            previousNodesOnPage,
          ) {
            return currentNode.headlineLevel === 1
          },
          styles: {
            tableExample: {
              fontSize: Number(printSetting.font_size_content),
              margin: [0, 0, 0, 0],
            },
            tableHeader: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
              height: Number(printSetting.table_header_height),
              color: 'black',
              // fillColor: '#CCCCCC',
              alignment: 'center',
            },
            tableFooter: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
              height: Number(printSetting.table_footer_height),
              color: 'black',
              // fillColor: '#CCCCCC'
            },
            schoolNameCN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_cn),
              color: 'black',
            },
            schoolNameEN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_en),
              color: 'black',
            },
            titleCell: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
            },
            tableContent: {
              bold: false,
              fontSize: Number(printSetting.font_size_content),
            },
            signCell: {
              fontSize: Number(printSetting.font_size_signature),
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
        }
        resolve(docDefinition)
      })
    },
    // 生成傳票，批量
    async handlerPrintVoucherByIdList(vt_category, fy_code, vc_id_list) {
      try {
        if (!isInitPDF) {
          return initTips()
        }
        let t_ps_code
        this.$store.commit('setPrintList', {
          name: this.$t('setting.printout.navMenu.daily_voucher_list'),
          status: 0,
          startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          finishTime: '',
          url: '',
        })
        switch (vt_category) {
          case 'P':
            t_ps_code = 'pdfvoucherp'
            break
          case 'R':
            t_ps_code = 'pdfvoucherr'
            break
          case 'C':
            t_ps_code = 'pdfvoucherc'
            break
          case 'T':
            t_ps_code = 'pdfvouchert'
            break
          case 'J':
            t_ps_code = 'pdfvoucherj'
            break
          default:
            return false
        }
        const printSetting = await this.loadPrintoutSetting(t_ps_code)

        let cols = []

        vc_id_list.sort((a, b) => a - b)
        const vc_list = await fetchLedgers({ fy_code, vc_id_list: vc_id_list.join(',') })
        const docs = []
        const pdfIndex = this.$store.state.printList.printList.length - 1
        for (const item of vc_list) {
          const voucher = item
          const voucherLedgers = item.ledgers
          let d = {}
          switch (vt_category) {
            case 'P':
            case 'R':
            case 'C':
              d = await this.formatPrintDataByPayment(printSetting, voucher, voucherLedgers)
              break

            case 'T':
            case 'J':
              d = await this.formatPrintDataByTransfer(printSetting, voucher, voucherLedgers)
              break

            default:
              return Promise.reject()
          }
          const schoolInfo = d.schoolInfo
          const pageInfo = d.pageInfo
          const voucherInfo = d.voucherInfo
          const columns = d.columns
          const tableData = d.tableData
          cols = columns

          const voucherPage = await this.generatePDFContent({
            index: 0,
            schoolInfo,
            pageInfo,
            voucherInfo,
            columns,
            tableData,
            printSetting,
          })
          const doc = await this.generatePDF2({
            title: this.$t('setting.printout.navMenu.daily_voucher_list'),
            columns: cols,
            content: voucherPage,
            printSetting,
          })
          doc.printListIndex = pdfIndex
          doc.defaultStyle = {
            font: 'cn',
            fontSize: 9,
          }
          docs.push(doc)
        }
        await openPdf(docs)
        return true
      } catch (error) {
        return false
      }
    },
    /**
     *  ****************    傳票列表           ********************
     * */
    // 傳票列表
    async handlerPrintVoucherList(vt_category, fy_code, month, vc_id_list, data, voucherType) {
      if (!isInitPDF) {
        return initTips()
      }
      const t_ps_code = 'pdfcchecklist'
      const printSetting = await this.loadPrintoutSetting(t_ps_code)

      const language = printSetting.language

      let payeeStr = ''
      switch (vt_category) {
        case 'R':
          payeeStr = this.$t('setting.printout.daily.voucherList.vc_payer')
          break
        case 'P':
        case 'C':
        case 'T':
        case 'J':
        default:
          payeeStr = this.$t('setting.printout.daily.voucherList.vc_payee')
          break
      }

      // 表頭
      const columns = []
      const columnData = printSetting.columnData
        .filter(i => i.position > 0)
        .sort((a, b) => a.position - b.position)
      columnData.forEach(item => {
        // let key = item.name
        // if (key.substring(key.length - 1)) {
        //   key += language === 'en' ? 'en' : 'cn'
        // }
        let text = ''
        if (item.name === 'vc_payee') {
          text = payeeStr
        } else {
          text = this.$t('setting.printout.daily.voucherList.' + item.name)
        }
        columns.push({
          text,
          style: 'tableHeader',
          rowSpan: 1,
          alignment: item.alignment,
          width: item.width,
          border: [true, true, true, true],
          // ,
          // maxHeight: 25,
          // marginBottom: 25
        })
      })
      // 頁頭信息
      let logo
      try {
        logo = await this.generateSchoolLogo()
      } catch (e) {
        console.log('logo load error')
        return null
      }
      // 數據
      const schoolInfo = {
        name_cn: this.school.sch_name_cn,
        name_en: this.school.sch_name_en,
        logo: logo,
      }
      const title = this.$t('setting.printout.navMenu.daily_voucher_list')
      const category = voucherType[language === 'en' ? 'vt_description_en' : 'vt_description_cn']
      this.$store.commit('setPrintList', {
        name: title,
        status: 0,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        finishTime: '',
        url: '',
      })
      // 右邊表格
      const pageInfo = {
        title: title,
        filename: `${title}${month ? ' - ' + month : ''}`,
        data: [
          {
            label: this.$t('setting.printout.daily.voucherList.category') + ':',
            value: category,
          },
          {
            label: this.$t('setting.printout.daily.voucherList.month') + ':',
            value: month,
          },
        ],
      }

      const sumRow = columnData.map(() => ({ text: '' }))
      const tableData = []
      // vc_id_list.sort((a, b) => a - b)
      let total = 0
      const vc_list = await fetchLedgers({ fy_code, vc_id_list: vc_id_list.join(',') })

      // vc_list.sort((a, b) => {
      //   return ('' + a.vc_no).localeCompare(('' + b.vc_no))
      // }).sort((a, b) => {
      //   const a_no = ('' + a.vc_no).replace(/[a-zA-Z]/g, '')
      //   const b_no = ('' + b.vc_no).replace(/[a-zA-Z]/g, '')
      //   return a_no.localeCompare(b_no)
      // })

      // 傳票列表
      for (let i = 0; i < vc_list.length; i++) {
        // const vc_info = vc_list[i]
        // const item = await getVoucher({ fy_code, vc_id: vc_info.vc_id })
        const item = vc_list[i]
        const voucher = item
        const voucherLedgers = item.ledgers

        let rowTotal = 0
        // Total amount

        if (voucherLedgers.length === 1) {
          switch (vt_category) {
            case 'P':
            case 'C':
              rowTotal = Number(voucherLedgers[0].amount_dr)
              break
            case 'R':
              rowTotal = Number(voucherLedgers[0].amount_cr)
              break
            case 'T':
            case 'J':
              rowTotal = toDecimal(
                Number(voucherLedgers[0].amount_cr - voucherLedgers[0].amount_dr),
              )
              break
          }
        } else if (voucherLedgers.length > 1) {
          switch (vt_category) {
            case 'P':
            case 'C':
              rowTotal = voucherLedgers.reduce((t, item) => {
                return toDecimal(t + Number(item.amount_dr))
              }, 0)
              break
            case 'R':
              rowTotal = voucherLedgers.reduce((t, item) => {
                return toDecimal(t + Number(item.amount_cr))
              }, 0)
              break
            case 'T':
            case 'J':
              rowTotal = voucherLedgers.reduce((t, item) => {
                return toDecimal(t + Number(item.amount_cr) - Number(item.amount_dr))
              }, 0)
              break
          }
        }
        total += rowTotal

        // 傳票明細
        const vl_len = voucherLedgers.length
        voucherLedgers.forEach((ledger, voucherIndex) => {
          const row = []
          const rowSpan = 1 // voucherIndex === 0 ? vl_len : 1

          const notBorder = [true, true, true, true]
          const border = notBorder
          columnData.forEach((col, colIndex) => {
            switch (col.name) {
              case 'index': // 傳票序號
                row.push({
                  // pageBreak: voucherIndex === 5 ? 'before' : undefined,
                  text: voucherIndex === 0 ? i + 1 : '',
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  rowSpan: rowSpan,
                  border: notBorder,
                })
                break
              case 'row': // 傳票明細序號
                row.push({
                  text: voucherIndex + 1,
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  border: notBorder,
                })
                break
              case 'vc_date': // 日期
                {
                  const vc_date = new Date(voucher.vc_date)
                  const dateStr = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
                  row.push({
                    text: voucherIndex === 0 ? dateStr : '',
                    alignment: col.alignment,
                    style: 'tableContent',
                    width: col.width,
                    rowSpan: rowSpan,
                    border: notBorder,
                  })
                }
                break
              case 'vc_summary': //
                row.push({
                  text: ledger.descr,
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  rowSpan: rowSpan,
                  border: notBorder,
                })
                break
              case 'vc_method': // Cheque number
                row.push({
                  text:
                    voucherIndex === 0
                      ? this.$t('voucher_method.' + voucher.vc_method.toLowerCase())
                      : '',
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  rowSpan: rowSpan,
                  border: notBorder,
                })
                break
              case 'ac_name': // 賬目名稱
                row.push({
                  text: language === 'en' ? ledger.ac_name_en : ledger.ac_name_cn,
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  rowSpan: rowSpan,
                  border: border,
                })
                break
              case 'ref': // Cheque number
                {
                  let ref = voucher.ref
                  switch (voucher.vc_method) {
                    case 'CASH':
                      ref = 'CASH'
                      break
                    case 'AUTO':
                      ref = 'AUTOPY'
                      break
                    case 'TRANSF':
                      ref = 'TRANSF'
                      break
                  }
                  row.push({
                    text: voucherIndex === 0 ? ref : '',
                    alignment: col.alignment,
                    style: 'tableContent',
                    width: col.width,
                    rowSpan: rowSpan,
                    border: notBorder,
                  })
                }
                break
              case 'vc_receipt': // Receipt number
                {
                  const vc_receipt = voucher.vc_receipt
                  row.push({
                    text: voucherIndex === 0 ? vc_receipt : '',
                    alignment: col.alignment,
                    style: 'tableContent',
                    width: col.width,
                    rowSpan: rowSpan,
                    border: notBorder,
                  })
                }
                break
              case 'vc_no': // 傳票號碼
              case 'vc_payee': // 收款公司
                row.push({
                  text: voucherIndex === 0 ? voucher[col.name] : '',
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  rowSpan: rowSpan,
                  border: notBorder,
                })
                break
              case 'vc_amount': // 金額
                {
                  const v = amountFormat(rowTotal)
                  row.push({
                    text: voucherIndex === 0 ? v : '',
                    alignment: col.alignment,
                    style: 'tableContent',
                    width: col.width,
                    rowSpan: rowSpan,
                    border: notBorder,
                  })
                }
                break
              case 'vc_amount_detail': // 明細
                {
                  let v = 0
                  switch (vt_category) {
                    case 'P':
                    case 'C':
                      v = Number(ledger.amount_dr)
                      break
                    case 'R':
                      v = Number(ledger.amount_cr)
                      break
                    case 'T':
                    case 'J':
                      v = toDecimal(Number(ledger.amount_dr) - Number(ledger.amount_cr))
                      break
                  }
                  const a = amountFormat(v)
                  row.push({
                    text: a,
                    alignment: col.alignment,
                    style: 'tableContent',
                    width: col.width,
                    border: border,
                  })
                }
                break
              default:
                row.push({
                  text: voucher[col.name],
                  alignment: col.alignment,
                  style: 'tableContent',
                  width: col.width,
                  // border: [false, true, false, true],
                  border: notBorder,
                })
                break
            }
          })

          tableData.push(row)
        })
      }
      const totalIndex = columnData.findIndex(i => i.name === 'vc_amount')
      if (totalIndex !== -1) {
        sumRow[0].text = this.$t('print.total')
        sumRow[0].colSpan = totalIndex
        sumRow[0].alignment = 'right'
        sumRow[0].bold = true
        sumRow[0].style = 'tableFooter'
        sumRow[totalIndex].style = 'tableFooter'
        sumRow[totalIndex].text = amountFormat(total)
        sumRow[totalIndex].alignment = columnData[totalIndex].alignment
      }

      tableData.push(sumRow)
      const doc = await this.generatePDFList({
        schoolInfo,
        pageInfo,
        columns,
        tableData,
        printSetting,
      })
      const language1 = window.sessionStorage.getItem('language')
      doc.makeFooter(printSetting, language1)
      doc.printListIndex = this.$store.state.printList.printList.length - 1
      console.log(JSON.stringify(doc))
      await openPdf(doc)
    },

    // 生成PDF，傳票列表
    generatePDFList({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      return new Promise((resolve, reject) => {
        const $t = this.$t.bind(this)

        const margin_left = mm2pt(printSetting.margin_left)
        const margin_top = mm2pt(printSetting.margin_top)
        const margin_right = mm2pt(printSetting.margin_right)
        let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // * 2// + 10 // 預留頁尾位置

        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const sign_height = mm2pt(printSetting.sign_height)
        const sign_space = mm2pt(printSetting.sign_space)

        const title_width = mm2pt(printSetting.title_width)

        const bottom_sign = printSetting.sign_style.toString() === '2'

        // 表格寬度
        const widths = generateWidths(columns)

        // 學校信息
        const schoolTable = generateSchoolInfo(
          schoolInfo.name_cn,
          schoolInfo.name_en,
          schoolInfo.logo,
        )
        // 頁面信息
        const pageTable = generatePageInfo(
          pageInfo.title,
          pageInfo.filename,
          pageInfo.data,
          title_width,
          margin_right,
        )
        // 頁頭，包含LOGO，頁面信�?        const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

        // 簽名設置
        const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
        const signTable = generateSign(
          printSetting.sign_line,
          signColumn,
          printSetting.language,
          sign_height,
          sign_space,
          margin_left,
          margin_right,
          printSetting.font_size_signature,
          bottom_sign,
        )
        if (bottom_sign) {
          // 簽名固定底部時，需預留簽名位置
          margin_bottom += signTable.height // + 20// +50
        }
        const docDefinition = {
          info: {
            title: pageInfo.filename,
            author: 'Norray',
            subject: pageInfo.filename,
          },
          content: [
            {
              // Content
              width: '100%',
              style: 'tableExample',
              table: {
                dontBreakRows: true,
                keepWithHeaderRows: 2,
                // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
                widths: widths,
                // heights: [...Array(tableData.length + 2)].map((e, i) => i < 2 ? Number(printSetting.table_header_height) : 'auto'),
                // heights: [...Array(tableData.length + 2)].map((e, i) => 30),
                headerRows: 2,
                body: [
                  pageHeader, // 頁頭
                  columns, // 數據表頭
                  ...tableData, // 數據
                ],
              },
              // layout: 'lightHorizontalLines'
              layout: {
                defaultBorder: false,
                vLineWidth: lineWidth,
                hLineWidth: lineWidth,
                hLineColor: lineColor,
                vLineColor: lineColor,
              },
            },
          ],
          // footer: function(currentPage, pageCount, pageSize) {
          //   const data = []
          //   if (bottom_sign) {
          //     data.push(signTable)
          //   }
          //   data.push({
          //     text: $t('print.footer', printSetting.language, { currentPage, pageCount }),
          //     alignment: 'center',
          //     fontSize: printSetting.font_size_page_num
          //   })
          //   // const y = -22
          //   // data.push({
          //   //   canvas: [{
          //   //
          //   //     absolutePosition: {x: 50, y: 50},
          //   //     type: 'line', x1: margin_left, y1: y, x2: page_width - margin_right, y2: y, lineWidth: 0.5
          //   //   }]
          //   // })
          //   return data
          // },
          makeFooter: function(printSetting) {
            const funcStr = 'currentPage, pageCount, pageSize'
            const funcBody = `
              const data = []
              const printSetting = ${JSON.stringify(printSetting)}
              const bottom_sign = printSetting.sign_style.toString() === '2'
              const signTable = ${JSON.stringify(signTable)}
              if (bottom_sign) {
                data.push(signTable)
              }
              let text = ''
              const language = '${language}'
              if (language === 'zh-hk') {
                text = '�?' + currentPage + ' / ' + pageCount + ' �?
              } else {
                text = 'Page ' + currentPage + ' / ' + pageCount
              }
              data.push({
                text,
                alignment: 'center',
                fontSize: printSetting.font_size_page_num
              })
              return data
            `
            docDefinition.footer = new Function(funcStr, funcBody)
            console.log(docDefinition, bottom_sign, 'docDefinition')
          },
          styles: {
            tableExample: {
              fontSize: Number(printSetting.font_size_content),
              margin: [0, 0, 0, 0],
            },
            tableHeader: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
              height: Number(printSetting.table_header_height),
              color: 'black',
              // fillColor: '#CCCCCC',
              alignment: 'center',
            },
            tableFooter: {
              bold: true,
              fontSize: Number(printSetting.font_size_content),
              height: Number(printSetting.table_footer_height),
              color: 'black',
              // fillColor: '#CCCCCC'
            },
            schoolNameCN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_cn),
              color: 'black',
            },
            schoolNameEN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_en),
              color: 'black',
            },
            titleCell: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
            },
            tableContent: {
              bold: false,
              fontSize: Number(printSetting.font_size_content),
            },
            signCell: {
              fontSize: Number(printSetting.font_size_signature),
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
        }
        if (printSetting.sign_style.toString() === '1') {
          // 浮動
          docDefinition.content.push(signTable)
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    /**
     *  ****************    綜合傳票列表           ********************
     * */
    async handlerPrintConsolidate(vt_category, year, bank, date, vc_id_list) {
      if (!isInitPDF) {
        return initTips()
      }
      const $t = this.$t.bind(this)
      const t_ps_code = 'pdfconsolidate'
      const printSetting = await this.loadPrintoutSetting(t_ps_code)
      const language = printSetting.language
      const isEn = language === 'en'

      // 表頭
      const columns = []
      const columnData = printSetting.columnData
        .filter(i => i.position > 0)
        .sort((a, b) => a.position - b.position)

      columnData.forEach(item => {
        columns.push({
          text: this.$t('setting.printout.daily.consolidateVoucher.' + item.name),
          style: 'tableHeader',
          rowSpan: 1,
          alignment: item.alignment,
          width: item.width,
          border: [true, true, true, true],
        })
      })
      // 頁頭信息
      let logo
      try {
        logo = await this.generateSchoolLogo()
      } catch (e) {
        console.log('logo load error')
        return null
      }
      // 數據
      const schoolInfo = {
        name_cn: this.school.sch_name_cn,
        name_en: this.school.sch_name_en,
        logo: logo,
      }
      const dayStr = dateUtil.format(new Date(date), 'dd/MM/yyyy')
      let title = ''
      switch (vt_category) {
        case 'P':
          title = this.$t('setting.printout.daily.consolidateVoucher.title_p', {
            year: year.fy_name,
          })
          break
        case 'C':
          title = this.$t('setting.printout.daily.consolidateVoucher.title_c', {
            year: year.fy_name,
          })
          break
        case 'R':
          title = this.$t('setting.printout.daily.consolidateVoucher.title_r', {
            year: year.fy_name,
          })
          break
        case 'T':
          title = this.$t('setting.printout.daily.consolidateVoucher.title_t', {
            year: year.fy_name,
          })
          break
        case 'J':
          title = this.$t('setting.printout.daily.consolidateVoucher.title_j', {
            year: year.fy_name,
          })
          break
      }
      this.$store.commit('setPrintList', {
        name: title,
        status: 0,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        finishTime: '',
        url: '',
      })
      // 右邊表格
      const pageInfo = {
        title: title,
        filename: title,
        data: [],
      }

      const sumRow = columnData.map(() => ({ text: '', border: [true, true, true, true] }))
      const countRow = columnData.map(() => ({ text: '' }))
      const tableData = []
      // vc_id_list.sort((a, b) => a - b)
      let total_cheque = 0
      let total_transf = 0
      let total_other = 0
      let cheque_count = 0
      let transf_count = 0
      let other_count = 0
      const vc_list = await fetchLedgers({
        fy_code: year.fy_code,
        vc_id_list: vc_id_list.join(','),
      })
      // 傳票列表
      for (let i = 0; i < vc_list.length; i++) {
        // const vc_info = vc_list[i]
        // const item = await getVoucher({ fy_code, vc_id: vc_info.vc_id })
        const item = vc_list[i]
        const voucher = item
        const voucherLedgers = item.ledgers

        let rowTotal_cheque = 0
        let rowTotal_transf = 0
        let rowTotal_other = 0

        if (voucher.vc_method === 'CHEQUE') {
          cheque_count += 1
        } else if (voucher.vc_method === 'TRANSF') {
          transf_count += 1
        } else {
          other_count += 1
        }
        // Total amount
        if (voucherLedgers.length > 0) {
          // const sumDr = (t, item) => { return t + Number(item.amount_dr) }
          // const sumCr = (t, item) => { return t + Number(item.amount_cr) }
          // const sumCrSubDr = (t, item) => { return t + Number(item.amount_cr) - Number(item.amount_dr) }
          // const sum = (t, item) => { return t + Number(item.amount_cr) - Number(item.amount_dr) }
          const v = Number(item.vc_amount)
          switch (vt_category) {
            case 'P':
            case 'C':
              if (voucher.vc_method === 'CHEQUE') {
                // rowTotal_cheque = voucherLedgers.reduce(sumDr, 0)
                rowTotal_cheque = v
              } else if (voucher.vc_method === 'TRANSF') {
                // rowTotal_transf = voucherLedgers.reduce(sumDr, 0)
                rowTotal_transf = v
              } else {
                rowTotal_other = v
                // rowTotal_other = voucherLedgers.reduce(sumDr, 0)
              }
              break
            case 'R':
              if (voucher.vc_method === 'CHEQUE') {
                // rowTotal_cheque = voucherLedgers.reduce(sumCr, 0)
                rowTotal_cheque = v
              } else if (voucher.vc_method === 'TRANSF') {
                // rowTotal_transf = voucherLedgers.reduce(sumCr, 0)
                rowTotal_transf = v
              } else {
                rowTotal_other = v
                // rowTotal_other = voucherLedgers.reduce(sumCr, 0)
              }
              break
            case 'T':
            case 'J':
              if (voucher.vc_method === 'CHEQUE') {
                // rowTotal_cheque = voucherLedgers.reduce(sumCrSubDr, 0)
                rowTotal_cheque = v
              } else if (voucher.vc_method === 'TRANSF') {
                // rowTotal_transf = voucherLedgers.reduce(sumCrSubDr, 0)
                rowTotal_transf = v
              } else {
                // rowTotal_other = voucherLedgers.reduce(sumCrSubDr, 0)
                rowTotal_other = v
              }
              break
          }
        }
        total_cheque += rowTotal_cheque
        total_transf += rowTotal_transf
        total_other += rowTotal_other

        const row = []

        columnData.forEach((col, colIndex) => {
          const cell = {
            text: '',
            alignment: col.alignment,
            style: 'tableContent',
            width: col.width,
            border: [true, true, true, true],
          }
          switch (col.name) {
            case 'index': // 傳票序號
              cell.text = i + 1
              break
            case 'vc_date': // 日期
              {
                const vc_date = new Date(voucher.vc_date)
                cell.text = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
              }
              break
            case 'ac_name': {
              // 賬目名稱
              let ac_name_arr = voucherLedgers.map(ledger =>
                isEn ? ledger.ac_name_en : ledger.ac_name_cn,
              )
              ac_name_arr = this.uniqueArr(ac_name_arr)
              cell.text = ac_name_arr.join('/')
              break
            }
            case 'payment_info':
              // ?
              break
            case 'cheque_date':
              cell.text = voucher.vc_chq_date
              break
            case 'cheque_amount':
              if (voucher.vc_method === 'CHEQUE') {
                cell.text = amountFormat(voucher.vc_amount)
              }
              break
            case 'cheque_no':
              cell.text = voucher.ref
              break
            case 'transfer_amount':
              if (voucher.vc_method === 'TRANSF') {
                cell.text = amountFormat(voucher.vc_amount)
              }
              break
            case 'other_amount':
              if (voucher.vc_method !== 'CHEQUE' && voucher.vc_method !== 'TRANSF') {
                cell.text = amountFormat(voucher.vc_amount)
              }
              break
            case 'st_code':
              cell.text = voucher.vc_st_code
              break
            case 'st_name':
              cell.text = isEn ? voucher.st_name_en : voucher.st_name_cn
              break
            case 'vc_no':
            case 'vc_summary':
            case 'vc_payee':
              cell.text = voucher[col.name]
              break
          }
          row.push(cell)
        })
        tableData.push(row)
      }

      const totalChequeIndex = columnData.findIndex(i => i.name === 'cheque_amount')
      const totalTransfIndex = columnData.findIndex(i => i.name === 'transfer_amount')
      const totalOtherIndex = columnData.findIndex(i => i.name === 'other_amount')
      const totalLabelSpan = Math.min(
        ...[totalChequeIndex, totalTransfIndex, totalOtherIndex].filter(i => i !== -1),
      )
      if (totalLabelSpan > 0) {
        sumRow[0].text = this.$t('print.total')
        sumRow[0].colSpan = totalLabelSpan
        sumRow[0].alignment = 'right'
        sumRow[0].bold = true
        sumRow[0].style = 'tableFooter'
      }
      if (totalChequeIndex !== -1) {
        sumRow[totalChequeIndex].style = 'tableFooter'
        sumRow[totalChequeIndex].text = amountFormat(total_cheque)
        sumRow[totalChequeIndex].alignment = columnData[totalChequeIndex].alignment

        countRow[totalChequeIndex].text = this.$t(
          'setting.printout.daily.consolidateVoucher.cheque_count',
          language,
          { count: cheque_count },
        )
        countRow[totalChequeIndex].alignment = columnData[totalChequeIndex].alignment
      }
      if (totalTransfIndex !== -1) {
        sumRow[totalTransfIndex].style = 'tableFooter'
        sumRow[totalTransfIndex].text = amountFormat(total_transf)
        sumRow[totalTransfIndex].alignment = columnData[totalTransfIndex].alignment

        countRow[totalTransfIndex].text = this.$t(
          'setting.printout.daily.consolidateVoucher.transf_count',
          language,
          { count: transf_count },
        )
        countRow[totalTransfIndex].alignment = columnData[totalTransfIndex].alignment
      }
      if (totalOtherIndex !== -1) {
        sumRow[totalOtherIndex].style = 'tableFooter'
        sumRow[totalOtherIndex].text = amountFormat(total_other)
        sumRow[totalOtherIndex].alignment = columnData[totalOtherIndex].alignment

        countRow[totalOtherIndex].text = this.$t(
          'setting.printout.daily.consolidateVoucher.other_count',
          language,
          { count: other_count },
        )
        countRow[totalOtherIndex].alignment = columnData[totalOtherIndex].alignment
      }

      mergeTotalRow(sumRow, [totalChequeIndex, totalTransfIndex, totalOtherIndex])

      tableData.push(sumRow)
      tableData.push(countRow)

      const bankStr = bank
        ? isEn
          ? vc_list[0].ac_name_en
          : vc_list[0].ac_name_cn
        : $t('setting.printout.daily.consolidateVoucher.allBank')
      const fundStr = bank
        ? isEn
          ? vc_list[0].fund_name_en
          : vc_list[0].fund_name_cn
        : $t('setting.printout.daily.consolidateVoucher.allAccount')

      // info
      const info = generateConsolidateVoucherInfo(
        bankStr,
        fundStr,
        dayStr,
        $t,
        language,
        columnData.length,
      )

      const doc = await this.generatePDFConsolidate({
        schoolInfo,
        pageInfo,
        info,
        columns,
        tableData,
        printSetting,
      })
      const language1 = window.sessionStorage.getItem('language')
      doc.makeFooter(printSetting, language1)
      doc.printListIndex = this.$store.state.printList.printList.length - 1
      console.log(JSON.stringify(doc))
      await openPdf(doc)
    },

    // 生成PDF，傳票列表
    generatePDFConsolidate({ schoolInfo, pageInfo, info, columns, tableData, printSetting }) {
      return new Promise((resolve, reject) => {
        const $t = this.$t.bind(this)

        const margin_left = mm2pt(printSetting.margin_left)
        const margin_top = mm2pt(printSetting.margin_top)
        const margin_right = mm2pt(printSetting.margin_right)
        let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // * 2// + 10 // 預留頁尾位置

        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const sign_height = mm2pt(printSetting.sign_height)
        const sign_space = mm2pt(printSetting.sign_space)

        const title_width = mm2pt(printSetting.title_width)

        const bottom_sign = printSetting.sign_style.toString() === '2'

        // 表格寬度
        const widths = generateWidths(columns)

        // 學校信息
        const schoolTable = generateSchoolInfo(
          schoolInfo.name_cn,
          schoolInfo.name_en,
          schoolInfo.logo,
        )
        // 頁面信息
        const pageTable = generatePageInfo(
          pageInfo.title,
          pageInfo.filename,
          pageInfo.data,
          title_width,
          margin_right,
        )
        // 頁頭，包含LOGO，頁面信�?        const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

        // 簽名設置
        const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
        const signTable = generateSign(
          printSetting.sign_line,
          signColumn,
          printSetting.language,
          sign_height,
          sign_space,
          margin_left,
          margin_right,
          printSetting.font_size_signature,
          bottom_sign,
        )
        if (bottom_sign) {
          // 簽名固定底部時，需預留簽名位置
          margin_bottom += signTable.height // + 20// +50
        }
        const desc = generateConsolidateDesc($t, printSetting.language, columns.length)

        const docDefinition = {
          info: {
            title: pageInfo.filename,
            author: 'Norray',
            subject: pageInfo.filename,
          },
          content: [
            {
              // Content
              width: '100%',
              style: 'tableExample',
              table: {
                dontBreakRows: true,
                keepWithHeaderRows: 3,
                // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
                widths: widths,
                // heights: [...Array(tableData.length + 2)].map((e, i) => i < 2 ? Number(printSetting.table_header_height) : 'auto'),
                heights: [...Array(tableData.length + 2)].map((e, i) => 30),
                headerRows: 3,
                body: [
                  pageHeader, // 頁頭
                  info,
                  columns, // 數據表頭
                  ...tableData, // 數據
                ],
              },
              // layout: 'lightHorizontalLines'
              layout: {
                defaultBorder: false,
                vLineWidth: lineWidth,
                hLineWidth: lineWidth,
                hLineColor: lineColor,
                vLineColor: lineColor,
              },
            },
          ],
          makeFooter: function(printSetting) {
            const funcStr = 'currentPage, pageCount, pageSize'
            const funcBody = `
              const data = []
              const printSetting = ${JSON.stringify(printSetting)}
              const bottom_sign = printSetting.sign_style.toString() === '2'
              const signTable = ${JSON.stringify(signTable)}
              if (bottom_sign) {
                data.push(signTable)
              }
              let text = ''
              const language = '${language}'
              if (language === 'zh-hk') {
                text = '�?' + currentPage + ' / ' + pageCount + ' �?
              } else {
                text = 'Page ' + currentPage + ' / ' + pageCount
              }
              data.push({
                text,
                alignment: 'center',
                fontSize: printSetting.font_size_page_num
              })
              return data
            `
            docDefinition.footer = new Function(funcStr, funcBody)
            console.log(docDefinition, bottom_sign, 'docDefinition')
          },
          styles: {
            tableExample: {
              fontSize: Number(printSetting.font_size_content),
              margin: [0, 0, 0, 0],
            },
            tableHeader: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
              height: Number(printSetting.table_header_height),
              color: 'black',
              // fillColor: '#CCCCCC',
              alignment: 'center',
            },
            tableFooter: {
              bold: true,
              fontSize: Number(printSetting.font_size_content),
              height: Number(printSetting.table_footer_height),
              color: 'black',
              // fillColor: '#CCCCCC'
            },
            schoolNameCN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_cn),
              color: 'black',
            },
            schoolNameEN: {
              bold: true,
              fontSize: Number(printSetting.font_size_school_name_en),
              color: 'black',
            },
            titleCell: {
              bold: true,
              fontSize: Number(printSetting.font_size_title),
            },
            tableContent: {
              bold: false,
              fontSize: Number(printSetting.font_size_content),
            },
            signCell: {
              fontSize: Number(printSetting.font_size_signature),
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
        }
        if (printSetting.sign_style.toString() === '1') {
          // 浮動
          docDefinition.content.push(signTable)
        }
        docDefinition.content.push(...desc)
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },

    /**
     *  ****************    收據           ********************
     * */
    async handlerPrintReceipt(vt_category, fy_code, vc_id_list) {
      if (!isInitPDF) {
        return initTips()
      }
      let t_ps_code = 'pdfreceipt'

      switch (vt_category) {
        case 'P':
        case 'C':
          break
        case 'R':
          t_ps_code = 'pdfreceipt'
          break
        default:
          console.error('print receipt: category error', vt_category)
          return
      }

      const printSetting = await this.loadPrintoutSetting(t_ps_code)

      // 頁頭信息
      let logo
      try {
        logo = await this.generateSchoolLogo()
      } catch (e) {
        console.log('logo load error')
        return null
      }
      // 學校信息
      const schoolInfo = {
        name_cn: this.school.sch_name_cn,
        name_en: this.school.sch_name_en,
        logo: logo,
      }
      const data = []
      const vc_list = await fetchLedgers({ fy_code, vc_id_list: vc_id_list.join(',') })
      // 傳票列表
      for (let i = 0; i < vc_list.length; i++) {
        const voucher = vc_list[i]
        let func
        if (vt_category === 'R') {
          func = this.generateReceiptR
        } else {
          func = this.generateReceiptP2
        }
        const receipt = func({ index: i, schoolInfo, voucher, printSetting })
        data.push(receipt)
      }

      const title =
        vt_category === 'R'
          ? this.$t('setting.printout.daily.receipt.title_receipt')
          : this.$t('setting.printout.daily.receipt.title_payment')
      this.$store.commit('setPrintList', {
        name: title,
        status: 0,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        finishTime: '',
        url: '',
      })
      const doc = await this.generatePDFReceipt1({
        title,
        schoolInfo,
        data,
        printSetting,
      })
      doc.printListIndex = this.$store.state.printList.printList.length - 1
      console.log(JSON.stringify(doc))
      await openPdf(doc)
    },

    /**
     *  ****************    PaymentSlip           ********************
     * */
    async handlerPrintPaymentSlip(vt_category, fy_code, vc_id_list) {
      if (!isInitPDF) {
        return initTips()
      }
      let t_ps_code = 'pdfreceipt'

      switch (vt_category) {
        case 'C':
          break
        case 'R':
          t_ps_code = 'pdfreceipt'
          break
        case 'P':
          // t_ps_code = 'paymentSlip'
          t_ps_code = 'pdfreceipt'
          break
        default:
          console.error('print receipt: category error', vt_category)
          return
      }

      const printSetting = await this.loadPrintoutSetting(t_ps_code)

      // 頁頭信息
      let logo
      try {
        logo = await this.generateSchoolLogo()
      } catch (e) {
        console.log('logo load error')
        return null
      }
      // 學校信息
      const schoolInfo = {
        name_cn: this.school.sch_name_cn,
        name_en: this.school.sch_name_en,
        logo: logo,
      }
      const data = []
      const vc_list = await fetchLedgers({ fy_code, vc_id_list: vc_id_list.join(',') })
      const systemSettings = await fetchSystemSettings('AC')
      const tel = systemSettings.find(item => item.key === 'ac.sch_tel')
      const fax = systemSettings.find(item => item.key === 'ac.sch_fax')
      const address = systemSettings.find(item => item.key === 'ac.sch_addr_cn')
      const address_en = systemSettings.find(item => item.key === 'ac.sch_addr_en')
      const acc_dept_cn = systemSettings.find(item => item.key === 'ac.acc_dept_cn')
      const acc_dept_en = systemSettings.find(item => item.key === 'ac.acc_dept_en')
      // 傳票列表
      for (let i = 0; i < vc_list.length; i++) {
        const voucher = vc_list[i]
        const func = this.generatePaymentSlip
        const receipt = func({
          index: i,
          schoolInfo,
          voucher,
          printSetting,
          tel,
          fax,
          address,
          address_en,
          acc_dept_cn,
          acc_dept_en,
        })
        data.push(receipt)
      }
      console.log(data, 'data')
      const title = this.$t('btnTitle.paymentSlip')
      this.$store.commit('setPrintList', {
        name: title,
        status: 0,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        finishTime: '',
        url: '',
      })
      const doc = await this.generatePDFReceipt1({
        title,
        schoolInfo,
        data,
        printSetting,
      })
      doc.printListIndex = this.$store.state.printList.printList.length - 1
      console.log(JSON.stringify(doc))
      await openPdf(doc)
    },

    generateReceiptP({ index, schoolInfo, voucher, printSetting }) {
      let ref = ''
      let refs = voucher.ledgers.map(item => item.ref).filter(i => i)
      refs = this.uniqueArr(refs)
      if (refs.length > 0) {
        ref = ` [Ref : ${refs.join(',')}]`
      }
      const vc_date = voucher.vc_date
      let dateStr = ''
      if (vc_date) {
        dateStr = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
      }
      const layout = {
        defaultBorder: false,
        vLineWidth: lineWidth,
        hLineWidth: lineWidth,
        hLineColor: lineColor,
        vLineColor: lineColor,
        paddingLeft,
        paddingRight,
        paddingTop,
        paddingBottom,
      }

      let cheque = ''
      switch (voucher.vc_method) {
        case 'AUTO':
          cheque = 'AUTOPY'
          break
        case 'CHEQUE':
          cheque = voucher.ref
          break
        case 'CASH':
          cheque = 'CASH'
          break
        case 'TRANSF':
          cheque = 'TRANSF'
          break
        case 'OTHER':
          cheque = '|' + voucher.ref
          break
      }

      return {
        // Content
        width: '100%',
        style: 'tableExample',
        alignment: 'center',
        margin: [0, 0, 0, (index + 1) % 3 === 0 ? 0 : 30],
        table: {
          dontBreakRows: true,
          keepWithHeaderRows: 4,
          headerRows: 4,
          widths: ['*'],
          heights: [70, 50, 50, 50],
          body: [
            [
              {
                // 收據頁頭
                columns: [
                  { width: '*', text: '' },
                  {
                    width: 'auto',
                    table: {
                      widths: ['*'],
                      body: [
                        [
                          {
                            style: 'tableExample',
                            alignment: 'center',
                            table: {
                              widths: ['*'],
                              body: [
                                [
                                  {
                                    margin: [5, 0, 5, 0],
                                    width: '*',
                                    alignment: 'justify',
                                    text: this.$t('message.receipt'),
                                    fontSize: 15,
                                    bold: true,
                                    columns: [
                                      {
                                        alignment: 'left',
                                        text: this.$t('message.receiptLeft'),
                                      },
                                      {
                                        alignment: 'right',
                                        text: this.$t('message.receiptRight'),
                                      },
                                    ],
                                    border: [false, false, false, false],
                                  },
                                ],
                                [
                                  {
                                    margin: [5, 0, 5, 0],
                                    width: '*',
                                    alignment: 'justify',
                                    text: 'RECEIPT',
                                    fontSize: 15,
                                    bold: true,
                                    border: [false, false, false, true],
                                  },
                                ],
                              ],
                            },
                            layout,
                          },
                        ],
                      ],
                    },
                    layout,
                  },
                  { width: '*', text: '' },
                ],
              },
            ],
            [
              {
                // 第一張
                table: {
                  widths: [100, '*', 90, 100],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.receiptReceived'),
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: schoolInfo.name_en,
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.chequeNumber'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'Received from',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: schoolInfo.name_cn,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'Cheque No.',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: cheque,
                        border: [false, false, false, true],
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第二張
                table: {
                  widths: [90, 130, 100, '*'],
                  heights: [20, 20, 20, 20],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.totalAmount'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.payment'),
                      },
                      {
                        margin: [10, 12, 10, 0],
                        colSpan: 1,
                        rowSpan: 2,
                        height: 40,
                        maxHeight: 40,
                        style: (voucher.vc_summary + ref).length > 35 ? 'smallSummary' : 'summary',
                        text: voucher.vc_summary + ref,
                        border: [false, false, false, true],
                      },
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'The sum of',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: 'HK$' + amountFormat(voucher.vc_amount),
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'in payment of',
                      },
                      '',
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第三張
                table: {
                  alignment: 'left',
                  widths: [110, '*', 100, '*'],
                  heights: [20, 20, 15, 15],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.receiptDate'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.payee'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'Date of Receipt :',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: dateStr,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'Received by :',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: '',
                        border: [false, false, false, true],
                      },
                    ],
                    [
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        width: '*',
                        alignment: 'center',
                        fontSize: 9,
                        columns: [
                          {
                            width: '*',
                            alignment: 'left',
                            text: ' (',
                          },
                          {
                            width: 'auto',
                            alignment: 'center',
                            text: voucher.vc_payee,
                          },
                          {
                            width: '*',
                            alignment: 'right',
                            text: ') ',
                          },
                        ],
                      },
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: `Our Ref. : ${voucher.vc_no}`,
                        colSpan: 4,
                        fontSize: 9,
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
          ],
        },
        // layout: 'lightHorizontalLines'
        layout,
      }
    },
    generateReceiptR({ index, schoolInfo, voucher, printSetting }) {
      const s = generateSchoolInfo(schoolInfo.name_cn, schoolInfo.name_en, schoolInfo.logo, '70%')
      s.alignment = 'left'
      let ref = ''
      let refs = voucher.ledgers.map(item => item.ref).filter(i => i)
      refs = this.uniqueArr(refs)
      if (refs.length > 0) {
        ref = ` [Ref : ${refs.join(',')}]`
      }
      const vc_date = voucher.vc_date
      let dateStr = ''
      if (vc_date) {
        dateStr = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
      }
      const layout = {
        defaultBorder: false,
        vLineWidth: lineWidth,
        hLineWidth: lineWidth,
        hLineColor: lineColor,
        vLineColor: lineColor,
        paddingLeft,
        paddingRight,
        paddingTop,
        paddingBottom,
      }

      let cheque = ''
      switch (voucher.vc_method) {
        case 'AUTO':
          cheque = 'AUTOPY'
          break
        case 'CHEQUE':
          cheque = voucher.ref
          break
        case 'CASH':
          cheque = 'CASH'
          break
        case 'TRANSF':
          cheque = 'TRANSF'
          break
        case 'OTHER':
          cheque = '|' + voucher.ref
          break
      }

      return {
        // Content
        width: '100%',
        style: 'tableExample',
        alignment: 'center',
        margin: [0, 0, 0, (index + 1) % 3 === 0 ? 0 : 30],
        table: {
          dontBreakRows: true,
          keepWithHeaderRows: 4,
          headerRows: 4,
          widths: ['*'],
          heights: [70, 50, 50, 50],
          body: [
            [
              {
                // 收據頁頭
                columns: [
                  s,
                  {
                    width: '30%',
                    table: {
                      body: [
                        [
                          {
                            style: 'tableExample',
                            alignment: 'center',
                            table: {
                              body: [
                                [
                                  {
                                    alignment: 'center',
                                    text: this.$t('message.receipt') + ' RECEIPT',
                                    fontSize: 15,
                                    bold: true,
                                    border: [false, false, false, true],
                                  },
                                ],
                                [
                                  {
                                    alignment: 'left',
                                    text: this.$t('message.numberNo') + '1. : ',
                                    fontSize: 15,
                                    bold: true,
                                    border: [false, false, false, false],
                                  },
                                ],
                              ],
                            },
                          },
                        ],
                      ],
                    },
                    layout,
                  },
                ],
              },
            ],
            [
              {
                // 第一�?                table: {
                  widths: [100, '*', 90, 100],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.receiptReceived'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.chequeNumber'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'Received from',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: voucher.vc_payee,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'Cheque No.',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: cheque,
                        border: [false, false, false, true],
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第二張
                table: {
                  widths: [90, 130, 100, '*'],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.totalAmount'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.payment'),
                      },
                      {
                        margin: [10, 0, 10, 0],
                        colSpan: 1,
                        rowSpan: 2,
                        height: 40,
                        maxHeight: 40,
                        style: (voucher.vc_summary + ref).length > 35 ? 'smallSummary' : 'summary',
                        text: voucher.vc_summary + ref,
                        border: [false, false, false, true],
                      },
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'The sum of',
                      },
                      {
                        margin: [20, 0, 20, 0],
                        text: 'HK$' + amountFormat(voucher.vc_amount),
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'in payment of',
                      },
                      '',
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第三張
                table: {
                  alignment: 'left',
                  widths: [110, '*', 110, '*'],
                  heights: [20, 20, 15, 15],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.receiptDate'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.payee'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'Date of Receipt :',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: dateStr,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'Received by :',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: '',
                        border: [false, false, false, true],
                      },
                    ],
                    [
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: schoolInfo.name_cn,
                        fontSize: 9,
                      },
                    ],
                    [
                      {
                        alignment: 'left',
                        text: `Our Ref. :${voucher.vc_no}`,
                        colSpan: 3,
                        fontSize: 9,
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: schoolInfo.name_en,
                        fontSize: 9,
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
          ],
        },
        layout,
      }
    },
    generateReceiptP2({ index, schoolInfo, voucher, printSetting }) {
      const s = generateSchoolInfo(schoolInfo.name_cn, schoolInfo.name_en, schoolInfo.logo, '70%')
      s.alignment = 'left'
      let ref = ''
      let refs = voucher.ledgers.map(item => item.ref).filter(i => i)
      refs = this.uniqueArr(refs)
      if (refs.length > 0) {
        ref = ` [Ref : ${refs.join(',')}]`
      }
      const vc_date = voucher.vc_date
      let dateStr = ''
      if (vc_date) {
        dateStr = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
      }
      const layout = {
        defaultBorder: false,
        vLineWidth: lineWidth,
        hLineWidth: lineWidth,
        hLineColor: lineColor,
        vLineColor: lineColor,
        paddingLeft,
        paddingRight,
        paddingTop,
        paddingBottom,
      }

      let cheque = ''
      switch (voucher.vc_method) {
        case 'AUTO':
          cheque = 'AUTOPY'
          break
        case 'CHEQUE':
          cheque = voucher.ref
          break
        case 'CASH':
          cheque = 'CASH'
          break
        case 'TRANSF':
          cheque = 'TRANSF'
          break
        case 'OTHER':
          cheque = '|' + voucher.ref
          break
      }
      console.log('s', s)
      return {
        // Content
        width: '30%',
        style: 'tableExample',
        alignment: 'right',
        margin: [0, 0, 0, (index + 1) % 3 === 0 ? 0 : 30],
        table: {
          dontBreakRows: true,
          keepWithHeaderRows: 4,
          headerRows: 4,
          widths: ['*'],
          heights: [70, 50, 50, 50],
          body: [
            [
              {
                // 收據頁頭
                columns: [
                  // s,
                  {
                    width: '70%',
                    alignment: 'right',
                    style: 'tableExample',
                    table: {
                      body: [
                        [
                          {
                            style: 'tableExample',
                            alignment: 'right',
                            table: {
                              body: [
                                [
                                  {
                                    alignment: 'right',
                                    text: this.$t('message.receipt') + ' RECEIPT',
                                    fontSize: 15,
                                    bold: true,
                                    border: [false, false, false, false],
                                  },
                                ],
                                // [{
                                //   alignment: 'left',
                                //   text: '編號 No. : ',
                                //   fontSize: 15,
                                //   bold: true,
                                //   border: [false, false, false, false]
                                // }]
                              ],
                            },
                          },
                        ],
                      ],
                    },
                    layout,
                  },
                ],
              },
            ],
            [
              {
                // 第一�?                table: {
                  widths: [100, '*', 90, 100],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.receiptReceived'),
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: schoolInfo.name_en,
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.chequeNumber'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'Received from',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: schoolInfo.name_cn,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'Cheque No.',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: cheque,
                        border: [false, false, false, true],
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第二張
                table: {
                  widths: [90, 130, 100, '*'],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.totalAmount'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.payment'),
                      },
                      {
                        margin: [10, 0, 10, 0],
                        colSpan: 1,
                        rowSpan: 2,
                        height: 40,
                        maxHeight: 40,
                        style: (voucher.vc_summary + ref).length > 35 ? 'smallSummary' : 'summary',
                        text: voucher.vc_summary + ref,
                        border: [false, false, false, true],
                      },
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'The sum of',
                      },
                      {
                        margin: [20, 0, 20, 0],
                        text: 'HK$' + amountFormat(voucher.vc_amount),
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'in payment of',
                      },
                      '',
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第三張
                table: {
                  alignment: 'left',
                  widths: [110, '*', 110, '*'],
                  heights: [20, 20, 15, 15],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.receiptDate'),
                      },
                      {
                        text: '',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('message.payee'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'Date of Receipt :',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: dateStr,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'left',
                        text: 'Received by :',
                      },
                      {
                        margin: [10, 0, 10, 0],
                        text: '',
                        border: [false, false, false, true],
                      },
                    ],
                    [
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: voucher.vc_payee,
                        fontSize: 9,
                      },
                    ],
                    [
                      {
                        alignment: 'left',
                        text: `Our Ref. :${voucher.vc_no}`,
                        colSpan: 3,
                        fontSize: 9,
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                      },
                      {
                        text: '',
                        fontSize: 9,
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
          ],
        },
        layout,
      }
    },
    generatePaymentSlip({
      index,
      schoolInfo,
      voucher,
      printSetting,
      tel,
      fax,
      address,
      address_en,
      acc_dept_cn,
      acc_dept_en,
    }) {
      const s = generateSchoolInfo(schoolInfo.name_cn, schoolInfo.name_en, schoolInfo.logo, '70%')
      s.alignment = 'left'
      let ref = ''
      let refs = voucher.ledgers.map(item => item.ref).filter(i => i)
      refs = this.uniqueArr(refs)
      if (refs.length > 0) {
        ref = ` [Ref : ${refs.join(',')}]`
      }
      const vc_date = voucher.vc_date
      let dateStr = ''
      if (vc_date) {
        dateStr = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
      }
      const layout = {
        defaultBorder: false,
        vLineWidth: lineWidth,
        hLineWidth: lineWidth,
        hLineColor: lineColor,
        vLineColor: lineColor,
        paddingLeft,
        paddingRight,
        paddingTop,
        paddingBottom,
      }
      const layout2 = {
        defaultBorder: true,
        vLineWidth: lineWidth,
        hLineWidth: lineWidth,
        hLineColor: '#CACACA',
        vLineColor: '#CACACA',
        paddingLeft,
        paddingRight,
        paddingTop,
        paddingBottom,
      }
      let cheque = ''
      switch (voucher.vc_method) {
        case 'AUTO':
          cheque = 'AUTOPY'
          break
        case 'CHEQUE':
          cheque = voucher.ref
          break
        case 'CASH':
          cheque = 'CASH'
          break
        case 'TRANSF':
          cheque = 'TRANSF'
          break
        case 'OTHER':
          cheque = '|' + voucher.ref
          break
      }
      console.log(voucher, 'voucher')
      const refText = voucher.ledgers.map(item => item.ref).join(',')
      const year = dateUtil.format(new Date(voucher.vc_date), 'yyyy')
      const month = dateUtil.format(new Date(voucher.vc_date), 'MM')
      const day = dateUtil.format(new Date(voucher.vc_date), 'dd')
      return {
        // Content
        width: '100%',
        style: 'tableExample',
        alignment: 'center',
        margin: [0, 0, 0, 30],
        table: {
          dontBreakRows: true,
          keepWithHeaderRows: 7,
          headerRows: 7,
          widths: ['*'],
          heights: ['auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto'],
          body: [
            [
              {
                // 第一�?                table: {
                  widths: [28, 'auto', '*', 0],
                  body: [
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: this.$t('setting.printout.navMenu.to'),
                      },
                      '',
                      {
                        margin: [10, 0, 10, 0],
                        alignment: 'right',
                        text: `Our Ref. :${voucher.vc_no}`,
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 10, 0],
                        alignment: 'left',
                        text: 'To:',
                      },
                      {
                        margin: [0, 0, 0, 0],
                        alignment: 'left',
                        text: voucher.vc_payee,
                        bold: true,
                        border: [false, false, false, true],
                      },
                      '',
                      '',
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第二張
                table: {
                  widths: ['auto', '*', 'auto', 'auto', 'auto'],
                  body: [
                    [
                      {
                        margin: [35, 5, 10, 0],
                        alignment: 'left',
                        text: this.$t('setting.printout.navMenu.enclosed'),
                      },
                      '',
                      '',
                      {
                        margin: [3, 8, 3, 0],
                        alignment: 'left',
                        text: this.$t('setting.printout.navMenu.amount'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [35, 0, 10, 0],
                        alignment: 'left',
                        text: 'Enclosed please find one Bank cheque No.',
                      },
                      '',
                      {
                        margin: [25, 0, 25, 0],
                        text: voucher.ref,
                        noWrap: true,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [3, 0, 3, 0],
                        alignment: 'left',
                        text: 'amount',
                      },
                      {
                        margin: [25, 0, 25, 0],
                        text: `$${amountFormat(voucher.vc_amount)}`,
                        noWrap: true,
                        border: [false, false, false, true],
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第三張
                table: {
                  widths: ['auto', '*', 'auto', 'auto'],
                  body: [
                    [
                      {
                        margin: [0, 5, 0, 0],
                        alignment: 'left',
                        text: this.$t('setting.printout.navMenu.in_payment_of'),
                      },
                      '',
                      {
                        margin: [3, 5, 0, 0],
                        alignment: 'left',
                        text: this.$t('setting.printout.navMenu.with_invoice'),
                      },
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 0, 0],
                        alignment: 'left',
                        text: 'In payment of',
                      },
                      {
                        margin: [0, 0, 0, 0],
                        text: voucher.vc_summary,
                        noWrap: true,
                        border: [false, false, false, true],
                      },
                      {
                        margin: [3, 0, 3, 0],
                        alignment: 'left',
                        text: 'with invoice(s) No.',
                      },
                      {
                        margin: [20, 0, 20, 0],
                        text: refText,
                        noWrap: true,
                        border: [false, false, false, true],
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第四張
                table: {
                  widths: ['auto', '*', 'auto'],
                  body: [
                    [
                      {
                        margin: [0, 10, 0, 0],
                        alignment: 'left',
                        text: this.$t('setting.printout.navMenu.please_issue_receipt'),
                      },
                      '',
                      '',
                    ],
                    [
                      {
                        margin: [0, 0, 0, 0],
                        alignment: 'left',
                        text: 'Please issure receipt!',
                      },
                      '',
                      {
                        margin: [0, 0, 0, 0],
                        text: acc_dept_cn.value,
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第五張
                table: {
                  widths: ['*'],
                  body: [
                    [
                      {
                        margin: [0, 0, 0, 0],
                        alignment: 'right',
                        text: acc_dept_en.value,
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第六張
                table: {
                  widths: ['*'],
                  body: [
                    [
                      {
                        margin: [0, 5, 0, 5],
                        alignment: 'right',
                        text: `${year}${this.$t(
                          'setting.printout.navMenu.year',
                          language,
                        )}${month}${this.$t(
                          'setting.printout.navMenu.month',
                          language,
                        )}${day}${this.$t('setting.printout.navMenu.day')}`,
                      },
                    ],
                  ],
                },
                layout,
              },
            ],
            [
              {
                // 第七張
                table: {
                  widths: ['*'],
                  body: [
                    [
                      [
                        {
                          // 第七張
                          table: {
                            widths: ['*'],
                            body: [
                              [
                                {
                                  alignment: 'center',
                                  text:
                                    this.$t('setting.printout.navMenu.address') +
                                    address.value,
                                },
                              ],
                              [
                                {
                                  alignment: 'center',
                                  text:
                                    this.$t(
                                      'setting.printout.navMenu.Please_end_receipts_to',
                                      language,
                                    ) + address_en.value,
                                },
                              ],
                              [
                                {
                                  table: {
                                    widths: ['*', '*', '*', '*'],
                                    alignment: 'center',
                                    body: [
                                      [
                                        {
                                          border: [false, false, false, false],
                                          text: '',
                                        },
                                        {
                                          border: [false, false, false, false],
                                          text:
                                            this.$t('setting.printout.navMenu.Tel') +
                                            tel.value,
                                        },
                                        {
                                          border: [false, false, false, false],
                                          text:
                                            this.$t('setting.printout.navMenu.Fax') +
                                            fax.value,
                                        },
                                        {
                                          border: [false, false, false, false],
                                          text: '',
                                        },
                                      ],
                                    ],
                                  },
                                },
                              ],
                            ],
                          },
                          layout,
                        },
                      ],
                    ],
                  ],
                },
                layout: layout2,
              },
            ],
          ],
        },
        layout,
      }
    },
    generatePDFReceipt1({ title, schoolInfo, data, printSetting }) {
      return new Promise((resolve, reject) => {
        const margin_left = mm2pt(printSetting.margin_left)
        const margin_top = mm2pt(printSetting.margin_top)
        const margin_right = mm2pt(printSetting.margin_right)
        const margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // * 2// + 10 // 預留頁尾位置

        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const docDefinition = {
          info: {
            title: title,
            author: 'Norray',
            subject: title,
          },
          content: data,
          styles: {
            tableExample: {
              fontSize: 12,
              margin: [0, 0, 0, 0],
            },
            schoolNameCN: {
              fontSize: 14,
              margin: [0, 0, 0, 0],
            },
            schoolNameEN: {
              fontSize: 14,
              margin: [0, 0, 0, 0],
            },
            smallSummary: {
              fontSize: 9,
              alignment: 'center',
            },
            summary: {
              alignment: 'center',
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    /**
     *  ****************    郵寄標籤           ********************
     * */
    async handlerPrintMailingLabel(company_id_list) {
      if (!isInitPDF) {
        return initTips()
      }
      const t_ps_code = 'pdlabel'

      const printSetting = await this.loadPrintoutSetting(t_ps_code)

      const margin = mm2pt(printSetting.margin)
      const col_num = printSetting.col_num
      const row_num = printSetting.row_num
      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)
      const font_size = printSetting.font_size

      const cell_width = page_width / col_num
      const cell_height = page_height / row_num

      const data = []
      const company_list = await companiesAddressPrintList(company_id_list.join(','))
      // 公司列表
      let rowIndex = 0
      for (let i = 0; i < company_list.length; i++) {
        rowIndex = parseInt(i / col_num)
        const colIndex = i % col_num
        if (colIndex === 0) {
          data.push([])
        }

        const company = company_list[i]
        const name = company.comp_name || ''
        const attention = company.comp_attention || ''
        const address =
          (company.comp_add1 ? '\n' + company.comp_add1 : '') +
          (company.comp_add2 ? '\n' + company.comp_add2 : '') +
          (company.comp_add3 ? '\n' + company.comp_add3 : '') +
          (company.comp_add4 ? '\n' + company.comp_add4 : '')

        const label = this.generateMailingLabel({
          index: i,
          name,
          address,
          attention,
          width: cell_width,
          height: cell_height,
          margin,
          font_size,
        })
        data[rowIndex].push(label)
      }
      const empty = this.generateMailingLabel({
        index: 0,
        name: '',
        address: '',
        attention: '',
        width: cell_width,
        height: cell_height,
        margin,
      })
      // fill column
      if (data[rowIndex].length !== col_num) {
        const eLen = col_num - data[rowIndex].length
        for (let i = 0; i < eLen; i++) {
          data[rowIndex].push(empty)
        }
      }
      // fill row
      if (rowIndex > 0 && rowIndex % row_num !== row_num - 1) {
        const dRow = row_num - (rowIndex % row_num) - 1
        data.push(...[...Array(dRow)].map(() => [...Array(col_num)].map(() => empty)))
      }

      const title = this.$t('setting.printout.navMenu.daily_mailing_label')
      this.$store.commit('setPrintList', {
        name: title,
        status: 0,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        finishTime: '',
        url: '',
      })
      const doc = await this.generatePDFMailingLabel({
        title,
        data,
        printSetting,
      })
      doc.printListIndex = this.$store.state.printList.printList.length - 1
      console.log(JSON.stringify(doc))
      await openPdf(doc)
    },

    generateMailingLabel({ index, name, address, attention, width, height, margin, font_size }) {
      const paddingInsert = insertData(`function() { return margin }`, { margin })
      const layout = {
        defaultBorder: false,
        vLineWidth: lineWidth,
        hLineWidth: lineWidth,
        hLineColor: lineColor,
        vLineColor: lineColor,
        paddingLeft: paddingInsert,
        paddingRight: paddingInsert,
        paddingTop: paddingInsert,
        paddingBottom: paddingInsert,
      }

      return {
        // Content
        // width: '100%',
        alignment: 'left',
        border: [false, false, true, true],
        margin: [0, 0, 0, 0],
        table: {
          dontBreakRows: false,
          keepWithHeaderRows: 0,
          headerRows: 0,
          widths: [width - margin * 2],
          heights: [height - margin * 2 - 0.2],
          body: [
            [
              {
                fontSize: font_size,
                fillColor: '#fff',
                // text: text,
                text: [
                  { text: name, bold: true },
                  { text: address },
                  attention
                    ? { text: [{ text: '\n\nAttn. :', bold: true }, { text: attention }] }
                    : undefined,
                ],
                maxHeight: height - margin * 2 - 0.2,
              },
            ],
          ],
        },
        layout,
      }
    },
    generatePDFMailingLabel({ title, data, printSetting }) {
      return new Promise((resolve, reject) => {
        // const margin = mm2pt(printSetting.margin)
        const dividing_line = printSetting.dividing_line
        const lineColor = '' + dividing_line === '1' ? '#dcdcdc' : '#fff'
        const layout = {
          defaultBorder: false,
          hLineColor: lineColor,
          vLineColor: lineColor,
          paddingLeft,
          paddingRight,
          paddingTop,
          paddingBottom,
        }

        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const col_num = printSetting.col_num
        const row_num = printSetting.row_num

        const cell_width = page_width / col_num
        const cell_height = page_height / row_num
        const docDefinition = {
          info: {
            title: title,
            author: 'Norray',
            subject: title,
          },
          content: {
            // Content
            width: '100%',
            // style: 'tableExample',
            alignment: 'left',
            margin: [0, 0, 0, 0],
            table: {
              dontBreakRows: false,
              keepWithHeaderRows: 0,
              headerRows: 0,
              widths: [...Array(col_num)].map(() => cell_width - 0.2 * col_num),
              heights: [...Array(row_num)].map(() => cell_height - 0.2 * row_num),
              body: data,
            },
            layout,
          },
          styles: {
            tableExample: {
              fontSize: 12,
              margin: [0, 0, 0, 0],
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [0, 0, 0, 0],
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    /**
     *  ****************    信封           ********************
     * */
    async handlerPrintEnvepole(company_id_list) {
      if (!isInitPDF) {
        return initTips()
      }
      const t_ps_code = 'pdfenvelope'

      const printSetting = await this.loadPrintoutSetting(t_ps_code)

      const position_x = mm2pt(printSetting.position_x)
      const position_y = mm2pt(printSetting.position_y)
      const offset_x = mm2pt(printSetting.offset_x)
      const offset_y = mm2pt(printSetting.offset_y)
      const font_size = printSetting.font_size

      const data = []
      const company_list = await companiesAddressPrintList(company_id_list.join(','))
      // 公司列表
      for (let i = 0; i < company_list.length; i++) {
        const company = company_list[i]
        const name = company.comp_name || ''
        const attention = company.comp_attention || ''
        const address =
          (company.comp_add1 ? company.comp_add1 : '') +
          (company.comp_add2 ? '\n' + company.comp_add2 : '') +
          (company.comp_add3 ? '\n' + company.comp_add3 : '') +
          (company.comp_add4 ? '\n' + company.comp_add4 : '')

        const envelope = this.generateEnvelope({
          index: i,
          name,
          address,
          attention,
          position_x,
          position_y,
          offset_x,
          offset_y,
          font_size,
        })
        data.push(envelope)
      }

      const title = this.$t('setting.printout.navMenu.daily_envelope')
      this.$store.commit('setPrintList', {
        name: title,
        status: 0,
        startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        finishTime: '',
        url: '',
      })
      const doc = await this.generatePDFEnvelope({
        title,
        data,
        printSetting,
      })
      doc.printListIndex = this.$store.state.printList.printList.length - 1
      await openPdf(doc)
    },

    generateEnvelope({
      index,
      name,
      address,
      attention,
      position_x,
      position_y,
      offset_x,
      offset_y,
      font_size,
    }) {
      return {
        absolutePosition: { x: position_x + offset_x, y: position_y + offset_y },
        headlineLevel: index > 0 ? 1 : 0,
        pageBreak: index > 0 ? 'before' : undefined,
        alignment: 'left',
        fontSize: font_size,
        table: {
          dontBreakRows: true,
          keepWithHeaderRows: 3,
          headerRows: 3,
          widths: ['auto', '*'],
          body: [
            [
              {
                text: 'To:',
                bold: true,
              },
              {
                text: name,
                bold: true,
              },
            ],
            [
              {
                text: '',
              },
              {
                text: address,
              },
            ],
            [
              {
                text: '',
              },
              {
                text: attention ? 'Attn. :' + attention : '',
              },
            ],
          ],
        },
        layout: 'noBorders',
      }
    },
    generatePDFEnvelope({ title, data, printSetting }) {
      return new Promise((resolve, reject) => {
        const page_width = mm2pt(printSetting.page_width + printSetting.offset_x)
        const page_height = mm2pt(printSetting.page_height + printSetting.offset_y)

        const docDefinition = {
          info: {
            title: title,
            author: 'Norray',
            subject: title,
          },
          content: data,
          styles: {
            tableExample: {
              // fontSize: 12,
              margin: [0, 0, 0, 0],
            },
          },
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [0, 0, 0, 0],
          pageBreakBefore: function(
            currentNode,
            followingNodesOnPage,
            nodesOnNextPage,
            previousNodesOnPage,
          ) {
            return currentNode.headlineLevel === 1
          },
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    /**
     *  ****************    支票           ********************
     * */
    async handlerPrintCheque(voucher, chq_template_code, isPreview) {
      if (!isInitPDF) {
        return initTips()
      }
      const t_ps_code = 'pdfcheque'
      let printSetting
      try {
        printSetting = await this.loadPrintoutSetting(t_ps_code, null, chq_template_code)
      } catch (e) {
        this.$message.error(this.$t('message.printSettingEmpty'))
        return
      }
      const language = printSetting.language

      const isHideAC =
        voucher.vt_category === 'P' &&
        voucher.vc_payee
          .toLowerCase()
          .split(' ')
          .some(i => i === 'cash')
      const data = await this.generateCheque({
        payee: voucher.vc_payee,
        date: voucher.vc_chq_date,
        amount: voucher.vc_amount,
        chq_template_code,
        printSetting,
        isPreview,
        isHideAC,
      })

      const title = this.$t('setting.printout.navMenu.daily_cheque')
      const doc = await this.generatePDFCheque({
        title,
        data,
        printSetting,
      })
      await openPdf(doc)
    },

    async generateCheque({
      payee,
      date,
      amount,
      chq_template_code,
      printSetting,
      isPreview,
      isHideAC,
    }) {
      const showUnderline = chq_template_code && !chq_template_code.includes('_')
      const showChequeStub = chq_template_code && chq_template_code.includes('-')
      const language = printSetting.language
      const cheque_width = mm2pt(printSetting.cheque_width)
      const cheque_height = mm2pt(printSetting.cheque_height)
      const offset_x = mm2pt(printSetting.offset_x)
      const offset_y = mm2pt(printSetting.offset_y)
      const amount_x = mm2pt(printSetting.amount_x) + offset_x
      const amount_y = mm2pt(printSetting.amount_y) + offset_y
      const date_x = mm2pt(printSetting.date_x) + offset_x
      const date_y = mm2pt(printSetting.date_y) + offset_y
      const payee_x = mm2pt(printSetting.payee_x) + offset_x
      const payee_y = mm2pt(printSetting.payee_y) + offset_y
      const text_amount_x = mm2pt(printSetting.text_amount_x) + offset_x
      const text_amount_y = mm2pt(printSetting.text_amount_y) + offset_y
      const ac_payee_only_x = mm2pt(printSetting.ac_payee_only_x) + offset_x
      const ac_payee_only_y = mm2pt(printSetting.ac_payee_only_y) + offset_y
      const crossed_bearer_x = mm2pt(printSetting.crossed_bearer_x) + offset_x
      const crossed_bearer_y = mm2pt(printSetting.crossed_bearer_y) + offset_y
      // 支票存根
      const stub_date_x = mm2pt(printSetting.stub_date_x) + offset_x
      const stub_date_y = mm2pt(printSetting.stub_date_y) + offset_y
      const stub_payee_x = mm2pt(printSetting.stub_payee_x) + offset_x
      const stub_payee_y = mm2pt(printSetting.stub_payee_y) + offset_y
      const stub_amount_x = mm2pt(printSetting.stub_amount_x) + offset_x
      const stub_amount_y = mm2pt(printSetting.stub_amount_y) + offset_y
      const stub_desc_x = mm2pt(printSetting.stub_desc_x) + offset_x
      const stub_desc_y = mm2pt(printSetting.stub_desc_y) + offset_y
      const stub_payee_width = mm2pt(printSetting.stub_payee_width)
      const stub_font_size = printSetting.stub_font_size

      const font_size = printSetting.font_size
      const font_weight = printSetting.font_weight === 1
      const payee_width = mm2pt(printSetting.payee_width)
      const text_width = mm2pt(printSetting.text_width)
      const text_indent = printSetting.text_indent
      const line_height = printSetting.line_height
      const crossed_width = mm2pt(printSetting.crossed_width)
      const crossed_space = mm2pt(printSetting.crossed_space)

      const amount_n = `**${amountFormat(amount)}**`
      let text_amount = amount_n
      switch (language) {
        case 'en':
          text_amount = chequeConvertEnglish(amount)
          break
        case 'cn':
          text_amount = chequeConvertCN(amount)
          break
        case 'numeric':
          text_amount = amount_n
          break
        default:
          text_amount = chequeConvertEnglish(amount)
          break
      }

      const line_width = 0.3
      const line_space = 1

      const rsi = this.remoteServerInfo
      const background_img_url = isPreview
        ? `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/cheque/cheque_${chq_template_code}.gif`
        : ''
      const background_img = isPreview
        ? await getUrlBase64(background_img_url)
        : getEmptyImageBase64()
      const ac_payee_only_img_url = require('@/assets/cheque/ac-payee-only-200-3.png')
      const ac_payee_only_img = await getUrlBase64(ac_payee_only_img_url)

      const dataStr = dateUtil.format(new Date(date), 'dd/MM/yyyy')

      const arr = [
        {
          // 背景�?          image: background_img,
          // text: '背景�?,
          width: cheque_width,
          // height: cheque_height,
          absolutePosition: { x: offset_x, y: offset_y },
        },
        {
          // 付款�?          absolutePosition: { x: payee_x, y: payee_y },
          width: payee_width,
          text: payee,
          fontSize: font_size,
          bold: font_weight,
        },
        {
          // 日期
          absolutePosition: { x: date_x, y: date_y },
          text: dataStr,
          fontSize: font_size,
          bold: font_weight,
        },
        {
          // 大寫金額
          absolutePosition: { x: text_amount_x, y: text_amount_y },
          fontSize: font_size,
          bold: font_weight,
          columns: [
            {
              // background: 'red',
              width: text_width,
              text: text_amount,
              lineHeight: line_height,
              leadingIndent: text_indent,
            },
          ],
        },
        {
          // 小寫金額
          absolutePosition: { x: amount_x, y: amount_y },
          text: amount_n,
          fontSize: font_size,
          bold: font_weight,
        },
      ]
      if (showUnderline && !isHideAC) {
        arr.push(
          {
            // A/C Payee Only
            image: ac_payee_only_img,
            // text: 'A/C Payee Only',
            width: cheque_height / 3,
            height: cheque_height / 3,
            absolutePosition: { x: ac_payee_only_x, y: ac_payee_only_y },
          },
          {
            // 刪或持票�?            absolutePosition: { x: crossed_bearer_x, y: crossed_bearer_y },
            canvas: [
              {
                // 'lineColor': 'gray',
                type: 'line',
                x1: 0,
                y1: 0,
                x2: crossed_width,
                y2: 0,
                lineWidth: line_width,
              },
              {
                type: 'line',
                x1: 0,
                y1: line_space,
                x2: crossed_width,
                y2: line_space,
                lineWidth: line_width,
              },
              {
                type: 'line',
                x1: 0,
                y1: crossed_space,
                x2: crossed_width,
                y2: crossed_space,
                lineWidth: line_width,
              },
              {
                type: 'line',
                x1: 0,
                y1: crossed_space + line_space,
                x2: crossed_width,
                y2: crossed_space + line_space,
                lineWidth: line_width,
              },
            ],
          },
        )
      }
      if (showChequeStub) {
        arr.push(
          {
            // 票根受款�?            absolutePosition: { x: stub_payee_x, y: stub_payee_y },
            fontSize: stub_font_size,
            bold: font_weight,
            columns: [
              {
                // background: 'red',
                width: stub_payee_width,
                text: payee,
                // lineHeight: line_height,
                // leadingIndent: text_indent
              },
            ],
          },
          {
            // 小寫金額
            absolutePosition: { x: stub_amount_x, y: stub_amount_y },
            text: amount_n,
            fontSize: stub_font_size,
            bold: font_weight,
          },
          {
            // 日期
            absolutePosition: { x: stub_date_x, y: stub_date_y },
            text: dataStr,
            fontSize: stub_font_size,
            bold: font_weight,
          },
        )
      }
      return arr
    },
    generatePDFCheque({ title, data, printSetting }) {
      return new Promise((resolve, reject) => {
        const page_width = mm2pt(printSetting.page_width)
        const page_height = mm2pt(printSetting.page_height)

        const docDefinition = {
          info: {
            title: title,
            author: 'Norray',
            subject: title,
          },
          content: data,
          styles: {},
          pageSize: {
            width: page_width,
            height: page_height,
          },
          pageMargins: [0, 0, 0, 0],
          pageBreakBefore: function(
            currentNode,
            followingNodesOnPage,
            nodesOnNextPage,
            previousNodesOnPage,
          ) {
            return currentNode.headlineLevel === 1
          },
        }
        // console.log(JSON.stringify(docDefinition))
        // openPdf(docDefinition)
        resolve(docDefinition)
      })
    },
    uniqueArr(array) {
      var r = []
      for (var i = 0, l = array.length; i < l; i++) {
        for (var j = i + 1; j < l; j++) {
          if (array[i] === array[j]) j === ++i
        }
        r.push(array[i])
      }
      return r
    },
  },
}
</script>
