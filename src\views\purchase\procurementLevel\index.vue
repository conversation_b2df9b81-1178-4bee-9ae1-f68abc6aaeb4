<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div v-if="hasPermission_Add" class="icon add" style="cursor: pointer" @click="onAdd">
            <svg-icon icon-class="add" />
          </div>
          <!-- 按鈕 -->
          <!--          <div v-if="hasPermission_Input" class="icon import" @click="importDialog = true">-->
          <!--            <svg-icon icon-class="import"/>-->
          <!--          </div>-->
          <!--  -->
          <!--          <div v-if="hasPermission_Output" class="icon export" @click="onExport">-->
          <!--            <svg-icon icon-class="export"/>-->
          <!--          </div>-->
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          ref="table"
          v-loading="loading"
          :data="tableData"
          :style-columns="styleColumns"
          :lang-key="langKey"
          :actions-min-width="135"
          class="e-table"
          border
          @changeWidth="changeColumnWidth"
        >
          <template slot="columns">
            <el-table-column
              v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
              :key="item.ss_key"
              :label="$t(langKey + item.ss_key)"
              :align="item.alignment"
              :width="item.width"
              :property="column_property(item)"
              :column-key="item.ss_key"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="item.ss_key === 'amount_range'">
                  {{
                    '$' +
                      (scope.row['min_amount'] / 1000).toFixed(0) +
                      ' - ' +
                      (scope.row['max_amount'] / 1000).toFixed(0) +
                      'K'
                  }}
                </span>
                {{ scope.row[column_property(item)] }}
              </template>
            </el-table-column>
          </template>
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
              <i
                v-if="hasPermission_Copy"
                class="edac-icon edac-icon-copy-file"
                @click="onCopy(scope)"
              />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit' || leftView === 'copy'"
        :edit-object="editObject"
        :default-account="preferences.filters.childrenFund ? '' : preferences.filters.account"
        :fy-code="preferences.filters.year"
        :view="leftView"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <!--    <el-dialog-->
    <!--      v-loading="loading"-->
    <!--      :title="$t('file.excelImport')"-->
    <!--      :visible.sync="importDialog"-->
    <!--      width="450px"-->
    <!--      class="dialog"-->
    <!--    >-->
    <!--      <UploadExcel :on-success="onImport" :on-template="onExport"/>-->
    <!--    </el-dialog>-->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'

import ETable from '@/components/ETable'
import UploadExcel from '@/components/UploadExcel/index'
import SelectTree from '@/components/SelectTree/index.vue'

// import TreeSelect from '@riophae/vue-treeselect'
// import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

// 導出Excel
import { deleteProcurementLevel, fetchProcurementLevels } from '@/api/purchase/procurementLevels'

export default {
  name: 'MasterProcurementLevelIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    ETable,
    UploadExcel,
    SelectTree,
    // TreeSelect
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',

      editObject: null,

      tableData: [],
      years: [],
      funds: [],
      accountOptions: [], // 會計科目，動態
      importDialog: false,
      loading: false,

      langKey: 'purchase.master.procurementLevel.',
      tableColumns: [
        'pro_level_code',
        'pro_level_name_',
        'pro_no_format',
        'amount_range',
        'suppliers_at_least',
        'review',
        'approve',
      ],
      desc: '',
      preferences: {
        filters: {
          year: '', // fy_code 活躍的會計週期編號
          fund: '', // fund_id 賬目類別id
          account: '', // ac_code 會計科目編號
          childrenFund: '', // ac_code 會計科目編號
          desc: '', // desc
        },
      },
      exportFileName: 'description_list', // 導出文件名
    }
  },
  computed: {
    ...mapGetters(['language']),
    vt_category_list() {
      return ['P', 'R', 'C', 'T', 'J', 'A'].map(i => {
        return {
          label: this.$t('voucher_type_category.' + i),
          value: i,
        }
      })
    },
    defaultAccounts() {
      return [this.allAccount]
    },
    allAccount() {
      return {
        name_cn: this.$t('master.account.all_account'),
        name_en: this.$t('master.account.all_account'),
        abbr_cn: this.$t('master.account.all_account'),
        abbr_en: this.$t('master.account.all_account'),
        fund_id: '',
        account_id: '',
        code: '',
      }
    },
    allFund() {
      return {
        label: this.$t('master.fund.all_fund2'),
        value: '',
      }
    },
    allYear() {
      return {
        label: this.$t('master.year.all_year'),
        value: '',
      }
    },
  },
  created() {
    this.accountOptions = this.defaultAccounts
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    onAdd(scope) {
      this.editObject = null
      this.leftView = 'add'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    nameAdd(name, type = 1) {
      const reg = new RegExp(/(.*)\((\d+)\)$/)
      const nameReg = name.match(reg)
      if (nameReg) {
        if (type === 1) {
          return nameReg[1] + '(' + (Number(nameReg[2]) + 1) + ')'
        } else {
          return nameReg[1] + '-' + (Number(nameReg[2]) + 1)
        }
      } else {
        if (type === 1) {
          return name + '(1)'
        } else {
          return name + '-1'
        }
      }
    },
    async onCopy(scope) {
      this.editObject = scope.row
      this.leftView = 'copy'
      // try {
      //   const item = await getProcurementLevel(scope.row.pro_level_id)
      //   const newItem = JSON.parse(JSON.stringify(item))
      //   newItem.pro_level_id = ''
      //   newItem.pro_level_name_cn = this.nameAdd(newItem.pro_level_name_cn)
      //   newItem.pro_level_name_en = this.nameAdd(newItem.pro_level_name_en)
      //   newItem.pro_level_code = this.nameAdd(newItem.pro_level_code, 2)
      //
      //   newItem.review_handlers_json = JSON.stringify(newItem.review_handlers.map(row => {
      //     return row.map(item => ({
      //       type: item.staff_id ? 'S' : 'F',
      //       mandatory: 'Y',
      //       staff_id: item.staff_id || null,
      //       auto: item.auto
      //     }))
      //   }))
      //   newItem.approve_handlers_json = JSON.stringify(newItem.approve_handlers.map(row => {
      //     return row.map(item => ({
      //       type: item.staff_id ? 'S' : 'F',
      //       mandatory: 'Y',
      //       staff_id: item.staff_id || null,
      //       auto: item.auto
      //     }))
      //   }))
      //   try {
      //     const res = await createProcurementLevel({
      //       ...newItem
      //     })
      //
      //     console.log(res)
      //     this.$message.success(this.$t('message.success'))
      //     this.onViewCancel()
      //     this.reloadData()
      //   } catch (e) {
      //     console.log(e)
      //   }
      // } catch (e) {
      //   console.log(e)
      // }
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.pro_level_name_en : scope.row.pro_level_name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const pro_level_id = scope.row.pro_level_id
          return new Promise((resolve, reject) => {
            deleteProcurementLevel(pro_level_id)
              .then(res => {
                if (this.editObject && this.editObject.pro_level_id === pro_level_id) {
                  this.onViewCancel()
                }
                this.reloadData()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      // 獲取所有會計週期
      this.reloadData()
      // fetchYears()
      //   .then(res => {
      //     this.years = res
      //   })
      //   // 帳目類別
      //   .then(() => fetchFunds({ fund_type: 'F' }))
      //   .then(res => {
      //     this.funds = res
      //   })
      //   .then(this.loadUserPreference)
      //   // 帳目類別
      //   .then(() => {
      //     let bool = false
      //     this.funds.forEach(i => {
      //       if (i.fund_id === this.preferences.filters.fund) {
      //         bool = true
      //         return
      //       }
      //     })
      //     if (!bool) {
      //       this.preferences.filters.fund = ''
      //     }
      //
      //     bool = false
      //     this.years.forEach(i => {
      //       if (i.fy_code === this.preferences.filters.year) {
      //         bool = true
      //         return
      //       }
      //     })
      //     if (!bool) {
      //       this.preferences.filters.year = ''
      //     }
      //
      //     // 異步獲取了
      //     if (this.preferences.filters.fund) {
      //       return this.loadAccountTree(this.preferences.filters.fund)
      //     } else {
      //       this.accountOptions = this.defaultAccounts
      //       return Promise.resolve()
      //     }
      //   })
      //   .then(this.reloadData)
      //   .catch(() => {})
    },
    reloadData() {
      this.loading = true
      fetchProcurementLevels()
        .then(res => {
          this.tableData = res
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.reloadData()
      }
    },
    column_property(item) {
      if (item.ss_key === '_index') {
        return '#'
      } else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key + (this.language === 'en' ? 'en' : 'cn')
      } else {
        return item.ss_key
      }
    },
    column_label(item) {
      if (item.ss_key === '_index') {
        return '#'
      } else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key.substring(item.ss_key.length - 1)
      } else {
        return item.ss_key
      }
    },
    repeat(str, n) {
      return new Array(n + 1).join(str)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .fund,
  .account,
  .year {
    width: 130px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}

.tree-select {
  width: 402px;
  label {
    color: #606266;
    font-weight: normal;
  }
  margin-top: 5px;
  vertical-align: bottom;
  /deep/ .vue-treeselect__control {
    border: 1px solid #dcdfe6;
  }
  /deep/ .vue-treeselect__menu {
    border: 1px solid #dcdfe6;
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.e-table {
  .operation_icon {
    .edac-icon-copy-file {
      font-size: 14px;
    }
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
.filter {
  .filter-icon {
    background: #68afff;
    border-radius: 4px;
    font-size: 20px !important;
    cursor: pointer;
    position: relative;
    display: inline-block;
    line-height: 20px;

    width: 20px;
    text-align: center;
    height: 20px;
    vertical-align: middle;
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
      width: 1.5em;
      height: 1.5em;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
    i {
      display: block;
      width: 20px;
      vertical-align: middle;
      text-align: center;
      color: white;
      line-height: 20px !important;
    }
  }
}

.vue-treeselect {
  display: inline-block;
  position: relative;
  width: 425px;

  .vue-treeselect__control {
    line-height: 25px;
    height: 25px;
    vertical-align: middle;
    .vue-treeselect__placeholder,
    .vue-treeselect__single-value {
      line-height: 25px;
      height: 25px;
      vertical-align: middle;
    }
  }
}
.dialog {
  .el-dialog__body {
    height: auto !important;
  }
}
</style>
