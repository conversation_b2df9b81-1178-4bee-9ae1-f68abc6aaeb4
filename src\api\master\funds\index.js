import request from '@/utils/request'

/**
 * 返回新增后的賬目類別id
 * @param {string} fund_code 賬目類別編號
 * @param {string} fund_name_cn 賬目類別中文
 * @param {string} fund_name_en 賬目類別英文
 * @param {string} fund_abbr_cn 賬目類別中文簡稱
 * @param {string} fund_abbr_en 賬目類別英文簡稱
 * @param {string} fund_type 賬目類別類型,F=賬目類別(大類),G=賬目類別(細類)
 * @param {integer} nature_code 賬目性質編號,fund_type=G時必須傳,津貼=G,收入/支出=I/E,收入=I,支出=E,借方結餘=Dr,貸方結餘=Cr
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {string} income_bg_year 收入預算中活躍的會計週期編號,格式: 17,18,...
 * @param {string} expense_bg_year 支出預算中活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_fund_id 父賬目類別id,fund_type=G時必須傳
 * @param {integer} seq 所處層級里的位置,fund_type=G時必須傳
 */
export function createFund(
  fund_code,
  fund_name_cn,
  fund_name_en,
  fund_abbr_cn,
  fund_abbr_en,
  fund_type,
  active_year,
  nature_code,
  income_bg_year,
  expense_bg_year,
  parent_fund_id,
  seq,
) {
  return request({
    url: '/funds/actions/create',
    method: 'post',
    data: {
      fund_code,
      fund_name_cn,
      fund_name_en,
      fund_abbr_cn,
      fund_abbr_en,
      fund_type,
      active_year,
      nature_code,
      income_bg_year,
      expense_bg_year,
      parent_fund_id,
      seq,
    },
  })
}

/**
 * 修改賬目類別
 * @param {integer} fund_id 賬目類別id
 * @param {string} fund_code 賬目類別編號
 * @param {string} fund_name_cn 賬目類別中文
 * @param {string} fund_name_en 賬目類別英文
 * @param {string} fund_abbr_cn 賬目類別中文簡稱
 * @param {string} fund_abbr_en 賬目類別英文簡稱
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...

 */
export function editFund(
  fund_id,
  fund_code,
  fund_name_cn,
  fund_name_en,
  fund_abbr_cn,
  fund_abbr_en,
  fund_type,
  active_year,
  nature_code,
  income_bg_year,
  expense_bg_year,
  parent_fund_id,
  seq,
) {
  return request({
    url: '/funds/actions/update',
    method: 'post',
    data: {
      fund_id,
      fund_code,
      fund_name_cn,
      fund_name_en,
      fund_abbr_cn,
      fund_abbr_en,
      fund_type,
      active_year,
      nature_code,
      income_bg_year,
      expense_bg_year,
      parent_fund_id,
      seq,
    },
  })
}

/**
 * 刪除賬目類別
 * @param {integer} fund_id 賬目類別id

 */
export function deleteFund(fund_id) {
  return request({
    url: '/funds/actions/delete',
    method: 'post',
    data: {
      fund_id,
    },
  })
}

/**
 * 返回所有賬目類別
 */
export function fetchFunds({ fund_type, parent_fund_id, fund_id, fy_code }) {
  return request({
    url: '/funds',
    method: 'get',
    params: {
      fund_type,
      parent_fund_id,
      fund_id,
      fy_code,
    },
  })
}

/**
 * 獲取某賬目類別
 * @param {integer} fund_id 賬目類別id
 */
export function getFund(fund_id) {
  return request({
    url: '/funds/actions/inquire',
    method: 'get',
    params: {
      fund_id,
    },
  })
}

export default { createFund, editFund, deleteFund, fetchFunds, getFund }
