<template>
  <header v-if="show">
    <el-breadcrumb separator="/">
      <slot>
        <!--        <el-breadcrumb-item>-->
        <!--          {{ $t($route.matched[0].meta.title) }}-->
        <!--        </el-breadcrumb-item>-->
        <!--        <el-breadcrumb-item>-->
        <!--          {{ $t($route.meta.title) }}-->
        <!--        </el-breadcrumb-item>-->

        <template v-for="item in $route.matched">
          <el-breadcrumb-item v-if="item.meta.title" :key="item.name">
            {{ $t(item.meta.title) }}
          </el-breadcrumb-item>
        </template>
      </slot>
    </el-breadcrumb>
  </header>
</template>

<script>
export default {
  name: 'VBreadCrumb',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    console.log(this.$route)
  },
  methods: {},
}
</script>

<style scoped>
header {
  /*margin: 0 10px 5px 0;*/
}
</style>
