<template>
  <!--收款人彈窗-->
  <el-dialog
    :visible.sync="showDialog"
    :title="$t('daily.dialog.payee')"
    :close-on-click-modal="false"
    @open="onOpenDialogAccount"
  >
    <el-row ref="content" class="dialog-content">
      <el-col :span="12" class="left">
        <addPage
          ref="addPage"
          :default-parent="searchFilter.fund_id"
          :edit-parent="addParent"
          class="voucher-dialog-add"
          @onCancel="onCancel"
        />
      </el-col>
      <el-col :span="12" class="right">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="100%"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="$t('daily.label.ac_code')" property="comp_code" width="150" />
          <el-table-column
            :label="$t('daily.label.comp_name')"
            property="comp_name"
            min-width="150"
          />
          <el-table-column
            :label="$t('daily.label.comp_abbr')"
            property="comp_abbr"
            min-width="150"
          />
        </el-table>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import addPage from '@/views/assistance/payeePayer/add'

import { searchCompanies } from '@/api/assistance/payeePayer'
import { fetchCompanyGroups } from '@/api/assistance/payeePayer/payeePayerGroup'
import ETable from '@/components/ETable/index'

export default {
  name: 'DialogPayeeShortcut',
  components: {
    ETable,
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
    addName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      p_code: 'ac.assistance.payee_payer',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      currentRowItem: null,
      currentRowIndex: -1,
      showDialog: this.dialogVisible,
      dialogSelectAccount: null,
      dialogView: 'list',
      companyGroupList: [],
      companyList: [],
      tableData: [],
      searchFilter: {
        company_group_id: '',
        name: '',
        type: '',
      },
      defaultFilter: {
        company_group_id: '',
        name: '',
        type: '',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp
    }
  },
  computed: {
    ...mapGetters(['language']),
    TQOptions() {
      return [
        {
          label: 'N/A',
          value: '',
        },
        {
          label: 'Quotation',
          value: 'Q',
        },
        {
          label: 'Tender',
          value: 'T',
        },
      ]
    },
    allPayee() {
      return {
        label: this.$t('daily.label.allPayee'),
        value: '',
      }
    },

    typeOptions() {
      return [
        {
          label: this.$t('daily.label.allType'),
          value: '',
        },
        {
          label: 'B',
          value: 'B',
        },
        {
          label: 'I',
          value: 'I',
        },
        {
          label: 'E',
          value: 'E',
        },
      ]
    },
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    onClear(update) {
      this.searchFilter = Object.assign({}, this.defaultFilter)
      if (update) this.fetchData()
    },
    onOpenDialogAccount() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.onClear()
      fetchCompanyGroups({ fy_code: this.fy_code })
        .then(res => {
          this.companyGroupList = res
        })
        .then(this.fetchData)
      this.$nextTick(() => {
        this.$refs.addPage && this.$refs.addPage.setName(this.addName)
      })
    },
    fetchData() {
      this.tableLoading = true
      searchCompanies({})
        .then(res => {
          this.tableData = res
          // for (let i = 0; i < 50; i++) {
          //   this.tableData.push(res[0])
          // }
          // this.tableData = res
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    onAdd() {
      // const fund_id = this.searchFilter.fund_id
      // if (fund_id) {
      //   if (this.searchFilter.fund_id_target) {
      //     this.addParent = { fund_id: this.searchFilter.fund_id_target }
      //   }
      this.dialogView = 'add'
      // }
    },
    onCancel(update) {
      // this.dialogView = 'list'
      // if (update) this.fetchData()
      this.showDialog = false
      this.$emit('close', update)
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      // this.$emit('selectRow', currentRow)
      // this.showDialog = false
      console.log(currentRow)
      this.$refs.addPage && this.$refs.addPage.setAcCode(currentRow.comp_code)
      this.$refs.addPage && this.$refs.addPage.setYear(currentRow.active_year)
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectPayee {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
.dialog-content {
  height: 100%;
  overflow-y: hidden;
  .left,
  .right {
    overflow-y: auto;
    height: 100%;
  }
  .right {
    padding-left: 20px;
  }
}
</style>
