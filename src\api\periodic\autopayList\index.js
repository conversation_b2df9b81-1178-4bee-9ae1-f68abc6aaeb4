import request from '@/utils/request'

/**
 * 查詢自動轉賬列表
 * @param {string} vt_category 類別:P=付款,R=收款
 * @param {string} ac_code 銀行會計賬目編號(ac_bank不等於X)
 * @param {string} begin_date  開始日期
 * @param {string} end_date  結束日期
 */
export function fetchAutopayList({ vt_category, ac_code, begin_date, end_date }) {
  return request({
    url: '/cycle/autopay-list',
    method: 'get',
    params: {
      vt_category,
      ac_code,
      begin_date,
      end_date,
    },
  })
}

export default { fetchAutopayList }
