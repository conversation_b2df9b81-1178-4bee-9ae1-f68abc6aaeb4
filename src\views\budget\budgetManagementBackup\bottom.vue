<template>
  <div v-loading="loading" class="budget-management-bottom">
    <div class="filter">
      <el-form :inline="true" class="mini-form">
        <el-form-item :label="$t('filters.years')">
          <el-select v-model="currentYearCode" style="width: 100px" @change="onChangeYear">
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
        </el-form-item>
        <!-- ---------- 預算 ---------- -->
        <el-form-item :label="'預算'" prop="parent_budget_id">
          <BudgetSelectTreeVue
            :data="treeData"
            :budget-id.sync="currentBudgetId"
            :language="language"
            style="width: 300px"
            @change="onChangeBudgetGroup"
          />
        </el-form-item>
        <el-form-item class="icon-button">
          <i class="edac-icon action-icon edac-icon-setting1" @click="onShowSetting" />
          <!--              <i class="edac-icon edac-icon-save"/>-->
          <!--              <i class="edac-icon edac-icon-excel_add"/>-->
          <!--          <i v-if="hasPermission_Output" class="edac-icon action-icon edac-icon-excel" @click="onExport"/>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="bottom-table">
      <ETable
        ref="table"
        :data="tableData"
        :style-columns="styleColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-checkbox="false"
        :default-top="230"
        :show-actions="false"
        :full-column="true"
        border
        @changeWidth="changeColumnWidth"
        @selection-change="onSelectionChange"
      >
        <template slot="columns">
          <el-table-column
            v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
            :key="item.ss_key"
            :label="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :column-key="item.ss_key"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="item.ss_key === 'amount'">
                {{ amountFormat(scope.row.amount_dr - scope.row.amount_cr) }}
              </span>
              <span v-else>{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)],
                  customDateFormat
                )
              }}</span>
            </template>
          </el-table-column>
        </template>
      </ETable>
    </div>

    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="full-screen-with-first-field"
      @reloadStyleSheets="loadUserStyle"
      @close="onCloseCustomStyleDialog"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import BudgetSelectTreeVue from '../common/BudgetSelectTreeVue'
import ETable from '@/components/ETable'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
import { fetchBudgetLedgers } from '@/api/budget'
import { amountFormat } from '@/utils'

export default {
  name: 'BudgetManagementBottom',
  components: {
    BudgetSelectTreeVue,
    ETable,
    customStyle,
  },
  mixins: [mixinPermission, loadCustomStyle],
  props: {
    budgetTree: {
      type: Array,
      default: () => [],
    },
    years: {
      type: Array,
      default: () => [],
    },
    fyCode: {
      type: String,
      default: '',
    },
    parentBudgetId: {
      type: [String, Number],
      default: '',
    },
    selectedBudgetId: {
      type: [String, Number],
      default: '',
    },
    parentBudget: {
      type: Object,
      default: () => ({}),
    },
    refresh: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      langKey: 'budget.budgetManagement.label.',
      loading: false,
      customDateFormat: 'dd/MM/yy',

      showDialog: false,
      selectedVoucherList: [], // 勾選的
      tableData: [],
      tableColumns: [
        'vc_date',
        'vc_no',
        'descr',
        'vc_payee',
        'ref',
        'amount',
        'budget_code',
        'ac_code',
      ],
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    currentYearCode: {
      get() {
        return this.fyCode
      },
      set(val) {
        this.$emit('update:fyCode', val)
      },
    },
    currentBudgetId: {
      get() {
        return this.selectedBudgetId
      },
      set(val) {
        this.$emit('update:selectedBudgetId', val)
        this.$nextTick(() => {
          this.fetchData()
        })
      },
    },
    treeData() {
      if (this.parentBudget && this.parentBudget.budget_id) {
        const item = Object.assign({}, this.parentBudget)
        item.children = this.budgetTree
        return [item]
      } else {
        return this.budgetTree
      }
    },
  },
  watch: {
    currentBudgetId: {
      immediate: false,
      handler(newVal, oldVal) {
        // if (newVal !== oldVal) {
        // this.$nextTick(() => {
        //   this.fetchData()
        // })
        // }
      },
    },
    currentYearCode: {
      immediate: false,
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          // this.$nextTick(() => {
          //   this.fetchData()
          // })
          console.log('change year')
        }
      },
    },
    refresh: {
      immediate: false,
      handler(newVal) {
        if (newVal) {
          this.fetchData()
          this.$emit('update:refresh', false)
        }
      },
    },
  },
  mounted() {},
  methods: {
    fetchData() {
      if (this.loading) return
      this.loading = true
      const fy_code = this.currentYearCode
      const budget_id = this.currentBudgetId
      if (!fy_code || !budget_id) return
      fetchBudgetLedgers({ fy_code, budget_id })
        .then(res => {
          this.tableData = res
        })
        .finally(() => {
          this.loading = false
        })
    },
    onChangeBudgetGroup(val) {
      this.$emit('changeBudget', val)
      // this.$nextTick(this.fetchData)
    },
    onChangeYear(val) {
      this.$emit('changeYear', val)
    },
    onSelectionChange(val) {
      console.log('onSelectionChange', val)
      this.selectedVoucherList = val
    },
    onCloseCustomStyleDialog() {},
    amountFormat,
  },
}
</script>

<style lang="scss" scoped>
.budget-management-bottom {
  padding: 10px;
  height: 100%;
  .bottom-table {
    height: calc(100% - 40px);
    .el-table {
      height: 100%;
    }
  }
}
</style>
