# 環境變量遷移說明

## 概述
本次更新將原有的 Vue CLI 2 環境變量配置遷移到 Vue CLI 3+ 的 `.env` 文件格式。

## 變更內容

### 1. 舊配置文件（已遷移）
- `config/global.js` - 全局配置變量
- `config/dev.env.js` - 開發環境配置
- `config/prod.env.js` - 生產環境配置
- `config/sit.env.js` - SIT環境配置

### 2. 新配置文件
- `.env` - 生產環境配置
- `.env.development` - 開發環境配置

- `.env.example` - 配置示例文件

### 3. 變量名稱對照表

| 舊變量名 (config/global.js) | 新變量名 (.env) |
|---------------------------|----------------|
| LOCALHOST | VUE_APP_DEV_HOST |
| LOCAL_PROTOCOL | VUE_APP_DEV_PROT |
| PROXY_PATH | VUE_APP_PROXY_PATH |
| PROXY_TARGET | VUE_APP_PROXY_TARGET |
| SERVER_NAME | VUE_APP_SERVER_NAME |
| PROTOCOL | VUE_APP_PROTOCOL |
| IP | VUE_APP_IP |
| PORT | VUE_APP_PORT |
| PROJECT_ROOT | VUE_APP_PROJECT_ROOT |
| REMOTE_PROJECT_NAME | VUE_APP_REMOTE_PROJECT_NAME |
| URI | VUE_APP_URI |
| RPT_PROTOCOL | VUE_APP_RPT_PROTOCOL |
| RPT_IP | VUE_APP_RPT_IP |
| RPT_PORT | VUE_APP_RPT_PORT |
| RPT_URL | VUE_APP_RPT_URL |
| WEB_CODE | VUE_APP_WEB_CODE |
| TUTORIAL_PATH | VUE_APP_TUTORIAL_PATH |

| 舊變量名 (env.js) | 新變量名 (.env) |
|------------------|----------------|
| BASE_API | VUE_APP_BASE_API |
| ENV_CONFIG | VUE_APP_ENV_CONFIG |

### 4. 代碼更新

#### 更新的文件：
- `config/index.js` - 更新環境變量引用方式，移除默認值
- `config/load-env.js` - 新增統一的環境變量加載模塊
- `src/store/modules/app.js` - 更新環境變量引用方式，移除默認值
- `src/utils/request.js` - 更新 BASE_API 引用，移除默認值
- `src/utils/env.js` - 環境變量工具函數（主要用於客戶端）
- `build/webpack.prod.conf.js` - 移除舊的環境配置引用
- `build/webpack.base.conf.js` - 使用統一的環境變量加載
- `build/check-env.js` - 新增環境變量檢查腳本
- `.env.example` - 更新為新格式

#### 環境變量使用方式：
```javascript
// 舊方式（已棄用）
const { PROTOCOL, IP } = require('../.env')

// 舊方式（有默認值，已棄用）
const PROTOCOL = process.env.VUE_APP_PROTOCOL || 'http'
const IP = process.env.VUE_APP_IP || '*************'

// 新方式（無默認值，編譯時檢查）
const PROTOCOL = process.env.VUE_APP_PROTOCOL
const IP = process.env.VUE_APP_IP
```

#### 重要：編譯時 vs 運行時
- **客戶端代碼**：`process.env.VUE_APP_*` 在編譯時會被 webpack 的 DefinePlugin 替換為實際的字符串值
- **構建配置**：在 Node.js 環境中（如 config/index.js），`process.env` 是運行時變量
- **環境變量加載順序**：
  1. `config/index.js` 首先被加載，使用 `config/load-env.js` 加載環境變量
  2. `build/webpack.base.conf.js` 重用已加載的環境變量
  3. 避免了加載順序導致的環境變量獲取不到的問題
- **錯誤檢查**：如果環境變量缺失，構建會在早期階段失敗

### 5. 重要說明

1. **VUE_APP_ 前綴**：所有在客戶端代碼中使用的環境變量必須以 `VUE_APP_` 開頭
2. **環境文件優先級**：
   - `.env.development` - 開發環境專用

   - `.env` - 默認/生產環境
3. **向後兼容**：舊的配置文件暫時保留，但建議逐步移除對它們的依賴

### 6. 使用方法

#### 開發環境
```bash
npm run dev
# 自動加載 .env.development
```

#### 生產環境
```bash
npm run build:prod
# 自動加載 .env
```



### 7. 自定義配置

如需自定義配置，請：
1. 複製 `.env.example` 為 `.env` 或 `.env.development`
2. 修改相應的環境變量值
3. 重新啟動開發服務器

### 8. 環境變量檢查

為了確保配置的完整性，系統已移除所有環境變量的默認值。如果缺少必需的環境變量，構建過程將會失敗並顯示錯誤信息。

#### 檢查腳本：
```bash
# 檢查當前環境的環境變量
npm run check-env

# 檢查開發環境
npm run check-env:dev

# 檢查生產環境
npm run check-env:prod

# 檢查SIT環境
npm run check-env:sit
```

#### 自動檢查：
- 構建過程會自動檢查所需的環境變量
- 如果缺少必需的變量，構建會立即失敗
- 錯誤信息會明確指出缺少哪些環境變量

### 9. 注意事項

- **環境變量策略**：
  - 客戶端代碼：無默認值，缺少時構建失敗
  - 開發服務器配置：可以有合理的默認值（如 host: '0.0.0.0', port: 8080）
- 環境變量文件不應提交到版本控制系統（已在 .gitignore 中配置）
- 修改環境變量後需要重新啟動開發服務器
- 所有環境變量值都是字符串類型
- 構建前會自動檢查環境變量的完整性
- webpack DefinePlugin 會在編譯時將 `process.env.VUE_APP_*` 替換為實際值
