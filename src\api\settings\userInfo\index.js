import request from '@/utils/request'
/**
 * 查詢(當前)用戶詳情
 */
export function getMe() {
  return request({
    url: '/users/me',
    method: 'get',
    params: {},
  })
}

/**
 * 修改(當前)用戶密碼
 * @param password  舊密碼
 * @param new_password  新密碼
 */
export function changePsw({ password, new_password }) {
  return request({
    url: '/users/me/actions/change-psw',
    method: 'post',
    data: {
      password,
      new_password,
    },
  })
}

export default { getMe, changePsw }
