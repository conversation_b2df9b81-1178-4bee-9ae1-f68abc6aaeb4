<template>
  <div id="account-select">
    <el-row>
      <el-col :span="4" style="width: auto">
        <div v-if="selectedAccounts.length > 0" class="actions">
          <el-button
            type="primary"
            size="mini"
            class="new-row"
            icon="el-icon-plus"
            @click="onAddRow"
          >
            <!--{{ $t('button.add') }}-->
          </el-button>
        </div>
      </el-col>
      <el-col :span="20">
        <el-table
          v-loading="loading"
          :show-header="false"
          :data="selectedAccounts"
          v-bind="$attrs"
          class="account-table"
          style="height: auto; width: 451px"
        >
          <el-table-column :label="$t('table.index')" type="index" width="50" align="right" />
          <el-table-column
            class-name="account"
            :label="$t('table.account')"
            width="350"
            align="left"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <el-select v-model="scope.row.account_id" style="width: 100%">
                <el-option
                  v-for="item in accountList"
                  :key="item.account_id"
                  :value="item.account_id"
                  :label="`[${item.ac_code}] ${
                    item[language === 'en' ? 'ac_name_en' : 'ac_name_cn']
                  }`"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('table.action')" width="50" align="left">
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>
                <i class="el-icon-delete" @click="removeRow(scope.row)" />
              </span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="bottom-action">
              <el-button
                type="primary"
                size="mini"
                class="new-row"
                icon="el-icon-plus"
                @click="onAddRow"
              >
                <!--{{ $t('button.add') }}-->
              </el-button>
            </div>
          </template>
          <!--          <template slot="append">-->
          <!--            <div class="bottom-action">-->
          <!--              <el-button type="primary" size="mini" class="new-row" @click="onAddRow">add</el-button>-->
          <!--              &lt;!&ndash;          <i class="el-icon-plus" @click="onAddRow"/>&ndash;&gt;-->
          <!--            </div>-->
          <!--          </template>-->
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { searchAccounts } from '@/api/master/account'

export default {
  name: 'BudgetAccountSelect',
  props: {
    data: {
      type: String,
      default: '',
    },
    fyCode: {
      type: String,
      required: true,
    },
    hasList: {
      type: Boolean,
      default: false,
    },
    defaultAccounts: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: true,
      accounts: [],
      selectedAccounts: [],
    }
  },
  computed: {
    ...mapGetters(['language']),
    accountList() {
      if (this.hasList) {
        return this.defaultAccounts
      } else {
        return this.accounts
      }
    },
  },
  watch: {
    selectedAccounts: {
      deep: true,
      immediate: true,
      handler(val) {
        if (this.loading) return
        const v = val.map(i => i.account_id).join(',')
        this.$emit('update:data', v)
      },
    },
    data: {
      immediate: true,
      deep: true,
      handler(val) {
        console.log(val, 'val')

        if (this.loading) return
        if (!val) {
          this.selectedAccounts = []
        } else {
          console.log(val.split(','))

          this.selectedAccounts = val.split(',').map(i => ({ account_id: i ? parseInt(i) : '' }))
          console.log(this.selectedAccounts, 'selectedAccounts')
        }
      },
    },
  },
  created() {
    if (this.hasList) {
      // this.accounts = this.defaultAccounts.map(i => Object.assign({}, i))

      this.selectedAccounts = (this.data || '')
        .split(',')
        .filter(i => i)
        .map(i => ({ account_id: parseInt(i) }))
      this.loading = false
    } else {
      const fy_code = this.fyCode
      this.loading = true
      searchAccounts({ fy_code })
        .then(res => {
          this.accounts = res
        })
        .finally(() => {
          this.selectedAccounts = (this.data || '')
            .split(',')
            .filter(i => i)
            .map(i => ({ account_id: parseInt(i) }))
          this.loading = false
        })
    }
  },
  methods: {
    onAddRow() {
      const item = {
        account_id: '',
      }
      this.selectedAccounts.push(item)
    },
    removeRow(row) {
      const data_index = this.selectedAccounts.findIndex(i => i.account_id === row.account_id)
      data_index > -1 && this.selectedAccounts.splice(data_index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.actions {
  text-align: left;

  /*float: right;*/
  /*z-index: 1;*/
  /*position: absolute;*/
  /*right: 0;*/
  /*width: 480px;*/
  .new-row {
  }

  /deep/ {
    i {
      vertical-align: text-top !important;
    }
  }
}

.el-icon-delete {
  cursor: pointer;
}

.bottom-action {
  text-align: left;
}

#account-select {
  /deep/ {
    .el-table {
      background: transparent;
    }

    .el-table__empty-block {
      min-height: 35px;
    }

    .el-table__body-wrapper {
      height: auto;
    }

    .el-select {
      input {
        height: 23px;
      }
    }

    .el-table th,
    .el-table tr {
      background-color: transparent;
    }

    .el-table__empty-text {
      width: 100%;
    }
  }
}

.account-table {
  /deep/ {
    td.account {
      .cell {
        padding: 0;
      }
    }

    .el-table__body-wrapper > table .el-table__row > td:nth-child(1) {
      border: none;
    }

    .el-table__body-wrapper > table .el-table__row > td {
      border: none;
    }
  }
}
</style>
