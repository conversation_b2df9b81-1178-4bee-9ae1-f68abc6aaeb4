<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 撥款 -->
        <el-select v-model="preferences.filters.fund" class="fund" @change="reloadData">
          <el-option :label="allFund.label" :value="allFund.value" />
          <el-option
            v-for="item in funds"
            :key="item.fund_id"
            :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
            :value="item.fund_id"
          />
        </el-select>
        <!-- 類別 -->
        <el-select v-model="preferences.filters.vt_category" class="category" @change="reloadData">
          <el-option :label="allVTCategory.label" :value="allVTCategory.value" />
          <el-option
            v-for="item in vt_category_list"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!-- 會計週期 -->
        <el-select v-model="preferences.filters.year" class="year" @change="reloadData">
          <el-option :label="allYear.label" :value="allYear.value" />
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div v-if="hasPermission_Add" :title="$t('btnTitle.add')" class="icon add" @click="onAdd">
            <svg-icon icon-class="add" class="action-icon" />
          </div>
          <!-- 導入按鈕 -->
          <div
            v-if="hasPermission_Input"
            :title="$t('btnTitle.importExcel')"
            class="icon import"
            @click="importDialog = true"
          >
            <svg-icon icon-class="import" class="action-icon" />
          </div>
          <!-- 導出按鈕 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            class="icon export action-icon"
            @click="onExport"
          >
            <svg-icon icon-class="export" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :data="tableData"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <!--          <template slot="columns">-->
          <!--            <el-table-column-->
          <!--              v-for="item in styleColumns"-->
          <!--              :key="item.ss_id"-->
          <!--              :type="item.ss_key === '_index' ? 'index' : ''"-->
          <!--              :label="item.ss_key === '_index' ? '#' : $t(langKey + item.ss_key)"-->
          <!--              :align="item.alignment"-->
          <!--              :width="item.width"-->
          <!--              :property="item.ss_key"-->
          <!--              :column-key="item.ss_key"-->
          <!--            />-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :table-data="tableData"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      width="450px"
      class="dialog"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'

import {
  deleteVoucherType,
  fetchVoucherTypes,
  exportVoucherTypes,
  importVoucherTypes,
} from '@/api/master/voucherType'
import { fetchYears } from '@/api/master/years' // year
import { fetchFunds } from '@/api/master/funds' // 撥款

import ETable from '@/components/ETable'
import UploadExcel from '@/components/UploadExcel/index'
import treeToArray from '@/components/TreeTable/eval.js'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

// 導出Excel
import { exportExcel, importExcel } from '@/utils/excel'

export default {
  name: 'MasterVoucherTypeIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    ETable,
    UploadExcel,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',

      editObject: null,
      editTypeObject: null,

      func: treeToArray,
      expandAll: false,
      editParent: null,
      args: [null, null, 'timeLine'],

      tableData: [],
      years: [],
      funds: [],

      importDialog: false,
      loading: false,

      langKey: 'master.voucher_type.label.',
      tableColumns: [
        'fund_name_',
        'vt_category',
        'vt_code',
        'vt_description_',
        'vt_format',
        'chq_name_',
        // 'chq_template_code',
        'vt_ac_code',
        'vt_group',
      ],

      preferences: {
        filters: {
          year: '', // fy_code 活躍的會計週期編號
          fund: '', // fund_id 賬目類別id
          vt_category: '', // 傳票類別類型
        },
      },
      exportFileName: 'VoucherTypes', // 導出文件名
    }
  },
  computed: {
    ...mapGetters(['language']),
    vt_category_list() {
      return ['P', 'R', 'C', 'T', 'J', 'A'].map(i => {
        return {
          label: this.$t('voucher_type_category.' + i),
          value: i,
        }
      })
    },
    allVTCategory() {
      return {
        label: this.$t('voucher_type_category.ALL'),
        value: '',
      }
    },
    allFund() {
      return {
        label: this.$t('master.fund.all_fund'),
        value: '',
      }
    },
    allYear() {
      return {
        label: this.$t('master.year.all_year'),
        value: '',
      }
    },
  },
  created() {},
  mounted() {
    this.fetchData()
  },
  methods: {
    /**
     * Button ADD
     * @param scope
     */
    onAdd(scope) {
      this.editObject = null
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'add'
    },
    /**
     * Buttion Edit
     * @param scope
     */
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    /**
     * Buttion Delete
     * @param scope
     */
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.vt_description_en : scope.row.vt_description_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const voucher_type_id = scope.row.voucher_type_id
          return new Promise((resolve, reject) => {
            deleteVoucherType(voucher_type_id)
              .then(res => {
                if (this.editObject && this.editObject.voucher_type_id === voucher_type_id) {
                  this.onViewCancel()
                }
                this.reloadData()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    /**
     * Init Data
     */
    fetchData() {
      // 獲取所有會計週期
      fetchYears()
        .then(res => {
          this.years = res
        })
        // 帳目類別
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.fund) {
            const fund = this.funds.find(i => i.fund_id === this.preferences.filters.fund)
            if (!fund) {
              this.preferences.filters.fund = ''
            }
          }
          if (this.preferences.filters.year) {
            const year = this.year.find(i => i.fy_code === this.preferences.filters.year)
            if (!year) {
              this.preferences.filters.year = ''
            }
          }
          if (this.preferences.filters.vt_category) {
            const vt_category = this.vt_category_list.find(
              i => i.value === this.preferences.filters.vt_category,
            )
            if (!vt_category) {
              this.preferences.filters.vt_category = ''
            }
          }
        })
        .then(() => {
          return fetchVoucherTypes({
            fund_id: this.preferences.filters.fund,
            fy_code: this.preferences.filters.year,
            vt_category: this.preferences.filters.vt_category,
          })
        })
        .then(res => {
          this.tableData = res
        })
        .catch(() => {})
    },
    /**
     * Reload Data
     */
    reloadData() {
      return new Promise((resolve, reject) => {
        this.loading = true
        fetchVoucherTypes({
          fund_id: this.preferences.filters.fund,
          fy_code: this.preferences.filters.year,
          vt_category: this.preferences.filters.vt_category,
        })
          .then(res => {
            this.tableData = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    /**
     * Children cancel
     * @param update
     */
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.reloadData()
      }
    },
    /**
     * Button Export
     */
    onExport() {
      if (this.loading) {
        return
      }
      this.loading = true
      exportVoucherTypes()
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * Button Import
     * @param results UploadExcel Data
     * @param header UploadExcel Header
     */
    onImport({ results, header }) {
      this.loading = true
      importExcel(this, importVoucherTypes, results, header)
        .then(() => this.reloadData())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .fund {
    width: 120px;
  }
  .category {
    width: 120px;
  }
  .year {
    width: 130px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
.dialog {
  .el-dialog__body {
    height: auto !important;
  }
}
</style>
