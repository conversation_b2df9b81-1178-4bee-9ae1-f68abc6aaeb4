import request from '@/utils/request'

/**
 * 返回新增后的職員id
 * @param {string} comp_code 收付款公司編號
 * @param {string} comp_name 公司名
 * @param {string} comp_abbr 公司簡稱
 * @param {string} comp_group 類別, B=收付款,I=收款,E=付款
 * @param {string} comp_supplier 是否供應商 Y=是,N=否
 * @param {string} comp_add1 公司地址1
 * @param {string} comp_add2 公司地址2
 * @param {string} comp_add3 公司地址3
 * @param {string} comp_add4 公司地址4
 * @param {string} comp_tel 公司電話
 * @param {string} comp_fax 公司傳真
 * @param {string} comp_attention 聯絡人
 * @param {string} comp_email 公司電郵
 * @param {string} comp_remark 公司備註
 * @param {string} comp_ap 是否為A/P,可填N或Y
 * @param {string} comp_credit Credit
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {string} parent_company_group_id 父收付款公司類別組id((不傳則為根節點)
 * @param {string} seq 在組別中的順序
 */
export function createCompany(
  comp_code,
  comp_name,
  comp_abbr,
  comp_group,
  comp_supplier,
  comp_add1,
  comp_add2,
  comp_add3,
  comp_add4,
  comp_tel,
  comp_fax,
  comp_attention,
  comp_email,
  comp_remark,
  comp_ap,
  comp_credit,
  active_year,
  parent_company_group_id,
  seq,
) {
  return request({
    url: '/companies/actions/create',
    method: 'post',
    data: {
      comp_code,
      comp_name,
      comp_abbr,
      comp_group,
      comp_supplier,
      comp_add1,
      comp_add2,
      comp_add3,
      comp_add4,
      comp_tel,
      comp_fax,
      comp_attention,
      comp_email,
      comp_remark,
      comp_ap,
      comp_credit,
      active_year,
      parent_company_group_id,
      seq,
    },
  })
}

/**
 * 修改收付款公司
 * @param {integer} company_id 收付款公司id
 * @param {string} comp_code 收付款公司編號
 * @param {string} comp_name 公司名
 * @param {string} comp_abbr 公司簡稱
 * @param {string} comp_group 類別, B=收付款,I=收款,E=付款
 * @param {string} comp_supplier 是否供應商 Y=是,N=否
 * @param {string} comp_add1 公司地址1
 * @param {string} comp_add2 公司地址2
 * @param {string} comp_add3 公司地址3
 * @param {string} comp_add4 公司地址4
 * @param {string} comp_tel 公司電話
 * @param {string} comp_fax 公司傳真
 * @param {string} comp_attention 聯絡人
 * @param {string} comp_email 公司電郵
 * @param {string} comp_remark 公司備註
 * @param {string} comp_ap 是否為A/P,可填N或Y
 * @param {string} comp_credit Credit
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {string} parent_company_group_id 父收付款公司類別組id((不傳則為根節點)
 * @param {string} seq 在組別中的順序
 */
export function updateCompany(
  company_id,
  comp_code,
  comp_name,
  comp_abbr,
  comp_group,
  comp_supplier,
  comp_add1,
  comp_add2,
  comp_add3,
  comp_add4,
  comp_tel,
  comp_fax,
  comp_attention,
  comp_email,
  comp_remark,
  comp_ap,
  comp_credit,
  active_year,
  parent_company_group_id,
  seq,
) {
  return request({
    url: '/companies/actions/update',
    method: 'post',
    data: {
      company_id,
      comp_code,
      comp_name,
      comp_abbr,
      comp_group,
      comp_supplier,
      comp_add1,
      comp_add2,
      comp_add3,
      comp_add4,
      comp_tel,
      comp_fax,
      comp_attention,
      comp_email,
      comp_remark,
      comp_ap,
      comp_credit,
      active_year,
      parent_company_group_id,
      seq,
    },
  })
}

/**
 * 刪除收付款公司
 * @param {integer} company_id 收付款公司id
 */
export function deleteCompany(company_id) {
  return request({
    url: '/companies/actions/delete',
    method: 'post',
    data: {
      company_id,
    },
  })
}

/**
 * 獲取付款公司列表
 * @param {string} fy_code 選擇的會計週期
 * @param {string} comp_supplier 傳Y只返回供應商,傳N只返回非供應商,不傳返回全部
 */
export function getCompanies(fy_code, comp_supplier) {
  return request({
    url: '/companies',
    method: 'get',
    params: {
      fy_code,
      comp_supplier,
    },
  })
}

/**
 * 查詢收付款公司樹
 * @param {string} fy_code 選擇的會計週期
 */
export function getCompaniesTree(fy_code) {
  return request({
    url: '/companies/tree',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 查詢收付款公司樹節點
 * @param {string} fy_code 選擇的會計週期
 * @param {integer} parent_company_group_id 上級收付款公司類別id
 * @param {integer} except_company_group_id 排除收付款公司類別id
 * @param {integer} except_company_id 排除收付款公司id
 */
export function getCompaniesTreeNode({
  fy_code,
  parent_company_group_id,
  except_company_group_id,
  except_company_id,
}) {
  return request({
    url: '/companies/tree/node',
    method: 'get',
    params: {
      fy_code,
      parent_company_group_id,
      except_company_group_id,
      except_company_id,
    },
  })
}

/**
 * 獲取某收付款公司詳情
 * @param {integer} company_id 收付款公司id
 */
export function getCompany(company_id) {
  return request({
    url: '/companies/actions/inquire',
    method: 'get',
    params: {
      company_id,
    },
  })
}

/**
 * 匯出收付款公司及收付款公司類別excel數據
 */
export function exportCompanies() {
  return request({
    url: '/companies/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入收付款公司及收付款公司類別excel數據
 * @param {string} data_json 匯入的數據
 */
export function importCompanies(data_json) {
  return request({
    url: '/companies/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

/**
 * 返回符合條件的收付款公司
 * @param {integer} fy_code 會計週期編號
 * @param {integer} parent_company_group_id 父職員類別id
 * @param {string} name 模糊搜索名字
 * @param {string} comp_group 類別, B=收付款,I=收款,E=付款
 * @return {Promise}
 */
export function searchCompanies({ fy_code, parent_company_group_id, name, comp_group }) {
  return request({
    url: '/companies/actions/search',
    method: 'get',
    params: {
      fy_code,
      parent_company_group_id,
      name,
      comp_group,
    },
  })
}

/**
 * 返回公司地址打印列表
 * @param {string} company_ids 需要打印的公司id拼接
 * @return {Promise}
 */
export function companiesAddressPrintList(company_ids) {
  return request({
    url: '/companies/address/print-list',
    method: 'get',
    params: {
      company_ids,
    },
  })
}
