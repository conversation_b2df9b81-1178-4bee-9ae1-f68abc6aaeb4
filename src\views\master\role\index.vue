<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <!--<div slot="pane-right-filters"/>-->
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="icon add"
            @click="onAddRole"
          >
            <svg-icon icon-class="add" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :data="roles"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <!--          <template slot="columns">-->
          <!--            <el-table-column-->
          <!--              v-for="item in styleColumns"-->
          <!--              :key="item.ss_key"-->
          <!--              :type="item.ss_key === '_index' ? 'index' : ''"-->
          <!--              :label="item.ss_key === '_index' ? '#' : $t(langKey + item.ss_key)"-->
          <!--              :align="item.alignment"-->
          <!--              :width="item.width"-->
          <!--              :property="item.ss_key + (language === 'en' ? '_en' : '_cn')"-->
          <!--              :column-key="item.ss_key"-->
          <!--            />-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditRole(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDeleteRole(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-role="editRole"
        :system="system"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'
import addPage from './add'
import { deleteRole, fetchRoles } from '@/api/master/role'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

export default {
  name: 'MasterRoleIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    addPage,
  },
  mixins: [mixinPermission, loadCustomStyle],
  props: {
    system: {
      type: String,
      default: 'AC',
    },
    viewName: {
      type: String,
      default: 'MasterRoleIndex',
    },
  },
  data() {
    return {
      isInit: false,
      leftView: '',
      roles: [],
      editRole: null,
      langKey: 'master.role.label.',
      tableColumns: ['role_name_'],
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    onAddRole() {
      this.editRole = null
      this.leftView = 'add'
    },
    onEditRole(scope) {
      this.editRole = scope.row
      this.leftView = 'edit'
    },
    onDeleteRole(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.role_name_en : scope.row.role_name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const role_id = scope.row.role_id
          return new Promise((resolve, reject) => {
            deleteRole(role_id)
              .then(res => {
                if (this.editRole && this.editRole.role_id === role_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchRoles(this.system)
        .then(res => {
          this.roles = res
        })
        .catch(() => {})
        .finally(() => {
          this.isInit = true
        })
    },
    onViewCancel(update) {
      this.editRole = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
