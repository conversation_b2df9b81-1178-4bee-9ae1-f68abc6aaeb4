<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 描述 -->
      <el-form-item :rules="rules" :label="$t('assistance.description.label.desc')" prop="desc">
        <el-input v-model="form.desc" clearable />
      </el-form-item>
      <!-- 編號 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.description.label.ac_code')"
        prop="ac_code"
      >
        <el-select v-model="form.ac_code" class="fund" style="width: 100%">
          <el-option
            v-for="item in accounts"
            :key="item.fund_id"
            :label="`[${item.ac_code}] ${language === 'en' ? item.ac_name_en : item.ac_name_cn}`"
            :value="item.ac_code"
          >
            [{{ item.ac_code }}] {{ language === 'en' ? item.ac_name_en : item.ac_name_cn }}
          </el-option>
        </el-select>
      </el-form-item>

      <!-- BIE -->
      <el-form-item :label="$t('assistance.description.label.BIE')">
        <el-checkbox-group v-model="BIE_arr">
          <el-checkbox v-for="item in BIE_list" :key="item.value" :label="item.label" />
        </el-checkbox-group>
      </el-form-item>

      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.description.label.active_year')">
        <el-checkbox-group v-model="active_year_arr">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getDescription, editDescription } from '@/api/assistance/description'
import { fetchYears } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'

export default {
  name: 'AssistanceDescriptionEdit',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      form: {
        desc_id: '',
        ac_code: '',
        desc: '',
        BIE: '',
        active_year: '',
      },
      defaultForm: {
        desc_id: '',
        ac_code: '',
        desc: '',
        BIE: '',
        active_year: '',
      },
      rules: [
        {
          // 必填
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      years: [],
      funds: [],
      accounts: [],
      active_year_arr: [],
      BIE_arr: [],
      account_arr: [],
      selectAccountList: [],
      loading: true,
      dialogVisible: false,
      treeProps: {
        children: 'children',
        label: 'ac_name_cn',
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
    BIE_list() {
      return ['B', 'I', 'E'].map(i => {
        return {
          label: i,
          value: i,
        }
      })
    },
    allVTCategory() {
      return {
        label: this.$t('voucher_type_category.ALL'),
        value: '',
      }
    },
    allFund() {
      return {
        label: this.$t('master.fund.all_fund'),
        value: '',
      }
    },
  },
  watch: {
    editObject() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    handleClose() {},
    changePosition() {
      this.forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentStaffType(staffType, html, startLevel = 1) {
      let text = this.language === 'en' ? staffType.st_type_en : staffType.st_type_cn
      if (html) {
        text = '&nbsp;'.repeat((staffType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    checkRequired(rule, value, callback) {},
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getDescription(this.editObject.desc_id)
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              this.BIE_arr = res.BIE ? res.BIE.split(',') : []
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(fetchYears)
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({}))
        .then(res => {
          this.accounts = res
        })
        .finally(() => {
          this.loading = false
        })
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }

        const desc_id = this.form.desc_id
        const desc = this.form.desc

        // BIE
        const BIE_arr = this.BIE_arr
        BIE_arr.sort(function(a, b) {
          const order = ['B', 'I', 'E']
          return order.indexOf(a) - order.indexOf(b)
        })
        const BIE = BIE_arr.filter(i => i).join(',')
        const ac_code = this.form.ac_code
        const active_year = this.active_year_arr.filter(i => i).join(',')

        // 編輯
        editDescription({
          desc_id,
          desc,
          BIE,
          ac_code,
          active_year,
        })
          .then(res => {
            this.$message.success(this.$t('message.editSuccess'))
            this.$emit('onCancel', true)
          })
          .catch(() => {
            // this.$message.err(err)
          })
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style scoped>
.account-item {
  width: 100%;
  text-align: left;
}
</style>
