<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 會計週期 -->
        <el-select v-model="preferences.filters.year" class="year" @change="reloadData">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div v-if="hasPermission_Add" :title="$t('btnTitle.add')" class="icon add" @click="onAdd">
            <svg-icon icon-class="add" class="action-icon" />
          </div>
          <!-- 導出按鈕 - 頁面 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="icon export"
            @click="onExport('PAGE')"
          >
            <svg-icon icon-class="excel" class="action-icon" />
          </div>
          <!-- 導出按鈕 - 全部 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="icon export"
            @click="onExport('ALL')"
          >
            <svg-icon icon-class="excel_add" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <e-table
          :data="tableData"
          :style-columns="styleColumns"
          :amount-columns="amountColumns"
          :lang-key="langKey"
          :formatters="formatters"
          class="sales-table"
          border
          @changeWidth="changeColumnWidth"
        >
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
            </div>
          </template>
        </e-table>
        <div class="total">
          <span>{{ $t('stock.sales.label.total') }}: {{ total }}</span>
        </div>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :edit-parent="editParent"
        :fy-code="fyCode"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      width="450px"
    >
      <!-- <UploadExcel :on-success="onImport" :on-template="onExport"/> -->
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'

// import { deleteVoucherType, fetchVoucherTypes, exportVoucherTypes, importVoucherTypes } from '@/api/master/voucherType'
import { deleteSales, fetchSales } from '@/api/stock/sales'
import { fetchYears } from '@/api/master/years' // year

import ETable from '@/components/ETable'
import treeToArray from '@/components/TreeTable/eval.js'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

// 導出Excel
// import { exportExcel, importExcel } from '@/utils/excel'
import { sum, amountFormat } from '@/utils'
import { salesInvoicesExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'

export default {
  name: 'StockSalesIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    ETable,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',

      editObject: null,
      editTypeObject: null,

      func: treeToArray,
      expandAll: false,
      editParent: null,
      fyCode: '18',
      args: [null, null, 'timeLine'],

      tableData: [],
      years: [],

      importDialog: false,
      loading: false,

      langKey: 'stock.sales.label.',
      tableColumns: ['date', 'type', 'amount', 'chq_no', 'voucher_no'],
      amountColumns: ['amount'],

      preferences: {
        filters: {
          year: '', // fy_code 活躍的會計週期編號
        },
      },
      exportFileName: 'sales', // 導出文件名
      formatters: {
        type: this.typeFormat,
      },
    }
  },
  computed: {
    ...mapGetters(['language', 'user_id']),
    allYear() {
      return {
        label: this.$t('stock.sales.label.openingBalance'),
        value: '',
      }
    },
    total() {
      const a = this.tableData.map(i => i.amount)
      return amountFormat(sum(a))
    },
  },
  created() {},
  mounted() {
    this.fetchData()
  },
  methods: {
    /**
     * Button ADD
     * @param scope
     */
    onAdd(scope) {
      this.editObject = null
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.fyCode = this.preferences.filters.year
      this.leftView = 'add'
    },
    /**
     * Buttion Edit
     * @param scope
     */
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    /**
     * Buttion Delete
     * @param scope
     */
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.pi_no : scope.row.pi_no
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const sales_invoice_id = scope.row.sales_invoice_id
          return new Promise((resolve, reject) => {
            deleteSales(sales_invoice_id)
              .then(res => {
                if (this.editObject && this.editObject.sales_invoice_id === sales_invoice_id) {
                  this.onViewCancel()
                }
                this.reloadData()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    /**
     * Init Data
     */
    fetchData() {
      // 加載偏好設置
      fetchYears()
        .then(res => {
          this.years = res
          const year = res.find(i => i.fy_code === this.preferences.filters.year)
          if (res.length && !year) {
            this.preferences.filters.year = res[0].fy_code
          }
        })
        .then(this.loadUserPreference)
        .then(() => {
          const year = this.years.find(i => i.fy_code === this.preferences.filters.year)
          if (!year) {
            if (this.years.length > 0) {
              this.preferences.filters.year = this.years[0].fy_code
            } else {
              this.preferences.filters.year = ''
              return Promise.reject()
            }
          }
          return fetchSales({
            fy_code: this.preferences.filters.year,
          })
        })
        .then(res => {
          this.tableData = res
        })
        .catch(() => {})
    },
    /**
     * Reload Data
     */
    reloadData() {
      this.fyCode = this.preferences.filters.year
      fetchSales({
        fy_code: this.preferences.filters.year,
      }).then(res => {
        this.tableData = res
      })
    },
    /**
     * Children cancel
     * @param update
     */
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.reloadData()
      }
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const fy_code = this.preferences.filters.year
      if (!user_id) {
        // this.$message.error('')
        return
      }
      this.loading = true
      salesInvoicesExport({ user_id, fy_code, export_type })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 類型格式轉換
     */
    typeFormat(row, column) {
      switch (row.type) {
        case 'S':
          return this.$t('stock.label.sales')
        case 'D':
          return this.$t('stock.label.damagedOrLost')
        case 'U':
          return this.$t('stock.label.internalUse')
        case 'W':
          return this.$t('stock.label.writeOff')
        default:
          return row.type
      }
    },
    /**
     * Button Import
     * @param results UploadExcel Data
     * @param header UploadExcel Header
     */
    // onImport({ results, header }) {
    //   this.loading = true
    //   importExcel(this, importVoucherTypes, results, header)
    //     .then(() => this.fetchTree())
    //     .catch(() => {})
    //     .finally(() => {
    //       this.loading = false
    //       this.importDialog = false
    //     })
    // }
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .fund {
    width: 120px;
  }
  .category {
    width: 120px;
  }
  .year {
    width: 130px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}

.sales-table {
  height: calc(100vh - 140px - 100px);
}
.total {
  padding: 0 20px;
  background-color: #e2e2e2;
  color: #404246;
  height: 25px;
  line-height: 25px;
  text-align: right;
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
</style>
