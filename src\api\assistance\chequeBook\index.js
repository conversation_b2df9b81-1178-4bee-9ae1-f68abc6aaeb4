import request from '@/utils/request'

/**
 * 新增支票簿
 * @param {string} fund_id  賬目類別id
 * @param {string} vt_code  傳票類別編號
 * @param {string} chqbk_code 支票簿編號
 * @param {string} chqbk_from 支票簿開始號碼
 * @param {string} chqbk_to 支票簿結束號碼
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 */
export function createChequeBook(fund_id, vt_code, chqbk_code, chqbk_from, chqbk_to, active_year) {
  return request({
    url: '/cheque-books/actions/create',
    method: 'post',
    data: {
      fund_id,
      vt_code,
      chqbk_code,
      chqbk_from,
      chqbk_to,
      active_year,
    },
  })
}

/**
 * 修改支票簿
 * @param {integer} cheque_book_id 支票簿id
 * @param {string} fund_id 賬目類別id
 * @param {string} vt_code  傳票類別編號
 * @param {string} chqbk_code 支票簿編號
 * @param {string} chqbk_from 支票簿開始號碼
 * @param {string} chqbk_to 支票簿結束號碼
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 */
export function updateChequeBook(
  cheque_book_id,
  fund_id,
  vt_code,
  chqbk_code,
  chqbk_from,
  chqbk_to,
  active_year,
) {
  return request({
    url: '/cheque-books/actions/update',
    method: 'post',
    data: {
      cheque_book_id,
      fund_id,
      vt_code,
      chqbk_code,
      chqbk_from,
      chqbk_to,
      active_year,
    },
  })
}

/**
 * 刪除支票簿
 * @param {integer} cheque_book_id 支票簿id
 */
export function deleteChequeBook(cheque_book_id) {
  return request({
    url: '/cheque-books/actions/delete',
    method: 'post',
    data: {
      cheque_book_id,
    },
  })
}

/**
 * 查詢所有支票簿
 * @param {string} fund_id 賬目類別id
 * @param {string} vt_code  傳票類別編號
 * @param {string} fy_code 活躍的會計週期編號,格式: 17,18,...
 * @param {string} ac_code 銀行會計賬號
 */
export function fetchChequeBooks({ fund_id, vt_code, fy_code, ac_code }) {
  return request({
    url: '/cheque-books',
    method: 'get',
    params: {
      fund_id,
      vt_code,
      fy_code,
      ac_code,
    },
  })
}

/**
 * 獲取某支票簿詳情
 * @param {integer} cheque_book_id 支票簿id
 */
export function getChequeBook(cheque_book_id) {
  return request({
    url: '/cheque-books/actions/inquire',
    method: 'get',
    params: {
      cheque_book_id,
    },
  })
}

export default {
  createChequeBook,
  updateChequeBook,
  deleteChequeBook,
  fetchChequeBooks,
  getChequeBook,
}
