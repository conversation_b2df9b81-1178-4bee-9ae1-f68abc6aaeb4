<template>
  <div
    :style="{
      width,
      height,
      minWidth: width,
      minHeight: height,
    }"
    class="occupy"
  >
    <span class="empty">&emsp;</span>
  </div>
</template>

<script>
export default {
  name: 'Occupy',
  props: {
    width: {
      type: [String],
      default: '100%',
    },
    height: {
      type: [String],
      default: '100%',
    },
  },
}
</script>

<style lang="scss" scoped>
.occupy {
  background: #ddd;
  .empty {
    user-select: none;
  }
}
</style>
