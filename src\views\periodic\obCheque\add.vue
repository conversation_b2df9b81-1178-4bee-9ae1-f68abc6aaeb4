<template>
  <div class="userInfo">
    <el-form ref="form" :model="form" label-position="right" label-width="100px">
      <!-- 支票日期 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('periodic.obCheque.label.cheque_date')"
        prop="vc_date"
      >
        <el-date-picker
          v-model="form.vc_date"
          :clearable="false"
          :format="styles.dateFormat"
          :placeholder="$t('placeholder.selectDate')"
          value-format="yyyy-MM-dd"
          type="date"
        />
      </el-form-item>
      <!-- 登錄名稱 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            validator: valiadateAmount,
          },
        ]"
        :label="$t('periodic.obCheque.label.amount')"
        class="amount"
        prop="amount"
      >
        <ENumeric v-model="form.amount" />
      </el-form-item>
      <!-- 收款人 -->
      <el-form-item :label="$t('periodic.obCheque.label.vc_payee')" prop="vc_payee">
        <el-input v-model="form.vc_payee" clearable />
      </el-form-item>
      <!-- 支票號碼 -->
      <el-form-item :label="$t('periodic.obCheque.label.ref')" prop="password">
        <el-input v-model="form.ref" />
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import ENumeric from '@/components/ENumeric'

import { createObCheque, editObCheque } from '@/api/periodic/obCheque'
export default {
  name: 'BankReconciliationAdd',
  components: {
    ENumeric,
  },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    accountId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      system: 'AC',
      form: {
        lg_id: undefined,
        vc_date: '',
        ref: '',
        vc_payee: '',
        vc_rdate: '',
        amount: '',
      },
      defaultForm: {
        lg_id: undefined,
        vc_date: '',
        ref: '',
        vc_payee: '',
        vc_rdate: '',
        amount: '',
      },
      roles: [],
      disPwd: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
  },
  watch: {
    editObject() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      if (this.editObject) {
        // 編輯
        this.form = Object.assign({}, this.editObject)
      } else {
        // 新增
        this.form = Object.assign({}, this.defaultForm)
      }
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const form = this.form
        const account_id = this.accountId
        const lg_id = form.lg_id
        const vc_date = form.vc_date
        const vc_payee = form.vc_payee
        const amount = form.amount
        const ref = form.ref

        if (this.editObject) {
          // 編輯
          editObCheque({
            lg_id,
            vc_date,
            account_id,
            vc_payee,
            amount,
            ref,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createObCheque({
            vc_date, // 期初支票日期
            account_id, // 銀行會計賬號
            vc_payee, // 收款人
            amount, // 金額
            ref, // 支票號碼
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    valiadateAmount(rule, value, callback) {
      if (value === 0 || isNaN(value)) {
        // return callback(new Error('两次输入密码不一致!'))
        return callback(new Error(' '))
      } else {
        callback()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.amount {
  /*height: 30px;*/
  line-height: 30px;
  input {
    height: 30px;
    line-height: 30px;

    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    outline: 0;
    /* padding: 0 15px; */
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    margin: 0;
    text-align: left;
    padding: 0 15px;
  }
}
</style>
