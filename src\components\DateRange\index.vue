<template>
  <div class="date-range">
    <el-date-picker
      v-model="start"
      :format="format"
      :value-format="valueFormat"
      :default-time="defaultTime"
      :clearable="clearable"
      :placeholder="$t('placeholder.beginDate')"
      :type="type"
      :style="{
        width: width,
      }"
      :class="{
        'no-clear': !clearable,
      }"
      class="start-date"
    />
    <span class="joiner">-</span>
    <el-date-picker
      v-model="end"
      :format="format"
      :value-format="valueFormat"
      :default-time="defaultTime"
      :clearable="clearable"
      :placeholder="$t('placeholder.endDate')"
      :type="type"
      :style="{
        width: width,
      }"
      :class="{
        'no-clear': !clearable,
      }"
      class="end-date"
    />
  </div>
</template>

<script>
export default {
  name: 'DateRange',
  props: {
    type: {
      type: String,
      default: 'date',
    },
    format: {
      type: String,
      default: 'dd/MM/yyyy',
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd',
    },
    defaultTime: {
      type: String,
      default: '00:00:00',
    },
    startDate: {
      type: [Date, String],
      default: '',
    },
    endDate: {
      type: [Date, String],
      default: '',
    },
    width: {
      type: String,
      default: '',
    },
    clearable: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    start: {
      get() {
        return this.startDate
      },
      set(val) {
        this.$emit('update:startDate', val)
        this.$emit('change', val, this.end)
      },
    },
    end: {
      get() {
        return this.endDate
      },
      set(val) {
        this.$emit('update:endDate', val)
        this.$emit('change', this.start, val)
      },
    },
  },
}
</script>

<style lang="scss" scoped>
.date-range {
  display: inline-flex;
  .joiner {
    text-align: center;
    width: 15px;
    vertical-align: middle;
  }
  .start-date,
  .end-date {
    width: calc(60px + 6em);
    &.no-clear {
      width: calc(35px + 6em);
      min-width: 105px;
      /deep/ {
        > input {
          padding-right: 5px;
        }
      }
    }
  }
}
</style>
