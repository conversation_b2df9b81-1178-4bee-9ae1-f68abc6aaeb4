<template>
  <div class="hedging-table-box">
    <el-tooltip
      popper-class="vc_rdate-tooltip"
      effect="dark"
      :content="row.old_vc_rdate"
      :disabled="row.vc_rstatus !== 'F'"
      placement="top"
    >
      <big-select
        v-model="row.vc_rdate"
        :disabled="!!(row.vc_rstatus === 'F' || row.ai_document_file_ledger_id)"
        :list="getDateList(row.vc_date, row.vc_rstatus)"
        :class="row.unpaid ? 'big-select-show' : ''"
        class="hedging-select"
        @change="onSelectChange"
      />
    </el-tooltip>
    <el-popover
      ref="popover"
      v-model="descViable1"
      v-loading="loading"
      :popper-class="popperClassName2"
      placement="bottom-end"
      trigger="manual"
    >
      <div v-if="hedgingTableList.length" class="select--pop-item">
        <BTable
          ref="table"
          :data="hedgingTableList"
          :style-columns="styleColumns"
          :lang-key="langKey"
          :auto-height="false"
          :resizable="false"
          :show-actions="false"
          :header-cell-style="
            () => {
              return {
                background: 'white !important',
                color: '#4C5362',
                border: 'none !important',
              }
            }
          "
          :cell-class-name="
            ({ row }) => {
              return {
                'hedging-class-name': true,
                'hedging-class-name-hover': mouseId === row.ai_document_file_ledger_id,
                'select-item': selectId === row.ai_document_file_ledger_id,
              }
            }
          "
          :border="false"
          @cell-click="selectRow"
          @cell-mouseenter="cellMouse"
        >
          <template slot="columns">
            <vxe-table-column
              v-for="item in styleColumns"
              :key="item.ss_key"
              :title="item.title ? $t(langKey + item.title) : ''"
              :width="item.width"
              :field="item.ss_key"
              :column-key="item.ss_key"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="item.ss_key === 'index'" :class="scope.row[item.ss_key]">
                  {{ scope.$rowIndex + 1 }}
                </span>
                <span v-else-if="item.ss_key === 'currency'">
                  {{
                    `${scope.row[item.ss_key]} ${t(scope.row.account_type)} ${scope.row.account_no}`
                  }}
                </span>
                <span v-else-if="item.ss_key === 'icon'" :class="scope.row['status']">
                  <el-tooltip
                    v-if="
                      !isMatching &&
                        scope.row.ledger_detail.lg_id &&
                        scope.row.ledger_detail.lg_id !== row.lg_id
                    "
                    :content="t('isSelect')"
                    class="item"
                    effect="dark"
                    placement="top"
                  >
                    <div slot="content">
                      {{ t('isSelect') }}
                      <br>
                      <span>{{
                        `${finDate(scope.row.ledger_detail.vc_date)} ${
                          scope.row.ledger_detail.ref || ''
                        } ${scope.row.ledger_detail.vc_payee} ${scope.row.ledger_detail.descr}`
                      }}</span>
                    </div>
                    <i class="el-icon-warning action-icon" style="color: #ff644c; margin: 0" />
                  </el-tooltip>
                  <i
                    v-else-if="selectId === scope.row.ai_document_file_ledger_id"
                    class="el-icon-check action-icon"
                    style="color: #3e97dd"
                  />
                </span>
                <span v-else :class="scope.row['status']">
                  {{ scope.row[item.ss_key] }}
                </span>
              </template>
            </vxe-table-column>
          </template>
        </BTable>
      </div>
      <div v-else class="no-data select--pop-item">
        {{ t('noData') }}
      </div>
      <div v-show="!row.unpaid" slot="reference">
        <div class="reference-box">
          <div class="btn">
            <i
              :style="{ fontSize: finFontSize }"
              class="el-icon-document action-icon"
              style="color: #3e97dd"
              @click="open"
            />
            <!-- <i v-if="(row.vc_rstatus !== 'E' && !row.ai_document_file_ledger_id)" :style="{fontSize: finFontSize}" class="el-icon-document action-icon"></i> -->
            <!-- <img :style="{width: finFontSize1,height: finFontSize1}" class="documentLine" src="../../../../assets/documentLine.png" alt=""> -->
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
import {
  fetchSuggestions,
  saveBankLedgers,
  editBankLedgers,
} from '@/api/periodic/bankReconciliation'
import BTable from '@/components/BTable'
import BigSelect from '@/components/BigSelect/index'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import dateUtil from '@/utils/date'
export default {
  components: {
    BTable,
    BigSelect,
  },
  props: {
    row: {
      type: Object,
      default: () => {},
    },
    rowIndex: {
      type: Number,
      default: 0,
    },
    ac_code: {
      type: String,
      default: '',
    },
    isMatching: {
      type: Boolean,
      default: false,
    },
    vc_rdate: {
      type: String,
      default: '',
    },
    begin_date: {
      type: String,
      default: '',
    },
    end_date: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      hedgingTableList: [],
      langKey: 'bankAiReconciliation.',
      descViable1: false,
      downImgTime: null,
      loading: false,
      mouseId: null,
      selectId: null,
      dateList: [],
      styleColumns: [
        {
          alignment: 'center',
          ss_key: 'index',
          ss_type: 'column',
          title: 'index',
          width: 45,
        },
        {
          alignment: 'center',
          ss_key: 'ledger_date',
          ss_type: 'column',
          title: 'vc_date',
          width: 101,
        },
        {
          alignment: 'center',
          ss_key: 'currency',
          ss_type: 'column',
          title: 'account',
          width: 170,
        },
        {
          alignment: 'center',
          ss_key: 'particulars',
          ss_type: 'column',
          title: 'summary',
          width: 140,
        },
        {
          alignment: 'center',
          ss_key: 'file_name',
          ss_type: 'column',
          title: 'fileName',
          width: 100,
        },
        {
          alignment: 'center',
          ss_key: 'icon',
          ss_type: 'column',
          title: '',
          width: 40,
        },
      ],
    }
  },

  watch: {
    row: {
      handler: function(val) {},
      deep: true,
    },
    descViable1: {
      handler: function(val) {
        if (val) {
          this.getHedgingTableList()
          this.mouseId = this.row.ai_document_file_ledger_id || null
        }
      },
      deep: true,
    },
  },
  created() {
    this.selectId = this.row.ai_document_file_ledger_id
    this.makeDateList()
  },
  computed: {
    ...mapGetters(['styles']),
    finFontSize() {
      return this.styles.globalFontSize + 'px'
    },
    finFontSize1() {
      return Number(this.styles.globalFontSize) + 1 + 'px'
    },
    popperClassName2() {
      const name = this.hedgingTableList.length <= 0 ? 'select--popover-show' : ''
      return 'select--popover ' + name
    },
  },
  methods: {
    makeDateList() {
      let begin_date = this.begin_date
      let end_date = this.end_date
      console.log(begin_date, end_date, 'begin_date, end_date')
      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, '1')
      const lastDateObj = new Date(year, month + 1, '0')
      if (!this.begin_date) {
        begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        this.begin_date = begin_date
      }
      if (!this.end_date) {
        end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        this.end_date = end_date
      }
      this.dateList = this.getAllDate(begin_date, end_date)
    },
    onSelectChange() {
      const old_vc_rstatus = this.row.old_vc_rstatus
      const params = {
        fy_code: this.row.fy_code,
        lg_id: this.row.lg_id,
        vc_rstatus: this.row.vc_rstatus,
        vc_rdate: this.row.vc_rdate,
      }
      switch (params.vc_rdate) {
        case 'F':
        case 'E':
          params.vc_rstatus = params.vc_rdate
          params.vc_rdate = ''
          break
        case 'U':
          params.vc_rstatus = params.vc_rdate
          if (params.vc_rstatus !== old_vc_rstatus) {
            params.vc_rdate = this.end_date
          } else {
            params.vc_rdate = this.row.old_vc_rdate
          }
          break
        default:
          params.vc_rstatus = 'D'
          break
      }
      editBankLedgers([params])
        .then(() => {
          this.$emit('updateTable')
        })
        .catch(err => {
          this.$message.error(err.message)
        })
    },
    dateByFormat(date, format = 'yyyy-MM-dd') {
      return dateUtil.format(date, format)
    },
    getAllDate(begin_date, end_date) {
      const arr = []
      arr.push({
        label: 'N/A',
        value: 'E',
      })
      const ab = begin_date.split('-')
      const ae = end_date.split('-')
      const db = new Date()
      db.setUTCFullYear(ab[0], ab[1] - 1, ab[2])
      const de = new Date()
      de.setUTCFullYear(ae[0], ae[1] - 1, ae[2])
      const unixDb = db.getTime() - 24 * 60 * 60 * 1000
      const unixDe = de.getTime() - 24 * 60 * 60 * 1000
      for (let k = unixDb; k <= unixDe;) {
        k = k + 24 * 60 * 60 * 1000
        const d = new Date(parseInt(k))
        arr.push({
          label: this.dateByFormat(d, this.styles.dateFormat),
          value: this.dateByFormat(d),
        })
      }

      // arr.push({
      //   label: 'Future',
      //   value: 'F',
      // })
      arr.push({
        label: 'Unpaid',
        value: 'U',
      })
      return arr
    },
    getDateList(vc_date, vc_rstatus) {
      const data = this.dateList.filter(i => {
        if ('EFU'.includes(i.value)) {
          return true
        }
        const iDate = new Date(i.value)
        const vDate = new Date(vc_date)
        return iDate >= vDate
      })

      if (vc_rstatus === 'F') {
        console.log(vc_date, vc_rstatus, 'vc_date, vc_rstatus')
        console.log(this.dateList, 'this.dateList')
        data.push({
          label: 'Future',
          value: 'F',
        })
      }
      return data
    },
    finDate(date) {
      return dayjs(date).format('DD/MM/YY')
    },
    closeUpload() {
      this.$emit('closeDescription', true)
    },
    onSelectBriefDesc(item) {},
    // mouseleave(){
    //   if(this.downImgTime){
    //     clearTimeout(this.downImgTime)
    //     this.downImgTime = null
    //   }
    //   this.mouseId = this.selectId
    //   const row = this.hedgingTableList.find(item => item.ai_document_file_ledger_id === this.selectId)
    //   this.$emit('handleMouseenter', {row})
    // },
    cellMouse({ row }) {
      if (this.mouseId === row.ai_document_file_ledger_id) return
      this.mouseId = row.ai_document_file_ledger_id
      this.$emit('handleMouseenter', { row: { ...row, needRefresh: true } })
    },
    // mouseleave(){
    //   this.mouseId = null
    // },
    formatDate(date) {
      if (!date || date === 'E') return ''
      date = date.replace(/-/g, '/')
      return dateUtil.format(new Date(date), this.styles.dateFormat)
    },
    async getHedgingTableList() {
      if (this.loading) return
      this.loading = true
      try {
        const params = {
          ac_code: this.ac_code,
          vc_rdate: this.row.vc_date,
          show_already_selected: this.isMatching ? 'N' : 'Y',
          amount_dr: this.row.amount_dr,
          amount_cr: this.row.amount_cr,
          select_ai_document_file_ledger_id: this.row.ai_document_file_ledger_id,
        }
        if (this.row.ref) {
          params.ref = this.row.ref
        }
        console.log(params)
        let res = await fetchSuggestions(params)
        if (this.isMatching && !this.vc_rdate) {
          res = res.filter(item => !item.ledger_detail.lg_id)
        }
        this.hedgingTableList = res
        this.selectId = this.row.ai_document_file_ledger_id
      } catch (error) {
        this.$message.error(error.message)
      }
      this.loading = false
    },
    async selectRow({ row }) {
      if (
        !this.isMatching &&
        row.ledger_detail.lg_id &&
        row.ledger_detail.lg_id !== this.row.lg_id
      ) {
        return
      }
      let isUnbundle = false
      let ai_document_file_ledger_id = row.ai_document_file_ledger_id
      if (this.selectId === row.ai_document_file_ledger_id) {
        ai_document_file_ledger_id = null
        isUnbundle = true
      }
      try {
        const params = {
          fy_code: this.row.fy_code,
          lg_id: this.row.lg_id,
          ai_document_file_ledger_id,
        }
        await saveBankLedgers(params)
        this.selectId = !isUnbundle ? row.ai_document_file_ledger_id : null
        this.$message({
          message: this.$t('message.success'),
          type: 'success',
        })
        this.descViable1 = false
        setTimeout(() => {
          this.$emit('updateTable')
        }, 100)
      } catch (err) {
        console.log(err)
      }
    },
    open() {
      this.descViable1 = !this.descViable1
      if (!this.descViable1) {
        if (this.downImgTime) {
          clearTimeout(this.downImgTime)
          this.downImgTime = null
        }
        if (this.mouseId !== this.selectId) {
          const row = this.hedgingTableList.find(
            item => item.ai_document_file_ledger_id === this.selectId,
          )
          this.$emit('handleMouseenter', { row: { ...row, needRefresh: true } })
        }
      } else {
        this.$emit('open', this.rowIndex)
      }
    },
    close(rowIndex = -1) {
      if (rowIndex === -1 || (rowIndex > -1 && rowIndex !== this.rowIndex)) {
        this.descViable1 = false
      }
    },
  },
}
</script>

<style scoped lang="scss">
.btn {
  width: 25px;
  height: 25px;
  background: #f9f9f9;
  border-radius: 0px 4px 4px 0px;
  border: 1px solid #dcdfe6;
  border-left: none;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1px;
}
.margin--top {
  margin-top: 40px;
}
.select--pop-item {
  overflow-y: auto;
  padding: 5px;
  width: 612px;
  max-height: 400px;
  .item {
    width: 90%;
    margin: 5px;
  }
}
::v-deep {
  .el-popover__reference-wrapper {
    .el-input {
      width: 100%;
    }
  }
}
.toast-text {
  font-size: 12px;
  color: #666;
}
.select--pop-item::-webkit-scrollbar {
  width: 5px;
}
.select--pop-item::-webkit-scrollbar-thumb {
  border-radius: 5px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #c1c1c1;
}
.el-upload__tip {
  font-family: MicrosoftJhengHeiRegular;
  font-size: 12px;
  color: #949494 !important;
  line-height: 16px;
  text-align: center;
  font-style: normal;
}
.document-table {
  max-height: 500px;
  margin-top: 30px;
}
.dialog-footer {
  font-size: 14px;
}
.no-data {
  text-align: center;
  font-size: 14px;
  height: 200px;
  line-height: 180px;
  color: #666;
}
::v-deep {
  .hedging-class-name {
    background: #fff;
    border: none !important;
    cursor: pointer;
  }
  .hedging-class-name-hover {
    background: #e8f5fe;
  }
  .select-item {
    color: #3e97dd;
  }
  .vxe-table--border-line {
    // display: none  !important;
  }
  .vxe-table--header-border-line {
    // display: none !important;
  }
  .body--wrapper {
    height: 100% !important;
  }
}
.documentLine {
  cursor: pointer;
}
.vc_rdate_input {
  width: 100%;
  height: 25px;
  border: 1px solid #dcdfe6;
  border-radius: 4px 0px 0px 4px;
  padding-left: 15px;
  background-color: #fff;
  cursor: pointer;
}
.reference-box {
  display: flex;
  align-items: center;
}
.disabled {
  cursor: not-allowed !important;
  background-color: #f5f7fa;
  i {
    cursor: not-allowed !important;
    color: #c0c4cc !important;
  }
}
.hedging-table-box {
  display: flex;
  align-items: center;
}
</style>
<style lang="scss">
.select--popover {
  margin: 0 !important;
  padding: 0 !important;
}
.vxe-table--tooltip-wrapper {
  z-index: 9999 !important;
}
.hedging-select {
  .el-input__inner {
    padding-right: 0 !important;
    border-radius: 4px 0 0 4px !important;
  }
  .el-input__suffix {
    display: none !important;
  }
}
.big-select-show {
  width: 100%;
  .el-input__inner {
    border-radius: 4px !important;
  }
}
</style>
