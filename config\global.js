'use strict'
// dev(開發模式)
const localhost = '*************' // HOST
const localProtocol = '9527'  // 開發環境端口
const proxyPath = '/api'
const proxyTarget = 'http://*************/edac/public/api' // 後端API根目錄


// prod(生產模式)
const serverName = '"*************"' // vue 項目所在服務器名 ，發送請求時加入 header
const protocol = 'http'
const ip = '*************'
const port = '80'
const projectRoot = 'edac'
const remoteProjectName= 'edac'
const uri = 'public/storage'
const rptProtocol = 'http'
const rptIp = '*************'
const rptPort = '8080'
const rptUrl = 'EDReport/run'
const webCode = 'AC'
const TUTORIAL_PATH = 'https://www.hkschoolsoftware.com/loading/tutorial?tmp=464ei1Qv8oFENolBHwzRkkp%2FnQqorf7sG2CYjq1RhQ'

module.exports = {
  // dev
  LOCALHOST: localhost,
  LOCAL_PROTOCOL: localProtocol,
  PROXY_PATH: proxyPath,
  PROXY_TARGET: proxyTarget,

  // prod
  SERVER_NAME: serverName,
  PROTOCOL: protocol,
  IP: ip,
  PORT: port,
  PROJECT_ROOT: projectRoot,
  REMOTE_PROJECT_NAME: remoteProjectName,
  URI: uri,
  RPT_PROTOCOL: rptProtocol,
  RPT_IP:rptIp,
  RPT_PORT: rptPort,
  RPT_URL: rptUrl,
  WEB_CODE: webCode,
  TUTORIAL_PATH: TUTORIAL_PATH
}
