<template>
  <el-form id="column-position" ref="form" class="form mini-form native" label-width="120px">
    <!-- 位置 -->
    <el-form-item
      v-if="canSort"
      :label="$t('setting.printout.label.position')"
      style="height: auto"
    >
      <el-drag-select
        v-model="positions"
        :placeholder="$t('placeholder.selectDragSort')"
        style="width: 300px"
        multiple
      >
        <el-option
          v-for="(item, index) in options"
          :key="index"
          :label="$t(langKey + item)"
          :value="item"
        />
      </el-drag-select>
    </el-form-item>
    <!--    <divider width="80%"/>-->
    <!-- 列屬性 -->
    <el-form-item
      v-for="item in columnData"
      :key="item.name"
      :label="$t(langKey + item.name)"
      class="form-item-simple"
    >
      <el-input-number v-model="item.width" :controls="false" />
      <el-select v-model="item.alignment">
        <el-option :label="$t('setting.printout.content.alignment_left')" value="left" />
        <el-option :label="$t('setting.printout.content.alignment_center')" value="center" />
        <el-option :label="$t('setting.printout.content.alignment_right')" value="right" />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
import ElDragSelect from '@/components/DragSelect' // base on element-ui
import Divider from '@/components/Divider'

export default {
  name: 'ColumnsPositions',
  components: { ElDragSelect, Divider },
  props: {
    data: {
      type: Array,
      default: () => [],
      request: true,
    },
    columnFieldList: {
      type: Array,
      default: () => [],
      request: true,
    },
    langKey: {
      type: String,
      default: '',
      request: true,
    },
    canSort: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      positions: [],
      // columnData: [],
      options: [],
    }
  },
  computed: {
    columnData: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  watch: {
    data(newVal, oldVal) {
      if (!this.isEquivalent(newVal, oldVal)) {
        console.log('update data for columns positions')
        this.formatData()
      }
    },
    // columnData: {
    //   get() { return this.data },
    //   set(val) { this.$emit('update:data', val) }
    // },
    positions(val) {
      console.log(val)
      const data = this.columnData
      data.forEach(item => {
        const x = val.findIndex(v => v === item.name)
        item.position = x === null ? 0 : x + 1
      })
      this.columnData = data
    },
  },
  mounted() {
    this.formatData()
  },
  methods: {
    isEquivalent(a, b) {
      const aProps = Object.getOwnPropertyNames(a)
      const bProps = Object.getOwnPropertyNames(b)
      if (aProps.length !== bProps.length) {
        return false
      }
      for (let i = 0; i < aProps.length; i++) {
        const propName = aProps[i]
        if (propName === '__ob__') {
          continue
        }
        if (typeof a[propName] === 'object') {
          if (!this.isEquivalent(a[propName], b[propName])) {
            return false
          }
        } else if (a[propName] !== b[propName]) {
          return false
        }
      }
      return true
    },
    formatData() {
      let positions = [] // = [...Array(this.columnFieldList.length)].map(() => '')
      const columnData = []
      const options = []
      if (!this.columnFieldList.length) {
        return
      }
      this.columnFieldList.forEach(field => {
        options.push(field)
      })

      if (!this.data || !this.data.length) {
        // 初始化
        this.columnFieldList.forEach((field, index) => {
          columnData.push({
            name: field,
            width: 100,
            alignment: 'left',
            position: index + 1,
          })
        })
        this.columnData = columnData
        positions = this.columnFieldList.map(key => key)
      } else {
        this.columnFieldList.forEach((field, index) => {
          const i = this.data.findIndex(i => i.name === field)
          if (i === -1) {
            this.data.push({
              name: field,
              width: 100,
              alignment: 'left',
              position: 0,
            })
          }
        })
        for (let i = this.data.length - 1; i >= 0; i--) {
          const item = this.data[i]
          // console.log(i, item.name)
          if (!this.columnFieldList.includes(item.name)) {
            this.data.splice(i, 1)
            // console.log('刪除', item)
          }
        }

        // let i = 0
        this.data.forEach(item => {
          const newItem = Object.assign({}, item)
          columnData.push(newItem)
        })
        positions = this.data
          .filter(i => i.position)
          .sort((a, b) => {
            return a.position - b.position
          })
          .map(i => i.name)
      }
      this.options = options
      this.positions = positions
    },
  },
}
</script>

<style lang="scss" scoped>
#column-position {
  padding-top: 4px;
  /deep/ {
    .el-form-item__content {
      height: auto;
    }
    .el-select {
      height: auto !important;
    }
    .el-input {
      height: auto !important;
    }
  }
}
</style>
