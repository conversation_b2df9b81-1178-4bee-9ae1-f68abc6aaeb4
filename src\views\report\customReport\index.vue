<template>
  <div ref="page" v-loading="loading" class="app-container">
    <div class="container">
      <div class="left-box">
        <template v-if="!isAddOrEdit">
          <div ref="filter" class="filter">
            <el-form :inline="true" label-width="60px" class="mini-form">
              <!-- 年份 -->
              <el-form-item :label="$t('filters.years')">
                <el-select
                  ref="year"
                  v-model="preferences.filters.selectedYearId"
                  class="year"
                  style="width: 100px"
                  @change="onChangeYear"
                >
                  <el-option
                    v-for="item in years"
                    :key="item.fy_id"
                    :label="item.fy_name"
                    :value="item.fy_id"
                  />
                </el-select>
              </el-form-item>
              <!-- 時期 -->
              <el-form-item :label="$t('filters.period')">
                <date-range
                  :start-date.sync="firstDate"
                  :end-date.sync="lastDate"
                  :disabled="!preferences.filters.selectedYearId"
                  :picker-options="pickerOptions"
                />
              </el-form-item>

              <el-form-item>
                <el-button size="mini" type="primary" @click="onAdd">
                  {{ $t('customReport.addStatement') }}
                </el-button>
                <el-button size="mini" type="primary" @click="reloadData">
                  {{ $t('customReport.exportAll') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="table-box">
            <ETable
              :data="tableData"
              :v-loading="loading"
              :default-top="215"
              :show-actions="false"
              :row-class-name="rowClassName"
              border
              style="width: 100%"
              @row-click="onRowClick"
            >
              <template slot="columns">
                <el-table-column type="index" width="50" />
                <el-table-column
                  :label="$t('customReport.title')"
                  prop="report_name"
                  width="400"
                />
                <el-table-column
                  :label="$t('customReport.actions')"
                  prop="actions"
                  min-width="600"
                >
                  <template slot-scope="scope">
                    <i class="el-icon-edit action-icon" style="margin-right: 5px;" @click="onEdit(scope)" />
                    <i class="el-icon-close action-icon" @click="onDelete(scope)" />
                  </template>
                </el-table-column>
              </template>
            </ETable>
          </div>
        </template>
        <template v-else>
          <Add @changeRightBox="changeRightBox" @cancel="onCancel" />
        </template>
      </div>
      <div class="right-box">
        <RightBox :custom-report-id="custom_report_id" :is-add-or-edit="isAddOrEdit" />
      </div>
    </div>
  </div>
</template>

<script>
import DateRange from '@/components/DateRange/index'
import loadPreferences from '@/views/mixins/loadPreferences'
import ETable from '@/components/ETable'
import RightBox from './components/rightBox'
import { fetchYears, searchTheDate } from '@/api/master/years'
import { fetchCustomReports } from '@/api/report/customReports'
import dayjs from 'dayjs'
import Add from './components/add'
export default {
  name: 'ReportCustomizedReports',
  components: {
    DateRange,
    ETable,
    RightBox,
    Add,
  },
  mixins: [loadPreferences],
  data() {
    return {
      langKey: 'customReport.',
      loading: false,
      preferences: {
        filters: {
          selectedYearId: '',
          selectedMonth: '',
          begin_date: '',
          end_date: '',
        },
      },
      years: [],
      tableData: [],
      addContentAreaVisible: false,
      firstDate: '',
      lastDate: '',
      pickerOptions: {},
      custom_report_id: '',
      selectIndex: -1,
      isAddOrEdit: false,
    }
  },
  created() {
    this.getYears()
    this.getCustomReports()
  },
  methods: {
    onAdd() {
      this.custom_report_id = ''
      this.isAddOrEdit = true
    },
    rowClassName({ row, rowIndex }) {
      if (rowIndex === this.selectIndex) return 'selected'
      return ''
    },
    async getYears() {
      try {
        const res = await fetchYears()
        if (res) {
          this.years = res
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getCustomReports() {
      try {
        const res = await fetchCustomReports()
        if (res) {
          this.tableData = res.map((item, index) => ({
            ...item,
            index,
            is_selected: false,
          }))
        }
      } catch (error) {
        console.log(error)
      }
    },
    async onChangeYear(year) {
      try {
        const fy_code = this.years.find(item => item.fy_id === year).fy_code
        if (!fy_code) return
        const res = await searchTheDate({ fy_code })
        if (res) {
          console.log(res)
          this.firstDate = res.the_first_day
          this.lastDate = res.the_last_day
          this.pickerOptions = {
            disabledDate: (time) => {
              // 禁用firstDate之前lastDate之後
              return time.getTime() < dayjs(this.firstDate).valueOf() || time.getTime() > dayjs(this.lastDate).valueOf()
            },
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    onChangeMonth(month) {
    },
    fetchData() {
    },
    reloadData() {
    },
    async onRowClick(row) {
      this.selectIndex = row.index
      this.custom_report_id = row.custom_report_id
    },
    onEdit(row) {
      console.log(row)
    },
    onDelete(row) {
      console.log(row)
    },
    changeRightBox(data) {
      console.log(data)
    },
    onCancel() {
      this.isAddOrEdit = false
    },
  },
}
</script>

<style scoped lang="scss">
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.el-icon-date {
  cursor: pointer;
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  padding: 0;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 0 0 8px;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    border-collapse: collapse;
    tbody {
      tr.vxe-body--row {
        td.vxe-body--column {
          border: none;
          background: none;
        }
      }
      .sum-row {
        border-bottom: 1px solid #e6e6e6;
      }
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
    .selected {
      background: #E8F5FE;
    }
  }
  .cash-book-table {
    height: calc(100vh - 200px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
.container {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 10px);
  .left-box {
    width: 70%;
    padding: 14px 20px 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      .el-table {
        height: 100%;
      }
    }
  }
  .right-box {
    width: 30%;
    padding: 14px 20px 20px 20px;
    border-left: 1px solid #e6e6e6;
    height: 100%;
    overflow-y: auto;
  }
}
</style>
