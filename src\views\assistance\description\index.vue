<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <el-row>
          <!-- 賬目類別 -->
          <el-select v-model="preferences.filters.fund" class="fund" @change="changeFund">
            <el-option :label="allFund.label" :value="allFund.value" />
            <el-option
              v-for="item in funds"
              :key="item.fund_id"
              :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
              :value="item.fund_id"
            />
          </el-select>

          <!-- 會計週期 -->
          <el-select v-model="preferences.filters.year" class="year" @change="reloadData">
            <el-option :label="allYear.label" :value="allYear.value" />
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
          <!-- desc -->
          <el-input
            v-model="preferences.filters.desc"
            class="fund"
            @keyup.enter.native="reloadData"
          />

          <!-- search按鈕 -->
          <div class="filter-icon search" @click="reloadData">
            <i class="edac-icon edac-icon-search" :title="$t('btnTitle.search')" />
          </div>

          <!-- 會計科目 -->
          <!--          <TreeSelect-->
          <!--            :disabled="!preferences.filters.fund"-->
          <!--            :options="accountOptions"-->
          <!--            :value="preferences.filters.account"-->
          <!--            :searchable="false"-->
          <!--            :show-count="true"-->
          <!--            :clearable="false"-->
          <!--            :default-expand-level="Infinity"-->
          <!--            :normalizer="normalizer"-->
          <!--            class="tree-select"-->
          <!--            @select="onSelect"-->
          <!--          >-->
          <!--            <label-->
          <!--              slot="option-label"-->
          <!--              slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"-->
          <!--              :class="labelClassName">-->
          <!--              {{ language === 'en' ? node.label_en : node.label_cn }}-->
          <!--              <span-->
          <!--                v-if="shouldShowCount"-->
          <!--                :class="countClassName">-->
          <!--                ({{ count }})-->
          <!--              </span>-->
          <!--            </label>-->
          <!--          </TreeSelect>-->

          <SelectTree
            v-model="preferences.filters.account"
            :options="accountOptions"
            :props="{
              label: language === 'en' ? 'name_en' : 'name_cn',
              value: preferences.filters.childrenFund ? 'fund_id' : 'code',
              code: 'code',
              group_id: 'fund_id',
              value_id: 'account_id',
              children: 'children',
            }"
            class="tree-select"
            @change="onChangeTreeSelect"
          />
        </el-row>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div v-if="hasPermission_Add" :title="$t('btnTitle.add')" class="icon add" @click="onAdd">
            <svg-icon icon-class="add" class="action-icon" />
          </div>
          <!-- 按鈕 -->
          <div
            v-if="hasPermission_Input"
            :title="$t('btnTitle.importExcel')"
            class="icon import"
            @click="importDialog = true"
          >
            <svg-icon icon-class="import" class="action-icon" />
          </div>
          <!--  -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            class="icon export"
            @click="onExport"
          >
            <svg-icon icon-class="export" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          v-loading="loading"
          :data="tableData"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <template slot="columns">
            <el-table-column
              v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
              :key="item.ss_key"
              :label="$t(langKey + item.ss_key)"
              :align="item.alignment"
              :width="item.width"
              :property="column_property(item)"
              :column-key="item.ss_key"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="column_label(item) === 'BIE'">{{
                  scope.row['BIE'] && scope.row['BIE'].replace(/,/g, ' ')
                }}</span>
                <span v-else>{{ scope.row[column_label(item)] }}</span>
              </template>
            </el-table-column>
          </template>
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add'"
        :edit-object="editObject"
        :default-account="preferences.filters.childrenFund ? '' : preferences.filters.account"
        :fy-code="preferences.filters.year"
        @onCancel="onViewCancel"
      />
      <editPage
        v-else-if="leftView === 'edit'"
        :edit-object="editObject"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      width="450px"
      class="dialog"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'
import editPage from './edit'

import {
  deleteDescription,
  fetchDescriptions,
  exportDescriptions,
  importDescriptions,
} from '@/api/assistance/description'
import { fetchYears } from '@/api/master/years' // year
import { fetchFunds } from '@/api/master/funds' // 撥款
import { getAccountTree } from '@/api/master/account' // 撥款

import ETable from '@/components/ETable'
import UploadExcel from '@/components/UploadExcel/index'
import SelectTree from '@/components/SelectTree/index.vue'

// import TreeSelect from '@riophae/vue-treeselect'
// import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

// 導出Excel
import { exportExcel, importExcel } from '@/utils/excel'

export default {
  name: 'MasterDescriptionIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    editPage,
    ETable,
    UploadExcel,
    SelectTree,
    // TreeSelect
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      leftView: '',

      editObject: null,

      tableData: [],
      years: [],
      funds: [],
      accountOptions: [], // 會計科目，動態
      importDialog: false,
      loading: false,

      langKey: 'assistance.description.label.',
      tableColumns: ['ac_code', 'desc', 'BIE'],
      desc: '',
      preferences: {
        filters: {
          year: '', // fy_code 活躍的會計週期編號
          fund: '', // fund_id 賬目類別id
          account: '', // ac_code 會計科目編號
          childrenFund: '', // ac_code 會計科目編號
          desc: '', // desc
        },
      },
      exportFileName: 'description_list', // 導出文件名

      normalizer(node) {
        return {
          id: !node.account_id ? node.fund_id : node.code,
          label:
            (node.fund_id ? '' : node.code ? `[${node.code}]` : '') +
            (this.language === 'en' ? node.name_en : node.name_cn),
          label_en: (node.fund_id ? '' : node.code ? `[${node.code}]` : '') + node.name_en,
          label_cn: (node.fund_id ? '' : node.code ? `[${node.code}]` : '') + node.name_cn,
          children: node.children,
        }
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
    vt_category_list() {
      return ['P', 'R', 'C', 'T', 'J', 'A'].map(i => {
        return {
          label: this.$t('voucher_type_category.' + i),
          value: i,
        }
      })
    },
    defaultAccounts() {
      return [this.allAccount]
    },
    allAccount() {
      return {
        name_cn: this.$t('master.account.all_account'),
        name_en: this.$t('master.account.all_account'),
        abbr_cn: this.$t('master.account.all_account'),
        abbr_en: this.$t('master.account.all_account'),
        fund_id: '',
        account_id: '',
        code: '',
      }
    },
    allFund() {
      return {
        label: this.$t('master.fund.all_fund2'),
        value: '',
      }
    },
    allYear() {
      return {
        label: this.$t('master.year.all_year'),
        value: '',
      }
    },
  },
  created() {
    this.accountOptions = this.defaultAccounts
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    onSelect(node, instanceId) {
      this.preferences.filters.childrenFund = !node.account_id
      this.preferences.filters.account = node.account_id ? node.code : node.fund_id
      this.reloadData()
    },
    onChangeTreeSelect({ value, selectGroup, group_id, code }) {
      this.preferences.filters.childrenFund = selectGroup
      this.preferences.filters.account = selectGroup ? group_id : code
      this.reloadData()
    },
    onAdd(scope) {
      this.editObject = null
      this.leftView = 'add'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${scope.row.desc}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const desc_id = scope.row.desc_id
          return new Promise((resolve, reject) => {
            deleteDescription(desc_id)
              .then(res => {
                if (this.editObject && this.editObject.desc_id === desc_id) {
                  this.onViewCancel()
                }
                this.reloadData()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      // 獲取所有會計週期
      fetchYears()
        .then(res => {
          this.years = res
        })
        // 帳目類別
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
        })
        .then(this.loadUserPreference)
        // 帳目類別
        .then(() => {
          let bool = false
          this.funds.forEach(i => {
            if (i.fund_id === this.preferences.filters.fund) {
              bool = true
              return
            }
          })
          if (!bool) {
            this.preferences.filters.fund = ''
          }

          bool = false
          this.years.forEach(i => {
            if (i.fy_code === this.preferences.filters.year) {
              bool = true
              return
            }
          })
          if (!bool) {
            this.preferences.filters.year = ''
          }

          // 異步獲取了
          if (this.preferences.filters.fund) {
            return this.loadAccountTree(this.preferences.filters.fund)
          } else {
            this.accountOptions = this.defaultAccounts
            return Promise.resolve()
          }
        })
        .then(this.reloadData)
        .catch(() => {})
    },
    reloadData() {
      this.loading = true
      fetchDescriptions({
        fund_id: this.preferences.filters.childrenFund
          ? this.preferences.filters.account
          : this.preferences.filters.fund,
        fy_code: this.preferences.filters.year,
        ac_code: this.preferences.filters.childrenFund
          ? undefined
          : this.preferences.filters.account,
        desc: this.preferences.filters.desc,
      })
        .then(res => {
          this.tableData = res
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.reloadData()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      this.loading = true
      exportDescriptions()
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onImport({ results, header }) {
      this.loading = true
      importExcel(this, importDescriptions, results, header)
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    // 修改帳目類別，重新獲取會計科目
    changeFund(val) {
      this.preferences.filters.account = ''
      this.preferences.filters.childrenFund = false
      if (val) {
        this.loadAccountTree(val).then(this.reloadData)
      } else {
        this.reloadData()
      }
    },
    column_property(item) {
      if (item.ss_key === '_index') {
        return '#'
      } else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key + (this.language === 'en' ? 'en' : 'cn')
      } else {
        return item.ss_key
      }
    },
    column_label(item) {
      if (item.ss_key === '_index') {
        return '#'
      } else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key.substring(item.ss_key.length - 1)
      } else {
        return item.ss_key
      }
    },
    // 載入會計科目樹
    loadAccountTree(fund_id) {
      return new Promise((resolve, reject) => {
        getAccountTree(fund_id)
          .then(res => {
            this.accountOptions = [this.allAccount, ...res]
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    repeat(str, n) {
      return new Array(n + 1).join(str)
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .fund,
  .account,
  .year {
    width: 130px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}

.tree-select {
  width: 402px;
  label {
    color: #606266;
    font-weight: normal;
  }
  margin-top: 5px;
  vertical-align: bottom;
  /deep/ .vue-treeselect__control {
    border: 1px solid #dcdfe6;
  }
  /deep/ .vue-treeselect__menu {
    border: 1px solid #dcdfe6;
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
.filter {
  .filter-icon {
    background: #68afff;
    border-radius: 4px;
    font-size: 20px !important;
    cursor: pointer;
    position: relative;
    display: inline-block;
    line-height: 20px;

    width: 20px;
    text-align: center;
    height: 20px;
    vertical-align: middle;
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
      width: 1.5em;
      height: 1.5em;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
    i {
      display: block;
      width: 20px;
      vertical-align: middle;
      text-align: center;
      color: white;
      line-height: 20px !important;
    }
  }
}

.vue-treeselect {
  display: inline-block;
  position: relative;
  width: 425px;

  .vue-treeselect__control {
    line-height: 25px;
    height: 25px;
    vertical-align: middle;
    .vue-treeselect__placeholder,
    .vue-treeselect__single-value {
      line-height: 25px;
      height: 25px;
      vertical-align: middle;
    }
  }
}
.dialog {
  .el-dialog__body {
    height: auto !important;
  }
}
</style>
