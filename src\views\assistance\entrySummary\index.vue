<template>
  <div class="app-container">
    <VBreadCrumb :show="true" class="breadcrumb" />

    <el-form
      ref="form"
      v-loading="loading"
      :model="preferences.filters"
      label-position="right"
      label-width="80px"
    >
      <el-form-item>
        <el-button size="mini" type="primary" @click="onExport">
          {{ $t('button.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import loadPreferences from '@/views/mixins/loadPreferences'
import { exportExcel } from '@/utils/excel'
import { exportEntrySummary } from '@/api/assistance/entrySummary'

export default {
  name: 'AssistanceEntrySummary',
  components: {
    VBreadCrumb,
  },
  mixins: [loadPreferences],
  data() {
    return {
      loading: false,
      preferences: {
        filters: {},
      },
    }
  },
  computed: {},
  created() {
    this.saveUserLastPage()
  },
  methods: {
    async onExport() {
      try {
        this.loading = true
        const res = await exportEntrySummary()
        await exportExcel(res, '')
        this.$message.success(this.$t('file.exportSuccess'))
      } catch (e) {
        this.$message.error(this.$t('file.exportError'))
        console.log(e)
      }
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.year {
  width: 400px;
}
</style>
