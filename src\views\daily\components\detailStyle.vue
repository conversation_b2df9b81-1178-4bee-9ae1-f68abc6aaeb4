<template>
  <div id="customStyleDialog" class="styleDialog">
    <el-dialog
      :visible.sync="dialogVisible"
      :before-close="onCancel"
      :show-close="false"
      :close-on-click-modal="true"
      :title="title"
      width="60%"
      @open="openDialog"
    >
      <div style="min-width: 640px">
        <el-form v-loading="loading" :model="form" label-width="120px" label-position="right">
          <el-form-item :label="$t(langKey + 'detailStyle')" />
          <el-form-item :label="$t(langKey + 'staff')" prop="show_staff">
            <el-checkbox v-model="form.show_staff" true-label="1" false-label="0" size="mini" />
          </el-form-item>
          <el-form-item :label="$t(langKey + 'ref')" prop="show_ref">
            <el-checkbox v-model="form.show_ref" true-label="1" false-label="0" size="mini" />
          </el-form-item>
          <el-form-item :label="$t(langKey + 'budget')" prop="show_budget">
            <el-checkbox v-model="form.show_budget" true-label="1" false-label="0" size="mini" />
          </el-form-item>
          <el-form-item :label="$t(langKey + 'quote')" prop="show_quote">
            <el-checkbox v-model="form.show_quote" true-label="1" false-label="0" size="mini" />
          </el-form-item>
          <el-form-item :label="$t(langKey + 'dept')" prop="show_dept">
            <el-checkbox v-model="form.show_dept" true-label="1" false-label="0" size="mini" />
          </el-form-item>
          <el-form-item :label="$t(langKey + 'contra')" prop="show_contra">
            <el-checkbox v-model="form.show_contra" true-label="1" false-label="0" size="mini" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="onCancel">{{ $t('button.cancel') }}</el-button>
        <el-button size="mini" type="info" @click="onLoadDefault">{{
          $t('button.loadDefault')
        }}</el-button>
        <el-button size="mini" type="info" @click="onSaveDefault">{{
          $t('button.saveDefault')
        }}</el-button>
        <el-button size="mini" type="danger" @click="initData">{{ $t('button.reset') }}</el-button>
        <el-button size="mini" type="primary" @click="onSave">{{ $t('button.update') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import {
  getUserStyleSheets,
  editUserStyleSheets,
  getDefaultStyleSheets,
  editDefaultStyleSheets,
  editStaffStyleSheets,
  getStaffStyleSheets,
} from '@/api/settings/user/style'

import ElDragSelect from '@/components/DragSelect' // base on element-ui

export default {
  components: { ElDragSelect },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'Setting',
    },
    langKey: {
      type: String,
      default: 'master.user.label.',
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
  },
  // props: ['dialogVisible'],
  data() {
    return {
      form: {
        show_staff: '0',
        show_ref: '0',
        show_budget: '0',
        show_quote: '0',
        show_dept: '0',
        show_contra: '0',
      },
      loading: true,
      editData: [],
      editDataTemp: [],
      positionOptions: [],
      positions: [],
      leftViewWidth: 50,
      firstFieldWidth: 150,
      // ss_code: 'ac.daily.detail',
      items: ['show_staff', 'show_ref', 'show_budget', 'show_quote', 'show_dept', 'show_contra'],
      // permission_code: null
    }
  },
  computed: {
    ...mapGetters(['system', 'user_id']),
    options() {
      return this.columns.map(i => i)
    },
    ss_code() {
      // return this.ss_code ? this.ss_code : (this.$route.meta.p_code + '.detail')
      return this.$route.meta.p_code + '.detail'
    },
    permission_code() {
      // return this.ss_code ? this.ss_code : (this.$route.meta.p_code + '.detail')
      return this.ss_code
    },
  },
  created() {
    // this.permission_code = this.ss_code ? this.ss_code : this.$route.meta.p_code
  },
  mounted() {},
  activated() {},
  methods: {
    openDialog() {
      this.initData()
    },
    initData() {
      this.loading = true
      this.generateDefaultData()
        .then(this.fetchData)
        .then(this.loadData)
        .catch(() => {})
        .finally(() => {
          this.$nextTick(() => {
            this.loading = false
          })
        })
    },
    fetchData() {
      const system = this.system
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code
      let user_id = this.user_id
      let staff_id = this.user_id

      let api = getStaffStyleSheets
      if (system === 'AC') {
        api = getUserStyleSheets
        staff_id = undefined
      } else {
        user_id = undefined
      }
      return new Promise((resolve, reject) => {
        api({ user_id, staff_id, system, ss_code })
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    generateDefaultData() {
      return new Promise((resolve, reject) => {
        const form = {}
        this.items.forEach(key => {
          form[key] = '0'
          form[key + '_id'] = ''
        })
        this.form = form
        resolve(form)
      })
    },
    loadData(styleData) {
      // 獲取已保存的設置
      const form = Object.assign({}, this.form)
      styleData
        .filter(item => item.ss_type === 'item')
        .forEach(item => {
          form[item.ss_key] = item.value
          form[item.ss_key + '_id'] = item.ss_id
        })
      this.form = form
    },
    onCancel(done) {
      this.$emit('update:dialogVisible', false)
    },
    getSettingJSON() {
      const defaultItem = {
        ss_id: '',
        user_id: null,
        staff_id: null,
        system: this.system,
        ss_code: this.permission_code,
        ss_type: 'item',
        seq: 1,
        ss_key: '',
        value: null,
        alignment: 'left',
        width: 100,
        x: 1,
        y: null,
      }
      const form = this.form
      const data = []
      for (let i = 0; i < this.items.length; i++) {
        const key = this.items[i]
        const item = Object.assign({}, defaultItem)
        item.ss_id = form[key + '_id']
        item.ss_key = key
        item.value = form[key]
        item.seq = i + 1
        data.push(item)
      }

      return data
    },
    onSave() {
      let user_id = this.user_id
      let staff_id = this.user_id
      const system = this.system
      const ss_code = this.permission_code
      const ss_setting_json = this.getSettingJSON()
      if (!ss_setting_json) {
        return
      }
      let api = editStaffStyleSheets
      if (system === 'AC') {
        api = editUserStyleSheets
        staff_id = undefined
      } else {
        user_id = undefined
      }
      api({ user_id, staff_id, system, ss_code, ss_setting_json })
        .then(res => {
          this.$message.success(this.$t('message.success'))
          this.$emit('update:dialogVisible', false)
          this.$emit('reloadStyleSheets')
          this.$emit('close')
        })
        .catch(() => {})
    },
    onLoadDefault() {
      this.loading = true
      const system = this.system
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code
      this.generateDefaultData()
        .then(() => {
          return getDefaultStyleSheets({ system, ss_code })
        })
        .then(this.loadData)
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    onSaveDefault() {
      const system = this.system
      const ss_code = this.permission_code
      const ss_setting_json = this.getSettingJSON()
      editDefaultStyleSheets({ system, ss_code, ss_setting_json })
        .then(res => {
          this.$message.success(this.$t('message.success'))
        })
        .catch(() => {})
    },
    handleLabel(item) {
      if (item.ss_id) {
        if (item.ss_key === '_index') {
          return '#'
        } else {
          return this.langKey + item.ss_key
        }
      } else {
        return '---'
      }
    },
  },
}
</script>
<style scoped>
.dialog_item {
  display: flex;
}
</style>
<style lang="scss">
.flex {
  display: flex;
  margin-right: 10px;
  width: 150px;
  .num {
    width: 50px;
  }
}
.dialog_item .el-input {
  width: 120px;
  margin-right: 20px;
}
.styleDialog .el-dialog__header {
  padding: 20px 20px 0px;
  /* color: #2e9eff */
}
.styleDialog .el-dialog__header .el-dialog__title {
  color: #2e9eff;
  font-weight: bold;
}
.styleDialog .el-dialog__body {
  padding: 20px 20px;
}
.styleDialog .el-form-item {
  margin-bottom: 10px;
}
.el-dialog__body {
  max-height: 561px;
  overflow: auto;
}

#customStyleDialog.styleDialog {
  .select-align {
    .el-input {
      width: 100px;
    }
  }
  .el-form {
    .el-form-item {
      .el-form-item__label {
        width: 140px !important;
      }
      .el-form-item__content {
        margin-left: 100px;
      }
    }
    .widthInput {
      width: 50px;
      margin-right: 10px;
      .el-input {
        line-height: 30px;
      }
      input {
        width: 50px;
        padding: 0 5px;
        text-align: center;
      }
    }
  }
}
</style>
