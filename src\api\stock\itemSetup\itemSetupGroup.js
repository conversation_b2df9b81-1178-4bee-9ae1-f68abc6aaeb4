import request from '@/utils/request'

/**
 * 新增貨品組別
 * @param {string} sg_code 貨品組別編號
 * @param {string} sg_name_cn 貨品組別中文
 * @param {string} sg_name_en 貨品組別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_stock_group_id 父貨品組別id
 * @param {integer} seq 所處層級里的位置
 */
export function createStockGroup({
  sg_code,
  sg_name_cn,
  sg_name_en,
  active_year,
  parent_stock_group_id,
  seq,
}) {
  return request({
    url: '/stock-groups/actions/create',
    method: 'post',
    data: {
      sg_code,
      sg_name_cn,
      sg_name_en,
      active_year,
      parent_stock_group_id,
      seq,
    },
  })
}

/**
 * 修改貨品組別
 * @param {integer} stock_group_id 貨品組別id
 * @param {string} sg_code 貨品組別編號
 * @param {string} sg_name_cn 貨品組別中文
 * @param {string} sg_name_en 貨品組別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_stock_group_id 父貨品組別id
 * @param {integer} seq 所處層級里的位置
 */
export function updateStockGroup({
  stock_group_id,
  sg_code,
  sg_name_cn,
  sg_name_en,
  active_year,
  parent_stock_group_id,
  seq,
}) {
  return request({
    url: '/stock-groups/actions/update',
    method: 'post',
    data: {
      stock_group_id,
      sg_code,
      sg_name_cn,
      sg_name_en,
      active_year,
      parent_stock_group_id,
      seq,
    },
  })
}

/**
 * 刪除貨品組別
 * @param {integer} stock_group_id 貨品組別id
 */
export function deleteStockGroup(stock_group_id) {
  return request({
    url: '/stock-groups/actions/delete',
    method: 'post',
    data: {
      stock_group_id,
    },
  })
}

/**
 * 獲取貨品組別列表
 * @param {integer} stock_group_id 貨品組別id(返回結果將會排除此id)
 * @param {string} fy_code 選擇的會計週期
 */
export function fetchStockGroups(stock_group_id, fy_code) {
  return request({
    url: '/stock-groups',
    method: 'get',
    params: {
      stock_group_id,
      fy_code,
    },
  })
}

/**
 * 獲取某貨品組別詳情
 * @param {integer} stock_group_id 貨品組別id
 */
export function getStockGroup(stock_group_id) {
  return request({
    url: '/stock-groups/actions/inquire',
    method: 'get',
    params: {
      stock_group_id,
    },
  })
}

export default {
  createStockGroup,
  updateStockGroup,
  deleteStockGroup,
  fetchStockGroups,
  getStockGroup,
}
