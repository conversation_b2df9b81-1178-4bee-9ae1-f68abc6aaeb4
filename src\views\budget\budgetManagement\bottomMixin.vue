<script>
import { listenTo } from '@/utils/resizeListen'
import {
  cancelScrollBarSync,
  getTreeBudgetByCode,
  getTreeBudgetById,
  scrollBarSync,
} from '@/views/budget/common/utils'
import { editBudgetStage, fetchBudgetLedgers, fetchBudgetTree, getBudgetByCode } from '@/api/budget'
import BudgetSelectTreeVue from '../common/BudgetSelectTreeVue'
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'

export default {
  components: {
    BudgetSelectTreeVue,
    ETable,
    customStyle,
  },
  props: {},
  data() {
    return {
      leftResizeListen: {},
      leftInfoHeight: 70,
      leftDataTableWrapper: {},
      leftSummaryTableWrapper: {},
      showRightTable: true,
      leftColumnWidths: {
        icon: 120,
        budget_code: 120,
        name_: 150,
        budget_IE: 50,
        budget_amount: 100,
        actual_amount: 100,
      },
      bottomTableData: [],
      selectedVoucherList: [], // 勾選的
      addParent: false, // 勾選的

      tableColumns: [
        'vc_date',
        'vc_no',
        'descr',
        'vc_payee',
        'ref',
        'amount',
        'budget_code',
        'ac_code',
      ],
    }
  },
  computed: {
    bottomTreeData() {
      if (this.addParent && this.bottomBudgetGroup && this.bottomBudgetGroup.budget_id) {
        console.log(JSON.parse(JSON.stringify(this.bottomBudgetTree)), 9999)

        const item = Object.assign({}, this.bottomBudgetGroup)
        item.children = this.bottomBudgetTree
        return [item]
      } else {
        return this.bottomBudgetTree
      }
    },
  },
  created() {},
  beforeDestroy() {},
  mounted() {},
  updated() {},
  methods: {
    onSelectionChange(val) {
      console.log('onSelectionChange', val)
      this.selectedVoucherList = val
    },
    onCloseCustomStyleDialog() {},
    onChangeBottomBudget(val) {
      this.fetchBottomData()
    },
    fetchBottomData(addParent = false) {
      if (this.bottomLoading) return
      this.bottomLoading = true

      const fy_code = this.preferences.filters.bottomYear
      const budget_id = this.preferences.filters.bottomBudgetId
      console.log('fetchBottomData', this.preferences, fy_code, budget_id)
      if (!fy_code || !budget_id) {
        this.bottomLoading = false
        return
      }
      this.addParent = addParent
      fetchBudgetLedgers({ fy_code, budget_id })
        .then(res => {
          console.log(466)

          this.bottomTableData = res
        })
        .finally(() => {
          this.bottomLoading = false
        })
    },
    async changeBottomList(fy_code, budget_id, needAddParent = true) {
      if (!budget_id) return false
      try {
        this.preferences.filters.bottomBudgetId = ''
        if (fy_code !== this.preferences.filters.bottomYear) {
          this.preferences.filters.bottomYear = fy_code
          await this.handleChangeBottomYear(fy_code, false)
          this.bottomBudgetGroup = {}
        } else {
          // this.bottomBudgetGroup = Object.assign({}, parent)
        }
        this.preferences.filters.bottomBudgetId = budget_id
        console.log('budget_id', budget_id)

        await this.fetchBottomData(needAddParent)
        // getTreeBudgetById(this.bottomBudgetTree, this.preferences.filters.bottomBudgetId)
      } catch (e) {
        console.error(e)
      }
    },
  },
}
</script>
