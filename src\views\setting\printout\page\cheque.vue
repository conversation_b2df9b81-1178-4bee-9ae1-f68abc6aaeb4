<template>
  <div v-if="!hookVisible">
    <right v-loading="loading">
      <el-main slot="content">
        <el-row>
          <el-col :span="10">
            <el-form ref="form" :model="form" class="form mini-form" label-width="140px">
              <!-- 格式 -->
              <el-form-item :label="$t('setting.printout.label.style')" prop="chq_template_code">
                <el-select
                  v-model="form.chq_template_code"
                  :placeholder="$t('setting.printout.label.style')"
                  @change="onChangeChequeTemplate"
                >
                  <el-option
                    v-for="item in cheques"
                    :key="item.chq_id"
                    :label="language === 'en' ? item.chq_name_en : item.chq_name_cn"
                    :value="item.chq_template_code"
                  />
                </el-select>
              </el-form-item>
              <!-- 紙大小 -->
              <pageSizeForm
                :page-style.sync="form.page_style"
                :page-width.sync="form.page_width"
                :page-height.sync="form.page_height"
                :page-orientation.sync="form.page_orientation"
              />
              <!-- 字體粗細 -->
              <el-form-item :label="$t('setting.printout.label.fontWeight')">
                <el-select
                  v-model="form.font_weight"
                  :placeholder="$t('setting.printout.label.fontWeight')"
                >
                  <el-option :label="$t('setting.printout.content.normal')" :value="0" />
                  <el-option :label="$t('setting.printout.content.bold')" :value="1" />
                </el-select>
              </el-form-item>
              <!-- 字體大小 -->
              <el-form-item :label="$t('setting.printout.label.fontSize')">
                <el-input-number v-model="form.font_size" :min="0" :controls="false" />
              </el-form-item>
              <!-- 收款人寬度 -->
              <el-form-item :label="$t('setting.printout.label.payee_width')" class="form-item">
                <el-input-number v-model="form.payee_x" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 金額寬度 -->
              <el-form-item :label="$t('setting.printout.label.text_width')" class="form-item">
                <el-input-number v-model="form.text_width" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 金額縮排 -->
              <el-form-item :label="$t('setting.printout.label.text_indent')" class="form-item">
                <el-input-number v-model="form.text_indent" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 金額行高 -->
              <el-form-item :label="$t('setting.printout.label.line_height')" class="form-item">
                <el-input-number
                  v-model="form.line_height"
                  :min="0"
                  :step="0.1"
                  :controls="false"
                />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 語言 -->
              <el-form-item :label="$t('setting.printout.label.language')">
                <el-select
                  v-model="form.language"
                  :placeholder="$t('setting.printout.label.language')"
                >
                  <el-option :label="$t('setting.printout.content.chinese')" value="cn" />
                  <el-option :label="$t('setting.printout.content.english')" value="en" />
                  <el-option :label="$t('setting.printout.content.auto')" value="auto" />
                  <el-option :label="$t('setting.printout.content.numeric')" value="numeric" />
                </el-select>
              </el-form-item>
              <template v-if="showChequeStub">
                <!-- 支票頭字體大小 -->
                <el-form-item :label="$t('setting.printout.label.stubFontSize')">
                  <el-input-number v-model="form.stub_font_size" :min="0" :controls="false" />
                </el-form-item>
                <!-- 支票頭收款寬度 -->
                <el-form-item
                  :label="$t('setting.printout.label.stub_payee_width')"
                  class="form-item"
                >
                  <el-input-number v-model="form.stub_payee_width" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
              </template>
              <!--    ----------------------   -->
              <!-- 分割線 -->
              <div
                class="el-divider el-divider--horizontal"
                style="
                  width: 400px;
                  margin: 15px 0;
                  position: relative;
                  height: 1px;
                  background: #e6e6e6;
                "
              >
                <div
                  style="
                    position: absolute;
                    top: -6px;
                    background: white;
                    left: 180px;
                    padding: 0 10px;
                  "
                >
                  {{ $t('setting.printout.label.position') }}
                </div>
              </div>
              <!--    ----------------------   -->
              <!-- 支票 -->
              <el-form-item :label="$t('setting.printout.label.cheque')" class="form-item-simple">
                <el-input-number
                  v-model="form.cheque_width"
                  :min="0"
                  :max="form.page_width"
                  :controls="false"
                />
                <el-input-number
                  v-model="form.cheque_height"
                  :min="0"
                  :max="form.page_height"
                  :controls="false"
                />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 基數 -->
              <el-form-item :label="$t('setting.printout.label.offset')" class="form-item-simple">
                <el-input-number v-model="form.offset_x" :min="0" :controls="false" />
                <el-input-number v-model="form.offset_y" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 金額 -->
              <el-form-item :label="$t('setting.printout.label.amount')" class="form-item-simple">
                <el-input-number v-model="form.amount_x" :min="0" :controls="false" />
                <el-input-number v-model="form.amount_y" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 日期 -->
              <el-form-item :label="$t('setting.printout.label.date')" class="form-item-simple">
                <el-input-number v-model="form.date_x" :min="0" :controls="false" />
                <el-input-number v-model="form.date_y" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 收款人 -->
              <el-form-item :label="$t('setting.printout.label.payee')" class="form-item-simple">
                <el-input-number v-model="form.payee_x" :min="0" :controls="false" />
                <el-input-number v-model="form.payee_y" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <!-- 金額大寫 -->
              <el-form-item
                :label="$t('setting.printout.label.text_amount')"
                class="form-item-simple"
              >
                <el-input-number v-model="form.text_amount_x" :min="0" :controls="false" />
                <el-input-number v-model="form.text_amount_y" :min="0" :controls="false" />
                <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
              </el-form-item>
              <template v-if="showUnderline">
                <!-- 存入受款人戶口 -->
                <el-form-item
                  :label="$t('setting.printout.label.ac_payee_only')"
                  class="form-item-simple"
                >
                  <el-input-number v-model="form.ac_payee_only_x" :min="0" :controls="false" />
                  <el-input-number v-model="form.ac_payee_only_y" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
                <!-- 刪或持票人 -->
                <el-form-item
                  :label="$t('setting.printout.label.crossed_bearer')"
                  class="form-item-simple"
                >
                  <el-input-number v-model="form.crossed_bearer_x" :min="0" :controls="false" />
                  <el-input-number v-model="form.crossed_bearer_y" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
                <!-- 刪線寬度間距 -->
                <el-form-item
                  :label="$t('setting.printout.label.crossed_width_space')"
                  class="form-item-simple"
                >
                  <el-input-number v-model="form.crossed_width" :min="0" :controls="false" />
                  <el-input-number v-model="form.crossed_space" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
              </template>
              <!-- 支票頭 -->
              <template v-if="showChequeStub">
                <!-- 支票頭日期 -->
                <el-form-item
                  :label="$t('setting.printout.label.stub_date')"
                  class="form-item-simple"
                >
                  <el-input-number v-model="form.stub_date_x" :min="0" :controls="false" />
                  <el-input-number v-model="form.stub_date_y" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
                <!-- 票根受款人 -->
                <el-form-item
                  :label="$t('setting.printout.label.stub_payee')"
                  class="form-item-simple"
                >
                  <el-input-number v-model="form.stub_payee_x" :min="0" :controls="false" />
                  <el-input-number v-model="form.stub_payee_y" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
                <!-- 票根描述 -->
                <!--<el-form-item :label="$t('setting.printout.label.stub_desc')" class="form-item-simple">-->
                <!--  <el-input-number v-model="form.stub_desc_x" :min="0" :controls="false" />-->
                <!--  <el-input-number v-model="form.stub_desc_y" :min="0" :controls="false" />-->
                <!--  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>-->
                <!--</el-form-item>-->
                <!-- 票根金額 -->
                <el-form-item
                  :label="$t('setting.printout.label.stub_amount')"
                  class="form-item-simple"
                >
                  <el-input-number v-model="form.stub_amount_x" :min="0" :controls="false" />
                  <el-input-number v-model="form.stub_amount_y" :min="0" :controls="false" />
                  <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
                </el-form-item>
              </template>
              <!-- 操作欄 -->
              <actions
                :style-list="styleList"
                :on-fetch="onFetch"
                :on-save="onSaveCheque"
                :on-delete="onDelete"
                :on-copy="onCopy"
                :show-add="false"
              />
            </el-form>
          </el-col>
          <el-col :span="10">
            <div class="paper-preview">
              <el-button type="primary" @click="onPreview">
                {{ $t('setting.printout.label.paperPreview') }}
              </el-button>
            </div>
            <!-- 支票預覽 -->
            <chequePreview
              ref="chequePreview"
              v-loading="loading"
              :loading="loading"
              :form-language="form.language"
              :chq_template_code="form.chq_template_code"
              :page-width="form.cheque_width"
              :page-height="form.cheque_height"
              :date-x="form.date_x"
              :date-y="form.date_y"
              :text-amount-x="form.text_amount_x"
              :text-amount-y="form.text_amount_y"
              :text-width="form.text_width"
              :text-indent="form.text_indent"
              :line-height="form.line_height"
              :payee-x="form.payee_x"
              :payee-y="form.payee_y"
              :amount-x="form.amount_x"
              :amount-y="form.amount_y"
              :font-weight="form.font_weight"
              :font-size="form.font_size"
              :ac-payee-only-x="form.ac_payee_only_x"
              :ac-payee-only-y="form.ac_payee_only_y"
              :crossed-bearer-x="form.crossed_bearer_x"
              :crossed-bearer-y="form.crossed_bearer_y"
              :crossed-width="form.crossed_width"
              :crossed-space="form.crossed_space"
              :text-payee="previewForm[form.language === 'en' ? 'payee_en' : 'payee_cn']"
              :text-amount2="previewForm[showAmountKey]"
              :text-date="previewForm['date']"
              :text-amount="previewForm['amount']"
              :stub-font-size="form.stub_font_size"
              :stub-date-x="form.stub_date_x"
              :stub-date-y="form.stub_date_y"
              :stub-payee-x="form.stub_payee_x"
              :stub-payee-y="form.stub_payee_y"
              :stub-payee-width="form.stub_payee_width"
              :stub-amount-x="form.stub_amount_x"
              :stub-amount-y="form.stub_amount_y"
              :stub-desc-x="form.stub_desc_x"
              :stub-desc-y="form.stub_desc_y"
              :show-cheque-stub="showChequeStub"
            />
            <div class="preview-inputs">
              <el-input
                v-model="previewForm[form.language === 'en' ? 'payee_en' : 'payee_cn']"
                class="preview-inputs-input"
                clearable
              />
              <el-input
                v-model="previewForm[showAmountKey]"
                class="preview-inputs-input"
                clearable
              />
              <el-input v-model="previewForm.date" class="preview-inputs-input" clearable />
              <el-input v-model="previewForm.amount" class="preview-inputs-input" clearable />
            </div>
            <PaperPreview
              v-if="showPaperPreview"
              :visible.sync="showPaperPreview"
              :default-form="defaultForm"
              :cheque-img="chequeImg"
              :form="form"
              @close="showPaperPreview = $event"
            />
          </el-col>
        </el-row>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'
import pageSizeForm from '../components/pageSizeForm'
import chequePreview from '../components/chequePreview'
import actions from '../components/actions'
import Divider from '@/components/Divider'
import { fetchCheques } from '@/api/master/cheques'
import { getPrintSetting } from '@/api/settings/printSetting'
import { mapGetters } from 'vuex'
import dateUtil from '@/utils/date'

import PaperPreview from './paperPreviewPop.vue'
import html2canvas from 'html2canvas'

export default {
  name: 'SettingPrintoutDailyCheque',
  components: {
    right,
    pageSizeForm,
    chequePreview,
    Divider,
    PaperPreview,
    actions,
  },
  mixins: [common],
  data() {
    return {
      form: {},
      defaultForm: {
        chq_template_code: '',
        page_style: '',
        page_width: 210,
        page_height: 148,
        page_orientation: 1,
        language: 'cn',
        cheque_width: 210,
        cheque_height: 148,
        offset_x: 0,
        offset_y: 0,
        font_size: 12,
        font_weight: 0,
        amount_x: 155,
        amount_y: 40,
        date_x: 169,
        date_y: 12,
        payee_x: 17,
        payee_y: 18,
        payee_width: 145,
        text_amount_x: 13,
        text_amount_y: 40,
        text_width: 124,
        text_indent: 12,
        line_height: 2,
        ac_payee_only_x: 0,
        ac_payee_only_y: 0,
        crossed_bearer_x: 180,
        crossed_bearer_y: 31,
        crossed_width: 30,
        crossed_space: 2,

        stub_font_size: 10,
        stub_date_x: 0,
        stub_date_y: 0,
        stub_payee_x: 0,
        stub_payee_y: 0,
        stub_payee_width: 0,
        stub_amount_x: 0,
        stub_amount_y: 0,
        stub_desc_x: 0,
        stub_desc_y: 0,
      },
      ps_code: 'pdfcheque',
      psg_code: '',
      cheques: [],
      showPaperPreview: false,
      isInit: false,
      previewForm: {
        amount_en:
          'One Million, Two Hundred and Thirty-Four Thousand, Five Hundred and Sixty-Seven Dollars and Eighty-Nine Cents Only',
        amount_cn: this.$t('chequePreview.amountCn'),
        amount_default: '** 1,234,567.89 **',
        amount: '** 1,234,567.89 **',
        date: '',
        payee_en: '',
        payee_cn: '',
      },
      chequeImg: '',
    }
  },
  computed: {
    ...mapGetters(['school']),
    showUnderline() {
      return this.form && this.form.chq_template_code && !this.form.chq_template_code.includes('_')
    },
    showChequeStub() {
      return (
        (!!this.form && this.form.chq_template_code && this.form.chq_template_code.includes('-')) ||
        false
      )
    },
    showAmountKey() {
      switch (this.form.language) {
        case 'en':
          return 'amount_en'
        case 'cn':
          return 'amount_cn'
        case 'auto':
          return this.language === 'en' ? 'amount_en' : 'amount_cn'
        default:
          return 'amount_default'
      }
    },
  },
  watch: {
    // 'form.chq_template_code'(val) {
    //   this.psg_code = val
    // }
  },
  created() {
    this.previewForm.payee_en = this.school.sch_name_en
    this.previewForm.payee_cn = this.school.sch_name_cn
    this.previewForm.date = dateUtil.format(new Date(), 'dd/MM/yyyy')
    fetchCheques().then(res => {
      this.cheques = res
    })
  },
  methods: {
    onChangeChequeTemplate(val) {
      this.psg_code = val
      this.$nextTick(() => {
        this.onFetch(0)
      })
    },
    onFetch(using) {
      const ps_code = this.ps_code
      const psg_code = this.psg_code ? this.psg_code : undefined
      console.log('mixin', ps_code)
      this.loading = true
      return new Promise((resolve, reject) => {
        getPrintSetting({ ps_code, psg_code })
          .then(res => {
            this.allConfig = res
            let data = null
            // // 空form
            if (!res.length) {
              data = Object.assign({}, this.defaultForm)
              data.chq_template_code = this.psg_code
              this.form = data
              resolve(data)
              return
            }
            let item
            if (using && !psg_code) {
              item = res.find(i => i.using === 1)
            } else {
              item = res.find(i => i.psg_code === psg_code)
            }
            if (!item) item = res[0]
            data = Object.assign({}, item.config_json)
            this.psg_code = item.psg_code
            this.form = data
            resolve(data)
            return data
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    onSaveCheque(e) {
      const { cheque_width, cheque_height, offset_x, offset_y, page_width, page_height } = this.form
      const width = cheque_width + offset_x
      const height = cheque_height + offset_y
      if (width > page_width) {
        this.$message.error(
          this.$t('message.chequeSettingWidthInvalid', {
            width: page_width - offset_x,
          }),
        )
        return
      }
      if (height > page_height) {
        this.$message.error(
          this.$t('message.chequeSettingHeightInvalid', {
            height: page_height - offset_y,
          }),
        )
        return
      }
      this.onSave(e)
    },
    async onPreview() {
      const img = await this.domToImage(this.$refs.chequePreview.$el)
      this.chequeImg = img
      this.showPaperPreview = true
    },
    domToImage(element) {
      return new Promise((resolve, reject) => {
        const rect = element.getBoundingClientRect()
        const imgWidth = rect.width
        const imgHeight = rect.height

        const drawWidth = window.outerWidth
        const drawHeight = window.outerHeight

        html2canvas(element, {
          backgroundColor: 'white',
          allowTaint: true,
          useCORS: true,
          width: drawWidth,
          height: drawHeight,
          scrollX: 0,
          scrollY: 0,
        }).then(canvas => {
          const croppedCanvas = document.createElement('canvas')
          const croppedCtx = croppedCanvas.getContext('2d')
          croppedCanvas.width = imgWidth
          croppedCanvas.height = imgHeight
          croppedCtx.drawImage(canvas, 0, 0, imgWidth, imgHeight, 0, 0, imgWidth, imgHeight)
          const imgData = croppedCanvas.toDataURL('image/png')
          resolve(imgData)
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-form-item {
    margin-bottom: 5px !important;
    height: 25px;
    line-height: 25px;
  }
  /*.el-input-number.el-input-number--medium {*/
  /*  width: 100px !important;*/
  /*  .el-input--medium {*/
  /*    width: 100px !important;*/
  /*  }*/
  /*}*/
}
.preview-inputs {
  margin-top: 20px;
  width: 100%;
  .preview-inputs-input {
    display: block;
    margin-bottom: 10px;
    width: 100% !important;
    /deep/ {
      .el-input__suffix {
        .el-input__icon {
          line-height: 26px;
          height: 26px;
        }
      }
    }
  }
}
.paper-preview {
}
</style>
