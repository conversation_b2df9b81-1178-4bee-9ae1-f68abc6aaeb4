html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, abbr, address, cite, code, del, dfn, em, img, ins, kbd, q, samp, small, strong, sub, sup, var, b, i, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
  font-family: "Microsoft YaHei";
  font-size: 10px;
}

body {
  line-height: 1;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  display: block;
}

nav ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote {
  &:before, &:after {
    content: '';
    content: none;
  }
}

q {
  &:before, &:after {
    content: '';
    content: none;
  }
}

a {
  margin: 0;
  padding: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

/* change colours to suit your needs */

ins {
  background-color: #ff9;
  color: #000;
  text-decoration: none;
}

/* change colours to suit your needs */

mark {
  background-color: #ff9;
  color: #000;
  font-style: italic;
  font-weight: bold;
}

del {
  text-decoration: line-through;
}

abbr[title], dfn[title] {
  border-bottom: 1px dotted;
  cursor: help;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* change border colour to suit your needs */

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #cccccc;
  margin: 1em 0;
  padding: 0;
}

input, select {
  vertical-align: middle;
}

/* 設置所有el-form-item__label font-weight 為500*/

.el-form-item__label {
  font-weight: normal !important;
}

/* 所有el-form-item 底部margin為12px */

.el-form-item {
  margin-bottom: 5px !important;
}

/* 表格高度統一縮小 */

.el-table--medium {
  td, th {
    padding: 2px 0 !important;
  }
}
/*
??????????????
 */
:not(.native) .el-input--medium .el-input__inner {
  height: 30px;
  line-height: 30px;
}
:not(.native) .el-input-number--medium .el-input__inner {
  height: 30px;
  line-height: 30px;
}

:not(.native) .el-input-number--medium {
  line-height: 28px;
}

.el-dialog {
  width: 75%;
  .el-dialog__header {
    padding: 5px 10px;
  }
  .el-dialog__body {
    padding: 20px 10px;
    max-height: 60vh;
  }
  .el-dialog__headerbtn {
    top: 7px;
  }
}

.voucher-dialog-add{
  padding-left: 30px;
  padding-right: 30px;
}

/* checkbox 靠左 */

.el-checkbox {
  + .el-checkbox {
    margin-left: 0px;
  }
  margin-right: 30px;
}

/* 滚动槽 */

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.06);
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.08);
}

/* 滚动条滑块 */

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

/* icon */

.svg-icon, [class*=" el-icon-"], [class^=el-icon-] {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}


.app-content{

  :not(.native) .el-form-item--medium .el-form-item__content,
  :not(.native) .el-form-item--medium .el-form-item__label,
  :not(.native) .el-input__suffix,
  :not(.native) .el-input__icon{
    //line-height: 30px!important;
    /*height: 30px!important;*/
  }

  .el-input__suffix{
    top: 2px;
  }
}


$actionIconColor: #68AFFF;
$settingColor: #B9B6B6;
$disableColor: #B9B6B6;
$fontColor: #707070;

/* 操作列 */
.operation_icon{
  .svg-icon{
    cursor: pointer;
    font-size: 14px!important;
    color: #707070;
    margin: 0 10px;
  }
  .svg-icon.no-cursor{
    cursor: auto;
  }
}


/* 導入錯誤的對話框 */
.import-error-dialog {
  .el-message-box__content{
    .el-message-box__status {
      top: 22px;
    }
    .import-error-dialog-content {
      max-height: calc(100vh - 300px);
      overflow-y: auto;
    }
  }
}

.EDAC {
  /* 不顯示驗證成功邊框 */
  .el-form-item.is-success .el-input__inner,
  .el-form-item.is-success .el-input__inner:focus,
  .el-form-item.is-success .el-textarea__inner,
  .el-form-item.is-success .el-textarea__inner:focus{
    border-color: #dcdfe6;
  }
  .el-dialog__wrapper{
    &:not(.upload-dialog) {
      .el-dialog__body {
        //max-height: calc(100vh - 300px);
        max-height: 60vh;
        height: 60vh;
        overflow: auto;
      }
    }

  }

  .svg-icon:hover {
      opacity: 0.8;
    }


  .action-icon{
    cursor: pointer;
    transition: opacity 0.3s;
    &.edac-icon{
      color: $buttonColor;
      &:hover {
        color: $buttonHoverColor;
        opacity: 0.8;
      }
    }
    &.disable{
      &.not-allowed{
        cursor: not-allowed;
      }
      cursor: not-allowed;
      color: $disableColor;
      &:hover {
        color: $disableColor;
      }
    }
  }
  .svg-icon {
    vertical-align: sub;
    fill: currentColor;
    color: $actionIconColor;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    &:hover {
      color: $buttonHoverColor;
    }
    &.circle {
      border-radius: 50%;
    }
    &.setting-full {
      color: $settingColor;
    }
  }
  table {
    .svg-icon {
      width: 16px;
      height: 16px;
    }
  }
  .actions-icon{
    .edac-icon{
      color: $actionIconColor;
      &.disable{
        color: $disableColor;
      }
      &.edac-icon-setting1{
        color: $settingColor;
      }
    }
  }

  .vue-treeselect{
    .vue-treeselect__single-value{
      color: #707070;
    }
    .vue-treeselect__label{
      color: #707070;
    }
    .vue-treeselect__tip.vue-treeselect__no-children-tip {
       display: none;
    }
    .vue-treeselect__placeholder.vue-treeselect-helper-zoom-effect-off{
      line-height: inherit;
    }
  }
  /* 所有SelectOption高度 */
  :not(.native) .el-select-dropdown.el-popper > div.el-scrollbar .el-select-dropdown__item{
    height: 25px;
    line-height: 25px;
  }

  /* ETable */
  .e-table{
    /* 選中行顏色 */
    .el-table__body tr.current-row>td{
      background-color: #6abdff;
    }
    /* 省略號 */
    .cell {
      white-space: nowrap;
    }
  }
  //.el-table--border{
  //  border: 1px solid #ebeef5;
  //}
  .el-table {
    .count-row td{
      background-color: #f5f7fa;
      color: #404246;
    }
    .pending-row {
      cursor: pointer;
    }
  }

  th.gutter {
    display: none;
  }


  .mini-form:not(.native) {
    //height: 25px;
    //line-height: 25px;
        .el-select-dropdown.el-popper > div.el-scrollbar .el-select-dropdown__item,
        .el-input,
        .el-select,
        input,
        .el-input__icon,
        .el-form-item__content,
        .el-form-item__label{
          height: 25px;
          line-height: 25px;
        }
        .el-button{
          padding: 5px 15px;
          vertical-align: middle;
        }
        .el-input__icon,
        .el-date-editor,
        [class^=el-icon-] {
          vertical-align: middle;
        }
        .el-input__inner{
          height: 25px;
          line-height: 25px;
          .el-range-separator, input{
            height: 22px;
            line-height: 22px;
          }

        }
        .el-date-editor .el-range-separator{
          padding: 0;
        }


    &.mini-width{
      /deep/ {
        .el-form-item{
          margin-right: 0;
        }
      }
    }

    :not(.native) .el-input__suffix,
    :not(.native) .el-input__icon{
      line-height: 25px!important;
      /*height: 25!important;*/
    }
  }

  .el-input-number.readonly {

  }

  .vue-splitter-container .splitter-pane-resizer {
    background-color: #eaeaea;
    opacity: 1;
    &.horizontal {
      height: 11.5px;
      //min-height: 11.5px;
    }
     &.vertical {
      width: 11.5px;
      //min-width: 11.5px;
    }
  }

}




#nprogress {
  .bar {
    background: #067ad8 !important;
  }
}
