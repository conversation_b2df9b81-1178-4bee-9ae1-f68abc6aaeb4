<template>
  <SelectTree
    v-model="currentBudgetId"
    :options="treeData"
    :props="{
      label: language === 'en' ? 'name_en' : 'name_cn',
      value: 'budget_id',
      code: 'budget_code',
      group_id: 'budget_id',
      value_id: 'budget_id',
      children: 'children',
    }"
    :disabled="disabled"
    :placeholder="placeholder"
    @change="onChangeGroup"
    @focus="onFocus"
  >
    <template v-if="data" slot-scope="{ data }">
      <el-option
        v-for="item in data"
        :key="item.budget_id"
        :label="budgetLabel(item)"
        :value="item"
        :disabled="disabledMethod(item)"
        :style="`${
          statusColor && currentBudgetId !== item.budget_id
            ? `color: ${statusColor[item.budget_stage]};`
            : ''
        }`"
      >
        <span>{{ repeat('&nbsp;', item.level * 4 + (item.children ? 0 : 2)) }}</span>
        <i v-if="item.children && item.children.length > 0" class="el-icon-caret-bottom" />
        <i v-else class="el-icon-caret-right" />
        <span>{{ budgetLabel(item) }}</span>
      </el-option>
      <slot name="append" />
    </template>
  </SelectTree>
</template>

<script>
import SelectTree from '@/components/SelectTree/index.vue'
export default {
  name: 'BudgetSelectTreeVue',
  components: {
    SelectTree,
  },
  props: {
    selectAll: {
      type: Boolean,
      default: false,
    },
    // language: {
    //   type: String,
    //   default: 'zh-hk'
    // },
    budgetId: {
      type: [Number, String],
      default: '',
    },
    budgetCode: {
      type: [Number, String],
      default: '',
    },
    disabledMethod: {
      type: Function,
      default: item => item.budget_type === 'F',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: undefined,
    },
    allLabelCn: {
      type: String,
      default() {
        return this.$t('enquiry.timeType.all')
      },
    },
    allLabelEn: {
      type: String,
      default: 'All',
    },
    statusColor: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {}
  },
  computed: {
    currentBudgetId: {
      get() {
        return this.budgetId || null
      },
      set(val) {
        this.$emit('update:budgetId', val)
      },
    },
    allBudget() {
      return {
        budget_IE: 'B',
        budget_code: '',
        budget_default: '',
        budget_id: '',
        budget_type: 'F',
        level: 0,
        children: [],
        name_cn: this.allLabelCn,
        name_en: this.allLabelEn,
        parent_budget_id: null,
      }
    },
    treeData() {
      if (this.selectAll) {
        const all = Object.assign({}, this.allBudget)
        all.children = this.data
        return [all]
      } else {
        return this.data
      }
    },
  },
  methods: {
    onFocus() {
      this.$emit('focus')
    },
    onChangeGroup(data) {
      console.log(data)
      this.$emit('update:budgetCode', data.budget_code)
      this.$emit('change', data)
    },
    repeat(str, n) {
      return new Array(n + 1).join(str)
    },
    budgetLabel(item) {
      const code = item.budget_code ? `[${item.budget_code}] ` : ''
      const label = item[this.language === 'en' ? 'name_en' : 'name_cn']
      return code + label
    },
  },
}
</script>

<style scoped></style>
