<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div style="height: 100%">
      <header v-if="false">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('router.settingScreenBasicSetting') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t($route.meta.title) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </header>
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 類別 -->
          <el-form-item :label="$t('filters.type')">
            <el-select v-model="preferences.filters.selectedType" class="year" style="width: 120px">
              <el-option
                v-for="(key, value) in typeOption"
                :key="key"
                :label="$t(key)"
                :value="value"
              />
            </el-select>
          </el-form-item>
          <!-- 銀行 -->
          <el-form-item :label="$t('filters.bank')">
            <el-select
              v-model="preferences.filters.selectedAcCode"
              class="year"
              style="width: 250px"
            >
              <el-option :label="allFund.label" :value="allFund.value" />
              <el-option
                v-for="item in bankList"
                :key="item.ac_code"
                :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
                :value="item.ac_code"
              />
            </el-select>
          </el-form-item>
          <!-- 時段 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.selectedPeriod"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :clearable="false"-->
            <!--              type="daterange"-->
            <!--              align="right"-->
            <!--              unlink-panels-->
            <!--              value-format="yyyy-MM-dd"-->
            <!--              style="width: 250px"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>
          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <!--              @change="onChangeMonth"-->
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" class="action-button" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              type="primary"
              size="mini"
              class="action-button"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
        </div>
      </div>
      <b-table
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-actions="true"
        :actions-min-width="5"
        :show-checkbox="false"
        :default-top="230"
        :filter-class-function="filterClassFunction"
        :height="pageHeight - filtersHeight - 55"
        action-label=" "
        class="b-table"
        border
        @changeWidth="changeColumnWidth"
      >
        <template slot="columns">
          <vxe-table-column
            v-for="item in columns"
            :key="item.ss_key"
            :title="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :class-name="item.ss_key + ' mini-form'"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :field="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :params="{ key: item.ss_key }"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="checkCanShow(scope.row, item)">{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)]
                )
              }}</span>
            </template>
          </vxe-table-column>
        </template>
      </b-table>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { autopayListExport } from '@/api/report/excel'
import { editStaffPreference, editUserPreference } from '@/api/settings/user/preference'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { fetchAutopayList } from '@/api/periodic/autopayList'
import { fetchAccounts } from '@/api/master/account'

import BTable from '@/components/BTable'
import DateRange from '@/components/DateRange/index'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'

import dateUtil from '@/utils/date'
import { exportExcel } from '@/utils/excel'
import { listenTo } from '@/utils/resizeListen'
// px2pt

export default {
  name: 'PeriodicAutopayListIndex',
  components: {
    DateRange,
    BTable,
    customStyle,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, loadPrintoutSetting, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      bankList: [],
      tableData: [],
      data: [],
      langKey: 'periodic.autopayList.label.',
      tableColumns: [
        'vc_date',
        'vc_no',
        'bank_ac_code',
        'ac_code',
        'ac_name_',
        'descr',
        'lg_amount',
        'vc_amount',
        'fund_name_',
        'vc_payee',
        'vc_receipt',
      ],
      amountColumns: ['lg_amount', 'vc_amount'],
      preferences: {
        filters: {
          selectedType: 'P',
          selectedAcCode: '',
          // selectedPeriod: [],
          begin_date: '',
          end_date: '',
          selectedYearCode: '',
          selectedMonth: '',
        },
      },
      childPreferences: ['selectedMonth'],
      years: [],
      monthList: [],
      selectedYearId: '',
      showYearPicker: false,
      periods: [],
      ps_code: 'pdfautopaylist',

      yearLastDate: new Date(),
      yearStartDate: new Date(),
      dateValueFormat: 'yyyy-MM-dd',

      filtersResizeListen: {},
      filtersHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school', 'remoteServerInfo', 'user_id', 'system']),
    typeOption() {
      return {
        P: 'periodic.autopayList.typeOption.type_P',
        R: 'periodic.autopayList.typeOption.type_R',
      }
    },
    allFund() {
      return {
        label: this.$t('filters.all'),
        value: '',
      }
    },
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
  },
  methods: {
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({ ac_bank: 'S,C,F,P,A' }))
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (
            this.bankList &&
            this.bankList.length > 0 &&
            this.preferences.filters.selectedAcCode !== ''
          ) {
            let bool = false
            this.bankList.forEach(i => {
              if (i.ac_code === this.preferences.filters.selectedAcCode) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
            }
          }
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        this.loading = true
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, '1')
        const lastDateObj = new Date(year, month + 1, '0')

        const filter_data = {
          vt_category: '',
          ac_code: '',
          begin_date: '',
          end_date: '',
        }

        filter_data.vt_category = this.preferences.filters.selectedType
        filter_data.ac_code = this.preferences.filters.selectedAcCode
        // filter_data.begin_date = this.preferences.filters.selectedPeriod === null || this.preferences.filters.selectedPeriod[0] === undefined ? (dateUtil.format(beginDateObj, 'yyyy-MM-dd')) : this.preferences.filters.selectedPeriod[0]
        // filter_data.end_date = this.preferences.filters.selectedPeriod === null || this.preferences.filters.selectedPeriod[1] === undefined ? (dateUtil.format(lastDateObj, 'yyyy-MM-dd')) : this.preferences.filters.selectedPeriod[1]
        // this.preferences.filters.selectedPeriod = [filter_data.begin_date, filter_data.end_date]

        filter_data.begin_date = this.preferences.filters.begin_date
        filter_data.end_date = this.preferences.filters.end_date
        if (!this.preferences.filters.begin_date) {
          filter_data.begin_date = dateUtil.format(beginDateObj, this.dateValueFormat)
          this.preferences.filters.begin_date = filter_data.begin_date
        }
        if (!this.preferences.filters.end_date) {
          filter_data.end_date = dateUtil.format(lastDateObj, this.dateValueFormat)
          this.preferences.filters.end_date = filter_data.end_date
        }

        fetchAutopayList(filter_data)
          .then(res => {
            let group = 0
            res.forEach((item, index) => {
              if (item.tx_num === 1) {
                group++
              }
              item._group = group
            })
            this.tableData = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.system
      const pf_code = system.toLowerCase() + '.home'
      const content = {
        page: this.$route.name,
      }
      let api = editStaffPreference
      if (system === 'AC') {
        api = editUserPreference
      }
      api(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    onChangeMonth(val) {
      if (val === '') {
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateValueFormat)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateValueFormat)
        // this.preferences.filters.selectedPeriod = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, 'yyyy-MM-dd')

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, 'yyyy-MM-dd')
      // this.preferences.filters.selectedPeriod = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
    },
    filterClassFunction(row) {
      if (row.row._group % 2 === 0) {
        return 'table-stripe'
      }
    },

    checkCanShow(row, item) {
      if (row.tx_num === 1) {
        if (item.ss_key === 'vc_receipt' && (row.vc_receipt === 'X' || row.vc_receipt === 'Y')) {
          return false
        } else {
          return true
        }
      } else if (
        item.ss_key !== 'vc_date' &&
        item.ss_key !== 'vc_no' &&
        item.ss_key !== 'vc_method' &&
        item.ss_key !== 'vc_amount' &&
        item.ss_key !== 'bank_ac_code' &&
        item.ss_key !== 'fund_name_' &&
        item.ss_key !== 'vc_payee' &&
        item.ss_key !== 'vc_receipt'
      ) {
        return true
      }
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateValueFormat,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateValueFormat,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const vt_category = this.preferences.filters.selectedType
      const ac_code = this.preferences.filters.selectedAcCode || undefined

      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, '1')
      const lastDateObj = new Date(year, month + 1, '0')
      // const begin_date = this.preferences.filters.selectedPeriod === null || this.preferences.filters.selectedPeriod[0] === undefined ? (dateUtil.format(beginDateObj, 'yyyy-MM-dd')) : this.preferences.filters.selectedPeriod[0]
      // const end_date = this.preferences.filters.selectedPeriod === null || this.preferences.filters.selectedPeriod[1] === undefined ? (dateUtil.format(lastDateObj, 'yyyy-MM-dd')) : this.preferences.filters.selectedPeriod[1]

      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!this.preferences.filters.begin_date) {
        begin_date = dateUtil.format(beginDateObj, this.dateValueFormat)
        this.preferences.filters.begin_date = begin_date
      }
      if (!this.preferences.filters.end_date) {
        end_date = dateUtil.format(lastDateObj, this.dateValueFormat)
        this.preferences.filters.end_date = end_date
      }
      if (!user_id || !vt_category || !begin_date || !end_date) {
        // this.$message.error('')
        return
      }
      this.loading = true
      autopayListExport({ user_id, export_type, vt_category, ac_code, begin_date, end_date })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  display: inline-block;
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }

    // 日期選擇器
    /deep/ .el-input__inner > {
      // 日期選擇器
      .el-range-input {
        height: 22px !important;
      }
    }

    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
    /deep/ {
      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  .el-table {
    height: calc(100vh - 220px);
    /deep/ {
      .el-table__body-wrapper {
        height: calc(100vh - 220px);
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
      .vc_chq_date {
        margin: 2px 0;
        .cell {
          height: 27px;
          line-height: 27px;
          padding: 0;
          .el-select {
            width: 100%;
          }
        }
      }
      .solid-border-top td {
        border-top: solid 1px #808080;
      }
    }
  }
  .el-button {
    padding: 5px 15px;
    vertical-align: middle;
  }
}
</style>
