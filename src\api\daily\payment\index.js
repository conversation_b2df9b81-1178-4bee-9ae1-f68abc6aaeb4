import request from '@/utils/request'

/**
 * 返回新增后的傳票id
 * @param {string} vc_no 傳票編號
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} vt_code 傳票類別編號
 * @param {string} vc_date 傳票日期
 * @param {string} vc_summary 傳票描述
 * @param {string} vc_method CASH=現金,TRANSF=轉賬,CHEQUE=支票,AUTO=自動,OTHER=其它
 * @param {string} ref vc_method=CHEQUE時是支票號碼,vc_method=OTHER時是其它備註
 * @param {string} vc_chq_date 支票日期(vc_method=CHEQUE需傳)
 * @param {string} vc_payee 收款人(公司)描述(不是編號)
 * @param {string} vc_st_code 職員code
 * @param {string} vc_extra 各個Ledger不同收款人 N=否,Y=是
 * @param {string} vc_receipt 是否需要收据,X=不需要,Y=已收到,N=未收到
 * @param {string} ledgers_json 賬目json,格式: [{"ac_code":"1101G","account_id":"1","descr":"職員薪酬","tx_type":"E","amount_dr":"100.00","amount_cr":"0.00","vc_payee":"XX公司","ref":"","budget_code":null,"st_code":null,"vc_contra":null,"vc_dept":null,"vc_quote":null,"vc_qnum":null},...]
 * @param {number} vc_status 當vt_category=J時並且傳票日期為會計週期最後一天時必須傳入(1=正常,2=審計修正1,3=審計修正2,4=審計修正3)
 * @param {string} post_row_ids 如果數據是過賬得來的，傳過賬數據的id
 * @return {Promise}
 */
export function createVoucher({
  vc_no,
  vt_category,
  vt_code,
  vc_date,
  vc_summary,
  vc_method,
  ref,
  vc_chq_date,
  vc_payee,
  vc_st_code,
  vc_extra,
  vc_receipt,
  ledgers_json,
  vc_status,
  post_row_ids,
}) {
  return request({
    url: '/vouchers/actions/create',
    method: 'post',
    data: {
      vc_no,
      vt_category,
      vt_code,
      vc_date,
      vc_summary,
      vc_method,
      ref,
      vc_chq_date,
      vc_payee,
      vc_st_code,
      vc_extra,
      vc_receipt,
      vc_status,
      ledgers_json: JSON.stringify(ledgers_json),
      post_row_ids,
    },
  })
}

/**
 * 更新傳票
 * @param {string} fy_code 會計週期編號
 * @param vt_category
 * @param {string} vc_id 傳票id
 * @param {string} vc_no 傳票編號
 * @param {string} vt_code 傳票類別編號
 * @param {string} vc_date 傳票日期
 * @param {string} vc_summary 傳票描述
 * @param {string} vc_method CASH=現金,TRANSF=轉賬,CHEQUE=支票,AUTO=自動,OTHER=其它
 * @param {string} ref vc_method=CHEQUE時是支票號碼(必傳),vc_method=OTHER時是其它備註
 * @param {string} vc_chq_date 支票日期(vc_method=CHEQUE需傳)
 * @param {string} vc_payee 收款人(公司)描述(不是編號)
 * @param {string} vc_st_code 職員code
 * @param {string} vc_extra 各個Ledger不同收款人 N=否,Y=是
 * @param {string} vc_receipt 是否需要收据,X=不需要,Y=已收到,N=未收到
 * @param {string} ledgers_json 賬目json,格式: [{"ac_code":"1101G","account_id":"1,","descr":"職員薪酬","tx_type":"E","amount_dr":"100.00","amount_cr":"","vc_payee":"XX公司","ref":"","budget_code":"AA","st_code":"AA","vc_contra":"AA","vc_dept":"AA"},...]
 * @return {Promise}
 */
export function editVoucher({
  fy_code,
  vt_category,
  vc_id,
  vc_no,
  vt_code,
  vc_date,
  vc_summary,
  vc_method,
  ref,
  vc_chq_date,
  vc_payee,
  vc_st_code,
  vc_extra,
  vc_receipt,
  ledgers_json,
  vc_status,
}) {
  return request({
    url: '/vouchers/actions/update',
    method: 'post',
    data: {
      fy_code,
      vt_category,
      vc_id,
      vc_no,
      vt_code,
      vc_date,
      vc_summary,
      vc_method,
      ref,
      vc_chq_date,
      vc_payee,
      vc_st_code,
      vc_extra,
      vc_receipt,
      vc_status,
      ledgers_json: JSON.stringify(ledgers_json),
    },
  })
}

/**
 * 刪除傳票
 * @param {string} fy_code 會計週期編號
 * @param {string} vc_id 傳票id
 * @return {Promise}
 */
export function deleteVoucher({ fy_code, vc_id }) {
  return request({
    url: '/vouchers/actions/delete',
    method: 'post',
    data: {
      fy_code,
      vc_id,
    },
  })
}

/**
 * 返回傳票列表
 * @param {string} fy_code 會計週期編號
 * @param {string} pd_code 會計週期月份編號
 * @param {string} vt_code 傳票類別編號,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @return {Promise}
 */
export function fetchVouchers({ fy_code, pd_code, vt_code, vt_category }) {
  return request({
    url: '/vouchers',
    method: 'get',
    params: {
      fy_code,
      pd_code,
      vt_code,
      vt_category,
    },
  })
}

/**
 * 獲取傳票詳情
 * @param {string} fy_code 會計週期編號
 * @param {string} vc_id 傳票id
 * @return {Promise}
 */
export function getVoucher({ fy_code, vc_id }) {
  return request({
    url: '/vouchers/actions/inquire',
    method: 'get',
    params: {
      fy_code,
      vc_id,
    },
  })
}

/**
 * 返回賬目列表
 * @param {string} fy_code 會計週期編號
 * @param {string} vc_id_list 傳票id,逗號分隔
 * @return {Promise}
 */
export function fetchLedgers({ fy_code, vc_id_list }) {
  return request({
    url: '/ledgers',
    method: 'get',
    params: {
      fy_code,
      vc_id: vc_id_list,
    },
  })
}

export default {
  createVoucher,
  editVoucher,
  deleteVoucher,
  fetchVouchers,
  getVoucher,
  fetchLedgers,
}

/**
 * 匯出傳票數據或模板
 * @param {string} [channel] 指定驗證token通道,可传AC或BG或PC,不傳默認AC
 * @param {string} fy_code 會計週期
 * @param {string} with_data 是否帶數據,Y=帶,N=不帶
 * @return {Promise}
 */
export function exportVouchersTemplate({ fy_code, with_data }) {
  return request({
    url: '/vouchers/actions/data-export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      with_data,
    },
  })
}

/**
 * 匯入傳票數據
 * @param {string} fy_code 會計週期
 * @param {string} user_id 錄入人user_id
 * @param {string} voucher_json 匯入的數據
 * @param {string} ledger_json 匯入的數據
 * @return {Promise}
 */
export function importVouchersTemplate({ voucher, ledger }, fy_code, user_id) {
  return request({
    url: '/vouchers/actions/data-import',
    method: 'post',
    data: {
      fy_code,
      user_id,
      voucher_json: JSON.stringify(voucher),
      ledger_json: JSON.stringify(ledger),
    },
  })
}

/**
 * 導出單張傳票
 * @param {string} fy_code 會計週期編號
 * @param {string} [vc_no] 傳票vc_no,不傳則是獲取模板沒有數據
 * @param {string} [show_voucher_info] 1=顯示傳票信息,不傳或0=不顯示
 * @return {Promise}
 */
export function exportVoucher({ fy_code, vc_no, show_voucher_info }) {
  return request({
    url: '/vouchers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      vc_no,
      show_voucher_info,
    },
  })
}

/**
 * 將傳票匯入的Excel轉化成json
 * @param {string} fy_code 會計週期編號
 * @param {string} data 匯入的數據
 * @return {Promise}
 */
export function importVoucher(data, fy_code) {
  return request({
    url: '/vouchers/actions/import',
    method: 'post',
    data: {
      fy_code,
      data_json: JSON.stringify(data),
    },
  })
}

/**
 * 更改傳票的收款狀態
 * @param {string} fy_code 會計週期編號
 * @param {string} vc_id 傳票id
 * @param {string} vc_receipt 傳Y或N
 * @return {Promise}
 */
export function changeReceiptStatus({ fy_code, vc_id, vc_receipt }) {
  return request({
    url: '/vouchers/actions/change-receipt',
    method: 'post',
    data: {
      fy_code,
      vc_id,
      vc_receipt,
    },
  })
}

/**
 * 匯出多條傳票的模板
 * @param {string} fy_code 會計週期
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @return {Promise}
 */
export function exportVoucherMultiple({ fy_code, vt_category }) {
  return request({
    url: '/vouchers/actions/multiple-export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      vt_category,
    },
  })
}

/**
 * 匯入多條傳票數據
 * @param {string} fy_code 會計週期
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} voucher_json 匯入的數據
 * @param {string} ledger_json 匯入的數據
 * @return {Promise}
 */
export function importVoucherMultiple({ voucher, ledger }, fy_code, vt_category) {
  return request({
    url: '/vouchers/actions/multiple-import',
    method: 'post',
    data: {
      fy_code,
      vt_category,
      voucher_json: JSON.stringify(voucher),
      ledger_json: JSON.stringify(ledger),
    },
  })
}

/**
 * 快速匯出傳票
 * @param {string} fy_code 會計週期編號
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @return {Promise}
 */
export function fastExportVouchers({ fy_code, vt_category, with_data }) {
  return request({
    url: '/vouchers/actions/data-fast-export',
    method: 'get',
    responseType: 'blob',
    params: { fy_code, vt_category, with_data },
  })
}

/**
 * 快速匯入傳票
 * @param {string} fy_code 會計週期編號
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @return {Promise}
 */
export function fastImportVouchers({ sheet }, fy_code, user_id, channel) {
  return request({
    url: '/vouchers/actions/data-fast-import',
    method: 'post',
    data: { fy_code, user_id, voucher_ledger_json: JSON.stringify(sheet), channel },
  })
}
