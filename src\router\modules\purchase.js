import Layout from '@/views/layout/Layout'

const purchaseRouter = [
  {
    // daily
    path: '/daily',
    component: Layout,
    redirect: 'noredirect',
    name: 'purchaseDaily',
    meta: {
      title: 'router.purchase.daily',
      p_code: 'pc.daily',
      icon: 'daily',
    },
    children: [
      {
        // 採購申請
        path: 'procurement',
        component: () => import('@/views/purchase/procurementApplications/index.vue'),
        name: 'purchaseDailyProcurement',
        needAPrint: 'Y',
        // redirect: '/daily/procurement/list',
        redirect: { name: 'purchaseDailyProcurementList' },
        // index: 'purchaseDailyProcurementList',
        meta: {
          title: 'router.purchase.dailyProcurement',
          p_code: 'pc.daily.procurement',
        },
        children: [
          {
            // List
            path: 'list',
            component: () => import('@/views/purchase/procurementApplications/list.vue'),
            name: 'purchaseDailyProcurementList',
            needAPrint: 'Y',
            meta: {
              title: 'router.purchase.dailyProcurement',
              p_code: 'pc.daily.procurement',
            },
            props: true,
          },
          {
            // Add
            path: 'add',
            component: () => import('@/views/purchase/procurementApplications/add.vue'),
            name: 'purchaseDailyProcurementAdd',
            needAPrint: 'A',
            meta: {
              title: 'router.purchase.dailyProcurementAdd',
              p_code: 'pc.daily.procurement',
            },
            props: true,
          },
          {
            // Edit
            path: 'edit/:id',
            component: () => import('@/views/purchase/procurementApplications/add.vue'),
            name: 'purchaseDailyProcurementEdit',
            needAPrint: 'E',
            meta: {
              title: 'router.purchase.dailyProcurementEdit',
              p_code: 'pc.daily.procurement',
            },
            props: true,
          },
          {
            // View
            path: 'view/:id',
            component: () => import('@/views/purchase/procurementApplications/add.vue'),
            name: 'purchaseDailyProcurementView',
            needAPrint: 'Y',
            meta: {
              title: 'router.purchase.dailyProcurementView',
              p_code: 'pc.daily.procurement',
            },
            props: true,
          },
        ],
      },
      {
        // 採購審批
        path: 'approval',
        component: () => import('@/views/purchase/procurementApproval/index.vue'),
        name: 'purchaseDailyApproval',
        // redirect: '/daily/approval/list',
        // index: 'purchaseDailyApprovalList',
        redirect: { name: 'purchaseDailyApprovalList' },
        meta: {
          title: 'router.purchase.dailyApproval',
          p_code: 'pc.daily.approval',
        },
        props: true,
        children: [
          {
            // List
            path: 'list',
            component: () => import('@/views/purchase/procurementApproval/list.vue'),
            name: 'purchaseDailyApprovalList',
            needAPrint: 'Y',
            meta: {
              title: 'router.purchase.DailyApproval',
              p_code: 'pc.daily.approval',
            },
            props: true,
          },
          {
            // View
            path: 'view/:id',
            component: () => import('@/views/purchase/procurementApplications/add.vue'),
            name: 'purchaseDailyApprovalView',
            needAPrint: 'Y',
            meta: {
              title: 'router.purchase.dailyApprovalView',
              p_code: 'pc.daily.approval',
            },
            props: true,
          },
        ],
      },
    ],
  },
  {
    path: '/master',
    component: Layout,
    redirect: 'noredirect',
    name: 'purchaseMaster',
    meta: {
      title: 'router.purchase.master',
      p_code: 'pc.master',
      icon: 'setting',
    },
    children: [
      {
        // 會計週期
        path: 'period',
        component: () => import('@/views/master/years/index.vue'),
        name: 'purchaseMasterACPeriod',
        meta: {
          title: 'router.purchase.period',
          p_code: 'pc.master.ac_period',
        },
      },
      {
        // 同事檔案
        path: 'staff',
        component: () => import('@/views/assistance/staff/index.vue'),
        name: 'purchaseMasterStaff',
        meta: {
          title: 'router.purchase.staff',
          p_code: 'pc.master.staff',
        },
      },
      {
        // 採購等級
        path: 'procurement-level',
        component: () => import('@/views/purchase/procurementLevel/index.vue'),
        name: 'purchaseMasterProcurementLevel',
        meta: {
          title: 'router.purchase.masterProcurementLevel',
          p_code: 'pc.master.procurement_level',
        },
      },
      {
        // 角色設定
        path: 'role',
        component: () => import('@/views/purchase/role/index.vue'),
        name: 'purchaseMasterRole',
        meta: {
          title: 'router.purchase.role',
          p_code: 'pc.master.role_set',
        },
      },
      {
        // 系統設定
        path: 'setting',
        component: () => import('@/views/purchase/setting/index.vue'),
        name: 'purchaseMasterSystemSetting',
        meta: {
          title: 'router.purchase.systemSetting',
          p_code: 'pc.master.system_setting',
        },
      },
    ],
  },
]

export default purchaseRouter
