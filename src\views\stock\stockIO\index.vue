<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <header v-if="false">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ $t('router.settingScreenBasicSetting') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t($route.meta.title) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </header>
    <div class="fliter">
      <el-form :inline="true" class="mini-form">
        <!-- 存貨項目 -->
        <el-form-item :label="$t('stock.stockItem')">
          <el-select
            v-model="preferences.filters.selectedCode"
            style="width: 250px"
            @change="reloadData"
          >
            <el-option
              v-for="item in stockList"
              :key="item.stock_id"
              :label="`[${item.sk_code}] ${language === 'en' ? item.sk_name_en : item.sk_name_cn}`"
              :value="item.sk_code"
            />
          </el-select>
        </el-form-item>
        <!-- 會計週期 -->
        <el-form-item :label="$t('stock.years')">
          <el-select
            v-model="preferences.filters.selectedYear"
            class="year"
            style="width: 110px"
            @change="fetchStockList(false)"
          >
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="actions-icon">
        <i
          v-if="hasPermission_Output"
          :title="$t('btnTitle.exportExcel')"
          class="edac-icon action-icon edac-icon-excel"
          @click="onExport"
        />
      </div>
    </div>
    <div class="ioTable">
      <el-table
        :data="tableData"
        :span-method="spanMethod"
        :row-class-name="isStripe"
        stripe
        height="100%"
        style="width: max-content"
      >
        <el-table-column
          :formatter="formatDate"
          :label="$t('stock.stockIo.label.date')"
          prop="date"
          width="100"
          align="center"
        />
        <el-table-column
          :label="$t('stock.stockIo.label.inv_chq_no')"
          prop="inv_chq_no"
          width="120"
          align="center"
        />
        <el-table-column
          :label="$t('stock.stockIo.label.voucher_no')"
          prop="voucher_no"
          width="120"
          align="center"
        />
        <el-table-column
          :formatter="typeFormat"
          :label="$t('stock.stockIo.label.type')"
          prop="type"
          width="100"
          align="center"
        />
        <el-table-column :label="$t('stock.stockIo.label.in')" align="center">
          <el-table-column
            :label="$t('stock.stockIo.label.quantity')"
            prop="in_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockIo.label.unitPrice')"
            prop="in_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockIo.label.amount')"
            prop="in_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockIo.label.out')" align="center">
          <el-table-column
            :label="$t('stock.stockIo.label.quantity')"
            prop="out_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockIo.label.unitPrice')"
            prop="out_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockIo.label.unitPrice')"
            prop="out_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockIo.label.balance')" align="center">
          <el-table-column
            :label="$t('stock.stockIo.label.quantity')"
            prop="bf_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockIo.label.unitPrice')"
            prop="bf_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockIo.label.amount')"
            prop="bf_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getInOut } from '@/api/stock/stockIO'
import { fetchYears } from '@/api/master/years'
import { getStocks } from '@/api/stock/itemSetup'
import loadPreferences from '@/views/mixins/loadPreferences'
import permission from '@/views/mixins/permission'

import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { exportExcel } from '@/utils/excel'
import { stockInOutExport } from '@/api/report/excel'

export default {
  name: 'StockIOIndex',
  mixins: [loadPreferences, permission],
  data() {
    return {
      loading: false,
      years: '',
      tableData: [],
      stockList: [],
      preferences: {
        filters: {
          selectedYear: '',
          selectedCode: '',
        },
      },
      childPreferences: ['selectedCode'],
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },

  methods: {
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      if (!row.date) {
        return 'count-row'
      }
    },
    /**
     * 類型格式轉換
     */
    typeFormat(row, column) {
      console.log(row.type)
      switch (row.type) {
        case 'S':
          return this.$t('stock.label.sales')
        case 'D':
          return this.$t('stock.label.damagedOrLost')
        case 'U':
          return this.$t('stock.label.internalUse')
        case 'W':
          return this.$t('stock.label.writeOff')
        case 'P':
          return this.$t('stock.label.purchase')
        default:
          return ''
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.tableData.length - 1) {
        if (columnIndex < 2 || columnIndex === 3) {
          return { rowspan: 0, colspan: 0 }
        } else if (columnIndex === 2) {
          return { rowspan: 1, colspan: 4 }
        }
      }
      return { rowspan: 1, colspan: 1 }
    },
    /**
     * 列計算
     */
    // getSummaries(param) {
    // console.log('param', param)
    // const { columns, data } = param
    // const sums = []
    // // const values = []
    // columns.forEach((column, index) => {
    //   // if (index <= 2) {
    //   //   sums[index] = ''
    //   //   return
    //   // }
    //   if (index === 3) {
    //     sums[index] = '總計'
    //     return
    //   }
    //   // if (index >= 10) {
    //   //   sums[index] = ''
    //   //   return
    //   // }
    //   // const values = data.map(item => {
    //   //   return item[column.property]
    //   // })
    //   // values.push(data[column.property])
    //   // console.log('property', column.property)
    //   // console.log('values', values)
    //   // console.log('data', data)
    //   // if (values.every(value => isNaN(value)) ===undefined) {
    //   //   sums[index] = values.reduce((prev, curr) => {
    //   //     const value = Number(curr);
    //   //     if (!isNaN(value)) {
    //   //       return prev + curr;
    //   //     } else {
    //   //       return prev;
    //   //     }
    //   //   }, 0);
    //   //   sums[index] += ' 元';
    //
    //   // } else {
    //   //   sums[index] = 'N/A';
    //   // }
    // })

    // return sums
    // },
    fetchData() {
      this.loading = false
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length) {
            const month = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
            if (month) {
              return
            }
            this.preferences.filters.selectedYear = this.years[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => this.fetchStockList(true))
        .finally(() => {
          this.loading = false
        })
    },
    fetchStockList(isNeedUpdateChildPreference) {
      this.stockList = []
      const fy_code = this.preferences.filters.selectedYear
      if (!fy_code) {
        return Promise.reject()
      }
      return new Promise((resolve, reject) => {
        getStocks(fy_code)
          .then(res => {
            this.stockList = res
          })
          .then(() => {
            if (isNeedUpdateChildPreference) {
              this.updateChildPreference()
            }
          })
          .then(() => {
            if (!this.stockList.length) {
              return Promise.reject()
            }
            const stock = this.stockList.find(
              i => i.sk_code === this.preferences.filters.selectedCode,
            )
            if (!stock) {
              this.preferences.filters.selectedCode = this.stockList[0].sk_code
            }
          })
          .then(this.reloadData)
          .then(() => {
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    reloadData() {
      const fy_code = this.preferences.filters.selectedYear
      const sk_code = this.preferences.filters.selectedCode
      if (!sk_code) {
        return Promise.reject()
      }
      return new Promise((resolve, reject) => {
        this.loading = true
        getInOut({
          fy_code,
          sk_code,
        })
          .then(res => {
            this.tableData = this.formatData(res)
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatAmount(row, column, cellValue, index) {
      if (typeof cellValue === 'string') {
        return cellValue
      } else if (cellValue == null) {
        return ''
      } else {
        return amountFormat(cellValue)
      }
    },
    formatDate(row, column, cellValue, index) {
      const d = dateUtil.format(new Date(cellValue), this.styles.dateFormat)
      return d
    },
    formatData(data) {
      const newData = []
      const sum = {
        date: undefined,
        inv_chq_no: undefined,
        voucher_no: this.$t('stock.stockIo.label.total') + '：',
        type: undefined,
        in_qty: 0,
        in_amount: 0,
        in_avg: 0,
        out_qty: 0,
        out_amount: 0,
        out_avg: 0,
        bf_qty: '', // 結餘不需要
        bf_amount: '',
        bf_avg: '',
      }
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        newData.push({
          ...item,
        })
        sum.in_qty = this.add(sum.in_qty, item.in_qty)
        sum.in_amount = this.add(sum.in_amount, item.in_amount)
        sum.out_qty = this.add(sum.out_qty, item.out_qty)
        sum.out_amount = this.add(sum.out_amount, item.out_amount)
        // sum.bf_qty = this.add(sum.bf_qty, item.bf_qty)
        // sum.bf_amount = this.add(sum.bf_amount, item.bf_amount)
      }
      sum.in_avg = toDecimal(sum.in_amount / sum.in_qty)
      if (sum.in_avg === Infinity || isNaN(sum.in_avg)) {
        sum.in_avg = 'N/A'
      }
      sum.out_avg = toDecimal(sum.out_amount / sum.out_qty)
      if (sum.out_avg === Infinity || isNaN(sum.out_avg)) {
        sum.out_avg = 'N/A'
      }
      // sum.bf_avg = sum.bf_amount / sum.bf_qty
      // if (sum.bf_avg === Infinity || isNaN(sum.bf_avg)) {
      //   sum.bf_avg = 'N/A'
      // }
      newData.push(sum)
      return newData
    },
    add(a, b) {
      const a1 = Number(a)
      const a2 = isNaN(a1) ? 0 : a1
      const b1 = Number(b)
      const b2 = isNaN(b1) ? 0 : b1
      return toDecimal(a2 + b2)
    },
    /**
     * Button Export
     */
    onExport() {
      if (this.loading) {
        return
      }
      const fy_code = this.preferences.filters.selectedYear
      const sk_code = this.preferences.filters.selectedCode
      if (!fy_code || !sk_code) {
        // this.$message.error('')
        return
      }
      this.loading = true
      stockInOutExport({ fy_code, sk_code })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .fliter {
    width: 900px;
    margin: 5px 0;
    display: flex;
    text-align: center;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
  }
  .ioTable {
    height: calc(100vh - 210px);
    /deep/ {
      .el-table {
        height: 100%;
        table {
          tbody {
            .cell {
              .el-input-number--medium {
                width: 100%;
              }
              .el-input {
                border-radius: 0;
              }
            }
            .count-row {
              td {
                background-color: #e2e2e2;
                color: #404246;
                &:first-child {
                  text-align: right;
                }
              }
              .cell {
                @extend .count-row;
              }
            }
          }
        }
      }
    }
  }
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
</style>
