import request from '@/utils/request'

/**
 * 新增傳票類別 返回新增后的傳票類別id
 * @param {integer} fund_id 賬目類別(大)id
 * @param {string} vt_category 傳票類別類型,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} vt_code 傳票類別編號
 * @param {string} vt_description_cn 傳票類別描述中文
 * @param {string} vt_description_en 傳票類別描述英文
 * @param {string} vt_format 傳票類別格式
 * @param {string} vt_ac_code 銀行賬號(會計科目編號)
 * @param {string} chq_template_code 支票款式編號
 * @param {string} vt_group 傳票類別組別
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @return {Promise}
 */
export function createVoucherType({
  fund_id,
  vt_category,
  vt_code,
  vt_description_cn,
  vt_description_en,
  vt_format,
  vt_ac_code,
  chq_template_code,
  vt_group,
  active_year,
}) {
  return request({
    url: '/voucher-types/actions/create',
    method: 'post',
    data: {
      fund_id,
      vt_category,
      vt_code,
      vt_description_cn,
      vt_description_en,
      vt_format,
      vt_ac_code,
      chq_template_code,
      vt_group,
      active_year,
    },
  })
}

/**
 * 修改傳票類別
 * @param {integer} voucher_type_id 傳票類別id
 * @param {integer} fund_id 賬目類別(大)id
 * @param {string} vt_category 傳票類別類型,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} vt_code 傳票類別編號
 * @param {string} vt_description_cn 傳票類別描述中文
 * @param {string} vt_description_en 傳票類別描述英文
 * @param {string} vt_format 傳票類別格式
 * @param {string} vt_ac_code 銀行賬號(會計科目編號)
 * @param {string} chq_template_code 支票款式編號
 * @param {string} vt_group 傳票類別組別
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @return {Promise}
 */
export function editVoucherType({
  voucher_type_id,
  fund_id,
  vt_category,
  vt_code,
  vt_description_cn,
  vt_description_en,
  vt_format,
  vt_ac_code,
  chq_template_code,
  vt_group,
  active_year,
}) {
  return request({
    url: '/voucher-types/actions/update',
    method: 'post',
    data: {
      voucher_type_id,
      fund_id,
      vt_category,
      vt_code,
      vt_description_cn,
      vt_description_en,
      vt_format,
      vt_ac_code,
      chq_template_code,
      vt_group,
      active_year,
    },
  })
}

/**
 * 刪除傳票類別
 * @param {integer} voucher_type_id 傳票類別id
 * @return {Promise}
 */
export function deleteVoucherType(voucher_type_id) {
  return request({
    url: '/voucher-types/actions/delete',
    method: 'post',
    data: {
      voucher_type_id,
    },
  })
}

/**
 * 返回所有傳票類別
 * @param {integer} fund_id 賬目類別id
 * @param {string} vt_category 傳票類別類型,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} vt_code
 * @param {integer} active_year 活躍的會計週期編號
 * @return {Promise}
 */
export function fetchVoucherTypes({ fund_id, vt_category, fy_code, vt_code }) {
  return request({
    url: '/voucher-types',
    method: 'get',
    params: {
      fund_id,
      vt_category,
      fy_code,
      vt_code,
    },
  })
}

/**
 * 獲取某傳票類別詳情
 * @param {integer} voucher_type_id 傳票類別id
 * @return {Promise}
 */
export function getVoucherType(voucher_type_id) {
  return request({
    url: '/voucher-types/actions/inquire',
    method: 'get',
    params: {
      voucher_type_id,
    },
  })
}

/**
 * 返回傳票類別excel數據
 */
export function exportVoucherTypes() {
  return request({
    url: '/voucher-types/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入傳票類別excel數據
 * @param {object} data_json 匯入的數據
 */
export function importVoucherTypes(data_json) {
  return request({
    url: '/voucher-types/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

/**
 * 獲取某傳票類別的單號列表
 * @param {integer} voucher_type_id 傳票類別id
 * @param {string} date 傳票日期
 * @param {string} vc_no 如果是修改傳票時查詢,傳入傳票當前記錄的傳票號碼
 * @return {Promise}
 */
export function fetchNoList({ voucher_type_id, date, vc_no }) {
  return request({
    url: '/voucher-types/no-list',
    method: 'get',
    params: {
      voucher_type_id,
      date,
      vc_no,
    },
  })
}

export default {
  createVoucherType,
  editVoucherType,
  deleteVoucherType,
  fetchVoucherTypes,
  getVoucherType,
  exportVoucherTypes,
  importVoucherTypes,
  fetchNoList,
}
