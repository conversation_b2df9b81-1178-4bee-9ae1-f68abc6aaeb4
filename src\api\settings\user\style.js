import request from '@/utils/request'

/**
 * 更新用戶頁面樣式設置
 * @param {integer} user_id 用戶id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} ss_code 功能編號
 * @param {string} ss_setting_json 樣式內容(json)
 */
export function editUserStyleSheets({ user_id, system, ss_code, ss_setting_json }) {
  return request({
    url: '/users/style-sheets/actions/update',
    method: 'post',
    data: {
      user_id,
      system,
      ss_code,
      ss_setting_json: JSON.stringify(ss_setting_json),
    },
  })
}

/**
 * 更新職員頁面樣式設置
 * @param {integer} staff_id 職員id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} ss_code 功能編號
 * @param {string} ss_setting_json 樣式內容(json)
 */
export function editStaffStyleSheets({ staff_id, system, ss_code, ss_setting_json }) {
  return request({
    url: '/staffs/style-sheets/actions/update',
    method: 'post',
    data: {
      staff_id,
      system,
      ss_code,
      ss_setting_json: JSON.stringify(ss_setting_json),
    },
  })
}

/**
 * 返回用戶樣式設置
 * @param {integer} user_id 用戶id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} ss_code 功能編號
 */
export function getUserStyleSheets({ user_id, system, ss_code }) {
  return request({
    url: '/users/style-sheets/actions/inquire',
    method: 'get',
    params: {
      user_id,
      system,
      ss_code,
    },
  })
}

/**
 * 返回職員樣式設置
 * @param {integer} staff_id 職員id
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} ss_code 功能編號
 */
export function getStaffStyleSheets({ staff_id, system, ss_code }) {
  return request({
    url: '/staffs/style-sheets/actions/inquire',
    method: 'get',
    params: {
      staff_id,
      system,
      ss_code,
    },
  })
}

/**
 * 更新系統預設頁面樣式設置
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} ss_code 功能編號
 * @param {string} ss_setting_json 樣式內容(json)
 */
export function editDefaultStyleSheets({ system, ss_code, ss_setting_json }) {
  return request({
    url: '/default/style-sheets/actions/update',
    method: 'post',
    data: {
      system,
      ss_code,
      ss_setting_json: JSON.stringify(ss_setting_json),
    },
  })
}

/**
 * 返回系統預設樣式設置
 * @param {string} system 系統 AC:賬目 BG:預算
 * @param {string} ss_code 功能編號
 */
export function getDefaultStyleSheets({ system, ss_code }) {
  return request({
    url: '/default/style-sheets/actions/inquire',
    method: 'get',
    params: {
      system,
      ss_code,
    },
  })
}

/**
 * 返回用戶多個樣式設置
 * @param {number} user_id 用戶id
 * @param {string} system 系統 AC:賬目 BG:預算 PC:採購
 * @param {string} ss_code_str 功能編號(使用逗號分隔)
 * @param {string} [ss_key] 樣式鍵名
 * @param {string} [ss_type] 樣式類型
 * @return {Promise}
 */
export function getUserStyleSheetsMulti({ user_id, system, ss_code_str, ss_key, ss_type }) {
  return request({
    url: '/users/style-sheets/actions/multi-inquire',
    method: 'get',
    params: {
      user_id,
      system,
      ss_code_str,
      ss_key,
      ss_type,
    },
  })
}

export default {
  editUserStyleSheets,
  editStaffStyleSheets,
  getUserStyleSheets,
  getStaffStyleSheets,
  editDefaultStyleSheets,
  getDefaultStyleSheets,
}

/**
 * 返回同事多個樣式設置
 * @param {number} staff_id 同事id
 * @param {string} system 系統 AC:賬目 BG:預算 PC:採購
 * @param {string} ss_code_str 功能編號(使用逗號分隔)
 * @param {string} [ss_key] 樣式鍵名
 * @param {string} [ss_type] 樣式類型
 * @return {Promise}
 */
export function getStaffStyleSheetsMulti({ staff_id, system, ss_code_str, ss_key, ss_type }) {
  return request({
    url: '/staffs/style-sheets/actions/multi-inquire',
    method: 'get',
    params: {
      staff_id,
      system,
      ss_code_str,
      ss_key,
      ss_type,
    },
  })
}
