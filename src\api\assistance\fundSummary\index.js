import request from '@/utils/request'

/**
 * 匯出財務報表類別關係
 * @param {string} fy_code 會計週期code
 * @return {Promise}
 */
export function exportFundSummaryTypesRelation({ fy_code }) {
  return request({
    url: '/fund-summary-types-relation/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
    },
  })
}

/**
 * 匯入財務報表類別關係
 * @param {string} fy_code 會計週期code
 * @param {string} cate_data_json 匯入的類別數據(Financial Statement Category)
 * @param {string} rel_data_json 匯入的關係數據(Category Relation)
 * @return {Promise}
 */
export function importFundSummaryTypesRelation({ cate_data_json, rel_data_json }, fy_code) {
  return request({
    url: '/fund-summary-types-relation/actions/import',
    method: 'post',
    data: {
      fy_code,
      cate_data_json: JSON.stringify(cate_data_json),
      rel_data_json: JSON.stringify(rel_data_json),
    },
  })
}

/**
 * 匯出財務報表
 * @param {string} fy_code 會計週期code
 * @param {string} pd_code 會計週期月份編號
 * @return {Promise}
 */
export function exportFinancialStatement({ fy_code, pd_code }) {
  return request({
    url: '/reports/financial-statement/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      pd_code,
    },
  })
}
