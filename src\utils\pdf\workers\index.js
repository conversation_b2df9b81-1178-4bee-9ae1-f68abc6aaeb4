import { initTips, isInitPDF } from '@/utils/pdf'
import PdfMarge from '@/utils/pdf/merge'
import { PDF_MSG_TYPE } from '@/utils/pdf/workers/constant'
import store from '../../../store/index'
/**
 * 將列表拆分
 * @param arr 列表
 * @param splitCount 最多拆分多少分， 默認3
 * @returns {*[]}
 */
function splitArray(arr, splitCount = 3) {
  const result = []
  let size = Math.ceil(arr.length / splitCount)
  if (size < 30) {
    size = 30
  }
  let index = 0
  for (let i = 0; i < arr.length; i += size) {
    result.push({
      index: index++,
      list: arr.slice(i, i + size),
      key: `${i}-${i + size}`,
    })
  }
  return result
}

export async function mergePDF(list) {
  try {
    const pdf = new PdfMarge()
    const first = await PdfMarge.loadUrlPdf(list[0])
    await pdf.init(first)
    for (let i = 1; i < list.length; i++) {
      await pdf.addUrlPdf(list[i])
    }
    return pdf.getBlobUrl()
  } catch (e) {
    console.error(e)
    return Promise.reject(e)
  }
}

export function generateReportList(list, onProgress, splitCount = 3) {
  return new Promise((resolve, reject) => {
    if (!list || list.length === 0) {
      reject(new Error('Empty List'))
      return
    }
    if (!isInitPDF) {
      initTips()
      this.$bus.emit('pdfSDKStatus', 'PDF_LOADING_FAILED')
      return
    }
    const groups = splitArray(list, splitCount)
    if (groups.length === 0) {
      reject(new Error('Empty List'))
      return
    }
    const length = list.length
    const workers = []
    const closeWorkers = () => {
      try {
        for (let i = 0; i < workers.length; i++) {
          workers[i] && workers[i].terminate()
        }
      } catch (e) {
        console.error(e)
      }
    }
    const onDone = () => {
      if (groups.length === 1) {
        resolve(groups[0].url)
        return
      }
      mergePDF(groups.map(group => group.url))
        .then(url => {
          closeWorkers()
          resolve(url)
        })
        .catch(e => {
          closeWorkers()
          reject(e)
        })
    }
    let completeCount = 0
    const onMessage = e => {
      const data = e.data
      switch (data.type) {
        case PDF_MSG_TYPE.DONE:
          if (data.printListData && data.printListData.index !== null) {
            store.commit('printListItemFinish', {
              index: data.printListData.index,
              url: data.printListData.url,
            })
          }
          groups[data.index].url = data.url
          if (groups.every(group => group.url)) {
            onDone()
          }
          break
        case PDF_MSG_TYPE.BUSY:
        case PDF_MSG_TYPE.ERROR:
          if (data.printListData && data.printListData.index !== null) {
            store.commit('printListItemError', { index: data.printListData.index })
          }
          closeWorkers()
          reject(data.error)
          break
        case PDF_MSG_TYPE.PAGE_DONE:
          completeCount++
          onProgress && onProgress(completeCount, length)
          break
        default:
          console.log('onMessage unhandled', data)
          break
      }
    }
    const WKModule = require('@/utils/pdf/workers/pdf.worker.js')
    const WK = WKModule.default || WKModule
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i]
      const worker = new WK()
      worker.onmessage = onMessage
      worker.postMessage({
        type: PDF_MSG_TYPE.VFS,
        vfs: window.pdfMake.vfs,
      })
      worker.postMessage({
        list: group.list,
        index: group.index,
        type: PDF_MSG_TYPE.PRINT,
      })
      workers.push(worker)
    }
  })
}

export default {
  generateReportList,
}
