# EDAC - 前端UI
====
> 環境配置
> node 版本：  v8.11.4  v12.10.0
> npm 版本：   v5.6.0  
> vue 版本：   v2.5.17
## 安裝
1. 安装依赖  
    `npm install`
2. 修改環境配置  
    > 新建（或複製`.env.example`）`.env`文件到根目錄  
    數據格式參考 config/global.js  
    僅填寫需修改的配置  
    
      ```javascript
      'use strict'
      const env = require('./config/global')
      module.exports = env
      
      // 配置開發環境
      env.localhost = '**************' 
      env.localProtocol = '9527'
      ```
3. 构建開發环境  
    `npm run dev`
4. 构建生产环境  
    `npm run build:prod`



## 插件使用
### PDF - PDFMake 
> 版本 0.1.56
#### 依賴庫
- [PDFMake](https://github.com/bpampuch/pdfmake)
- [nzh](https://github.com/cnwhy/nzh) *支票打印，金額轉中文大寫*
#### 文件
- src/utils/pdf/index.js  // 生成PDF文件
- src/utils/pdf/generator.js  // 生成通用數據格式
- static/js/vfs_fonts.min.js // 字體文件

#### 加載（初始化）
> 由於字體文件太大，所以需手動加載1次。  
在使用前根據`isInitPDF`判斷是否加載完成
```javascript
import { initPDFFont, isInitPDF } from '@/utils/pdf/index'
  
  if (!isInitPDF) { // 加載前判斷當前狀態，避免重複加載
    return
  }
  
  initPDFFont() // 加載
  .then(() => {
    // 加載成功
  })
  .catch(() => {
    // 加載失敗
  })
  // ...

```

#### 使用
>  [docDefinition 格式文檔](https://pdfmake.github.io/docs/)
```javascript
import { openPdf} from '@/utils/pdf/index'
const docDefinition = {
        content: [
          { text: '顯示文字', fontSize: 15 },
        ]
      }
openPdf(docDefinition)
```


## EXCEL - js-xlsx
######無pdf和 excel 功能版本 - 2019/05/20



