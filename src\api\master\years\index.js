import request from '@/utils/request'

/**
 * 新增會計週期
 * @param {number} fy_code 會計週期編號,一般取年份后兩位
 * @param {string} fy_name 會計週期
 * @param {string} fy_status 狀態: O:開啟,C:關閉
 * @param {string} fy_enquiry 可查詢: Y:可查詢,N:不可查詢
 * @param {string} periods_json 會計週期月份設定,格式: [{"pd_code":"1809","pd_name":"09/2017","pd_status":"O"},...]
 */
export function createYear(fy_code, fy_name, fy_status, fy_enquiry, periods_json) {
  return request({
    url: '/years/actions/create',
    method: 'post',
    data: {
      fy_code,
      fy_name,
      fy_status,
      fy_enquiry,
      periods_json,
    },
  })
}

/**
 * 修改會計週期
 * @param {number} fy_id 會計週期id
 * @param {string} fy_name 會計週期
 * @param {string} fy_status 狀態: O:開啟,C:關閉
 * @param {string} fy_enquiry 可查詢: Y:可查詢,N:不可查詢
 * @param {string} periods_json 會計週期月份設定,格式: [{"pd_code":"1809","pd_name":"09/2017","pd_status":"O"},...]
 */
export function editYear(fy_id, fy_name, fy_status, fy_enquiry, periods_json) {
  return request({
    url: '/years/actions/update',
    method: 'post',
    data: {
      fy_id,
      fy_name,
      fy_status,
      fy_enquiry,
      periods_json,
    },
  })
}

/**
 * 刪除會計週期
 * @param {number} fy_id 會計週期id
 */
export function deleteYear(fy_id) {
  return request({
    url: '/years/actions/delete',
    method: 'post',
    data: {
      fy_id,
    },
  })
}
function getYears() {
  return request({
    url: '/years',
    method: 'get',
  })
}
/**
 * 返回所有會計週期
 */
export function fetchYears() {
  // return new Promise((resolve, reject) => {
  //   if (new Date().getTime() - cache.time > 10000) {
  //     getYears().then(res => {
  //       cache.time = new Date().getTime()
  //       cache.list = res
  //       resolve(res)
  //     }).catch(err => {
  //       reject(err)
  //     })
  //   } else {
  //     resolve(cache.list)
  //   }
  // })
  return new Promise((resolve, reject) => {
    getYears()
      .then(res => {
        resolve(res)
      })
      .catch(err => {
        reject(err)
      })
  })
}

/**
 * 獲取某會計週期詳情
 * @param {number} fy_id 會計週期id
 */
export function getYear(fy_id) {
  return request({
    url: '/years/actions/inquire',
    method: 'get',
    params: {
      fy_id,
    },
  })
}
/**
 * 獲取某會計週期詳情
 * @param {number} fy_id 會計週期id
 */
export function getBudgetYears(fy_id) {
  return request({
    url: '/budget-years',
    method: 'get',
    params: {
      fy_id,
    },
  })
}

/**
 * 返回對應的會計週期的月份
 * @param {string} date 要查詢的日期
 * @return {Promise}
 */
export function searchByDate(date) {
  return request({
    url: '/periods/actions/search-by-date',
    method: 'get',
    params: {
      date,
    },
  })
}

/**
 * 返回符合條件的會計週期月份
 * @param {string} active_year 通過會計週期active_year,如傳"18,19",則返回18,19年的月份
 * @return {Promise}
 */
export function fetchPeriods(active_year) {
  return request({
    url: '/periods',
    method: 'get',
    params: {
      active_year,
    },
  })
}

/**
 * 返回pd_code或者fy_code查詢最後一日的日子
 * @param {string} fy_code 要查詢的會計週期
 * @param {string} pd_code 要查詢的會計週期月份
 * @return {Promise}
 */
export function searchTheDate({ fy_code, pd_code }) {
  return request({
    url: '/periods/actions/search-the-date',
    method: 'get',
    params: {
      fy_code,
      pd_code,
    },
  })
}

export default {
  createYear,
  editYear,
  deleteYear,
  fetchYears,
  getYear,
  searchByDate,
  fetchPeriods,
  searchTheDate,
}
