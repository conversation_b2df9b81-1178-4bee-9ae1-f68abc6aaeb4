<script>
import { mapGetters } from 'vuex'
import { editUserStyleSheets } from '@/api/settings/user/style'
import { getPrintSetting } from '@/api/settings/printSetting'

export default {
  props: {},
  data() {
    return {
      printoutSetting: null,
    }
  },
  computed: {
    ...mapGetters({ thisSystem: 'system', user_id: 'user_id' }),
    styleAttr() {
      return {}
    },
  },
  created() {},
  updated() {},
  methods: {
    loadPrintoutSetting(t_ps_code, using = 1, psg_code) {
      const ps_code = t_ps_code || this.ps_code
      if (!ps_code) {
        throw new Error('ps_code is empty')
      }
      const params = {
        using: using === null ? undefined : using,
        ps_code,
        psg_code,
      }
      this.loading = true
      return new Promise((resolve, reject) => {
        getPrintSetting(params)
          .then(res => {
            if (res.length) {
              const data = res[0].config_json
              // res.forEach(item => {
              //   switch (item.ss_type) {
              //     case 'column':
              //       if (data.columnData === undefined) {
              //         data.columnData = []
              //       }
              //       data.columnData.push({
              //         name: item.ss_key,
              //         width: item.width,
              //         alignment: item.alignment,
              //         position: item.x
              //       })
              //       break
              //     case 'sign_data':
              //       if (data.sign_data === undefined) {
              //         data.sign_data = []
              //       }
              //       data.sign_data.push(JSON.parse(item.value))
              //       break
              //     case 'fund_abbr':
              //       if (data.fund_abbr === undefined) {
              //         data.fund_abbr = []
              //       }
              //       data.fund_abbr.push(item.value)
              //       break
              //     default:
              //       data[item.ss_key] = item.value
              //       break
              //   }
              // })
              this.printoutSetting = data
              resolve(data)
            } else {
              reject(null)
            }
          })
          .catch(() => {
            reject()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    saveUserStyleSheets() {
      const user_id = this.user_id
      const system = this.thisSystem
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code

      const ss_setting_json = this.styleData
      editUserStyleSheets({ user_id, system, ss_code, ss_setting_json })
        .then(res => {})
        .catch(() => {})
    },
    /**
     * 頁面設置按鈕點擊
     */
    onShowSetting() {
      this.showDialog = true
    },
  },
}
</script>
