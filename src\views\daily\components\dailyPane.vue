<template>
  <div v-if="!paneLoading" class="app-container dailyPane">
    <split-pane
      ref="splitPane"
      :min-percent="minPercent"
      :default-percent="scale"
      split="vertical"
      @resize="resize"
    >
      <template slot="paneL">
        <div class="left">
          <VBreadCrumb class="breadcrumb" />
          <!--          <slot class="paneL" name="paneL">-->
          <!--            <div class="left-container" />-->
          <!--          </slot>-->
          <router-view :key="routerKey" :has-right="hasRight" />
        </div>
      </template>
      <template slot="paneR" class="paneR">
        <!--        <split-pane split="horizontal">-->
        <!--          <template slot="paneL">-->
        <!--            <div class="top-container" />-->
        <!--          </template>-->
        <!--          <template slot="paneR">-->
        <!--            <div class="bottom-container" />-->
        <!--          </template>-->
        <!--        </split-pane>-->
        <div class="enquiry-icon">
          <i
            v-if="permissionList[0]"
            :class="[{ disable: !paneStateList[0] }]"
            :title="$t('btnTitle.enquiry.grant')"
            class="edac-icon edac-icon-allowance action-icon"
            @click="triggerState(0)"
          />
          <i
            v-if="permissionList[1]"
            :class="[{ disable: !paneStateList[1] }]"
            :title="$t('btnTitle.enquiry.budget')"
            class="edac-icon edac-icon-budget action-icon"
            @click="triggerState(1)"
          />
          <!--          <i class="edac-icon edac-icon-budget disable"/>-->
          <i
            v-if="permissionList[2]"
            :class="[{ disable: !paneStateList[2] }]"
            :title="$t('btnTitle.enquiry.staff')"
            class="edac-icon edac-icon-office_clerk action-icon"
            @click="triggerState(2)"
          />
          <i
            v-if="permissionList[3]"
            :class="[{ disable: !paneStateList[3] }]"
            :title="$t('btnTitle.enquiry.dept')"
            class="edac-icon edac-icon-dept action-icon"
            @click="triggerState(3)"
          />
          <i
            v-if="permissionList[4]"
            :class="[{ disable: !paneStateList[4] }]"
            :title="$t('btnTitle.enquiry.contra')"
            class="edac-icon edac-icon-hedging action-icon"
            @click="triggerState(4)"
          />
          <i
            v-if="permissionList[5]"
            :class="[{ disable: !paneStateList[5] }]"
            :title="$t('btnTitle.enquiry.general')"
            class="edac-icon edac-icon-Search action-icon"
            @click="triggerState(5)"
          />
        </div>
        <div class="right-content">
          <div class="ms-pane">
            <div
              v-if="paneStateList[0]"
              :class="[{ 'last-pane': lastPane === 0 }]"
              :style="paneHeight"
              class="m-pane pane-1"
            >
              <EnquiryGrant />
            </div>
            <div v-if="paneStateList[1]" :style="paneHeight" class="m-pane pane-2">
              <EnquiryBudget />
            </div>
            <div
              v-if="paneStateList[2]"
              :class="[{ 'last-pane': lastPane === 2 }]"
              :style="paneHeight"
              class="m-pane pane-3"
            >
              <EnquiryStaff />
            </div>
            <div
              v-if="paneStateList[3]"
              :class="[{ 'last-pane': lastPane === 3 }]"
              :style="paneHeight"
              class="m-pane pane-4"
            >
              <EnquiryDepartment />
            </div>
            <div
              v-if="paneStateList[4]"
              :class="[{ 'last-pane': lastPane === 4 }]"
              :style="paneHeight"
              class="m-pane pane-5"
            >
              <EnquiryContra />
            </div>
            <div
              v-if="paneStateList[5]"
              :class="[{ 'last-pane': lastPane === 5 }]"
              :style="paneHeight"
              class="m-pane pane-list"
            >
              <EnquiryGeneral />
            </div>
          </div>
        </div>
      </template>
    </split-pane>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import splitPane from 'vue-splitpane'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'

import { editUserPreference, getUserPreference } from '@/api/settings/user/preference'

import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'

export default {
  name: 'DailyPane',
  components: {
    splitPane,
    VBreadCrumb,
    EnquiryGrant: () => import('@/views/daily/enquiry/grant'),
    EnquiryStaff: () => import('@/views/daily/enquiry/staff'),
    EnquiryDepartment: () => import('@/views/daily/enquiry/department'),
    EnquiryContra: () => import('@/views/daily/enquiry/contra'),
    EnquiryGeneral: () => import('@/views/daily/enquiry/general'),
    EnquiryBudget: () => import('@/views/daily/enquiry/budget'),
  },
  mixins: [loadCustomStyle, loadPreferences, mixinPermission],
  props: {},
  data() {
    return {
      isSave: false,
      preferences: {
        filters: {
          height: 100,
        },
      },
      paneStateList: [true, false, true, true, true, true],
      permissionList: [false, false, false, false, false, false],
    }
  },
  computed: {
    ...mapGetters({ thisSystem: 'system', user_id: 'user_id' }),
    minPercent() {
      return this.hasRight ? 30 : 0
    },
    scale() {
      return this.hasRight ? this.defaultScale : 100
    },
    hasRight() {
      return this.permissionList.some((v, i) => v && this.paneStateList[i])
    },
    paneHeight() {
      const num = this.paneStateList.filter(i => i).length
      const h = document.body.offsetHeight - 105 - 28 - 26 - 5
      const nh = h / num
      this.saveHeight(nh)
      return 'height:' + nh + 'px'
    },
    lastPane() {
      const arr = this.paneStateList
        .map((v, i) => {
          return { i, v }
        })
        .filter(i => i.v)
      // console.log(arr)
      return arr.length > 0 ? arr[arr.length - 1].i : -1
    },
    routerKey() {
      return this.$route.name !== undefined
        ? this.$route.name + +new Date()
        : this.$route + +new Date()
    },
  },
  watch: {
    $route(to, from) {
      // this.resetPane() // 查詢會刷新
    },
    paneStateList() {
      this.savePaneStateList()
    },
  },
  created() {
    this.saveUserLastPage()
    this.$bus.on('reloadVoucherPane', this.resetScale)
  },
  beforeDestroy() {
    console.log('beforeDestroy')
    this.$bus.off('reloadVoucherPane', this.resetScale)
  },
  mounted() {
    setTimeout(() => {
      this.$nextTick(() => {
        if (!this.$refs.splitPane || !this.$refs.splitPane.$el) return
        this.$nextTick(() => {
          this.$refs.splitPane.$el.onmouseup = this.save
          this.$refs.splitPane.$el.children[1].onmouseup = this.save
        })
      })
    }, 2000)
    this.loadPaneStateList().then(() => {
      this.permissionList = [
        this.hasPermissionByCode('ac.enquiry.grant', 'Y'),
        this.hasPermissionByCode('ac.enquiry.budget', 'Y'),
        this.hasPermissionByCode('ac.enquiry.staff', 'Y'),
        this.hasPermissionByCode('ac.enquiry.department', 'Y'),
        this.hasPermissionByCode('ac.enquiry.contra', 'Y'),
        this.hasPermissionByCode('ac.enquiry.general', 'Y'),
      ]
      for (let i = 0; i < this.paneStateList.length; i++) {
        if (!this.permissionList[i]) {
          this.paneStateList[i] = false
        }
      }
    })
  },
  methods: {
    resetPane() {
      this.paneLoading = true
      this.$nextTick(() => {
        this.paneLoading = false
      })
    },
    resetScale(val) {
      console.log('resetScale', this.$route.meta.p_code)
      this.defaultScale = val
    },
    saveHeight(height) {
      this.preferences.filters.height = height
    },
    getParentTop(el) {
      let top = el.offsetTop

      if (el.parentNode) {
        const pt = this.getParentTop(el.parentNode)
        if (pt) {
          top = top + pt
        }
      }
      return top
    },
    resize() {
      return new Promise(resolve => {
        console.log('resize')
        this.isSave = true
        this.defaultScale = this.$refs.splitPane.percent
        resolve()
      })
    },
    save() {
      if (this.isSave) {
        this.saveScale(this.defaultScale)
        this.isSave = false
      }
    },
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.thisSystem
      const content = {
        page: this.$route.name,
      }
      const pf_code = system.toLowerCase() + '.home'
      editUserPreference(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
    triggerState(i) {
      if (this.permissionList[i]) {
        this.$set(this.paneStateList, i, !this.paneStateList[i])
      }
    },
    loadPaneStateList() {
      const user_id = this.user_id
      const system = this.thisSystem
      const pf_code = 'ac.enquiry'
      return new Promise((resolve, reject) => {
        getUserPreference(user_id, system, pf_code, 'paneStateList')
          .then(res => {
            const list = JSON.parse(res)
            if (Array.isArray(list) && list.length === 6) {
              this.paneStateList = list
            } else {
              this.paneStateList = [true, true, true, true, true, true]
            }
          })
          .catch(() => {
            this.paneStateList = [true, true, true, true, true, true] // [false, false, false, false, false, false]
          })
          .finally(() => {
            resolve()
          })
      })
    },
    savePaneStateList() {
      const user_id = this.user_id
      const system = this.thisSystem
      const pf_code = 'ac.enquiry'
      const content = JSON.stringify(this.paneStateList)
      return new Promise((resolve, reject) => {
        editUserPreference(user_id, system, pf_code, 'paneStateList', content)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.app-container {
  position: relative;
  height: 100%;
  padding: 0;
  .left {
    padding: 10px 8px 20px 20px;
    display: block;
    position: relative;
    height: 100%;
  }
}
.breadcrumb {
  margin-bottom: 10px;
}

.enquiry-icon {
  /*float: right;*/
  text-align: right;
  padding: 10px 20px;
  line-height: 1;

  position: fixed;
  top: 50px;
  right: 0;
  width: 100%;
  min-width: 1000px;
  left: 0;
  pointer-events: none;

  i.edac-icon {
    font-size: 20px;
    color: $actionIconColor;
    pointer-events: all;
    &.disable {
      cursor: pointer;
      color: $disableColor;
    }
  }
}
.dailyPane {
  /deep/ {
    .splitter-pane-resizer {
      opacity: 1 !important;
      background: #eaeaea !important;
      background-clip: padding-box !important;
    }
    .splitter-pane-resizer.vertical {
      width: 1px;
      height: 100%;
      margin-left: 0px;
      border-left: 0;
      border-right: 0;
      cursor: col-resize;
      width: 5px;
      border-right: 4px solid hsla(0, 0%, 100%, 0);
      border-left: 0px solid hsla(0, 0%, 100%, 0);
    }
    .splitter-pane.vertical.splitter-paneR {
      padding-left: 0px;
    }
  }
}

.right-content {
  padding-left: 5px;
  position: relative;
  height: calc(100% - 30px);
  overflow-y: scroll; /* 超出就滾動 */

  .ms-pane {
    height: 100%;
    display: flex;
    /* align-items: stretch; */
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    .m-pane {
      padding: 5px;
      -webkit-box-flex: 1;
      -ms-flex: 1;
      flex: 1;

      position: relative;
      /*border-bottom: 1px solid hsl(0, 0%, 82%);*/
      &:last-child {
        border-bottom: none;
      }
      /deep/ {
        .e-table {
          /*height: auto;*/
          .el-table__body-wrapper {
            min-height: 50px;
            /*height: 100px;*/
          }
          .el-table__empty-block {
            /*min-height: auto;*/
          }
        }
      }
      &:after {
        content: '';
        width: calc(100% + 5px);
        height: 1px;
        background-color: #eaeaea;
        position: absolute;
        z-index: 2;
        left: -5px;
        bottom: 1px;
      }

      &.last-pane:after {
        content: unset;
        background-color: unset;
      }
    }
  }
}
</style>
<style lang="scss">
.EDAC.font-size-15,
.EDAC.font-size-16,
.EDAC.font-size-17,
.EDAC.font-size-18 {
  .enquiry-icon {
    top: 55px !important;
  }
}
</style>
