<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <header v-if="false">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ $t('router.settingScreenBasicSetting') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t($route.meta.title) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </header>
    <div class="filter">
      <el-form :inline="true" class="mini-form">
        <!-- 會計週期 -->
        <el-form-item :label="$t('stock.years')">
          <el-select
            v-model="preferences.filters.selectedYear"
            class="year"
            style="width: 130px"
            @change="onChangeYear"
          >
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
        </el-form-item>
        <!-- 類別 -->
        <el-form-item :label="$t(langKey + 'category')">
          <el-select
            v-model="preferences.filters.selectedGroupId"
            style="width: 130px"
            @change="reloadData"
          >
            <el-option :label="$t('filters.all')" value="" />
            <el-option
              v-for="item in budgetGroupList"
              :key="item.budget_id"
              :label="item[language === 'en' ? 'name_en' : 'name_cn']"
              :value="item.budget_id"
            />
          </el-select>
        </el-form-item>
        <!-- 状态 -->
        <el-form-item :label="$t(langKey + 'budget_stage')">
          <el-select
            v-model="preferences.filters.selectedStage"
            style="width: 130px"
            popper-class="status-select"
            @change="reloadData"
          >
            <el-option :label="$t('filters.all')" value="">
              <div style="padding: 5px 0">
                <div :style="{ margin: '0 5px' }">
                  {{ $t('filters.all') }}
                </div>
              </div>
            </el-option>
            <el-option
              v-for="item in stageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <div :style="{ background: statusColor[item.value], padding: '5px 0' }">
                <div class="el-select-dropdown__item" :style="{ color: '#fff', margin: '0 5px' }">
                  {{ item.label }}
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 負責 -->
        <el-form-item :label="$t(langKey + 'manager')">
          <el-select
            v-model="preferences.filters.selectedManager"
            style="width: 130px"
            @change="reloadData"
          >
            <el-option :label="$t('filters.all')" value="" />
            <el-option
              v-for="item in staffList"
              :key="item.staff_id"
              :label="item['st_name_' + (language === 'en' ? 'en' : 'cn')]"
              :value="item.staff_id"
            />
          </el-select>
        </el-form-item>
        <!-- 審批 -->
        <el-form-item :label="$t(langKey + 'approve')">
          <el-select
            v-model="preferences.filters.selectedApprove"
            style="width: 130px"
            @change="reloadData"
          >
            <el-option :label="$t('filters.all')" value="" />
            <el-option
              v-for="item in staffList"
              :key="item.staff_id"
              :label="item['st_name_' + (language === 'en' ? 'en' : 'cn')]"
              :value="item.staff_id"
            />
          </el-select>
        </el-form-item>
        <!-- 上年度 -->
        <el-form-item>
          <el-checkbox v-model="preferences.filters.lastYear">
            {{ $t('budget.budgetList.label.lastYear') }}
          </el-checkbox>
        </el-form-item>
        <!-- 打印 -->
        <el-form-item>
          <el-button type="primary" size="mini" @click="onPrint">
            {{ $t('button.print') }}
          </el-button>
          <div class="actions-icon">
            <i
              :title="$t('btnTitle.exportExcelPage')"
              class="edac-icon action-icon edac-icon-excel"
              @click="onExport('PAGE')"
            />
            <el-button @click="onExport('ALL')">
              {{ $t('budget.budgetManagement.label.fullList') }}
            </el-button>
            <!-- <i :title="$t('btnTitle.exportExcelAll')" class="edac-icon action-icon edac-icon-excel_add" @click="onExport('ALL')"/> -->
          </div>
          <!--          <el-button type="primary" size="mini" @click="onExport('ALL')">-->
          <!--            Full List-->
          <!--          </el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="tabla-area">
      <div class="budget-table">
        <el-table
          ref="dataTable"
          :data="tableData"
          :row-class-name="isStripe"
          height="100%"
          stripe
          :cell-style="cellStyle"
          style="width: max-content"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" align="center" width="50" />
          <el-table-column
            :label="$t(langKey + 'budget_code')"
            :width="columnWidths['budget_code']"
            align="center"
            prop="budget_code"
          />
          <el-table-column
            :label="$t(langKey + 'name_')"
            :prop="languageKey('name_')"
            :width="columnWidths['name_']"
            align="center"
          />
          <el-table-column
            :label="$t(langKey + 'manager_staff_name_')"
            :prop="languageKey('manager_staff_name_')"
            :width="columnWidths['manager_staff_name_']"
            align="center"
          />
          <el-table-column
            :formatter="formatterByStage"
            :label="$t(langKey + 'budget_stage')"
            :width="columnWidths['budget_stage']"
            align="center"
            prop="budget_stage"
          />
          <el-table-column
            v-if="preferences.filters.lastYear"
            :label="$t(langKey + 'lastYear')"
            align="center"
          >
            <el-table-column
              :label="$t(langKey + 'budget_IE')"
              :width="columnWidths['budget_IE']"
              align="center"
              prop="last_budget_IE"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.last_budget_IE)">I</span>
                <span v-if="'EB'.includes(scope.row.last_budget_IE)">E</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'budget_amount')"
              :width="columnWidths['budget_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_I_total_budget_amount) }}
                </span>
                <span v-if="'EB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_total_budget_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'actual_amount')"
              :width="columnWidths['actual_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_I_actual_amount) }}
                </span>
                <span v-if="'EB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_actual_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'balance')"
              :width="columnWidths['balance']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_I_balance) }}
                </span>
                <span v-if="'EB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_balance) }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column :label="$t(langKey + 'thisYear')" align="center">
            <el-table-column
              :label="$t(langKey + 'budget_IE')"
              :width="columnWidths['budget_IE']"
              align="center"
              prop="this_budget_IE"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.this_budget_IE)">I</span>
                <span v-if="'EB'.includes(scope.row.this_budget_IE)">E</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'proposed_amount')"
              :width="columnWidths['proposed_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_total_proposed_amount) }}
                </span>
                <span v-if="'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_total_proposed_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'approved_amount')"
              :width="columnWidths['proposed_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_total_approved_amount) }}
                </span>
                <span v-if="'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_total_approved_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'actual_amount')"
              :width="columnWidths['actual_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_actual_amount) }}
                </span>
                <span v-if="'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_actual_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'balance')"
              :width="columnWidths['balance']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_balance) }}
                </span>
                <span v-if="'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_balance) }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
      <div class="budget-summary-table">
        <el-table
          ref="summaryTable"
          :data="[summary]"
          :row-class-name="isStripe"
          :show-header="false"
          :span-method="summarySpanMethod"
          border
          height="100%"
          stripe
          style="width: max-content"
        >
          <el-table-column width="56px" />
          <el-table-column :width="columnWidths['budget_code']" align="right">
            <template v-if="scope && scope.row" slot-scope="scope">
              {{ $t('budget.budgetList.label.total') }}
            </template>
          </el-table-column>
          <el-table-column :width="columnWidths['name_']" />
          <el-table-column :width="columnWidths['manager_staff_name_']" />
          <el-table-column :width="columnWidths['budget_stage']" align="center" />
          <el-table-column
            v-if="preferences.filters.lastYear"
            :label="$t(langKey + 'lastYear')"
            align="center"
          >
            <el-table-column
              :label="$t(langKey + 'budget_IE')"
              :width="columnWidths['budget_IE']"
              align="center"
              prop="last_budget_IE"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="'IB'.includes(scope.row.last_budget_IE)">I</span>
                <span v-if="'EB'.includes(scope.row.last_budget_IE)">E</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'budget_amount')"
              :width="columnWidths['budget_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.last_budget_IE && 'IB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_I_total_budget_amount) }}
                </span>
                <span v-if="scope.row.last_budget_IE && 'EB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_total_budget_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'actual_amount')"
              :width="columnWidths['actual_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.last_budget_IE && 'IB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_total_budget_amount) }}
                </span>
                <span v-if="scope.row.last_budget_IE && 'EB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_actual_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'balance')"
              :width="columnWidths['balance']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.last_budget_IE && 'IB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_I_balance) }}
                </span>
                <span v-if="scope.row.last_budget_IE && 'EB'.includes(scope.row.last_budget_IE)">
                  {{ amountFormat(scope.row.last_E_balance) }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column :label="$t(langKey + 'thisYear')" align="center">
            <el-table-column
              :label="$t(langKey + 'budget_IE')"
              :width="columnWidths['budget_IE']"
              align="center"
              prop="this_budget_IE"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span
                  v-if="scope.row.this_budget_IE && 'IB'.includes(scope.row.this_budget_IE)"
                >I</span>
                <span
                  v-if="scope.row.this_budget_IE && 'EB'.includes(scope.row.this_budget_IE)"
                >E</span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'proposed_amount')"
              :width="columnWidths['proposed_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.this_budget_IE && 'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_total_proposed_amount) }}
                </span>
                <span v-if="scope.row.this_budget_IE && 'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_total_proposed_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'approved_amount')"
              :width="columnWidths['approved_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.this_budget_IE && 'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_total_approved_amount) }}
                </span>
                <span v-if="scope.row.this_budget_IE && 'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_total_approved_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'actual_amount')"
              :width="columnWidths['actual_amount']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.this_budget_IE && 'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_actual_amount) }}
                </span>
                <span v-if="scope.row.this_budget_IE && 'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_actual_amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t(langKey + 'balance')"
              :width="columnWidths['balance']"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span v-if="scope.row.this_budget_IE && 'IB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_I_balance) }}
                </span>
                <span v-if="scope.row.this_budget_IE && 'EB'.includes(scope.row.this_budget_IE)">
                  {{ amountFormat(scope.row.this_E_balance) }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getBudgetYears } from '@/api/master/years'
import { getStocks } from '@/api/stock/itemSetup'
import loadPreferences from '@/views/mixins/loadPreferences'

import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { fetchBudgetGroupList, fetchBudgetSummary, exportBudgetLedgers } from '@/api/budget'
import { searchStaffs } from '@/api/assistance/staff'
import handlePDF from './handlePDF'
import { budgetSummaryExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'

export default {
  name: 'BudgetListIndex',
  mixins: [loadPreferences, handlePDF],
  data() {
    return {
      loading: false,
      years: '',
      tableData: [],
      stockList: [],
      preferences: {
        filters: {
          selectedYear: '',

          selectedGroupId: '',
          selectedStage: '',
          selectedManager: '',
          selectedApprove: '',

          lastYear: true, // 上年度
        },
      },
      childPreferences: ['selectedCode'],
      langKey: 'budget.budgetList.label.',
      budgetGroupList: [],
      staffList: [],
      summary: {
        this_budget_IE: '',
        this_I_total_proposed_amount: 0,
        this_I_total_approved_amount: 0,
        this_I_actual_amount: 0,
        this_E_total_proposed_amount: 0,
        this_E_total_approved_amount: 0,
        this_E_actual_amount: 0,
        last_budget_IE: '',
        last_I_total_budget_amount: 0,
        last_I_actual_amount: 0,
        last_E_total_budget_amount: 0,
        last_E_actual_amount: 0,
      },
      dataTableScrollEle: '',
      summaryTableScrollEle: '',

      columnWidths: {
        budget_code: 100,
        name_: 200,
        manager_staff_name_: 100,
        budget_stage: 80,
        budget_IE: 60,
        budget_amount: 110,
        proposed_amount: 110,
        approved_amount: 110,
        actual_amount: 110,
        balance: 110,
      },
      statusColor: {
        X: '#808080',
        S: '#FF0000',
        A: '#008000',
        O: '#0000FF',
        C: '#000000',
        R: '#808000',
        T: '#800000',
        K: '#004000',
        B: '#000080',
        D: '#000000',
      },
      selectData: [],
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school']),
    stageOptions() {
      let s = ['X', 'S', 'A', 'O', 'C']
      if (this.school.edbg_second_approve === 'Y') {
        s = ['X', 'S', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D']
      }
      return s.map(key => {
        return {
          label: this.$t('budget.budgetSet.label.stage_' + key.toLowerCase()),
          value: key,
        }
      })
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    const l = this.$refs['dataTable'].$el.querySelector('.el-table__body-wrapper')
    const r = this.$refs['summaryTable'].$el.querySelector('.el-table__body-wrapper')
    this.dataTableScrollEle = l
    this.summaryTableScrollEle = r
    addEventListener('scroll', this.scrollEvent, true)
  },
  beforeDestroy() {
    removeEventListener('scroll', this.scrollEvent, true)
  },
  methods: {
    scrollEvent() {
      this.dataTableScrollEle.scrollLeft = this.summaryTableScrollEle.scrollLeft
    },
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      // if (!row.date) {
      //   return 'count-row'
      // }
    },
    /**
     * 類型格式轉換
     */
    typeFormat(row, column) {
      console.log(row.type)
      switch (row.type) {
        case 'S':
          return this.$t('stock.label.sales')
        case 'D':
          return this.$t('stock.label.damagedOrLost')
        case 'U':
          return this.$t('stock.label.internalUse')
        case 'W':
          return this.$t('stock.label.writeOff')
        case 'P':
          return this.$t('stock.label.purchase')
        default:
          return ''
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.tableData.length - 1) {
        if (columnIndex < 2 || columnIndex === 3) {
          return { rowspan: 0, colspan: 0 }
        } else if (columnIndex === 2) {
          return { rowspan: 1, colspan: 4 }
        }
      }
      return { rowspan: 1, colspan: 1 }
    },
    fetchData() {
      this.loading = false
      getBudgetYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length) {
            const month = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
            if (month) {
              return
            }
            this.preferences.filters.selectedYear = this.years[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => {
          // 類別
          const fy_code = this.preferences.filters.selectedYear
          const budget_types = 'F'
          return fetchBudgetGroupList({ fy_code, budget_types })
        })
        .then(res => {
          this.budgetGroupList = res
          if (this.preferences.filters.selectedGroupId && res.length > 0) {
            const i = res.findIndex(b => b.budget_id === this.preferences.filters.selectedGroupId)
            if (i === -1) {
              this.preferences.filters.selectedGroupId = ''
            }
          }

          // 職員
          const fy_code = this.preferences.filters.selectedYear
          return searchStaffs({ fy_code })
        })
        .then(res => {
          this.staffList = res
        })
        .then(() => this.reloadData())
        .finally(() => {
          this.loading = false
        })
    },
    // fetchStockList(isNeedUpdateChildPreference) {
    //   this.stockList = []
    //   const fy_code = this.preferences.filters.selectedYear
    //   if (!fy_code) {
    //     return Promise.reject()
    //   }
    //   return new Promise((resolve, reject) => {
    //     getStocks(fy_code)
    //       .then(res => {
    //         this.stockList = res
    //       })
    //       .then(() => {
    //         if (isNeedUpdateChildPreference) {
    //           this.updateChildPreference()
    //         }
    //       })
    //       .then(() => {
    //         if (!this.stockList.length) {
    //           return Promise.reject()
    //         }
    //         const stock = this.stockList.find(i => i.sk_code === this.preferences.filters.selectedCode)
    //         if (!stock) {
    //           this.preferences.filters.selectedCode = this.stockList[0].sk_code
    //         }
    //       })
    //       .then(this.reloadData)
    //       .then(() => {
    //         resolve()
    //       })
    //       .catch(err => {
    //         reject(err)
    //       })
    //   })
    // },
    reloadData() {
      const fy_code = this.preferences.filters.selectedYear
      const parent_budget_id = this.preferences.filters.selectedGroupId || undefined
      const budget_stage = this.preferences.filters.selectedStage || undefined
      const manager_staff_id = this.preferences.filters.selectedManager || undefined
      const approve_staff_id = this.preferences.filters.selectedApprove || undefined

      this.loading = true
      return new Promise((resolve, reject) => {
        fetchBudgetSummary({
          fy_code,
          parent_budget_id,
          budget_stage,
          manager_staff_id,
          approve_staff_id,
        })
          .then(res => {
            this.tableData = this.formatData(res)
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatDate(row, column, cellValue, index) {
      const d = dateUtil.format(new Date(cellValue), this.styles.dateFormat)
      return d
    },
    formatData(data) {
      const newData = []
      const sum = {
        this_budget_IE: '',
        this_I_total_proposed_amount: 0,
        this_I_total_approved_amount: 0,
        this_I_actual_amount: 0,
        this_I_balance: 0,
        this_E_total_proposed_amount: 0,
        this_E_total_approved_amount: 0,
        this_E_actual_amount: 0,
        this_E_balance: 0,
        last_budget_IE: '',
        last_I_total_budget_amount: 0,
        last_I_actual_amount: 0,
        last_I_balance: 0,
        last_E_total_budget_amount: 0,
        last_E_actual_amount: 0,
        last_E_balance: 0,
      }
      let thisI = false
      let lastI = false
      let thisE = false
      let lastE = false
      const cols = [
        'this_I_total_proposed_amount',
        'this_I_total_approved_amount',
        'this_I_actual_amount',
        'this_E_total_proposed_amount',
        'this_E_total_approved_amount',
        'this_E_actual_amount',
        'last_I_total_budget_amount',
        'last_I_actual_amount',
        'last_E_total_budget_amount',
        'last_E_actual_amount',
      ]
      const balanceCols = {
        this_I_balance: ['this_I_total_approved_amount', 'this_I_actual_amount'],
        this_E_balance: ['this_E_total_approved_amount', 'this_E_actual_amount'],
        last_I_balance: ['last_I_total_budget_amount', 'last_I_actual_amount'],
        last_E_balance: ['last_E_total_budget_amount', 'last_E_actual_amount'],
      }
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        const newItem = Object.assign({}, item)
        newData.push(newItem)
        cols.forEach(key => {
          sum[key] = this.add(sum[key], item[key])
        })
        for (const key in balanceCols) {
          const l = balanceCols[key][0]
          const r = balanceCols[key][1]
          const balance = toDecimal(newItem[l] - newItem[r])
          newItem[key] = balance
          sum[key] = toDecimal(sum[key] + balance)
        }
        if (!thisI && 'IB'.includes(newItem.this_budget_IE)) {
          thisI = true
        }
        if (!thisE && 'EB'.includes(newItem.this_budget_IE)) {
          thisE = true
        }
        if (!lastI && 'IB'.includes(newItem.last_budget_IE)) {
          lastI = true
        }
        if (!lastE && 'EB'.includes(newItem.last_budget_IE)) {
          lastE = true
        }
      }
      if (thisI && thisE) {
        sum.this_budget_IE = 'B'
      } else if (thisI) {
        sum.this_budget_IE = 'I'
      } else if (thisE) {
        sum.this_budget_IE = 'E'
      } else {
        sum.this_budget_IE = ''
      }
      if (lastI && lastE) {
        sum.last_budget_IE = 'B'
      } else if (lastI) {
        sum.last_budget_IE = 'I'
      } else if (thisE) {
        sum.last_budget_IE = 'E'
      } else {
        sum.last_budget_IE = ''
      }

      this.summary = sum
      return newData
    },
    add(a, b) {
      const a1 = Number(a)
      const a2 = isNaN(a1) ? 0 : a1
      const b1 = Number(b)
      const b2 = isNaN(b1) ? 0 : b1
      return toDecimal(a2 + b2)
    },
    onChangeYear(val) {
      const year = this.years.find(i => i.fy_code === val)
      if (year) {
        const budget_types = 'F'
        fetchBudgetGroupList({ fy_code: year.fy_code, budget_types })
          .then(res => {
            this.budgetGroupList = res
            const group = res.find(i => i.budget_id === this.preferences.filters.selectedGroupId)
            if (!group) {
              this.preferences.filters.selectedGroupId = ''
            }
          })
          .then(this.reloadData)
          .catch(() => {})
      }
    },
    languageKey(key) {
      return key + (this.language === 'en' ? 'en' : 'cn')
    },
    formatterByStage(row, column, cellValue, index) {
      const stage = this.stageOptions.find(i => i.value === cellValue)
      return stage ? stage.label : ''
    },
    amountFormat: amountFormat,
    summarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 4,
        }
      } else if (columnIndex < 4) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    onExport(type) {
      if (this.loading) {
        return
      }
      const filters = this.preferences.filters
      let parent_budget_id, budget_stage, manager_staff_id, approve_staff_id
      const fy_code = filters.selectedYear
      const show_last_year = filters.lastYear ? 1 : 0
      if (type === 'ALL') {
        return exportBudgetLedgers({
          fy_code: this.preferences.filters.selectedYear,
          budget_id: this.preferences.filters.selectedGroupId,
          parent_budget_id: this.preferences.filters.selectedGroupId,
          budget_stage: this.preferences.filters.selectedStage,
          manager_staff_id: this.preferences.filters.selectedManager,
          approve_staff_id: this.preferences.filters.selectedApprove,
          budget_ids: this.selectData.map(i => i.budget_id).join(','),
        })
          .then(res => {
            exportExcel(res)
          })
          .catch(() => {
            this.$message.error(this.$t('file.exportError'))
          })
          .finally(() => {
            this.loading = false
          })
      }
      if (type === 'PAGE') {
        parent_budget_id = filters.selectedGroupId
        budget_stage = filters.selectedStage
        manager_staff_id = filters.selectedManager
        approve_staff_id = filters.selectedApprove
      }

      this.loading = true
      budgetSummaryExport({
        fy_code,
        parent_budget_id,
        budget_stage,
        manager_staff_id,
        approve_staff_id,
        show_last_year,
      })
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'budget_stage') {
        return {
          backgroundColor: this.statusColor[row.budget_stage],
          color: '#fff',
        }
      }
    },
    handleSelectionChange(val) {
      this.selectData = val
    },
  },
}
</script>

<style lang="scss" scoped>
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.app-container {
  height: 100%;

  header {
    margin: 0 20px 20px 0;
  }

  .filter {
    /*width: 900px;*/
    margin: 5px 0;
    display: flex;
    text-align: left;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }

    input {
      line-height: 30px;
      height: 30px;
    }

    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
  }

  .budget-table {
    height: calc(100vh - 250px);

    /deep/ {
      .el-table {
        height: 100%;
        .el-table__body-wrapper {
          overflow-x: hidden;
          overflow-y: auto;
        }

        table {
          tbody {
            .cell {
              span {
                display: block;
              }

              .el-input-number--medium {
                width: 100%;
              }

              .el-input {
                border-radius: 0;
              }
            }

            .count-row {
              td {
                background-color: #e2e2e2;
                color: #404246;

                &:first-child {
                  text-align: right;
                }
              }

              .cell {
                @extend .count-row;
              }
            }
          }
        }
      }
      .el-checkbox {
        margin: 5px 0 0 0 !important;
      }
    }
  }

  .budget-summary-table {
    /deep/ {
      .el-table {
        height: 100%;

        .el-table__body-wrapper {
          overflow: scroll;
          height: inherit !important;
        }

        table {
          tbody {
            .cell {
              span {
                display: block;
              }

              .el-input-number--medium {
                width: 100%;
              }

              .el-input {
                border-radius: 0;
              }
            }
          }
        }
      }
    }
  }
}

.actions-icon {
  /*float: right;*/
  padding: 0 1px;
  display: inline-block;
  .edac-icon {
    font-size: 18px;
    line-height: 25px;
    vertical-align: middle;
  }
}
</style>
<style lang="scss">
.status-select {
  .el-scrollbar {
    .el-scrollbar__wrap {
      .el-select-dropdown__list {
        .el-select-dropdown__item {
          padding: 0 !important;
        }
      }
    }
  }
}
</style>
