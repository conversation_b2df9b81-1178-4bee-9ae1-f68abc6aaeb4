<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 發票編號 -->
      <el-form-item :rules="rules" :label="$t('stock.purchase.label.pi_no')" prop="pi_no">
        <el-input v-model="form.pi_no" class="piNo" clearable />
      </el-form-item>
      <!-- 日期 -->
      <el-form-item :rules="rules" :label="$t('stock.purchase.label.date')" prop="date">
        <el-date-picker
          v-model="form.date"
          :format="styles.dateFormat"
          :picker-options="pickerOptions"
          :placeholder="$t('placeholder.selectDate')"
          value-format="yyyy-MM-dd"
          type="date"
          @change="getFyCode()"
        />
      </el-form-item>
      <!-- 供應商 -->
      <el-form-item :label="$t('stock.purchase.label.pi_payee')" prop="pi_payee">
        <el-select v-model="form.pi_payee" clearable>
          <el-option
            v-for="item in companies"
            :key="item.value"
            :label="item.comp_name"
            :value="item.comp_code"
          />
        </el-select>
      </el-form-item>
      <!-- 支票編號  -->
      <el-form-item :label="$t('stock.purchase.label.chq_no')" prop="chq_no">
        <el-input v-model="form.chq_no" clearable />
      </el-form-item>
      <!-- 傳票編號  -->
      <el-form-item :label="$t('stock.purchase.label.voucher_no')" prop="voucher_no">
        <el-input v-model="form.voucher_no" clearable />
      </el-form-item>
      <!-- 金額 -->
      <el-form-item :label="$t('stock.purchase.label.amount')" prop="amount" class="amount">
        <!-- <el-input v-model="form.amount" disabled/> -->
        <template>
          <ENumeric
            ref="amount"
            v-model="form.amount"
            :controls="false"
            :precision="2"
            :disabled="false"
            :read-only="true"
          />
        </template>
      </el-form-item>
      <!-- 貨品明細 -->
      <el-form-item :label="$t('stock.purchase.label.items')">
        <el-table :data="stocks" class="purchase-table" border style="width: 100%">
          <el-table-column
            :label="$t('stock.purchase.label.sk_code')"
            prop="sk_code"
            align="center"
            width="120"
          />
          <el-table-column
            :label="$t('stock.purchase.label.items')"
            :prop="language === 'en' ? 'sk_name_en' : 'sk_name_cn'"
            align="left"
            header-align="center"
            min-width="100"
          />
          <el-table-column
            :label="$t('stock.purchase.label.count')"
            align="center"
            class-name="count"
            width="80"
            prop="count"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                v-model="scope.row.count"
                :controls="false"
                :disabled="false"
                :precision="0"
                :min="0"
                @change.native="getRowAmount(scope.row, scope.$index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.purchase.label.price')"
            class-name="price"
            align="center"
            width="100"
            prop="purchase_price"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                v-model="scope.row.purchase_price"
                :controls="false"
                :precision="2"
                :min="0"
                :disabled="false"
                :class="[
                  {
                    noEqual: !priceEqual(scope.row),
                  },
                ]"
                @change.native="getRowAmount(scope.row, scope.$index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.purchase.label.amount')"
            prop="rowAmount"
            align="right"
            width="100"
            header-align="center"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                :ref="'rowAmount' + scope.$index"
                v-model="scope.row.rowAmount"
                :controls="false"
                :precision="2"
                :min="0"
                :disabled="false"
                :read-only="true"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// API
import { getPurchase, updatePurchase, createPurchase } from '@/api/stock/purchase'
// import { getStocksTreeNode } from '@/api/stock/itemSetup'
import { getCompanies } from '@/api/assistance/payeePayer'
import { getStocks } from '@/api/stock/itemSetup'
// import { fetchYears } from '@/api/master/years'
import { reject } from 'q'
import { searchByDate, searchTheDate } from '@/api/master/years'

import ENumeric from '@/components/ENumeric'

import { toDecimal } from '@/utils'

export default {
  name: 'StockPurchaseAdd',
  components: {
    ENumeric,
  },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
  },
  data() {
    const that = this
    return {
      form: {
        purchase_invoice_id: '',
        fy_code: '',
        pi_no: '',
        date: '',
        pi_payee: '',
        chq_no: '',
        voucher_no: '',
        stocks_json: '',
        amount: '',
      },
      defaultForm: {
        purchase_invoice_id: '',
        fy_code: '',
        pi_no: '',
        date: '',
        pi_payee: '',
        chq_no: '',
        voucher_no: '',
        stocks_json: '',
        amount: '',
      },
      rules: [
        {
          // 必填
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      companies: [],
      stocks: [],
      mid: [],
      unmodifiedStocks: [],
      loading: true,
      first_day: '',
      last_day: '',

      pickerOptions: {
        disabledDate(time) {
          if (that.fyCode) {
            if (that.first_day && that.last_day) {
              return (
                time.getTime() > that.last_day.getTime() ||
                time.getTime() < that.first_day.getTime()
              )
            }
            return true
          } else {
            return time.getTime() >= that.first_day.getTime()
          }
        },
        // shortcuts: [{
        //   text: '今天',
        //   onClick(picker) {
        //     picker.$emit('pick', new Date())
        //   }
        // }, {
        //   text: '昨天',
        //   onClick(picker) {
        //     const date = new Date()
        //     date.setTime(date.getTime() - 3600 * 1000 * 24)
        //     picker.$emit('pick', date)
        //   }
        // }]
      },
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    isNew() {
      return !this.editObject
    },
  },
  watch: {
    editObject() {
      this.initData()
    },
    fyCode: function(newFyCode) {
      this.form.fy_code = newFyCode
      this.loadDateRange().then(() => {
        this.initForm()
        const currentDate = new Date(this.form.date + ' 00:00')
        if (!this.inDateRange(currentDate)) {
          this.form.date = ''
        }
      })
    },
  },
  created() {
    this.initData()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    inDateRange(date) {
      if (this.first_day && this.last_day) {
        return (
          date.getTime() <= this.last_day.getTime() && date.getTime() >= this.first_day.getTime()
        )
      }
      return false
    },
    getRowAmount(row, index) {
      new Promise((resolve, reject) => {
        // 計算金額
        row.rowAmount = toDecimal(row.count * row.purchase_price)
        this.$refs['rowAmount' + index].currentVal = row.rowAmount
        this.form.amount = this.stocks
          .map(row => row.rowAmount)
          .reduce((acc, cur) => toDecimal(cur + acc), 0) // 計算總額
        this.$refs['amount'].currentVal = this.form.amount

        //

        this.forceUpdate()
        resolve()
      }).catch(err => {
        reject(err)
      })
    },
    getFyCode() {
      new Promise(() => {
        if (this.form.date !== null) {
          const date = this.form.date
          if (this.fyCode !== '') {
            searchByDate(date).then(res => {
              console.log('searchByDate', res)
              if (res.fy_code === this.fyCode) {
                this.form.fy_code = res.fy_code
              } else {
                this.$message.error(this.$t('message.dateError'))
                this.form.date = null
              }
            })
          } else {
            this.form.fy_code = ''
          }
        }
      })
    },
    checkRequired(rule, value, callback) {},
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getPurchase(this.editObject.purchase_invoice_id)
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.mid = res.stocks
              this.stocks = []
            })
            .then(() => getStocks(this.form.fy_code))
            .then(res => {
              // res.forEach(itemO => {
              // this.mid.forEach(ele2 => {
              //   if (ele1.sk_code === ele2.sk_code) {
              //     ele1.purchase_price = ele2.price
              //     ele1.count = ele2.qty
              //   } else if (ele1.count === undefined) {
              //     ele1.count = 0
              //   } else if (ele1.purchase_price === undefined) {
              //     ele1.purchase_price = 0
              //   }
              //   ele1.rowAmount = ele1.purchase_price * ele1.count
              // })
              // })
              // this.stocks = res
              const newStocks = []
              const unmodifiedStocks = []
              res.forEach(itemO => {
                // 未修改的價格
                unmodifiedStocks[itemO.sk_code] = Number(itemO.purchase_price)

                const newItem = Object.assign({}, itemO)
                const item = this.mid.find(i => i.sk_code === itemO.sk_code)
                if (item) {
                  newItem.purchase_price = item.price == null ? 0 : Number(item.price)
                  newItem.count = item.qty == null ? 0 : Number(item.qty)
                } else {
                  newItem.count = 0
                }

                newItem.count = Number(newItem.count)
                newItem.purchase_price = Number(newItem.purchase_price)
                newItem.sales_price = Number(newItem.sales_price)
                newItem.rowAmount = toDecimal(newItem.purchase_price * newItem.count)
                newStocks.push(newItem)
              })
              this.stocks = newStocks
              // 沒修改過的
              // this.unmodifiedStocks = res.map(i => {
              //   const newItem = Object.assign({}, i)
              //   newItem.purchase_price = Number(newItem.purchase_price)
              //   newItem.sales_price = Number(newItem.sales_price)
              //   return newItem
              // })
              this.unmodifiedStocks = unmodifiedStocks
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.stocks = []
          const unmodifiedStocks = {}
          console.log('fyCode', this.fyCode)
          getStocks(this.fyCode).then(res => {
            res.forEach(ele => {
              unmodifiedStocks[ele.sk_code] = Number(ele.purchase_price)

              ele.count = Number(ele.count)
              ele.purchase_price = Number(ele.purchase_price)
              ele.count = 0
              ele.rowAmount = toDecimal(ele.count * ele.purchase_price)
            })
            this.stocks = res
            this.unmodifiedStocks = unmodifiedStocks
          })
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.loadDateRange()
        .then(this.initForm)
        .then(() => getCompanies(undefined, 'Y'))
        .then(res => {
          this.companies = res
          if (res.length) {
            const comp = res.find(i => i.comp_code === this.form.pi_payee)
            if (comp) {
              return
            }
          }
          this.form.pi_payee = ''
        })
        .finally(() => {
          this.$refs['amount'].currentVal = this.form.amount
          this.loading = false
        })
    },
    loadDateRange() {
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: this.fyCode })
          .then(res => {
            this.first_day = new Date(res.the_first_day + ' 00:00')
            this.last_day = new Date(res.the_last_day + ' 00:00')
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        if (!this.stocks.length || this.stocks.length === 0) {
          this.$message.error(this.$t('message.theGoodsDoNotExist'))
          return
        }
        const purchase_invoice_id = this.form.purchase_invoice_id
        const fy_code = this.form.fy_code
        const pi_no = this.form.pi_no
        const date = this.form.date
        const pi_payee = this.form.pi_payee
        const chq_no = this.form.chq_no
        const voucher_no = this.form.voucher_no
        const stocks_json = []
        this.stocks.forEach(ele => {
          stocks_json.push({ sk_code: ele.sk_code, qty: ele.count, price: ele.purchase_price })
        })
        const stocks_json_string = JSON.stringify(stocks_json)
        if (this.editObject) {
          // 編輯
          updatePurchase({
            purchase_invoice_id: purchase_invoice_id,
            fy_code: fy_code,
            pi_no: pi_no,
            date: date,
            pi_payee: pi_payee,
            chq_no: chq_no,
            voucher_no: voucher_no,
            stocks_json: stocks_json_string,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        } else {
          // 新增
          createPurchase({
            fy_code: fy_code,
            pi_no: pi_no,
            date: date,
            pi_payee: pi_payee,
            chq_no: chq_no,
            voucher_no: voucher_no,
            stocks_json: stocks_json_string,
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    priceEqual(row) {
      const code = row.sk_code
      console.log('priceEqual', this.unmodifiedStocks[code], row.purchase_price)
      if (this.unmodifiedStocks[code]) {
        return this.unmodifiedStocks[code] === row.purchase_price
      } else {
        return false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.amount .el-form-item__content span {
  color: #606266;
}
.purchase-table {
  /deep/ {
    td.price,
    td.count {
      .cell {
        padding: 0;
      }
    }
    td.count {
      .vue-numberic {
        text-align: center;
      }
    }
    td.price {
      .noEqual {
        color: red;
      }
    }
  }
}
</style>
