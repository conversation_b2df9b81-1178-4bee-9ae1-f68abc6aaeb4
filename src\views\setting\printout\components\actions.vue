<template>
  <el-form-item class="actions">
    <el-button size="mini" type="primary" @click="onSave(0)">
      {{ $t('button.update') }}
    </el-button>
    <el-button size="mini" type="info" @click="onFetch(1)">
      {{ $t('button.reset') }}
    </el-button>
    <el-button v-if="showAdd" size="mini" type="success" @click="onSave(1)">
      {{ $t('button.add') }}
    </el-button>
    <el-button v-if="showAdd && styleList.length > 1" size="mini" type="danger" @click="onDelete">
      {{ $t('button.delete') }}
    </el-button>
    <el-button size="mini" type="warning" @click.native="onCopy">
      {{ $t('button.checkConfiguration') }}
    </el-button>
  </el-form-item>
</template>
<script>
export default {
  name: 'Actions',
  props: {
    styleList: {
      type: Array,
      default: () => [],
    },
    onSave: {
      type: Function,
      default: () => {},
    },
    onFetch: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
    onCopy: {
      type: Function,
      default: () => {},
    },
    showAdd: {
      type: Boolean,
      default: true,
    },
  },
}
</script>

<style scoped></style>
