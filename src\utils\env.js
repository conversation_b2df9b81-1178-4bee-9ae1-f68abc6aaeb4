/**
 * 環境變量工具函數
 */

/**
 * 獲取必需的環境變量，如果不存在則拋出錯誤
 * 注意：這個函數主要用於構建時檢查，編譯後 process.env[key] 會被替換為實際值
 * @param {string} key - 環境變量名稱
 * @returns {string} - 環境變量值
 * @throws {Error} - 當環境變量不存在時拋出錯誤
 */
function getRequiredEnv(key) {
  const value = process.env[key]
  if (value === undefined || value === null || value === '') {
    throw new Error(`Missing required environment variable: ${key}`)
  }
  return value
}

/**
 * 獲取可選的環境變量，如果不存在則返回默認值
 * @param {string} key - 環境變量名稱
 * @param {string} defaultValue - 默認值
 * @returns {string} - 環境變量值或默認值
 */
function getOptionalEnv(key, defaultValue = '') {
  return process.env[key] || defaultValue
}

/**
 * 檢查多個必需的環境變量
 * @param {string[]} keys - 環境變量名稱數組
 * @throws {Error} - 當任何環境變量不存在時拋出錯誤
 */
function checkRequiredEnvs(keys) {
  const missing = keys.filter(key => !process.env[key])
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

/**
 * 獲取所有 VUE_APP_ 開頭的環境變量
 * @returns {Object} - 包含所有 VUE_APP_ 環境變量的對象
 */
function getVueAppEnvs() {
  const vueAppEnvs = {}
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('VUE_APP_')) {
      vueAppEnvs[key] = process.env[key]
    }
  })
  return vueAppEnvs
}

module.exports = {
  getRequiredEnv,
  getOptionalEnv,
  checkRequiredEnvs,
  getVueAppEnvs,
}
