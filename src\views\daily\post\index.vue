<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div>
      <header v-if="false">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('router.settingScreenBasicSetting') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t($route.meta.title) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </header>
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 類別 -->
          <el-form-item :label="t('vt_category')">
            <el-select
              v-model="preferences.filters.vt_category"
              class="year"
              style="width: 180px"
              @change="initData"
            >
              <el-option
                v-for="item in vt_category_list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- 傳票類別 -->
          <el-form-item :label="t('vt_description')">
            <el-select
              v-model="preferences.filters.vc_vtcode"
              class="year"
              style="width: 180px"
              @change="initData"
            >
              <el-option
                v-for="item in postedVoucherTypeList"
                :key="item.vc_vtcode"
                :label="item[isEnglish ? 'vt_description_en' : 'vt_description_cn']"
                :value="item.vc_vtcode"
              />
            </el-select>
          </el-form-item>
          <!-- 時段 -->
          <el-form-item :label="t('vc_date')">
            <el-date-picker
              v-model="preferences.filters.vc_date"
              :placeholder="$t('placeholder.selectDate')"
              :format="styles.dateFormat"
              :value-format="dateValueFormat"
              type="date"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" class="action-button" @click="initData">
              {{ $t('button.fetch') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
        </div>
        <div style="position: absolute; right: 0">
          <el-button type="primary" size="mini" class="action-button" @click="openPostData">
            {{ $t('button.loadData') }}
          </el-button>
        </div>
      </div>
      <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTablick">
        <el-tab-pane
          v-for="(mop, mindex) in method_of_payment"
          :key="mop"
          :label="t('method_of_payment.' + mop)"
          :name="mop"
          :style="`height: ${pageHeight - filtersHeight - summaryHeight - 124}px`"
        >
          <b-table
            ref="table"
            v-loading="!loading && tableLoading"
            :data="tableData[mop]"
            :style-columns="styleColumns"
            :amount-columns="amountColumns"
            :lang-key="langKey"
            :show-index="false"
            :actions-min-width="80"
            :show-checkbox="true"
            :default-top="230"
            :filter-class-function="filterClassFunction"
            :checkbox-config="{ checkMethod: checkBoxStatus }"
            height="100%"
            class="b-table"
            border
            @changeWidth="changeColumnWidth"
            @checkbox-change="onSelectionChange"
            @checkbox-all="onSelectionChange"
          >
            <template slot="columns">
              <vxe-table-column
                v-for="item in columns"
                :key="item.ss_key"
                :title="$t(langKey + item.ss_key)"
                :align="item.alignment"
                :class-name="item.ss_key + ' mini-form'"
                :property="$refs.table[mindex].column_property(item)"
                :field="$refs.table[mindex].column_property(item)"
                :width="item.width"
                :column-key="item.ss_key"
                :params="{ key: item.ss_key }"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span v-if="item.ss_key === 'Ref'">
                    <span v-if="scope.row.Ref === 'cash'">CASH</span>
                    <span v-else-if="scope.row.Ref === 'transf'">TRANSF</span>
                    <span v-else>{{ scope.row.Ref }}</span>
                  </span>
                  <span v-else>{{
                    $refs.table[mindex].customFormatter(
                      item.ss_key,
                      scope.row[$refs.table[mindex].column_property(item)],
                      customDateFormat
                    )
                  }}</span>
                </template>
              </vxe-table-column>
            </template>
            <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
              <div class="operation_icon">
                <i
                  :title="$t('btnTitle.edit')"
                  class="el-icon-edit action-icon"
                  @click="onEditPost(scope.row)"
                />
              </div>
            </template>
          </b-table>
        </el-tab-pane>
      </el-tabs>

      <div ref="summary" class="summary">
        <span>
          <el-button
            :disabled="currentSelectVoucherList.length === 0 || hasOtherVoucher"
            type="primary"
            size="mini"
            class="action-button"
            @click="onSave"
          >{{ $t('button.generateVoucher') }}</el-button>
          <el-button
            :disabled="currentSelectVoucherList.length === 0"
            type="primary"
            size="mini"
            class="action-button"
            @click="onMultiEdit"
          >{{ t('multipleEdit') }}</el-button>
        </span>
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
      <!-- 獲取數據 -->
      <el-dialog
        v-if="showPostDialog"
        :visible.sync="showPostDialog"
        :title="t('load_data')"
        :close-on-click-modal="false"
        class="postDialog"
        width="400px"
      >
        <el-form ref="form" :model="form" label-width="100px">
          <!-- 類別 -->
          <el-form-item :rules="requiredRules" :label="t('vt_category')" prop="vt_category">
            <el-select v-model="form.vt_category" class="year" style="width: 180px">
              <el-option
                v-for="item in vt_category_list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :rules="form.vt_category === 'R' ? requiredRules : []"
            :label="t('date')"
            prop="date"
          >
            <el-date-picker
              v-model="form.date"
              :placeholder="$t('placeholder.selectDate')"
              :format="styles.dateFormat"
              :value-format="dateValueFormat"
              style="width: 180px"
              type="date"
            />
          </el-form-item>
          <el-form-item :rules="requiredRules" :label="t('voucher_date')" prop="voucher_date">
            <el-date-picker
              v-model="form.voucher_date"
              :placeholder="$t('placeholder.selectDate')"
              :format="styles.dateFormat"
              :value-format="dateValueFormat"
              style="width: 180px"
              type="date"
            />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="getPostData">{{
            $t('button.confirm')
          }}</el-button>
          <el-button size="mini" type="danger" @click="closePostData">{{
            $t('button.cancel')
          }}</el-button>
        </span>
      </el-dialog>
      <!-- 修改傳票 -->
      <el-dialog
        v-if="showDetailDialog"
        :visible.sync="showDetailDialog"
        :close-on-click-modal="false"
        :title="t('detail')"
      >
        <el-table v-loading="dTableLoading" :data="detailData" border="" height="100%">
          <el-table-column :label="t('vc_vtcode')" property="vc_vtcode" width="200">
            <template slot-scope="scope">
              <el-select
                v-if="scope && scope.row"
                v-model="scope.row.vc_vtcode"
                :disabled="scope.row.IE !== 'N'"
                size="mini"
                style="width: 100%"
                filterable
                clearable
                @change="changeVchrType(scope.row)"
              >
                <el-option
                  v-for="item in voucherTypeList"
                  :key="item.voucher_type_id"
                  :label="item[isEnglish ? 'vt_description_en' : 'vt_description_cn']"
                  :value="item.vt_code"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="t('vc_date')" property="vc_date" width="100" />
          <el-table-column :label="t('ac_code')" property="ac_code" width="150">
            <template slot-scope="scope">
              <el-select
                v-if="scope && scope.row"
                v-model="scope.row.ac_code"
                :disabled="scope.row.IE === 'N'"
                size="mini"
                style="width: 100%"
                filterable
                clearable
              >
                <el-option
                  v-for="item in accountList"
                  :key="item.account_id"
                  :label="item[isEnglish ? 'ac_name_en' : 'ac_name_cn']"
                  :value="item.ac_code"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="t('Ref')" property="Ref" width="120">
            <template slot-scope="scope">
              <template v-if="scope && scope.row">
                <span v-if="scope.row.Ref === 'cash'">CASH</span>
                <span v-else-if="scope.row.Ref === 'transf'">TRANSF</span>
                <span v-else>{{ scope.row.Ref }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column :label="t('Descr')" property="Descr" />
          <el-table-column :label="t('Amount_Dr')" align="right" property="Amount_Dr" width="100" />
          <el-table-column :label="t('Amount_Cr')" align="right" property="Amount_Cr" width="100" />
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="saveDetailData">{{
            $t('button.save')
          }}</el-button>
          <el-button size="mini" type="danger" @click="closeDetailDialog">{{
            $t('button.cancel')
          }}</el-button>
        </span>
      </el-dialog>
      <!-- 批量編輯傳票 -->
      <el-dialog
        v-if="showMultiEditDialog"
        :visible.sync="showMultiEditDialog"
        :close-on-click-modal="false"
        :title="t('multipleEdit')"
        width="400px"
        class="post-multi-edit-dialog"
      >
        <el-form
          ref="multiEditForm"
          v-loading="multiEditLoading"
          :model="multiEditForm"
          label-width="100px"
        >
          <!-- 傳票類別 -->
          <el-form-item :rules="requiredRules" :label="t('vt_description')" prop="vc_vtcode">
            <el-select
              v-model="multiEditForm.vc_vtcode"
              class="year"
              style="width: 250px"
              @change="onChangeMultiVoucherType"
            >
              <el-option
                v-for="item in voucherTypeList"
                :key="item.vt_code"
                :label="item[isEnglish ? 'vt_description_en' : 'vt_description_cn']"
                :value="item.vt_code"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button
            :loading="multiEditLoading"
            size="mini"
            type="primary"
            @click="saveMultiData"
          >{{ $t('button.save') }}</el-button>
          <el-button size="mini" type="danger" @click="showMultiEditDialog = false">{{
            $t('button.cancel')
          }}</el-button>
        </span>
      </el-dialog>
      <!-- 生成傳票 -->
      <el-dialog
        v-if="showGroupDialog"
        :visible.sync="showGroupDialog"
        :close-on-click-modal="false"
        :title="t('generateVoucher')"
        class="postDialog"
        width="400px"
      >
        <el-form ref="groupForm" :model="groupForm" label-width="100px">
          <!-- 傳票日期 -->
          <el-form-item :rules="requiredRules" :label="t('voucher_date')" prop="vc_date">
            <el-date-picker
              v-model="groupForm.vc_date"
              :placeholder="$t('placeholder.selectDate')"
              :format="styles.dateFormat"
              :value-format="dateValueFormat"
              style="width: 180px"
              type="date"
            />
          </el-form-item>
          <el-form-item :rules="requiredRules" :label="t('vc_summary')" prop="vc_summary">
            <el-input v-model="groupForm.vc_summary" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="onSaveGroup">{{
            $t('button.confirm')
          }}</el-button>
          <el-button size="mini" type="danger" @click="showGroupDialog = false">{{
            $t('button.cancel')
          }}</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import _ from 'lodash'
import {
  postData,
  getPost,
  fetchPosts,
  savePostDetailData,
  getPostVcTypeList,
  getMultiplePost,
  updateVoucherPostVtCode,
} from '@/api/daily/post'
import { fetchVoucherTypes, fetchNoList } from '@/api/master/voucherType'
import { createVoucher } from '@/api/daily/payment'
import { searchAccounts, searchAccountByCode } from '@/api/master/account'
import { editUserPreference } from '@/api/settings/user/preference'
import BTable from '@/components/BTable'
import DateRange from '@/components/DateRange/index'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import { listenTo } from '@/utils/resizeListen'

export default {
  name: 'DailyPostIndex',
  components: {
    BTable,
    DateRange,
    customStyle,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission],
  data() {
    return {
      loading: true,
      tableLoading: false,
      dLoading: true,
      dTableLoading: false,
      activeTab: 'CASH',
      funds: [],
      tableData: {
        CASH: [],
        CHECK: [],
        AUTOPAY: [],
        FPS: [],
        BANKIN: [],
        OTHER: [],
      },
      detailData: [],
      data: [],
      langKey: 'daily.post.label.',
      tableColumns: [
        'vc_date',
        'vt_description',
        'tra_date',
        'ac_code',
        'ac_name',
        'Ref',
        'Descr',
        'amount',
      ],
      amountColumns: ['amount'],
      preferences: {
        filters: {
          vt_category: '',
          vt_code: '',
          vc_date: '',
          vc_vtcode: '',
          date: '',
          voucher_date: '',
        },
      },
      form: {
        vt_category: '',
        date: '',
        voucher_date: '',
      },
      groupForm: {
        vc_date: '',
        vc_summary: '',
      },
      showPostDialog: false,
      showDetailDialog: false,
      showGroupDialog: false,
      selectedVoucherList: {
        CASH: [],
        CHECK: [],
        AUTOPAY: [],
        FPS: [],
        BANKIN: [],
        OTHER: [],
      },
      postedVoucherTypeList: [],
      voucherTypeList: [],
      accountList: [],
      childPreferences: [],
      customDateFormat: 'dd/MM/yy',
      dateValueFormat: 'yyyy-MM-dd',

      method_of_payment: ['CASH', 'CHECK', 'AUTOPAY', 'FPS', 'BANKIN', 'OTHER'],

      filtersResizeListen: {},
      filtersHeight: 40,
      summaryResizeListen: {},
      summaryHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,

      showMultiEditDialog: false,
      multiEditLoading: false,
      multiEditForm: {
        vt_category: '',
        post_row_ids: '',
        vc_vtcode: undefined,
        ac_code: '',
      },
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'user_id']),
    vt_category_list() {
      return ['P', 'R', 'C'].map(i => {
        return {
          label: this.$t('voucher_type_category.' + i),
          value: i,
        }
      })
    },
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    currentSelectVoucherList() {
      return this.selectedVoucherList[this.activeTab]
    },
    hasOtherVoucher() {
      return this.selectedVoucherList[this.activeTab].some(i => !i.account_id)
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
      this.summaryResizeListen = listenTo(this.$refs.summary, ({ width, height, ele }) => {
        this.summaryHeight = height
      })
    })
  },
  methods: {
    onSetting() {
      this.showDialog = true
    },
    checkBoxStatus({ row }) {
      // return row.account_id
      return true
    },
    async fetchData() {
      this.loading = true
      await this.loadUserPreference()
      if (this.preferences.filters.vt_category === '') {
        this.preferences.filters.vt_category = 'R'
      }
      await this.initData()
    },
    async initData() {
      this.loading = true
      const vt_category = this.preferences.filters.vt_category
      this.postedVoucherTypeList = await getPostVcTypeList({ vt_category })
      this.postedVoucherTypeList.forEach(item => {
        if (!item.vt_code) {
          item.vt_code = 'NULL'
          item.vt_description_cn = this.t('other') + `(${item.vc_vtcode})`
          item.vt_description_en = this.t('other') + `(${item.vc_vtcode})`
        }
      })
      const selected_vc_vtcode = this.postedVoucherTypeList.find(
        i => i.vc_vtcode === this.preferences.filters.vc_vtcode,
      )
      if (!selected_vc_vtcode) {
        this.preferences.filters.vc_vtcode = undefined
      }
      if (this.postedVoucherTypeList.length > 0) {
        if (!this.preferences.filters.vc_vtcode) {
          this.preferences.filters.vc_vtcode = this.postedVoucherTypeList[0].vc_vtcode
        }
      } else {
        this.preferences.filters.vc_vtcode = ''
      }
      for (const m in this.method_of_payment) {
        const filter_data = {
          vt_category: '',
          vt_code: '',
          vc_vtcode: '',
          method_of_payment: this.method_of_payment[m],
          vc_date: '',
        }

        filter_data.vt_category = this.preferences.filters.vt_category
        filter_data.vc_vtcode = this.preferences.filters.vc_vtcode
        if (this.preferences.filters.vc_vtcode) {
          const vt = this.postedVoucherTypeList.find(i => i.vc_vtcode === filter_data.vc_vtcode)
          filter_data.vt_code = vt.vt_code
        }
        filter_data.vc_date = this.preferences.filters.vc_date
        const res = await fetchPosts(filter_data)
        res.forEach(item => {
          item.amount =
            item[this.preferences.filters.vt_category === 'R' ? 'Amount_Dr' : 'Amount_Cr']
          item.ac_name = item[this.isEnglish ? 'ac_name_en' : 'ac_name_cn']
          item.vt_description = item[this.isEnglish ? 'vt_description_en' : 'vt_description_cn']
        })
        this.tableData[this.method_of_payment[m]] = res
      }
      this.loading = false
    },
    async onSave() {
      if (this.selectedVoucherList[this.activeTab].length === 0) {
        this.$message.error(this.$t('message.pleaseSelected'))
        return false
      }
      let num = 0
      Object.values(this.selectedVoucherList).forEach(item => {
        num = num + item.length
      })
      if (num <= 1) {
        let voucher_type_id
        let vt_code
        const vc_vtcode = this.preferences.filters.vc_vtcode
        if (vc_vtcode) {
          const vt = this.postedVoucherTypeList.find(i => i.vc_vtcode === vc_vtcode)
          if (vt && vt.vt_code && vt.vt_code !== 'NULL') {
            vt_code = vt.vt_code
          }
        }
        // 獲取傳票類型列表
        const voucherTypeList = await fetchVoucherTypes({
          vt_category: this.preferences.filters.vt_category,
          vt_code,
        })
        if (voucherTypeList.length === 0) {
          voucher_type_id = 0
          this.$message.error(this.$t('message.noVoucherType'))
          return false
        } else {
          voucher_type_id = voucherTypeList[0].voucher_type_id
        }
        this.$confirm(
          this.$t('message.confirmGenerateVoucher') + '?',
          this.$t('confirm.warningTitle'),
          {
            confirmButtonText: this.$t('confirm.confirmButtonText'),
            cancelButtonText: this.$t('confirm.cancelButtonText'),
            type: 'warning',
          },
        ).then(() => {
          // 獲取收費詳細數據
          this.getPostDetails(voucher_type_id)
        })
      } else {
        this.openSaveGroup()
      }
    },
    // 獲取收費詳細數據
    async getPostDetails(voucher_type_id) {
      this.loading = true
      try {
        for (
          let s_index = 0;
          s_index < this.selectedVoucherList[this.activeTab].length;
          s_index++
        ) {
          const vt_category = this.preferences.filters.vt_category
          const post_row_id = this.selectedVoucherList[this.activeTab][s_index].post_row_id
          const post_row_ids = post_row_id
          // 獲取收費詳細數據
          const vc_data = await getPost({ vt_category, post_row_id })
          const list = await fetchNoList({
            voucher_type_id: voucher_type_id,
            date: vc_data[0].vc_date,
          })
          if (list.length === 0) {
            this.$message.error(this.$t('message.noVoucherNo'))
            this.loading = false
            return false
          }
          this.$set(this.selectedVoucherList[this.activeTab][s_index], 'vc_no', list[0])
          for (let vc_data_index = 0; vc_data_index < vc_data.length; vc_data_index++) {
            if (vc_data[vc_data_index].IE !== 'N') {
              const ac_code = vc_data[vc_data_index].ac_code
              const account = await searchAccountByCode({ ac_code })
              if (!account.account_id) {
                this.$message.error(this.$t('message.noAccount'))
                this.loading = false
                return false
              }
              const account_id = account.account_id
              const ledger = {
                ac_code: ac_code,
                account_id: account_id,
                descr: vc_data[vc_data_index].Descr,
                tx_type: vc_data[vc_data_index].IE,
                amount_dr: vc_data[vc_data_index].Amount_Dr || 0,
                amount_cr: vc_data[vc_data_index].Amount_Cr || 0,
                vc_payee: '',
                ref: '',
                budget_code: null,
                st_code: null,
                vc_contra: null,
                vc_dept: null,
                vc_quote: null,
                vc_qnum: null,
              }
              vc_data[vc_data_index].ledger = ledger
            }
          }
          this.$set(this.selectedVoucherList[this.activeTab][s_index], 'vc_data', vc_data)
          const vc_no = this.selectedVoucherList[this.activeTab][s_index].vc_no
          const vt_code = this.selectedVoucherList[this.activeTab][s_index].vc_data[0].vc_vtcode
          console.log('vt_code', vt_code)
          const vc_date = this.selectedVoucherList[this.activeTab][s_index].vc_data[0].vc_date
          const vc_summary = this.selectedVoucherList[this.activeTab][s_index].vc_data[0].Descr
          const vc_chq_date = ''
          const vc_payee = ''
          const vc_st_code = ''
          const vc_extra = 'N'
          const vc_receipt = ''
          const vc_status = 1
          const ledgers_json = []
          const ref = this.selectedVoucherList[this.activeTab][s_index].Ref
          let vc_method = this.selectedVoucherList[this.activeTab][s_index].method_of_payment
          if (vc_method === 'CHECK') {
            vc_method = 'CHEQUE'
          } else if (vc_method === 'AUTOPAY') {
            vc_method = 'AUTO'
          } else if (vc_method === 'FPS' || vc_method === 'BANKIN') {
            vc_method = 'OTHER'
          }
          this.selectedVoucherList[this.activeTab][s_index].vc_data.forEach((ele, index) => {
            if (ele.IE !== 'N') {
              ledgers_json.push(ele.ledger)
            }
          })
          await createVoucher({
            post_row_ids,
            vc_no,
            vt_category,
            vt_code,
            vc_date,
            vc_summary,
            vc_method,
            ref,
            vc_chq_date,
            vc_payee,
            vc_st_code,
            vc_extra,
            vc_receipt,
            vc_status,
            ledgers_json,
          })
        }
        await this.initData()
      } catch (e) {
        this.loading = false
        console.error(e)
      }
    },
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.thisSystem
      const content = {
        page: this.$route.name,
      }
      const pf_code = system.toLowerCase() + '.home'
      editUserPreference(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
    filterClassFunction(row) {
      if (row.row._group % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSelectionChange({ records }) {
      this.selectedVoucherList[this.activeTab] = records
    },
    openPostData() {
      this.showPostDialog = true
      this.form = {
        vt_category: '',
        date: '',
        voucher_date: '',
      }
      this.form.vt_category = this.preferences.filters.vt_category
      this.form.date = this.dateFormatToStr(new Date())
      this.form.voucher_date = this.dateFormatToStr(new Date())
    },
    getPostData() {
      if (this.form.voucher_date < this.form.date) {
        this.$message.error(this.$t('message.voucherDateError'))
        return false
      }
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid, a) => {
          if (!valid) {
            return false
          }
          const post = this.form
          this.preferences.filters.vt_category = this.form.vt_category
          this.preferences.filters.date = this.form.date
          this.preferences.filters.voucher_date = this.form.voucher_date
          postData(post)
            .then(res => {
              this.initData()
            })
            .then(this.closePostData)
            .catch(err => {
              this.$message.error(err)
            })
        })
      })
    },
    closePostData() {
      this.showPostDialog = false
    },
    async closeDetailDialog() {
      this.showDetailDialog = false
    },
    async onEditPost(row) {
      this.showDetailDialog = true
      this.dTableLoading = true
      const vt_category = this.preferences.filters.vt_category
      const post_row_id = row.post_row_id
      try {
        // 傳票類型列表
        this.voucherTypeList = await fetchVoucherTypes({ vt_category })
        // 會計科目列表
        this.accountList = await searchAccounts({})
        // 獲取收費詳細數據
        this.detailData = await getPost({ vt_category, post_row_id })
        this.detailData.forEach(item => {
          item.default_vc_vtcode = item.vc_vtcode
          item.default_ac_code = item.ac_code
          const vc_type = this.voucherTypeList.find(i => i.vt_code === item.vc_vtcode)
          if (!vc_type) {
            item.vc_vtcode = ''
          }
          const ac = this.accountList.find(i => i.ac_code === item.ac_code)
          if (!ac) {
            item.ac_code = ''
          }
        })
        this.dTableLoading = false
      } catch (e) {
        this.dTableLoading = false
        console.error(e)
      }
    },
    async changeVchrType(row) {
      const vc_type = this.voucherTypeList.find(i => i.vt_code === row.vc_vtcode)
      if (vc_type) {
        row.ac_code = vc_type.vt_ac_code
        this.detailData.forEach(item => {
          item.vc_vtcode = row.vc_vtcode
        })
      } else {
        row.ac_code = ''
        this.detailData.forEach(item => {
          item.vc_vtcode = ''
        })
      }
    },
    async saveDetailData() {
      try {
        const vt_category = this.preferences.filters.vt_category
        const posted_data = []
        this.detailData.forEach(item => {
          posted_data.push({
            post_row_id: item.post_row_id,
            vc_vttype: item.vc_vttype,
            vc_vtcode: item.vc_vtcode || item.default_vc_vtcode,
            vc_date: item.vc_date,
            ac_code: item.ac_code || item.default_ac_code,
            IE: item.IE,
            Descr: item.Descr,
            Amount_Dr: item.Amount_Dr,
            Amount_Cr: item.Amount_Cr,
            Payer: item.Payer,
            Ref: item.Ref,
          })
        })
        await savePostDetailData({ vt_category, posted_data })
        await this.closeDetailDialog()
        await this.initData()
      } catch (e) {
        console.error(e)
      }
    },
    handleTablick() {
      const index = this.method_of_payment.findIndex(i => i === this.activeTab)
      this.$refs.table[index].$refs.singleTable.reloadData(this.tableData[this.activeTab])
      const ids = this.selectedVoucherList[this.activeTab].map(i => i.post_row_id)
      const checkboxData = []
      this.tableData[this.activeTab].forEach(i => {
        if (ids.includes(i.post_row_id)) {
          checkboxData.push(i)
        }
      })
      this.$refs.table[index].$refs.singleTable.setCheckboxRow([...checkboxData], true)
    },
    openSaveGroup() {
      if (this.selectedVoucherList[this.activeTab].length === 0) {
        this.$message.error(this.$t('message.pleaseSelected'))
        return false
      }
      this.groupForm = {
        vc_date: '',
        vc_summary: '',
      }
      this.showGroupDialog = true
    },
    onSaveGroup() {
      return new Promise((resolve, reject) => {
        this.$refs.groupForm.validate((valid, a) => {
          if (!valid) {
            return false
          }
          let voucher_type_id
          const vt_category = this.preferences.filters.vt_category
          let vt_code = ''
          const vc_vtcode = this.preferences.filters.vc_vtcode
          if (vc_vtcode) {
            const vt = this.postedVoucherTypeList.find(i => i.vc_vtcode === vc_vtcode)
            if (vt && vt.vt_code && vt.vt_code !== 'NULL') {
              vt_code = vt.vt_code
            }
          }
          // 獲取傳票類型列表
          fetchVoucherTypes({ vt_category, vt_code })
            .then(res => {
              if (res.length === 0) {
                voucher_type_id = 0
                this.$message.error(this.$t('message.noVoucherType'))
                return Promise.reject(this.$t('message.noVoucherType'))
              } else {
                voucher_type_id = res[0].voucher_type_id
              }
            })
            .then(() => {
              this.$confirm(
                this.$t('message.confirmGenerateVoucher') + '?',
                this.$t('confirm.warningTitle'),
                {
                  confirmButtonText: this.$t('confirm.confirmButtonText'),
                  cancelButtonText: this.$t('confirm.cancelButtonText'),
                  type: 'warning',
                },
              ).then(() => {
                // 獲取收費詳細數據
                this.getPostMutiDetails(voucher_type_id)
              })
            })
        })
      })
    },
    // 獲取收費詳細數據
    async getPostMutiDetails(voucher_type_id) {
      this.loading = true
      try {
        const post_row_idsArr = []
        Object.values(this.selectedVoucherList).forEach(item => {
          item.forEach(i => {
            post_row_idsArr.push(i.post_row_id)
          })
        })
        const post_row_ids = post_row_idsArr.join(',')
        console.log(post_row_ids, Object.values(this.selectedVoucherList))
        const vt_category = this.preferences.filters.vt_category

        // 獲取收費詳細數據
        const vc_data = await getMultiplePost({ vt_category, post_row_ids })
        const list = await fetchNoList({
          voucher_type_id: voucher_type_id,
          date: this.groupForm.vc_date,
        })
        if (list.length === 0) {
          this.$message.error(this.$t('message.noVoucherNo'))
          this.loading = false
          return false
        }
        const vc_no = list[0]
        for (let vc_data_index = 0; vc_data_index < vc_data.length; vc_data_index++) {
          if (vc_data[vc_data_index].IE !== 'N') {
            const ac_code = vc_data[vc_data_index].ac_code
            const account = await searchAccountByCode({ ac_code })
            if (!account.account_id) {
              this.$message.error(this.$t('message.noAccount'))
              this.loading = false
              return false
            }
            const account_id = account.account_id
            const ledger = {
              ac_code: ac_code,
              account_id: account_id,
              descr: vc_data[vc_data_index].Descr,
              tx_type: vc_data[vc_data_index].IE,
              amount_dr: vc_data[vc_data_index].Amount_Dr || 0,
              amount_cr: vc_data[vc_data_index].Amount_Cr || 0,
              vc_payee: '',
              ref: '',
              budget_code: null,
              st_code: null,
              vc_contra: null,
              vc_dept: null,
              vc_quote: null,
              vc_qnum: null,
            }
            vc_data[vc_data_index].ledger = ledger
          }
        }
        const vt_code = vc_data[0].vc_vtcode
        const vc_date = this.groupForm.vc_date
        const vc_summary = this.groupForm.vc_summary
        let ref = ''
        const vc_chq_date = ''
        const vc_payee = ''
        const vc_st_code = ''
        const vc_extra = 'N'
        const vc_receipt = ''
        const vc_status = 1
        const ledgers_json = []
        let num = 0
        Object.values(this.selectedVoucherList).forEach(item => {
          if (item.length > 0) {
            num++
          }
        })
        let vc_method = this.activeTab
        if (vc_method === 'CHECK') {
          vc_method = 'CHEQUE'
        } else if (vc_method === 'AUTOPAY') {
          vc_method = 'AUTO'
        } else if (vc_method === 'FPS' || vc_method === 'BANKIN') {
          vc_method = 'OTHER'
        }
        if (num > 1) {
          vc_method = 'OTHER'
          ref = null
        }
        vc_data.forEach((ele, index) => {
          if (ele.IE !== 'N') {
            ledgers_json.push(ele.ledger)
          }
        })
        await createVoucher({
          post_row_ids,
          vc_no,
          vt_category,
          vt_code,
          vc_date,
          vc_summary,
          vc_method,
          ref,
          vc_chq_date,
          vc_payee,
          vc_st_code,
          vc_extra,
          vc_receipt,
          vc_status,
          ledgers_json,
        })
        this.showGroupDialog = false
        await this.initData()
      } catch (e) {
        this.loading = false
        console.error(e)
      }
    },
    onMultiEdit() {
      console.log('onMultiEdit')
      if (this.selectedVoucherList[this.activeTab].length === 0) {
        this.$message.error(this.$t('message.pleaseSelected'))
        return false
      } else {
        this.multiEditForm.vc_vtcode = undefined
        this.multiEditForm.ac_code = ''
        this.multiEditForm.vt_category = this.preferences.filters.vt_category
        this.multiEditForm.post_row_ids = this.selectedVoucherList[this.activeTab]
          .map(i => {
            return i.post_row_id
          })
          .join(',')
        this.handleMultiEdit()
        this.showMultiEditDialog = true
      }
    },
    async handleMultiEdit() {
      this.multiEditLoading = true
      try {
        this.voucherTypeList = await fetchVoucherTypes({
          vt_category: this.multiEditForm.vt_category,
        })
      } catch (e) {
        console.error(e)
      }
      this.multiEditLoading = false
    },
    onChangeMultiVoucherType() {
      this.$refs.multiEditForm.validateField('vc_vtcode')
      const vc_vtcode = this.multiEditForm.vc_vtcode
      if (vc_vtcode) {
        const vc_type = this.voucherTypeList.find(i => i.vt_code === vc_vtcode)
        if (vc_type) {
          this.multiEditForm.ac_code = vc_type.vt_ac_code
        } else {
          this.multiEditForm.ac_code = ''
        }
      } else {
        this.multiEditForm.ac_code = ''
      }
    },
    saveMultiData() {
      console.log('saveMultiData')
      if (this.multiEditLoading) return
      this.multiEditLoading = true
      this.$refs.multiEditForm.validate(valid => {
        if (valid) {
          this.handleMultiSave()
        } else {
          this.multiEditLoading = false
          return false
        }
      })
    },
    async handleMultiSave() {
      try {
        const post_row_ids = this.multiEditForm.post_row_ids
        const vt_category = this.multiEditForm.vt_category
        const vc_vtcode = this.multiEditForm.vc_vtcode
        const ac_code = this.multiEditForm.ac_code
        const res = await updateVoucherPostVtCode({
          vt_category,
          post_row_ids,
          vc_vtcode,
          ac_code,
        })
        console.log(res)
        this.showMultiEditDialog = false
        this.multiEditLoading = false
        this.$message.success(this.$t('message.modifySuccess'))
        this.initData()
      } catch (e) {
        console.error(e)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  .b-table {
    /*height: calc(100vh - 195px);*/
    /deep/ {
      .el-table__body-wrapper {
        /*height: calc(100vh - 195px);*/
      }

      .el-input--medium .el-input__inner {
        height: 23px;
        line-height: 23px;
      }

      .vc_receipt .el-input--medium {
        line-height: 23px;
      }

      .vc_receipt_date .el-input--medium {
        line-height: 23px;
      }

      .vxe-table--main-wrapper {
        .vxe-table--body-wrapper {
          .vxe-body--row {
            .vxe-body--column {
              .vxe-cell {
              }

              &.vc_receipt {
                padding: 0;
                .vxe-cell {
                  line-height: inherit;
                  height: 100%;
                  min-height: 25px;
                  padding: 0;
                  .el-input {
                    display: block;
                    line-height: initial;
                    height: 100%;
                    input {
                      height: 100%;
                      line-height: 100%;
                      vertical-align: middle;
                    }
                  }
                }
              }
              &.vc_receipt_date {
                padding: 0;
                .vxe-cell {
                  line-height: inherit;
                  height: 100%;
                  min-height: 25px;
                  padding: 0;
                  .el-input {
                    display: block;
                    line-height: initial;
                    height: 100%;
                    width: 100%;
                    min-width: 120px;
                    input {
                      height: 100%;
                      line-height: 100%;
                      vertical-align: middle;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
        .el-date-editor.el-input {
          width: 150px;
        }
        .el-input--mini .el-input__inner {
          height: 25px;
          line-height: 25px;
        }
      }
    }
  }

  .el-button {
    padding: 5px 15px;
    vertical-align: middle;
  }
  /deep/ .postDialog {
    .el-dialog {
      .el-dialog__body {
        max-height: 22vh;
        height: 22vh;
        overflow: auto;
      }
    }
  }
  /deep/ .post-multi-edit-dialog {
    .el-dialog {
      .el-dialog__body {
        max-height: 22vh;
        height: 100px;
        overflow: auto;
      }
    }
  }
  .summary {
    margin: 5px 0;
    /deep/ {
      span {
        line-height: 25px !important;
        height: 25px !important;
      }
      .el-button {
        padding: 0px 15px;
        vertical-align: middle;
      }
    }
  }
}
</style>
