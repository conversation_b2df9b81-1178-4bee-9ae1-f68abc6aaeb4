import Layout from '@/views/layout/Layout'
import { deepClone } from '@/utils'

const other = [
  {
    // 新增
    path: '/add',
    component: () => import('@/views/daily/components/add.vue'),
    name: 'Add',
    props: route => route.params, // props
    meta: {
      title: 'router.voucherAdd',
      noCache: true,
    },
  },
  {
    // 編輯
    path: '/edit',
    component: () => import('@/views/daily/components/add.vue'),
    name: 'Edit',
    props: route => route.params, // props
    meta: {
      title: 'router.voucherEdit',
      noCache: true,
    },
  },
  {
    // 查看
    path: '/view',
    component: () => import('@/views/daily/components/view.vue'),
    name: 'View',
    props: route => route.params, // props
    meta: {
      title: 'router.voucherView',
      noCache: true,
    },
  },
  {
    // 重新排序
    path: '/reorderSummons',
    component: () => import('@/views/daily/components/reorderSummons.vue'),
    name: 'ReorderSummons',
    props: route => route.params, // props
    meta: {
      title: 'router.voucherReorderSummons',
      noCache: true,
    },
  },
]
const dailyRouter = {
  path: '/daily',
  component: Layout,
  redirect: 'noredirect',
  name: 'daily',
  meta: {
    title: 'router.daily',
    p_code: 'ac.daily',
    icon: 'daily',
  },
  children: [
    // 付款傳票
    {
      path: '/daily/p',
      redirect: '/daily/payment',
      component: () => import('@/views/daily/components/dailyPane.vue'),
      name: 'dailyPayment',
      meta: {
        title: 'router.dailyPayment', // ?
        p_code: 'ac.daily.payment',
        noCache: true,
        ignore: true,
      },
      children: [
        {
          // 列表
          path: '/daily/payment',
          component: () => import('@/views/daily/page/payment.vue'),
          name: 'dailyPaymentList',
          needAPrint: 'V,L,C,R,M,N',
          meta: {
            // title: 'router.dailyPayment',
            p_code: 'ac.daily.payment',
            noCache: true,
          },
        },
        // { // 新增
        //   path: '/daily/payment/add',
        //   component: () => import('@/views/daily/components/add.vue'),
        //   name: 'dailyPaymentAdd',
        //   props: (route) => (route.params), // props
        //   meta: {
        //     title: 'router.voucherAdd',
        //     p_code: 'ac.daily.payment',
        //     noCache: true
        //   }
        // },
        // { // 編輯
        //   path: '/daily/payment/edit',
        //   component: () => import('@/views/daily/components/add.vue'),
        //   name: 'dailyPaymentEdit',
        //   props: (route) => (route.params), // props
        //   meta: {
        //     title: 'router.voucherEdit',
        //     p_code: 'ac.daily.payment',
        //     noCache: true
        //   }
        // },
        // { // 查看
        //   path: '/daily/payment/view',
        //   component: () => import('@/views/daily/components/view.vue'),
        //   name: 'dailyPaymentView',
        //   props: (route) => (route.params), // props
        //   meta: {
        //     title: 'router.voucherView',
        //     p_code: 'ac.daily.payment',
        //     noCache: true
        //   }
        // },
        ...other.map(i => {
          const r = deepClone(i)
          r.path = '/daily/payment' + r.path
          r.name = 'dailyPayment' + r.name
          r.meta.p_code = 'ac.daily.payment'
          return r
        }),
      ],
    },
    // 現金傳票
    {
      path: '/daily/c',
      redirect: '/daily/petty_cash',
      component: () => import('@/views/daily/components/dailyPane.vue'),
      name: 'dailyPettyCash',
      meta: {
        title: 'router.dailyPettyCash', // ?
        p_code: 'ac.daily.petty_cash',
        noCache: true,
        ignore: true,
      },
      children: [
        {
          // 列表
          path: '/daily/petty_cash',
          component: () => import('@/views/daily/page/cash.vue'),
          name: 'dailyPettyCashList',
          needAPrint: 'V,L,C,R,M,N',
          meta: {
            // title: 'router.dailyPayment',
            p_code: 'ac.daily.petty_cash',
            noCache: true,
          },
        },
        ...other.map(i => {
          const r = deepClone(i)
          r.path = '/daily/petty_cash' + r.path
          r.name = 'dailyPettyCash' + r.name
          r.meta.p_code = 'ac.daily.petty_cash'
          return r
        }),
      ],
    },
    // 收款傳票
    {
      path: '/daily/r',
      redirect: '/daily/receipt',
      component: () => import('@/views/daily/components/dailyPane.vue'),
      name: 'dailyReceipt',
      meta: {
        title: 'router.dailyReceipt', // listTitle
        p_code: 'ac.daily.receipt',
        noCache: true,
        ignore: true,
      },
      children: [
        {
          // 列表
          path: '/daily/receipt',
          component: () => import('@/views/daily/page/receipt.vue'),
          name: 'dailyReceiptList',
          needAPrint: 'V,L,C,R,M,N',
          meta: {
            p_code: 'ac.daily.receipt',
            noCache: true,
          },
        },
        ...other.map(i => {
          const r = deepClone(i)
          r.path = '/daily/receipt' + r.path
          r.name = 'dailyReceipt' + r.name
          r.meta.p_code = 'ac.daily.receipt'
          return r
        }),
      ],
    },
    // 銀行轉賬
    {
      path: '/daily/t',
      redirect: '/daily/bank_transfer',
      component: () => import('@/views/daily/components/dailyPane.vue'),
      name: 'dailyBankTransfer',
      meta: {
        title: 'router.dailyBankTransfer', // listTitle
        p_code: 'ac.daily.bank_transfer',
        noCache: true,
        ignore: true,
      },
      children: [
        {
          // 列表
          path: '/daily/bank_transfer',
          component: () => import('@/views/daily/page/bankTransfer.vue'),
          name: 'dailyBankTransferList',
          needAPrint: 'V,L,C,R,M,N',
          meta: {
            p_code: 'ac.daily.bank_transfer',
            noCache: true,
          },
        },
        ...other.map(i => {
          const r = deepClone(i)
          r.path = '/daily/bank_transfer' + r.path
          r.name = 'dailyBankTransfer' + r.name
          r.meta.p_code = 'ac.daily.bank_transfer'
          return r
        }),
      ],
    },
    // 一般傳票
    {
      path: '/daily/j',
      redirect: '/daily/journal',
      component: () => import('@/views/daily/components/dailyPane.vue'),
      name: 'dailyJournal',
      meta: {
        title: 'router.dailyJournal', // listTitle
        p_code: 'ac.daily.journal',
        noCache: true,
        ignore: true,
      },
      children: [
        {
          // 列表
          path: '/daily/journal',
          component: () => import('@/views/daily/page/journal.vue'),
          name: 'dailyJournalList',
          needAPrint: 'V,L,C,R,M,N',
          meta: {
            p_code: 'ac.daily.journal',
            noCache: true,
          },
        },
        ...other.map(i => {
          const r = deepClone(i)
          r.path = '/daily/journal' + r.path
          r.name = 'dailyJournal' + r.name
          r.meta.p_code = 'ac.daily.journal'
          return r
        }),
      ],
    },

    // {
    //   path: 'petty_cash',
    //   component: () => import('@/views/daily/payment/cash'),
    //   name: 'dailyPettyCash',
    //   meta: {
    //     title: 'router.dailyPettyCash',
    //     p_code: 'ac.daily.petty_cash',
    //     noCache: true
    //   }
    // },
    // {
    //   path: 'receipt',
    //   component: () => import('@/views/daily/payment/receipt'),
    //   name: 'dailyReceipt',
    //   meta: {
    //     title: 'router.dailyReceipt',
    //     p_code: 'ac.daily.receipt',
    //     noCache: true
    //   }
    // },
    // {
    //   path: 'bank_transfer',
    //   component: () => import('@/views/daily/payment/bankTransfer'),
    //   name: 'dailyBankTransfer',
    //   meta: {
    //     title: 'router.dailyBankTransfer',
    //     p_code: 'ac.daily.bank_transfer',
    //     noCache: true
    //   }
    // },
    // {
    //   path: 'journal',
    //   component: () => import('@/views/daily/payment/journal'),
    //   name: 'dailyJournal',
    //   meta: {
    //     title: 'router.dailyJournal',
    //     p_code: 'ac.daily.journal',
    //     noCache: true
    //   }
    // },
    {
      path: 'cheque',
      component: () => import('@/views/daily/cheque/index.vue'),
      name: 'dailyCheque',
      needAPrint: 'P',
      meta: {
        title: 'router.dailyCheque',
        p_code: 'ac.daily.cheque',
        noCache: true,
      },
    },
    {
      path: 'address',
      component: () => import('@/views/daily/address/index.vue'),
      name: 'dailyAddress',
      needAPrint: 'Y',
      meta: {
        title: 'router.dailyAddress',
        p_code: 'ac.daily.address',
        noCache: true,
      },
    },
    {
      path: 'post',
      component: () => import('@/views/daily/post/index.vue'),
      name: 'dailyPost',
      needAPrint: 'P',
      meta: {
        title: 'router.dailyPost',
        p_code: 'ac.daily.post',
        noCache: true,
      },
    },
  ],
}

export default dailyRouter
