<template>
  <div class="items-step">
    <el-form v-if="form" ref="form" :model="{ form }">
      <el-table :data="form">
        <el-table-column type="index" />
        <el-table-column :label="t('itemName')" prop="item_name">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item :prop="`form[${scope.$index}].item_name`" required>
              <el-input
                v-model="form[scope.$index].item_name"
                :disabled="isReadonly"
                type="textarea"
                autosize
                resize="none"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('itemQty')" prop="item_qty" width="150">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item :prop="`form[${scope.$index}].item_qty`" required>
              <el-input-Number
                v-model="form[scope.$index].item_qty"
                :disabled="isReadonly"
                :controls="!isReadonly"
                :class="{
                  'align-right': isReadonly ? 'right' : '',
                }"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('itemRemark')" prop="item_remark">
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-form-item :prop="`form[${scope.$index}].item_remark`">
              <el-input
                v-model="form[scope.$index].item_remark"
                :disabled="isReadonly"
                type="textarea"
                autosize
                resize="none"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('action')" width="80">
          <template v-if="scope && scope.row" slot-scope="scope">
            <i v-if="!isReadonly" class="el-icon-delete action-icon" @click="onDelete(scope)" />
          </template>
        </el-table-column>
      </el-table>
      <el-form-item class="actions">
        <el-button size="mini" type="info" @click="onBack">
          {{ $t('button.back') }}
        </el-button>
        <el-button
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="primary"
          @click="onPrev"
        >
          {{ $t('button.prev') }}
        </el-button>
        <el-button
          v-if="!isReadonly"
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="success"
          @click="onSave"
        >
          {{ $t('button.saveFile') }}
        </el-button>
        <el-button
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="primary"
          @click="onNext"
        >
          {{ $t('button.next') }}
        </el-button>
      </el-form-item>
    </el-form>

    <svg-icon v-if="!isReadonly" icon-class="add" class-name="add-icon add-row" @click="onAddRow" />
  </div>
</template>
<script>
import { editProcurementApplicationsItems } from '@/api/purchase/procurementApplications'

export default {
  name: 'ItemsStep',
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      langKey: 'purchase.daily.procurementApplications.',
      // form: {
      //
      // }
    }
  },
  computed: {
    form: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  created() {
    if (!this.isReadonly && this.form.length === 0) {
      this.onAddRow()
    }
  },
  methods: {
    onAddRow() {
      this.form.push({
        item_name: '',
        item_index: this.form.length,
        item_remark: '',
        item_qty: 1,
      })
    },
    onDelete(scope) {
      this.form.splice(scope.$index, 1)
    },
    onSave(showTips = true) {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(async(valid, a) => {
          if (!valid && !this.isReadonly) {
            reject()
            return false
          }
          const pro_application_id = this.id
          const items = this.form.map((item, index) => {
            return {
              item_name: item.item_name,
              item_index: index + 1,
              item_remark: item.item_remark,
              item_qty: item.item_qty,
            }
          })
          try {
            await editProcurementApplicationsItems({
              pro_application_id,
              items,
            })
            if (showTips) {
              this.$message.success(this.t('saveSuccess'))
              this.onBack()
            }
            resolve()
          } catch (e) {
            console.log(e)
            reject(e)
          }
        })
      })
    },
    onBack() {
      this.$emit('back')
    },
    onNext() {
      this.$emit('next')
    },
    onPrev() {
      this.$emit('prev')
    },
  },
}
</script>

<style lang="scss" scoped>
.add-icon {
  cursor: pointer;
  &.add-row {
    position: absolute;
    right: -30px;
    top: 3px;
  }
}
.items-step {
  /deep/ {
    .el-input-number .el-input-number__increase,
    .el-input-number .el-input-number__decrease {
      top: 4px;
    }
    .el-input-number {
      vertical-align: bottom;
      .el-input {
        input {
          padding: 5px 15px;
          resize: none;
          min-height: 30px;
          height: 30px;
        }
      }
    }
    .el-form-item__error {
      display: none;
    }
    .el-input-number__decrease,
    .el-input-number__increase {
      top: 3px !important;
      height: 27px !important;
      line-height: 27px !important;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .actions {
    text-align: right;
    width: 1000px;
    padding-top: 10px;
  }
}
</style>
