<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <div>
      <header v-if="false">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('router.settingScreenBasicSetting') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t($route.meta.title) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </header>
      <div class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 年份 -->
          <el-form-item :label="$t('filters.years')">
            <el-select
              ref="year"
              v-model="preferences.filters.selectedYearCode"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>
          <!-- 類別 -->
          <el-form-item :label="$t('filters.type')">
            <SelectTree
              v-model="preferences.filters.selectedBudgetGroupId"
              :options="budgetGroupTree"
              :props="{
                label: language === 'en' ? 'name_en' : 'name_cn',
                value: 'budget_id',
                code: 'budget_code',
                group_id: 'budget_id',
                value_id: 'budget_id',
                children: 'children',
              }"
              style="width: 300px"
              @change="onChangeGroup"
            >
              <template v-if="data" slot-scope="{ data }">
                <el-option
                  v-for="item in data"
                  :key="item.budget_id"
                  :label="item[language === 'en' ? 'name_en' : 'name_cn']"
                  :value="item"
                  :disabled="item.budget_type === 'F'"
                >
                  <span>{{ repeat('&nbsp;', item.level * 4 + (item.children ? 0 : 2)) }}</span>
                  <i
                    v-if="item.children && item.children.length > 0"
                    class="el-icon-caret-bottom"
                  />
                  <span
                    v-if="!item.children && item.budget_code"
                  >[{{ item.budget_code }}]&nbsp;</span>
                  <span>{{ item[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
                </el-option>
              </template>
            </SelectTree>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" @click="onUpdate">
              {{ $t('button.update') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <ETable
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :show-index="false"
        :show-actions="true"
        :actions-min-width="5"
        :show-checkbox="false"
        :default-top="230"
        :filter-class-function="filterClassFunction"
        border
      >
        <template slot="columns">
          <el-table-column
            :label="$t(langKey + 'type')"
            :class-name="' mini-form'"
            align="left"
            width="300"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>
                {{
                  `[${scope.row.budget_code}] ${
                    scope.row[language === 'en' ? 'name_en' : 'name_cn']
                  }`
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(langKey + 'account')"
            class-name="account-type mini-form"
            align="left"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>
                <AccountSelect
                  :fy-code="preferences.filters.selectedYearCode"
                  :data.sync="scope.row.budget_account"
                  :default-accounts="accounts"
                  :has-list="true"
                  style="padding: 10px"
                />
              </span>
            </template>
          </el-table-column>
        </template>
      </ETable>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ETable from '@/components/ETable'
import SelectTree from '@/components/SelectTree/index.vue'
import AccountSelect from './account'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import { editStaffPreference, editUserPreference } from '@/api/settings/user/preference'

import { getBudgetYears } from '@/api/master/years'

import { editBudgetAccounts, fetchBudgetTree, searchBudget } from '@/api/budget'
import { searchAccounts } from '@/api/master/account'
import {
  formatBudgetData,
  getFirstItem,
  getTreeBudgetByCode,
  getTreeBudgetById,
} from '@/views/budget/common/utils'
// px2pt
let groupCount = 1

export default {
  name: 'PeriodicAutopayListIndex',
  components: {
    ETable,
    SelectTree,
    AccountSelect,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, loadPrintoutSetting],
  data() {
    return {
      loading: true,
      tableLoading: false,
      bankList: [],
      tableData: [],
      langKey: 'budget.accountInput.label.',
      preferences: {
        filters: {
          selectedYearCode: '',
          selectedBudgetGroupId: '',
        },
      },
      childPreferences: ['selectedBudgetGroupId'],
      years: [],
      budgetGroupTree: [],

      accounts: [],
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school', 'remoteServerInfo']),
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      getBudgetYears()
        .then(res => {
          this.years = res
          if (res.length > 0) {
            const year = res.find(i => i.fy_code === this.preferences.filters.selectedYearCode)
            if (!year) {
              this.preferences.filters.selectedYearCode = res[0].fy_code
            }
          }
        })
        .then(this.loadUserPreference)
        // .then(() => {
        //   const fy_code = this.preferences.filters.selectedYearCode
        //   if (fy_code !== '') {
        //     return fetchBudgetTree({ type: 'G', fy_code })
        //   } else {
        //     return Promise.reject()
        //   }
        // })
        // .then(res => {
        //   this.budgetGroupTree = res
        //   return this.updateChildPreferenceByName('selectedBudgetGroupId')
        // })
        .then(this.loadGroup)
        .then(this.reloadData)
        .then(this.loadOptions)
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadGroup() {
      const fy_code = this.preferences.filters.selectedYearCode
      if (fy_code !== '') {
        return new Promise((resolve, reject) => {
          fetchBudgetTree({ type: 'G', fy_code })
            .then(async res => {
              this.budgetGroupTree = res

              await this.updateChildPreferenceByName('selectedBudgetGroupId')
              const group = getTreeBudgetById(res, this.preferences.filters.selectedBudgetGroupId)
              // const group = res.find(i => i.budget_id === this.preferences.filters.selectedBudgetGroupId)
              this.preferences.filters.selectedBudgetGroupId = ''
              if (!group) {
                if (res.length > 0) {
                  const setGroup = arr => {
                    let success = false
                    for (let i = 0; i < arr.length; i++) {
                      const item = arr[i]
                      if (item.budget_type !== 'F') {
                        this.preferences.filters.selectedBudgetGroupId = item.budget_id
                        success = true
                      }
                      if (item.children && item.children.length) {
                        success = setGroup(item.children)
                      }
                      if (success) {
                        return true
                      }
                    }
                  }
                  setGroup(res)
                }
              } else {
                this.preferences.filters.selectedBudgetGroupId = group.budget_id
              }
              resolve(res)
            })
            .catch(err => {
              reject(err)
            })
        })
      } else {
        return Promise.reject()
      }
    },
    reloadData() {
      const fy_code = this.preferences.filters.selectedYearCode
      const parent_budget_id = this.preferences.filters.selectedBudgetGroupId
      if (!fy_code || !parent_budget_id) {
        this.tableData = []
        return Promise.reject(new Error('error'))
      }
      return new Promise((resolve, reject) => {
        this.loading = true
        searchBudget({ fy_code, parent_budget_id })
          .then(res => {
            this.tableData = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    loadOptions() {
      const fy_code = this.preferences.filters.selectedYearCode
      return new Promise((resolve, reject) => {
        searchAccounts({ fy_code })
          .then(res => {
            this.accounts = res
            resolve(this.accounts)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.thisSystem
      const pf_code = system.toLowerCase() + '.home'
      const content = {
        page: this.$route.name,
      }
      let api = editStaffPreference
      if (system === 'AC') {
        api = editUserPreference
      }
      api(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
    filterClassFunction(row) {
      row.row.tx_num === 1 ? groupCount++ : ''
      if (groupCount % 2 === 0) {
        return 'table-stripe'
      }
    },

    onChangeYear(id) {
      return new Promise((resolve, reject) => {
        this.loadGroup()
          .then(() => this.loadOptions())
          .then(() => this.reloadData())
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    repeat(str, n) {
      return new Array(n + 1).join(str)
    },
    onChangeGroup({ value }) {
      this.loadOptions().then(() => this.reloadData())
    },
    onUpdate() {
      if (this.tableData.length === 0) {
        return
      }
      const fy_code = this.preferences.filters.selectedYearCode
      const budget_account_json = JSON.stringify(
        this.tableData.map(item => {
          const account_ids = item.budget_account
          return {
            budget_id: item.budget_id,
            account_ids,
          }
        }),
      )
      this.loading = true
      editBudgetAccounts({ fy_code, budget_account_json })
        .then(() => {
          this.$message.success(this.$t('message.success'))
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }

    // 日期選擇器
    /deep/ .el-input__inner > {
      // 日期選擇器
      .el-range-input {
        height: 22px !important;
      }
    }

    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
    /deep/ {
      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  .el-table {
    height: calc(100vh - 190px);
    /deep/ {
      .el-table__body-wrapper {
        height: calc(100vh - 190px);
      }
    }
  }
  /deep/ table {
    tbody {
      .solid-border-top td {
        border-top: solid 1px #808080;
      }
      .account-type {
        padding: 0;
      }
    }
  }
  .el-button {
    padding: 5px 15px;
    vertical-align: middle;
  }
}
</style>
