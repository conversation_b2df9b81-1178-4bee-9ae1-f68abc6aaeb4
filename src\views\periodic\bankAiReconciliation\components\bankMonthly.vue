<template>
  <el-dialog :title="t('bankMonth')" :visible.sync="show" class="bank-monthly-dialog" width="85%">
    <uploadDialog
      :inner-visible="innerVisible"
      :selected-ac-code="form.selectedAcCode"
      :bank-list="bankList"
      @closeUpload="innerVisible = false"
      @uploadSuccess="uploadSuccess"
      @changeBank="changeBank"
    />
    <AddLedgerPop
      :add-inner-visible="addInnerVisible"
      :select-file-row="selectFileRow"
      :add-pre-type="addPreType"
      :ai_document_file_ledger_id="ai_document_file_ledger_id"
      @close="addInnerVisible = false"
      @createSuccess="createSuccess"
    />
    <div class="content">
      <div class="left-content">
        <div class="left-content__top" style="width: 100%">
          <el-form :model="form" :inline="true">
            <el-form-item :label="t('bank')" class="bank-form-item" style="width: 50%">
              <div class="form-flex">
                <el-select
                  v-model="form.selectedAcCode"
                  class="bank"
                  style="width: 220px"
                  @change="changeBank"
                >
                  <el-option
                    v-for="item in bankList"
                    :key="item.ac_code"
                    :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
                    :value="item.ac_code"
                  />
                </el-select>
                <el-button
                  v-if="hasPermission_T"
                  style="margin-left: 10px"
                  size="mini"
                  type="primary"
                  @click="innerVisible = true"
                >
                  {{ $t('button.uploadFile') }}
                </el-button>
                <el-checkbox
                  v-model="form.filterAbnormalLedger"
                  style="margin-left: 10px"
                  size="mini"
                  type="primary"
                  @change="filterAbnormalLedger"
                >
                  {{ t('filterAbnormalLedger') }}
                </el-checkbox>
                <i
                  v-if="!form.filterAbnormalLedger"
                  class="el-icon-refresh action-icon"
                  :style="{ cursor: 'pointer', fontSize: '14px' }"
                  @click="refreshAi"
                />
              </div>
            </el-form-item>
            <el-form-item>
              <div class="ledger-key">
                <el-select v-model="form.ledgerIndex" class="bank" style="width: 282px">
                  <el-option
                    v-for="item in ledgerKeys"
                    :key="item.index"
                    :label="item.value"
                    :value="item.index"
                  />
                </el-select>
                <el-button
                  style="margin-left: 10px"
                  size="mini"
                  type="primary"
                  @click="goLedgerPosition"
                >
                  {{ t('letsGo') }}
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="table-box">
          <div class="ai-analysis">
            <BTable
              ref="table"
              :data="fileList"
              :style-columns="styleColumns"
              :lang-key="langKey"
              :height="cs"
              :show-actions="false"
              :filter-class-function="filterClassFunction"
              :empty-text="t('letUploadFile')"
              border
              style="height: 100%"
              @cell-click="selectRow"
            >
              <template slot="columns">
                <vxe-table-column
                  v-for="item in styleColumns"
                  :key="item.ss_key"
                  :title="item.title ? $t(langKey + item.title) : ''"
                  :width="item.width"
                  :field="item.ss_key"
                  :column-key="item.ss_key"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span
                      v-if="item.ss_key === 'status'"
                      :class="scope.row[item.ss_key]"
                      :style="{ color: scope.row.status === 'ABANDONED' ? '#999' : '' }"
                    >
                      {{ t(scope.row[item.ss_key]) }}
                    </span>
                    <span
                      v-else-if="item.ss_key === 'options'"
                      :class="scope.row['status']"
                      style="display: flex; align-items: center; justify-content: center"
                      :style="{ color: scope.row.status === 'ABANDONED' ? '#999' : '' }"
                    >
                      <!-- <i v-if="scope.row.status !== 'RUNNING'" class="el-icon-refresh action-icon" :style="{ cursor: scope.row.status === 'ABANDONED' ? 'not-allowed' : 'pointer' }"
                        @click="refreshAi"></i> -->
                      <i
                        v-if="scope.row.status !== 'RUNNING'"
                        class="el-icon-close action-icon"
                        :style="{
                          cursor: scope.row.status === 'ABANDONED' ? 'not-allowed' : 'pointer',
                        }"
                        @click="deleteFiles(scope.row)"
                      />
                    </span>
                    <span v-else-if="item.ss_key === 'radio'" :class="scope.row['status']">
                      <el-radio
                        v-model="radio"
                        :disabled="scope.row.status === 'ABANDONED'"
                        :label="scope.row.ai_document_file_id"
                        @input="changeRadio"
                      >{{ '' }}</el-radio>
                    </span>
                    <span
                      v-else
                      :class="scope.row['status']"
                      :style="{ color: scope.row.status === 'ABANDONED' ? '#999' : '' }"
                    >
                      {{ scope.row[item.ss_key] }}
                    </span>
                  </template>
                </vxe-table-column>
              </template>
            </BTable>
          </div>
          <div v-if="fileLedgers.length" ref="ledgersTable" class="files-actions">
            <div
              v-for="(item, index) in fileLedgers"
              :id="'ledger' + index"
              :key="index + item.title"
            >
              <div class="title">
                <span>{{ item.title }}</span>
                <span>{{ item.dateValue }}</span>
              </div>
              <BTable
                ref="table"
                :data="item.data"
                :style-columns="ledgersStyleColumns"
                :lang-key="langKey"
                class="ledger-column-table"
                :show-actions="false"
                border
                @cell-mouseenter="handleMouseenter"
              >
                <template slot="columns">
                  <vxe-table-column
                    v-for="items in ledgersStyleColumns"
                    :key="items.ss_key"
                    :title="items.title ? $t(langKey + items.title) : ''"
                    :width="items.width"
                    :field="items.ss_key"
                    :align="items.alignment"
                    :column-key="items.ss_key"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="items.ss_key === 'status'" :class="scope.row[items.ss_key]">
                        {{ t(scope.row[item.ss_key]) }}
                      </span>
                      <span v-else-if="items.ss_key === 'options'" :class="scope.row['status']">
                        <i class="el-icon-plus action-icon" @click="onAddLedger(scope)" />
                        <i
                          :style="{ cursor: scope.row.lg_id === 0 ? 'pointer' : 'not-allowed' }"
                          class="el-icon-close action-icon"
                          @click="deleteLedgers(scope.row)"
                        />
                      </span>
                      <span v-else-if="items.ss_key === 'radio'" :class="scope.row['status']">
                        <el-tooltip class="item" effect="dark" placement="top">
                          <template slot="content">
                            <div style="lineheight: 18px">
                              <div>
                                {{ scope.row.lg_id > 0 ? t('enable') + ':' : t('disable') }}
                              </div>
                              <div v-if="scope.row.lg_id > 0">
                                {{ mouseText }}
                              </div>
                            </div>
                          </template>
                          <i
                            v-if="scope.row.lg_id > 0"
                            :style="{ fontSize: finFontSize }"
                            class="el-icon-success"
                            style="color: #ace5b6"
                            @mouseover="getRowText(scope)"
                          />
                          <i
                            v-else
                            :style="{ fontSize: finFontSize }"
                            class="el-icon-warning"
                            style="color: #f6d66a"
                          />
                        </el-tooltip>
                      </span>
                      <span v-else :class="scope.row['status']">
                        <span
                          v-if="
                            changeLedger.row.ai_document_file_ledger_id ===
                              scope.row.ai_document_file_ledger_id &&
                              changeLedger.ss_key === items.ss_key &&
                              scope.row.lg_id === 0
                          "
                        >
                          <input
                            v-if="items.ss_key !== 'ledger_date'"
                            ref="inputRef"
                            v-model="changeLedger.row[items.ss_key]"
                            @focus="inputFocus(scope.row, items.ss_key)"
                            @blur="updateLedgers(scope.row, items.ss_key)"
                          >
                          <el-date-picker
                            v-else
                            ref="inputRef"
                            v-model="changeLedger.row[items.ss_key]"
                            prefix-icon=" "
                            value-format="yyyy-MM-dd"
                            :clearable="false"
                            type="date"
                            @change="updateLedgers(scope.row, items.ss_key)"
                            @blur="updateLedgers(scope.row, items.ss_key)"
                          />
                        </span>
                        <span
                          v-else
                          :class="scope.row.lg_id === 0 ? 'click-text' : ''"
                          @click="onChange(item, items, scope)"
                        >{{ scope.row[items.ss_key] }}</span>
                      </span>
                    </template>
                  </vxe-table-column>
                </template>
              </BTable>
            </div>
          </div>
          <div
            v-else-if="!fileLedgers.length && rowStatus === 'RUNNING'"
            v-loading="true"
            class="actions-none"
          />
          <div v-else class="actions-none" :style="{ fontSize: styles.globalFontSize + 'px' }">
            {{ !fileList.length ? t('letUploadFile') : t('noData') }}
          </div>
        </div>
      </div>
      <div class="right-content-box">
        <div class="wrapper">
          <div class="wrapper-box" style="background: #4a4a4a">
            <template>
              <!--<div v-if="imgVisible" v-viewer.static="options" class="image-wrapper">-->
              <!--  <img ref="image" :src="currentImageUrl" alt="">-->
              <!--</div>-->
              <panZoom
                ref="panZoom"
                :options="{
                  autocenter: true,
                  transformOrigin: { x: 0.5, y: 0.5 },
                  minZoom: 0.05,
                  bounds: true,
                  boundsPadding: 0.2,
                  initialZoom: 0.05,
                }"
                selector=".right-content"
                @init="onZoomInit"
                @panstart="onPanStart"
                @panend="onPanEnd"
              >
                <div
                  :style="{
                    visibility: currentImageWidth > 0 ? '' : 'hidden',
                    width: currentImageWidth + 'px',
                    height: currentImageHeight + 'px',
                  }"
                  :class="{
                    'pan-ing': panIng,
                  }"
                  class="right-content"
                >
                  <img
                    :src="currentImageUrl"
                    @dragstart="
                      e => {
                        e.preventDefault()
                        return false
                      }
                    "
                  >
                  <template v-if="ocrSplitResults.length > 0">
                    <div
                      v-for="block in ocrSplitResults"
                      :key="block.index"
                      :class="{
                        'ocr-div': true,
                        'current-select': selectBlock.includes(block.index),
                        // 'recommended-div': checkOcrDivStyle(block.index, 'recommend'),
                        //'is-hit': true,
                      }"
                      :style="{
                        top: block.location.top + 'px',
                        left: block.location.left + 'px',
                        width: block.location.width + 'px',
                        height: block.location.height + 'px',
                        fontSize:
                          (block.location.height < 22 ? block.location.height - 2 : 20) + 'px',
                      }"
                    />
                  </template>
                  <!--</div>-->
                </div>
              </panZoom>
            </template>
            <span v-if="imgList.length > 1" class="left-icon" @click.stop.prevent="onPrevious">
              <i class="iconfont el-icon-arrow-left" />
            </span>
            <span v-if="imgList.length > 1" class="right-icon" @click.stop.prevent="onNext">
              <i class="iconfont el-icon-arrow-right" />
            </span>
            <!-- 上傳日期 / 頁數 -->
            <div class="image-info-wrapper">
              <div class="current-page">
                {{
                  t('currentPage', {
                    current: imgList.length ? imageIndex + 1 : '-',
                    total: imgList.length || '-',
                  })
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="" @click="show = false">{{ $t('button.back') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { fetchAccounts } from '@/api/master/account'
import uploadDialog from './upload.vue'
import BTable from '@/components/BTable'
import {
  getAiFiles,
  getFileActions,
  deleteFIleLedgers,
  getDocumentFileLedger,
  updateAiLedger,
  abandonedDocumentFiles,
} from '@/api/periodic/bankReconciliation'
import panZoom from '@/components/pan-zoom/component'
import { mapGetters } from 'vuex'
import AddLedgerPop from './addLedger.vue'
import dateUtil from '@/utils/date'
import dayjs from 'dayjs'
import { amountFormat } from '@/utils'
import permission from '../../../mixins/permission.vue'
const BASE = process.env.BASE_API

export default {
  components: {
    uploadDialog,
    panZoom,
    AddLedgerPop,
    BTable,
  },
  mixins: [permission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    ac_code: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      langKey: 'bankAiReconciliation.',
      show: false,
      innerVisible: false,
      form: {
        selectedAcCode: '',
        account: '',
        period: '',
        ledgerIndex: '',
        filterAbnormalLedger: false,
      },
      cs: '',
      bankList: [],
      fileList: [],
      fileLedgers: [],
      radio: '',
      currentImageWidth: 400,
      currentImageHeight: 400,
      panIng: false,
      currentImageUrl: '',
      imgInit: false,
      imgList: [],
      ledgerKeys: [],
      ocrResults: [],
      imageIndex: -1,
      showSecondaryWindow: false,
      channel: null,
      selectBlock: [],
      hoverGroup: null,
      imageLoadId: null,

      addInnerVisible: false,
      ai_document_file_ledger_id: null,
      mouseText: '',
      addPreType: false,
      changeLedger: {
        row: {},
      },
      rowStatus: '',
      rowIndex: 0,
    }
  },
  watch: {
    dialogVisible: {
      async handler(val) {
        this.show = val
        if (val) {
          this.$nextTick(() => {
            this.cs = document.querySelector('.left-content').offsetHeight - 42 + 'px'
          })
          await this.getBankList()
        }
      },
      immediate: true,
      deep: true,
    },
    ac_code: {
      handler(val) {
        this.form.selectedAcCode = val
      },
      immediate: true,
      deep: true,
    },
    show(val) {
      this.$emit('updateStatus', val)
    },
  },
  computed: {
    ...mapGetters(['language', 'styles', 'user_id', 'remoteServerInfo']),
    finFontSize() {
      return this.styles.globalFontSize + 'px'
    },
    styleColumns() {
      return [
        {
          alignment: 'center',
          ss_key: 'radio',
          ss_type: 'column',
          width: 35,
        },
        {
          alignment: 'center',
          title: 'uploadDate',
          ss_key: 'uploadDate',
          ss_type: 'column',
          width: 104,
        },
        {
          alignment: 'left',
          title: 'fileName',
          ss_key: 'file_name',
          ss_type: 'column',
          minWidth: '300px',
        },
        {
          alignment: 'center',
          title: 'result',
          ss_key: 'status',
          ss_type: 'column',
          width: 74,
        },
        {
          alignment: 'center',
          title: 'options',
          ss_key: 'options',
          ss_type: 'column',
          width: 50,
        },
      ]
    },
    ledgersStyleColumns() {
      return [
        {
          alignment: 'center',
          ss_key: 'radio',
          ss_type: 'column',
          width: 35,
        },
        {
          title: 'vc_date',
          ss_key: 'ledger_date',
          ss_type: 'column',
          width: 100,
        },
        {
          title: 'summary',
          ss_key: 'particulars',
          ss_type: 'column',
          minWidth: 180,
        },
        {
          alignment: 'right',
          title: 'amount_dr',
          ss_key: 'dr_amount',
          ss_type: 'column',
          width: 90,
        },
        {
          alignment: 'right',
          title: 'amount_cr',
          ss_key: 'cr_amount',
          ss_type: 'column',
          width: 90,
        },
        {
          title: 'options',
          ss_key: 'options',
          ss_type: 'column',
          width: 50,
        },
      ]
    },
    selectFileRow() {
      return this.fileList.find(item => item.ai_document_file_id === this.radio)
    },
    ocrSplitResults() {
      const ocrResultsCopy = JSON.parse(JSON.stringify(this.ocrResults))
      if (ocrResultsCopy[this.imageIndex] && ocrResultsCopy[this.imageIndex].length > 0) {
        return ocrResultsCopy[this.imageIndex]
      } else {
        return []
      }
    },
  },
  created() {
    this.showSecondaryWindow = true
    this.channel = new BroadcastChannel('EDAC_BroadcastChannelBankStatementPreview')
    this.channel.addEventListener('message', this.omChannelMessage)
  },
  beforeDestroy() {
    if (this.channel) {
      this.channel.removeEventListener('message', this.omChannelMessage)
      this.channel = null
    }
  },
  methods: {
    getBankList() {
      fetchAccounts({ ac_bank: 'C,S,F,P' }).then(res => {
        this.bankList = res
        if (!this.form.selectedAcCode) {
          this.form.selectedAcCode = this.bankList[0].ac_code
        }
        this.getFileList()
      })
    },
    async getRowText(scope) {
      try {
        const res = await getDocumentFileLedger(scope.row.ai_document_file_ledger_id)
        this.mouseText =
          dayjs(res.ledger.vc_date).format('DD/MM/YY') +
          ' ' +
          (res.ledger.vc_payee || '') +
          ' ' +
          res.ledger.descr
      } catch (err) {
        console.log(err)
      }
    },
    omChannelMessage(ev) {
      const { type } = ev // data
      switch (type) {
        case 'keepAlive':
          !this.showSecondaryWindow && (this.showSecondaryWindow = true)
          break
        case 'close':
          this.showSecondaryWindow && (this.showSecondaryWindow = false)
          break
        case 'ready':
          this.postMsg('init', {
            isFilePreview: this.isFilePreview,
            previewType: this.previewType,
            companyId: this.companyId,
            bankStatementFileId: this.bankStatementFileId,
            bankStatementGroupId: this.bankStatementGroupId,
          })
          this.postAlwaysIndexes()
          break
      }
    },
    postMsg(type, data, channel) {
      const c = channel || this.channel
      if (c) {
        c.postMessage(
          {
            type,
            from: 'page',
            data,
          },
          'page',
        )
      }
    },
    createSuccess() {
      this.addInnerVisible = false
      this.getFileMessage()
    },
    onAddLedger({ row, rowIndex }) {
      this.addInnerVisible = true
      this.ai_document_file_ledger_id = row.ai_document_file_ledger_id
      this.addPreType = rowIndex === 0
    },
    async deleteLedgers({ ai_document_file_ledger_id, lg_id }) {
      try {
        if (lg_id) return
        this.$confirm(this.$t('message.confirmDelete'), this.$t('message.tips'), {
          confirmButtonText: this.$t('button.confirm'),
          cancelButtonText: this.$t('button.cancel'),
          type: 'warning',
        }).then(async() => {
          await deleteFIleLedgers(ai_document_file_ledger_id)
          this.getFileMessage()
          this.$emit('delete')
        })
      } catch (err) {
        console.log(err)
      }
    },
    handleMouseenter({ row, rowIndex, column, columnIndex }, event) {
      if (row.page_in_group - 1 !== this.imageIndex) {
        this.imageIndex = row.page_in_group - 1
        this.refreshImg()
      }
      if (this.hoverGroup !== row.block_indexes) {
        this.hoverGroup = row.block_indexes
        const arr = row.block_indexes ? (row.block_indexes + '').split(',').map(i => Number(i)) : []
        const diff = this.isEqual(this.selectBlock, arr)
        if (!diff) {
          this.selectBlock = arr // row.blockIndexes ? (row.blockIndexes + '').split(',').map(i => Number(i)) : []
          this.postMsg('selectBlock', arr)
          this.postMsg('switchImage', this.imageIndex)
        }
      }
    },
    isEqual(arr1, arr2) {
      return JSON.stringify(arr1) === JSON.stringify(arr2)
    },
    goLedgerPosition() {
      const index = this.form.ledgerIndex
      const target = document.getElementById('ledger' + index)
      const parent = this.$refs.ledgersTable
      parent.scrollTop = target.offsetTop - parent.offsetTop
    },
    async getFileMessage() {
      try {
        const res = await getFileActions(this.radio)
        this.fileLedgers = this.formData(res.ledgers.filter(item => item.ledger_type !== 'BALANCE'))
        console.log(this.fileLedgers, 'this.fileLedgers')
        this.fileLedgers.map(v => {
          v.data.map(v1 => {
            // v1.ledger_date = dayjs(v1.ledger_date).format('DD/MM/YYYY')
            v1.dr_amount =
              !isNaN(Number(v1.dr_amount)) && Number(v1.dr_amount) ? amountFormat(v1.dr_amount) : ''
            v1.cr_amount =
              !isNaN(Number(v1.cr_amount)) && Number(v1.cr_amount) ? amountFormat(v1.cr_amount) : ''
            return v1
          })
          return v
        })
        const { remoteFilePath } = this.remoteServerInfo
        this.imgList = res.imgs.map(item => {
          return {
            tempUrl: remoteFilePath + item.file_path,
          }
        })
        const blockData = res.imgs.map(item => {
          return JSON.parse(item.block_data)
        })
        this.$nextTick(() => {
          this.loadImages()
        })
        this.ocrResults = blockData
        this.currentImageUrl = this.imgList.length ? this.imgList[0].url : ''
        this.imageIndex = 0
        if (this.ledgerKeys.length) {
          this.form.ledgerIndex = this.ledgerKeys[0].index
        } else {
          this.form.ledgerIndex = ''
        }
      } catch (err) {
        console.log(err)
      }
    },
    async loadImages() {
      const imageLoadId = new Date().getTime() + Math.random()
      this.imageLoadId = imageLoadId
      for (let i = 0; i < this.imgList.length; i++) {
        if (this.imageLoadId !== imageLoadId) break
        const url = this.imgList[i].tempUrl
        try {
          const img = await this.toDataURL(url)
          if (this.imageLoadId !== imageLoadId) break
          this.imgList[i].url = img.url
          this.imgList[i].width = img.width
          this.imgList[i].height = img.height
          this.imgList[i].status = 'done'
          if (i === this.imageIndex) {
            this.$nextTick(() => {
              this.refreshImg()
            })
          }
        } catch (e) {
          this.imageLinks[i].status = 'error'
        }
      }
    },
    toDataURL(src, outputFormat) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'Anonymous'
        // img.setAttribute('crossOrigin', 'anonymous')
        img.onload = function() {
          const canvas = document.createElement('CANVAS')
          const ctx = canvas.getContext('2d')
          canvas.height = this.naturalHeight
          canvas.width = this.naturalWidth
          ctx.drawImage(this, 0, 0)
          // const dataURL = canvas.toDataURL(outputFormat)
          // resolve({ url: dataURL, width: this.naturalWidth, height: this.naturalHeight })
          canvas.toBlob(blobObj => {
            const imgSrc = window.URL.createObjectURL(blobObj)
            resolve({ url: imgSrc, width: this.naturalWidth, height: this.naturalHeight })
          })
        }
        img.οnerrοr = function(e) {
          console.log(e)
          reject(e)
        }
        img.src = src
        // if (img.complete || img.complete === undefined) {
        //   img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw=='
        //   img.src = src
        // }
      })
    },
    refreshImg() {
      this.currentImageStatus = ''
      this.$nextTick(() => {
        this.imgInit = false
        this.imgVisible = false
        this.currentImageUrl = this.imgList[this.imageIndex].url
        this.currentImageWidth = this.imgList[this.imageIndex].width
        this.currentImageHeight = this.imgList[this.imageIndex].height
        this.$nextTick(() => {
          this.imgVisible = true
          this.onChangeZoomInit()
          this.currentImageStatus = this.imgList[this.imageIndex].status
          this.$nextTick(() => {
            this.onChangeZoomInit()
          })
        })
      })
    },
    onChangeZoomInit() {
      console.log('onChangeZoomInit')
      this.imgInit = true
      this.$nextTick(() => {
        this.waitPanElement()
      })
    },

    formData(arr) {
      const key = arr.map(item => {
        return (
          item.group_seq + ',' + item.account_type + ',' + item.account_no + ',' + item.currency
        )
      })
      const setKeys = [...new Set(key)]
      const obj = {}
      for (let i = 0; i < setKeys.length; i++) {
        obj[setKeys[i]] = arr.filter(
          item =>
            item.group_seq +
              ',' +
              item.account_type +
              ',' +
              item.account_no +
              ',' +
              item.currency ===
            setKeys[i],
        )
      }
      const newArr = []
      for (const key in obj) {
        newArr.push({
          date: key,
          data: obj[key],
          title: `${obj[key][0].currency} ${this.t(obj[key][0].account_type)} ${
            obj[key][0].account_no
          }`,
          dateValue: dayjs(obj[key][0].ledger_date).format('MM/YYYY'),
        })
      }
      this.ledgerKeys = newArr.map((item, index) => {
        return {
          value: `${item.title}   ${item.dateValue}`,
          index: index,
          dateValue: item.dateValue,
        }
      })
      console.log(obj, 'this.ledgerKeys')

      return newArr
    },
    changeRadio(val) {
      this.getFileMessage()
    },
    onZoomInit() {
      console.log('onZoomInit')
      // this.waitPanElement()
    },
    onPanStart() {
      this.panIng = true
    },
    onPanEnd() {
      this.panIng = false
    },
    changeBank(val) {
      this.form.selectedAcCode = val
      this.$emit('changeBank', val)
      this.getFileList()
    },
    uploadSuccess() {
      this.innerVisible = false
      this.getFileList()
      this.$emit('uploadSuccess')
    },
    async onlyGetFileList() {
      try {
        const res = await getAiFiles(this.form.selectedAcCode)
        this.fileList = res
        this.fileList.forEach(item => {
          item.uploadDate = item.created_at.split(' ')[0]
          //  item.uploadDate = dayjs(item.uploadDate).format('DD/MM/YYYY')
        })
      } catch (err) {
        console.log(err)
      }
    },
    async getFileList(isInit = true) {
      try {
        const res = await getAiFiles({
          ac_code: this.form.selectedAcCode,
          abandoned: this.form.filterAbnormalLedger ? 'Y' : 'N',
        })
        this.fileList = res
        this.fileList.forEach(item => {
          item.uploadDate = item.created_at.split(' ')[0]
          //  item.uploadDate = dayjs(item.uploadDate).format('DD/MM/YYYY')
        })
        this.rowStatus = this.fileList[this.rowIndex].status
        if (!isInit) {
          if (!this.form.filterAbnormalLedger) {
            await this.getFileMessage()
          }
          return
        }
        this.form.ledgerIndex = ''
        this.radio = ''
        if (!this.fileList.length) {
          this.fileLedgers = []
          this.ocrResults = []
          this.imgList = []
          this.currentImageUrl = ''
        } else {
          this.radio = this.fileList[0].ai_document_file_id
          await this.getFileMessage()
          if (this.ledgerKeys.length) {
            this.form.ledgerIndex = this.ledgerKeys[0].index
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async waitPanElement() {
      let i = 0
      while (!this.$refs.panZoom && i < 50) {
        i++
        await this.sleep(50)
      }
      this.$refs.panZoom && this.$refs.panZoom.autoZoom(false)
      await this.sleep(50)
      this.$refs.panZoom && this.$refs.panZoom.autoZoom(false)
    },
    sleep(time) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve()
        }, time)
      })
    },
    selectRow({ row, rowIndex }) {
      if (row.status === 'ABANDONED') return
      this.radio = row.ai_document_file_id
      this.rowIndex = rowIndex
      this.rowStatus = row.status
      this.changeRadio()
    },
    refreshAi() {
      this.getBankList()
    },
    async onPrevious() {
      if (this.imageIndex === 0) {
        this.imageIndex = this.imgList.length - 1
      } else {
        this.imageIndex--
      }
      // await this.load()
      // const imageDom = document.querySelector('.viewer-container .viewer-canvas .viewer-move')
      // if (imageDom) {
      //   imageDom.src = this.form.imageLink
      // }
      this.currentImageUrl = this.imgList[this.imageIndex].url
      this.currentImageWidth = 400
      this.currentImageHeight = 400
      this.currentImageStatus = this.imgList[this.imageIndex].status
      this.refreshImg()
    },
    onChange(item, items, scope) {
      console.log(item, items, scope)
      this.changeLedger = {
        title: item.title,
        row: scope.row,
        ss_key: items.ss_key,
      }
      this.$nextTick(() => {
        this.$refs.inputRef[0].focus()
      })
    },
    updateLedgers(row, key) {
      this.changeLedger = {
        row: {},
        ss_key: '',
      }
      if (
        (key === 'cr_amount' && (Number(row.cr_amount) <= 0 || isNaN(Number(row.cr_amount)))) ||
        (key === 'dr_amount' && (Number(row.dr_amount) <= 0 || isNaN(Number(row.dr_amount))))
      ) {
        return this.getFileMessage()
      }
      if (key === 'cr_amount' && row.dr_amount) {
        row.dr_amount = ''
      }
      if (key === 'dr_amount' && row.cr_amount) {
        row.cr_amount = ''
      }
      console.log(isNaN(Number(row.cr_amount)), 'isNaN(Number(row.cr_amount))')

      row.cr_amount = row.cr_amount ? amountFormat(row.cr_amount) : ''
      row.dr_amount = row.dr_amount ? amountFormat(row.dr_amount) : ''
      console.log(row.ledger_date, 'ledger_date')

      updateAiLedger({
        ...row,
        cr_amount: row.cr_amount ? row.cr_amount.replace(/,/g, '') : '',
        dr_amount: row.dr_amount ? row.dr_amount.replace(/,/g, '') : '',
        ledger_date: dayjs(row.ledger_date).format('YYYY-MM-DD'),
      }).then(res => {
        this.$message({
          message: this.$t('message.success'),
          type: 'success',
        })
      })
    },
    inputFocus(row, key) {
      row[key] = row[key] ? row[key].replace(/,/g, '') : ''
    },
    async onNext() {
      if (this.imageIndex === Number(this.imgList.length - 1)) {
        this.imageIndex = 0
      } else {
        this.imageIndex++
      }
      this.currentImageUrl = this.imgList[this.imageIndex].url
      this.currentImageWidth = this.imgList[this.imageIndex].width
      this.currentImageHeight = this.imgList[this.imageIndex].height
      this.currentImageStatus = this.imgList[this.imageIndex].status
      this.refreshImg()
      // await this.load()
      // const imageDom = document.querySelector('.viewer-container .viewer-canvas .viewer-move')
      // if (imageDom) {
      //   imageDom.src = this.form.imageLink
      // }
    },
    filterClassFunction(row) {
      let FAILURE = ''
      if (row.row.status === 'FAILURE') {
        FAILURE = 'FAILED'
      }
      if (row.row.ai_document_file_id === this.radio) {
        return 'vxe-body--row-click' + ' ' + FAILURE
      }
      if (row.$rowIndex % 2 === 0) {
        return 'table-stripe' + ' ' + FAILURE
      }
    },
    async deleteFiles(row) {
      if (row.status === 'ABANDONED') return
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${row.file_name}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          return new Promise((resolve, reject) => {
            abandonedDocumentFiles({ ai_document_file_id: row.ai_document_file_id })
              .then(res => {
                this.getFileList()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    filterAbnormalLedger() {
      this.getFileList(false)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog__title {
    font-size: 13px !important;
  }

  .el-dialog__body {
    padding-bottom: 0;
  }

  .ledger-column-table {
    .vxe-cell {
      height: 100%;

      span {
        display: block;
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .vxe-table--body-wrapper {
    max-height: 100% !important;
  }
  .vxe-table--header,
  .vxe-table--body {
    width: 100% !important;
  }
}

.bank-monthly-dialog {
  top: -30px !important;

  ::v-deep {
    .el-dialog .el-dialog__body {
      max-height: 70vh !important;
      height: 70vh !important;
      overflow-y: hidden !important;
    }
  }
}

.content {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  overflow-y: hidden;
  .left-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 1040px;
  }
}

.bank-form-item {
  padding-right: 10px !important;

  .form-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.ledger-key {
  margin-left: 4px;
}

.table-box {
  display: flex;
  height: calc(100% - 41px);
  .files-actions {
    margin: 0 20px;
    border: 1px solid #e8eaec;
    // height: 564px;
    width: calc(50% - 40px);
    overflow-x: hidden;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #3e97dd;
      color: white;
      line-height: 27px;
      font-size: 12px;
      padding: 0 10px;
    }

    ::v-deep {
      .vxe-table--main-wrapper .vxe-table--body-wrapper {
        height: auto !important;
      }
    }
  }

  .actions-none {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #e8eaec;
    margin: 0 20px;
    width: 50%;
    //  font-size: 20px !important;
  }

  .ai-analysis {
    width: 50%;
    height: 100%;
  }

  .FAILED {
    color: #f56c6c;
  }
}

.right-content-box {
  min-width: 540px;
  width: 35%;
  flex-shrink: 0;
  background: #4a4a4a;
  border-radius: 2px;
  overflow: auto;
}

.wrapper {
  //  padding: 4px 20px 20px;
  overflow: hidden;
  text-align: center;
  height: 100%;

  .wrapper-box {
    //max-height: 70vh;
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;

    .example {
      position: absolute;
      top: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .left-icon,
    .right-icon {
      position: absolute;
      top: 45%;
      width: 34px;
      height: 34px;
      display: flex;
      // background-color: #FAFAFA;
      background-color: rgba(250, 250, 250, 0.8);
      justify-content: center;
      cursor: pointer;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
    }

    .left-icon {
      left: 10px;
    }

    .right-icon {
      right: 10px;
    }

    > div {
      width: 100%;

      img {
        // width: 50%;
        //visibility: hidden;
      }
    }

    .image-info-wrapper {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      text-align: left;
      padding: 10px 40px;
      //background: #333333;
      //opacity: 0.8;
      background: rgba(51, 51, 51, 0.8);
      color: #ffffff;

      //height: 68px;
      div {
        line-height: 19px;
        margin-bottom: 10px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
  }
}

.ENABLE {
  ::v-deep {
    input {
      width: 100%;
    }

    .el-date-editor {
      width: 100%;
    }

    .el-input__inner {
      padding: 0;
      padding-left: 5px;
    }

    .el-input__prefix {
      display: none;
    }
  }
}

.ocr-div {
  position: absolute;
  // opacity: 0.2;

  background: rgba(255, 193, 5, 0.2);
  border: 1px solid #ffc105;
  color: #4c5362;
  //visibility: hidden;
  line-height: 1;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;

  &.current-select {
    // visibility: unset;
    opacity: 1;
  }

  &:hover {
    opacity: 1;
  }
}

.ocr-div.recommended-div {
  background-color: #d100f5;
}

.ocr-div.selected-div {
  background-color: #48c760;
}

.ocr-div.show-words {
  background-color: #ff980033;
  color: #4c5362;
  opacity: 1;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  visibility: unset;
}

.ocr-div.recommended-div.show-words {
  background-color: #d100f533;
  color: #4c5362;
}

.ocr-div.selected-div.show-words {
  //background-color: #48C76033;
  background-color: #ff980033;
  color: #4c5362;
}

.ocr-div.is-hit.show-words {
  background-color: #48c76033;
}

.click-text:hover {
  color: #3e97dd;
  cursor: pointer;
  text-decoration: underline;
}

.FAILURE {
  color: #f56c6c;
}
</style>
