<template>
  <div v-if="!hookVisible">
    <right v-loading="loading">
      <el-main slot="content">
        <el-form ref="form" :model="form" class="form mini-form" label-width="120px">
          <!-- 樣式 -->
          <el-form-item :label="$t('setting.printout.label.style')">
            <el-select
              v-model="psg_code"
              :placeholder="$t('setting.printout.label.style')"
              @change="onFetch(0)"
            >
              <el-option
                v-for="item in styleList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- 樣式名稱 -->
          <el-form-item
            :label="$t('setting.printout.label.styleName')"
            :rules="rules"
            prop="style_name"
          >
            <el-input v-model="form.style_name" />
          </el-form-item>
          <!-- 紙大小 -->
          <pageSizeForm
            :page-style.sync="form.page_style"
            :page-width.sync="form.page_width"
            :page-height.sync="form.page_height"
            :page-orientation.sync="form.page_orientation"
          />
          <!-- 語言 -->
          <el-form-item :label="$t('setting.printout.label.language')">
            <el-select v-model="form.language" :placeholder="$t('setting.printout.label.language')">
              <el-option :label="$t('setting.printout.content.chinese')" value="zh-hk" />
              <el-option :label="$t('setting.printout.content.english')" value="en" />
            </el-select>
          </el-form-item>
          <!-- 行數 -->
          <el-form-item :label="$t('setting.printout.label.rowNum')">
            <el-input-number v-model="form.row_num" :min="1" :max="20" :controls="false" />
          </el-form-item>
          <!-- 列數 -->
          <el-form-item :label="$t('setting.printout.label.colNum')">
            <el-input-number v-model="form.col_num" :min="1" :max="20" :controls="false" />
          </el-form-item>
          <!-- 邊界 -->
          <el-form-item :label="$t('setting.printout.label.margin')">
            <el-input-number v-model="form.margin" :min="0" :controls="false" />
            <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
          </el-form-item>
          <!-- 分隔線 -->
          <el-form-item :label="$t('setting.printout.label.dividingLine')">
            <el-checkbox v-model="form.dividing_line" :true-label="1" :false-label="0" />
          </el-form-item>
          <!-- 字體 -->
          <el-form-item :label="$t('setting.printout.label.fontSize')">
            <el-input-number v-model="form.font_size" :min="0" :controls="false" />
          </el-form-item>
          <!-- 操作欄 -->
          <actions
            :style-list="styleList"
            :on-fetch="onFetch"
            :on-save="onSave"
            :on-delete="onDelete"
            :on-copy="onCopy"
          />
        </el-form>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'
import signTable from '../components/signTable'
import signForm from '../components/signForm'
import marginForm from '../components/marginForm'
import pageSizeForm from '../components/pageSizeForm'
// import fontSizeForm from '../components/fontSizeForm'
import columnsPositions from '../components/columnsPositions'
import actions from '../components/actions'
import Divider from '@/components/Divider'

export default {
  name: 'SettingPrintoutDailyMailingLabel',
  components: {
    right,
    signTable,
    signForm,
    marginForm,
    pageSizeForm,
    // fontSizeForm,
    columnsPositions,
    Divider,
    actions,
  },
  mixins: [common],
  data() {
    return {
      form: {},
      defaultForm: {
        style_name: this.$t('print.mailingLabel'),
        page_style: '',
        page_width: 297,
        page_height: 210,
        page_orientation: 'L',
        language: 'zh-hk',
        font_size: 12,
        dividing_line: '1',
        margin: 2,
        row_num: 5,
        col_num: 5,
      },
      ps_code: 'pdlabel',
      psg_code: '',
    }
  },
  computed: {},
  created() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-form-item {
    margin-bottom: 5px !important;
    height: 25px;
    line-height: 25px;
  }
}
</style>
