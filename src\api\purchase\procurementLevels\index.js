import request from '@/utils/request'

/**
 * 新增採購等級
 * @param {string} pro_level_code 採購等級編號
 * @param {string} pro_level_name_cn 採購等級中文
 * @param {string} pro_level_name_en 採購等級英文
 * @param {string} pro_no_format 採購單號生成規則
 * @param {number} min_amount 金額範圍(起始)
 * @param {number} [max_amount] 金額範圍(結束)
 * @param {number} suppliers_at_least 供應商最少個數
 * @param {string} [invitation_letter_code] 邀請信類型
 * @param {string} [reject_letter_code] 不接納通知信
 * @param {string} [accept_letter_code] 接納通知信
 * @param {string} reason_for_suppliers 必須填寫邀請少於限定的供應商個數原因,可填N或Y
 * @param {string} reason_for_low_price 必須填寫不選擇最低報價原因,可填N或Y
 * @param {string} review 是否需要人手覆核,可填N或Y
 * @param {string} review_handlers_json 覆核人,type=F時staff_id傳null,type=S時mandatory傳Y,格式: [[{"type":"F","mandatory":"Y","staff_id":"null","auto":"N"},{"type":"S","mandatory":"Y","staff_id":"1","auto":"N"}],[]]
 * @param {string} approve 是否需要覆核人手審批,可填N或Y
 * @param {string} approve_handlers_json 審批人,type=F時staff_id傳null,type=S時mandatory傳Y,格式: [[{"type":"F","mandatory":"Y","staff_id":"null","auto":"N"},{"type":"S","mandatory":"Y","staff_id":"1","auto":"N"}],[]]
 * @return {Promise}
 */
export function createProcurementLevel({
  pro_level_code,
  pro_level_name_cn,
  pro_level_name_en,
  pro_no_format,
  min_amount,
  max_amount,
  suppliers_at_least,
  invitation_letter_code,
  reject_letter_code,
  accept_letter_code,
  reason_for_suppliers,
  reason_for_low_price,
  review,
  review_handlers_json,
  approve,
  approve_handlers_json,
}) {
  return request({
    url: '/procurement-levels/actions/create',
    method: 'post',
    data: {
      pro_level_code,
      pro_level_name_cn,
      pro_level_name_en,
      pro_no_format,
      min_amount,
      max_amount,
      suppliers_at_least,
      invitation_letter_code,
      reject_letter_code,
      accept_letter_code,
      reason_for_suppliers,
      reason_for_low_price,
      review,
      review_handlers_json,
      approve,
      approve_handlers_json,
    },
  })
}

/**
 * 更新採購等級
 * @param {number} pro_level_id 採購等級id
 * @param {string} pro_level_code 採購等級編號
 * @param {string} pro_level_name_cn 採購等級中文
 * @param {string} pro_level_name_en 採購等級英文
 * @param {string} pro_no_format 採購單號生成規則
 * @param {number} min_amount 金額範圍(起始)
 * @param {number} [max_amount] 金額範圍(結束)
 * @param {number} suppliers_at_least 供應商最少個數
 * @param {string} [invitation_letter_code] 邀請信類型
 * @param {string} [reject_letter_code] 不接納通知信
 * @param {string} [accept_letter_code] 接納通知信
 * @param {string} reason_for_suppliers 必須填寫邀請少於限定的供應商個數原因,可填N或Y
 * @param {string} reason_for_low_price 必須填寫不選擇最低報價原因,可填N或Y
 * @param {string} review 是否需要人手覆核,可填N或Y
 * @param {string} review_handlers_json 覆核人,type=F時staff_id傳null,type=S時mandatory傳Y,格式: [[{"type":"F","mandatory":"Y","staff_id":"null","auto":"N"},{"type":"S","mandatory":"Y","staff_id":"1","auto":"N"}],[]]
 * @param {string} approve 是否需要覆核人手審批,可填N或Y
 * @param {string} approve_handlers_json 審批人,type=F時staff_id傳null,type=S時mandatory傳Y,格式: [[{"type":"F","mandatory":"Y","staff_id":"null","auto":"N"},{"type":"S","mandatory":"Y","staff_id":"1","auto":"N"}],[]]
 * @return {Promise}
 */
export function editProcurementLevel({
  pro_level_id,
  pro_level_code,
  pro_level_name_cn,
  pro_level_name_en,
  pro_no_format,
  min_amount,
  max_amount,
  suppliers_at_least,
  invitation_letter_code,
  reject_letter_code,
  accept_letter_code,
  reason_for_suppliers,
  reason_for_low_price,
  review,
  review_handlers_json,
  approve,
  approve_handlers_json,
}) {
  return request({
    url: '/procurement-levels/actions/update',
    method: 'post',
    data: {
      pro_level_id,
      pro_level_code,
      pro_level_name_cn,
      pro_level_name_en,
      pro_no_format,
      min_amount,
      max_amount,
      suppliers_at_least,
      invitation_letter_code,
      reject_letter_code,
      accept_letter_code,
      reason_for_suppliers,
      reason_for_low_price,
      review,
      review_handlers_json,
      approve,
      approve_handlers_json,
    },
  })
}

/**
 * 返回採購等級列表
 * @return {Promise}
 */
export function fetchProcurementLevels() {
  return request({
    url: '/procurement-levels',
    method: 'get',
    params: {},
  })
}

/**
 * 刪除採購等級詳情
 * @param {number} pro_level_id 採購等級id
 * @return {Promise}
 */
export function deleteProcurementLevel(pro_level_id) {
  return request({
    url: '/procurement-levels/actions/delete',
    method: 'post',
    data: {
      pro_level_id,
    },
  })
}

/**
 * 返回採購等級詳情
 * @param {number} pro_level_id 採購等級id
 * @return {Promise}
 */
export function getProcurementLevel(pro_level_id) {
  return request({
    url: '/procurement-levels/actions/inquire',
    method: 'get',
    params: {
      pro_level_id,
    },
  })
}

/**
 * 返回採購等級詳情
 * @param {number} pro_level_id 採購等級id
 * @param {string} apply_date 申請日期
 * @param {string} [budget_code] 預算編號
 * @return {Promise}
 */
export function generateProcurementNo({ pro_level_id, apply_date, budget_code }) {
  return request({
    url: '/procurement-levels/actions/generate-no',
    method: 'get',
    params: {
      pro_level_id,
      apply_date,
      budget_code,
    },
  })
}
