<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 日期 -->
      <el-form-item :rules="rules" :label="$t('stock.sales.label.date')" prop="date">
        <el-date-picker
          v-model="form.date"
          :format="styles.dateFormat"
          :placeholder="$t('placeholder.selectDate')"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"
          type="date"
          @change="getFyCode()"
        />
        <!--
          :picker-options="pickerOptions"
          -->
      </el-form-item>
      <!-- 類別 -->
      <el-form-item :rules="rules" :label="$t('stock.sales.label.type')" prop="type">
        <el-select v-model="form.type">
          <el-option
            v-for="item in types"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 支票編號  -->
      <el-form-item :label="$t('stock.sales.label.chq_no')" prop="chq_no">
        <el-input v-model="form.chq_no" clearable />
      </el-form-item>
      <!-- 傳票編號  -->
      <el-form-item :label="$t('stock.sales.label.voucher_no')" prop="voucher_no">
        <el-input v-model="form.voucher_no" clearable />
      </el-form-item>
      <!-- 金額 -->
      <el-form-item :label="$t('stock.sales.label.amount')" prop="amount" class="amount">
        <ENumeric
          ref="amount"
          v-model="form.amount"
          :controls="false"
          :precision="2"
          :disabled="false"
          :read-only="true"
        />
      </el-form-item>
      <!-- 貨品明細 -->
      <el-form-item :label="$t('stock.sales.label.items')">
        <el-table :data="stocks" class="sales-table" border style="width: 100%">
          <el-table-column
            :label="$t('stock.sales.label.sk_code')"
            prop="sk_code"
            align="center"
            width="120"
          />
          <el-table-column
            :label="$t('stock.sales.label.items')"
            :prop="language === 'en' ? 'sk_name_en' : 'sk_name_cn'"
            align="left"
            header-align="center"
            min-width="100"
          />
          <el-table-column
            :label="$t('stock.sales.label.count')"
            align="center"
            class-name="count"
            width="80"
            prop="count"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                v-model="scope.row.count"
                :controls="false"
                :disabled="false"
                :precision="0"
                :min="0"
                :is-change="false"
                @change.native="getRowAmount(scope.row, scope.$index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.sales.label.price')"
            class-name="price"
            align="center"
            width="100"
            prop="sales"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                :ref="'sales' + scope.$index"
                v-model="scope.row.sales_price"
                :controls="false"
                :precision="2"
                :min="0"
                :disabled="false"
                :class="[
                  {
                    noEqual: !priceEqual(scope.row),
                  },
                ]"
                @change.native="getRowAmount(scope.row, scope.$index)"
                @blur.native="isChange(scope.row, scope.$index)"
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.sales.label.amount')"
            width="100"
            align="right"
            header-align="center"
            prop="rowAmount"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                :ref="'rowAmount' + scope.$index"
                v-model="scope.row.rowAmount"
                :controls="false"
                :precision="2"
                :min="0"
                :disabled="false"
                :read-only="true"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getSales, updateSales, createSales } from '@/api/stock/sales'
import { getStocks } from '@/api/stock/itemSetup'
import { reject } from 'q'
import { searchByDate, searchTheDate } from '@/api/master/years'

import ENumeric from '@/components/ENumeric'

import { toDecimal } from '@/utils'

export default {
  name: 'StockSalesAdd',
  components: {
    ENumeric,
  },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
  },
  data() {
    const that = this
    return {
      form: {
        sales_invoice_id: '',
        fy_code: '',
        date: '',
        type: '',
        chq_no: '',
        voucher_no: '',
        stocks_json: '',
        amount: '',
      },
      defaultForm: {
        sales_invoice_id: '',
        fy_code: '',
        date: '',
        type: '',
        chq_no: '',
        voucher_no: '',
        stocks_json: '',
        amount: '',
      },
      rules: [
        {
          // 必填
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      stocks: [],
      unmodifiedStocks: [],
      loading: true,
      first_day: '',
      last_day: '',

      pickerOptions: {
        disabledDate(time) {
          if (that.first_day && that.last_day) {
            return (
              time.getTime() > that.last_day.getTime() || time.getTime() < that.first_day.getTime()
            )
          }
          return true
        },
      },
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    types() {
      return [
        {
          value: 'S',
          label: this.$t('stock.label.sales'),
        },
        {
          value: 'D',
          label: this.$t('stock.label.damagedOrLost'),
        },
        {
          value: 'U',
          label: this.$t('stock.label.internalUse'),
        },
        {
          value: 'W',
          label: this.$t('stock.label.writeOff'),
        },
      ]
    },
    isNew() {
      return !this.editObject
    },
  },
  watch: {
    editObject() {
      this.initData()
    },
    fyCode: function(newFyCode) {
      if (this.isNew) {
        this.form.fy_code = newFyCode
        this.loadDateRange().then(() => {
          this.initForm()
          const currentDate = new Date(this.form.date + ' 00:00')
          if (!this.inDateRange(currentDate)) {
            this.form.date = ''
          }
        })
      }
    },
  },
  created() {
    this.initData()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    inDateRange(date) {
      if (this.first_day && this.last_day) {
        return (
          date.getTime() <= this.last_day.getTime() && date.getTime() >= this.first_day.getTime()
        )
      }
      return false
    },
    getRowAmount(row, index) {
      new Promise((resolve, reject) => {
        row.rowAmount = toDecimal(row.count * row.sales_price)
        this.$refs['rowAmount' + index].currentVal = row.rowAmount
        this.form.amount = this.stocks
          .map(row => row.rowAmount)
          .reduce((acc, cur) => toDecimal(cur + acc), 0) // 計算總額
        this.$refs['amount'].currentVal = this.form.amount
        this.forceUpdate()
        resolve()
      }).catch(err => {
        reject(err)
      })
    },
    isChange(row, index) {},
    getFyCode() {
      new Promise(() => {
        if (this.form.date !== null) {
          const date = this.form.date
          searchByDate(date).then(res => {
            console.log('searchByDate', res)
            if (res.fy_code === this.fyCode) {
              this.form.fy_code = res.fy_code
            } else {
              this.$message.error(this.$t('message.dateError'))
              this.form.date = null
            }
          })
        }
      })
    },
    checkRequired(rule, value, callback) {},
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getSales(this.editObject.sales_invoice_id)
            .then(res => {
              console.log('getSales', res)
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.mid = res.stocks
              this.stocks = []
            })
            .then(() => getStocks(this.form.fy_code))
            .then(res => {
              // console.log('getStocks', res)
              // res.forEach(ele1 => {
              //   this.mid.forEach(ele2 => {
              //     if (ele1.sk_code === ele2.sk_code) {
              //       ele1.sales_price = ele2.price
              //       ele1.count = ele2.qty
              //     } else if (ele1.count === undefined) {
              //       ele1.count = 0
              //     }
              //     ele1.rowAmount = ele1.sales_price * ele1.count
              //   })
              // })
              // this.stocks = res

              const newStocks = []
              const unmodifiedStocks = {}
              res.forEach(itemO => {
                // 未修改的價格
                unmodifiedStocks[itemO.sk_code] = Number(itemO.sales_price)

                const newItem = Object.assign({}, itemO)
                const item = this.mid.find(i => i.sk_code === itemO.sk_code)
                if (item) {
                  newItem.sales_price = item.price == null ? 0 : Number(item.price)
                  newItem.count = item.qty == null ? 0 : Number(item.qty)
                } else {
                  newItem.count = 0
                }

                newItem.count = Number(newItem.count)
                newItem.sales_price = Number(newItem.sales_price)
                newItem.rowAmount = toDecimal(newItem.sales_price * newItem.count)
                newStocks.push(newItem)
              })
              this.stocks = newStocks
              this.unmodifiedStocks = unmodifiedStocks

              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.stocks = []
          const unmodifiedStocks = {}
          getStocks(this.fyCode).then(res => {
            res.forEach(ele => {
              unmodifiedStocks[ele.sk_code] = Number(ele.sales_price)

              ele.count = Number(ele.count)
              ele.sales_price = Number(ele.sales_price)
              ele.count = 0
              ele.rowAmount = toDecimal(ele.count * ele.sales_price)
            })
            this.stocks = res
            this.unmodifiedStocks = unmodifiedStocks
          })
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.loadDateRange()
        .then(this.initForm)
        .then(() => {
          //
        })
        .finally(() => {
          this.$refs['amount'].currentVal = this.form.amount
          this.loading = false
        })
    },
    loadDateRange() {
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: this.fyCode })
          .then(res => {
            this.first_day = new Date(res.the_first_day + ' 00:00')
            this.last_day = new Date(res.the_last_day + ' 00:00')
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        if (!this.stocks.length || this.stocks.length === 0) {
          this.$message.error(this.$t('message.theGoodsDoNotExist'))
          return
        }
        const sales_invoice_id = this.form.sales_invoice_id
        const fy_code = this.form.fy_code
        const date = this.form.date
        const type = this.form.type
        const chq_no = this.form.chq_no
        const voucher_no = this.form.voucher_no
        const stocks_json = []
        this.stocks.forEach(ele => {
          stocks_json.push({ sk_code: ele.sk_code, qty: ele.count, price: ele.sales_price })
        })
        const stocks_json_string = JSON.stringify(stocks_json)
        if (this.editObject) {
          // 編輯
          updateSales({
            sales_invoice_id: sales_invoice_id,
            type: type,
            date: date,
            chq_no: chq_no,
            voucher_no: voucher_no,
            stocks_json: stocks_json_string,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        } else {
          // 新增
          createSales({
            fy_code: fy_code,
            date: date,
            type: type,
            chq_no: chq_no,
            voucher_no: voucher_no,
            stocks_json: stocks_json_string,
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    priceEqual(row) {
      const code = row.sk_code
      console.log('priceEqual', this.unmodifiedStocks[code], row.sales_price)
      if (this.unmodifiedStocks[code]) {
        return this.unmodifiedStocks[code] === row.sales_price
      } else {
        return false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
/*.el-table th div, .el-table th>.cell {*/
/*    -webkit-box-sizing: border-box;*/
/*    display: block;*/
/*}*/
/*.amount .el-form-item__content span{*/
/*  color:#606266*/
/*}*/

.sales-table {
  /deep/ {
    td.price,
    td.count {
      .cell {
        padding: 0;
      }
    }
    td.count {
      .vue-numberic {
        text-align: center;
      }
    }
    td.price {
      .noEqual {
        color: red;
      }
    }
  }
}
</style>
