<template>
  <div class="fundInfo">
    <el-form
      v-cloak
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- ---------- 預算編號 ---------- -->
      <el-form-item
        :rules="codeRules"
        :label="$t(langKey + 'budget_code')"
        :class="{ 'code-rules-error': haveExist }"
        prop="budget_code"
      >
        <el-input v-model="form.budget_code" clearable />
      </el-form-item>
      <!-- ---------- 中文名稱 ---------- -->
      <el-form-item :rules="rules" :label="$t(langKey + 'name_cn')" prop="name_cn">
        <el-input v-model="form.name_cn" clearable />
      </el-form-item>
      <!-- ---------- 英文名稱 ---------- -->
      <el-form-item :rules="rules" :label="$t(langKey + 'name_en')" prop="name_en">
        <el-input v-model="form.name_en" clearable />
      </el-form-item>
      <!-- ---------- 上級 ---------- -->
      <el-form-item :label="$t(langKey + 'parent')" prop="parent_budget_id">
        <el-select
          v-model="form.parent_budget_id"
          :placeholder="$t('placeholder.select')"
          style="width: 100%"
          @change="changeParentType"
        >
          <el-option
            :label="$t('budget.budgetSet.label.parent_default')"
            :disabled="true"
            value=""
          />
          <el-option
            v-for="item in parentList"
            :key="item.budget_id"
            :value="item.budget_id"
            :disabled="parentDisable(item)"
            :label="conversionParentStaffType(item)"
            v-html="conversionParentStaffType(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- ---------- 位置 ---------- -->
      <el-form-item :label="$t(langKey + 'position')" prop="seq">
        <el-select
          v-model="form.seq"
          :placeholder="$t('placeholder.select')"
          style="width: 100%"
          @change="changePosition"
        >
          <el-option
            v-for="(item, i) in sepOptions"
            :key="i"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.budget_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>

          <el-option :label="$t('budget.budgetSet.label.seq_default')" value="" />
        </el-select>
      </el-form-item>
      <!-- ---------- 收支類別 ---------- -->
      <el-form-item :rules="rules" :label="$t(langKey + 'EIType')" prop="budget_IE">
        <el-select
          v-model="form.budget_IE"
          :placeholder="$t('placeholder.select')"
          @change="changePosition"
        >
          <el-option
            v-for="(item, i) in IETypes"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- ---------- 建議金額 ---------- -->
      <el-form-item
        v-if="showProposedAmount"
        :rules="rules"
        :label="$t(langKey + 'proposed_amount')"
        prop="proposed_amount"
      >
        <ENumeric v-model="form.proposed_amount" :min="0" style="width: 180px" />
      </el-form-item>
      <!-- ---------- 審批金額 ---------- -->
      <el-form-item
        v-if="showApprovedAmount"
        :rules="rules"
        :label="$t(langKey + 'approved_amount')"
        prop="approved_amount"
      >
        <ENumeric v-model="form.approved_amount" :min="0" :disabled="false" style="width: 180px" />
      </el-form-item>
      <!-- ---------- 上次修訂 ---------- -->
      <el-form-item
        v-if="showLastProposedAmount"
        :rules="rules"
        :label="$t(langKey + 'last_proposed_amount')"
        prop="last_proposed_amount"
      >
        <ENumeric
          v-model="form.last_proposed_amount"
          :min="0"
          :disabled="false"
          style="width: 180px"
        />
      </el-form-item>
      <!-- ---------- 上次審批 ---------- -->
      <el-form-item
        v-if="showLastApprovedAmount"
        :rules="rules"
        :label="$t(langKey + 'last_approved_amount')"
        prop="last_approved_amount"
      >
        <ENumeric
          v-model="form.last_approved_amount"
          :min="0"
          :disabled="false"
          style="width: 180px"
        />
      </el-form-item>
      <!-- ---------- 會計賬 ---------- -->
      <el-form-item :label="$t(langKey + 'budget_account')">
        <accountSelect :data="accountList" :fy-code="fyCode" />
      </el-form-item>
      <!-- ---------- 狀態 ---------- -->
      <el-form-item :label="$t(langKey + 'budget_stage')" prop="budget_stage">
        <el-select
          v-model="form.budget_stage"
          :disabled="true"
          :placeholder="$t('placeholder.select')"
        >
          <el-option
            v-for="item in stageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- ---------- 活躍年度 ---------- -->
      <el-form-item :label="$t(langKey + 'budget_active')">
        <el-checkbox v-model="form.budget_active" true-label="Y" false-label="N" />
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { searchStaffs } from '@/api/assistance/staff'
import { createBudget, editBudget, fetchBudgetTree, getBudget } from '@/api/budget'
import { convertTreeToList } from '@/utils'
import accountSelect from './account'
import ENumeric from '@/components/ENumeric'

export default {
  name: 'BudgetSettingAdd',
  components: {
    accountSelect,
    ENumeric,
  },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      langKey: 'budget.budgetSet.label.',
      form: {
        fy_code: '',
        budget_type: '',
        budget_code: '',
        budget_IE: '',
        name_cn: '',
        name_en: '',
        budget_active: 'Y',
        seq: '', // 以上必填
        budget_stage: '', // budget_type=G，必填
        amount: 0,
        parent_budget_id: '',
        budget_account: '',
        manager_staffs_json: '',
        member_staffs_json: '',
        approve_staff_id: '',

        proposed_amount: 0,
        approved_amount: 0,
        last_proposed_amount: 0,
        last_approved_amount: 0,
      },
      defaultForm: {
        fy_code: '',
        budget_type: '',
        budget_code: '',
        budget_IE: '',
        name_cn: '',
        name_en: '',
        budget_active: 'Y',
        seq: '', // 以上必填
        budget_stage: '', // budget_type=G，必填
        amount: 0,
        parent_budget_id: '',
        budget_account: '',
        manager_staffs_json: '',
        member_staffs_json: '',
        approve_staff_id: '',

        proposed_amount: 0,
        approved_amount: 0,
        last_proposed_amount: 0,
        last_approved_amount: 0,
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      sepOptions: [],
      parentList: [],
      seqStartLevel: 1,
      loading: true,

      canChangeType: false,
      staffList: [],
      accountList: [],
      amountShow: {
        proposed_amount: ['X', 'S', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D'],
        approved_amount: ['X', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D'],
        last_proposed_amount: ['X', 'R', 'T', 'K', 'B', 'D'],
        last_approved_amount: ['X', 'K', 'B', 'D'],
      },
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    IETypes() {
      const arr = []
      let type = 0
      /*
       * 1 I
       * 2 E
       * 3 IE
       * 4 B
       * */
      // switch (this.budget_type) {
      //   case 'F':
      //     type = 4
      //     break
      //   case 'G':
      //     if (!this.editParent || (this.editParent && this.editParent.budget_IE === 'B')) {
      //       type = 3
      //     } else {
      //       type = this.editParent.budget_IE === 'I' ? 1 : 2
      //     }
      //     break
      //   case 'C':
      //   case 'D':
      //     type = this.editParent.budget_IE === 'I' ? 1 : 2
      //     break
      // }
      const pIE = (this.editParent && this.editParent.budget_IE) || 'B'
      switch (pIE) {
        case 'I':
          type = 1
          break
        case 'E':
          type = 2
          break
        case 'B':
          type = 4
          break
      }
      if (type === 1 || type > 2) {
        arr.push({
          label: this.$t('budget.budgetSet.label.income'),
          value: 'I',
        })
      }
      if (type > 1) {
        arr.push({
          label: this.$t('budget.budgetSet.label.expense'),
          value: 'E',
        })
      }
      // if (type === 4) {
      //   arr.push({
      //     label: this.$t('budget.budgetSet.label.incomeAndExpenditure'),
      //     value: 'B'
      //   })
      // }
      if (type < 4 && this.form.budget_IE === 'B') {
        this.$set(this.form, 'budget_IE', '')
      }
      return arr
    },
    budget_type() {
      if (this.editParent) {
        switch (this.editParent.budget_type) {
          case 'F':
            return this.isBudgetGroup ? 'G' : 'F'
          case 'G':
          case 'C':
            return 'C'
          default:
            throw new Error('budget type error')
        }
      } else {
        return this.isBudgetGroup ? 'G' : 'F'
      }
    },
    stageOptions() {
      const s = ['X', 'S', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D']
      return s.map(key => {
        return {
          label: this.$t('budget.budgetSet.label.stage_' + key.toLowerCase()),
          value: key,
        }
      })
    },
    showProposedAmount() {
      return true
      // if (this.form.parent_budget_id) {
      //   return this.showAmountInput('proposed_amount')
      // }
      // return false
    },
    showApprovedAmount() {
      return true
      // if (this.form.parent_budget_id) {
      //   return this.showAmountInput('approved_amount')
      // }
      // return false
    },
    showLastProposedAmount() {
      return true
      // if (this.form.parent_budget_id) {
      //   return this.showAmountInput('last_proposed_amount')
      // }
      // return false
    },
    showLastApprovedAmount() {
      return true
      // if (this.form.parent_budget_id) {
      //   return this.showAmountInput('last_approved_amount')
      // }
      // return false
    },
    codeRules() {
      return [
        {
          required: true,
          validator: this.checkCode,
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        console.log('checkCode', !value)
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.budget_code === i.budget_code) {
          return
        }
        codeArr.push(i.budget_code)
        if (i.children) {
          codeArr.push(...this.getCodeArr(i.children))
        }
      })
      return codeArr
    },
    changePosition() {
      this.forceUpdate()
    },
    conversionParentStaffType(item, html, startLevel = 1) {
      let text = this.language === 'en' ? item.name_en : item.name_cn
      if (html) {
        text = '&nbsp;'.repeat((item.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    changeParentType() {
      // if (!this.form.parent_budget_id) {
      //   this.form.seq = ''
      //   return
      // }

      const parent = this.parentList.find(i => i.budget_id === this.form.parent_budget_id)
      if (parent) {
        this.form.budget_stage = parent.budget_stage
      }
      const parent_budget_id = this.form.parent_budget_id
      const budget_id = this.editObject ? this.form.budget_id : undefined
      const fy_code = this.fyCode
      // const type = 'D'
      fetchBudgetTree({
        parent_budget_id,
        budget_id,
        fy_code,
        // type,
        only_node: '1',
      }).then(res => {
        this.sepOptions = res
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].budget_id || ''
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    setParent() {
      if (this.editParent && this.editParent.budget_id) {
        this.form.parent_budget_id = this.editParent.budget_id
      } else if (this.editObject && this.editObject.parent && this.editObject.parent.budget_id) {
        this.form.parent_budget_id = this.editObject.parent.budget_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        this.accountList = []
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          const fy_code = this.fyCode
          const budget_id = this.editObject.budget_id
          getBudget({ fy_code, budget_id })
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              if (res.budget_account) {
                this.accountList = res.budget_account
                  .split(',')
                  .filter(i => i)
                  .map(i => ({ account_id: i === '' ? '' : parseInt(i) }))
              }
              this.canChangeType = false
              // this.form.parent_st_type_id = res.staffTypeRelation && res.staffTypeRelation.parent_st_type_id ? res.staffTypeRelation.parent_st_type_id : ''
              this.form.seq_i = res.fundAccountRelation ? res.fundAccountRelation.seq : ''
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.form.fy_code = this.fyCode
          switch (this.editParent.budget_IE) {
            case 'I':
            case 'B':
              this.form.budget_IE = 'I'
              break
            case 'E':
              this.form.budget_IE = 'E'
              break
          }
          this.form.fy_code = this.fyCode
          this.canChangeType = true
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => {
          this.setParent()
        })
        .then(() => {
          const budget_id = this.editObject ? this.form.budget_id : undefined
          const fy_code = this.fyCode
          const type = 'C'
          return fetchBudgetTree({ budget_id, fy_code, type })
        })
        .then(res => {
          // function convertTreeToList(treeArray, level = 0) {
          //   const array = []
          //   treeArray.forEach(item => {
          //     const node = Object.assign({}, item)
          //     node.level = level + 1
          //     array.push(node)
          //     if (node.children) {
          //       array.push(...convertTreeToList(node.children, node.level))
          //       node.children = undefined
          //     }
          //   })
          //   return array
          // }
          this.parentList = convertTreeToList(res, 1)
        })
        .then(() => searchStaffs({ fy_code: this.fyCode }))
        .then(res => {
          this.staffList = res
        })

        .then(() => {
          this.changeParentType(this.form.parent_budget_id)
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 0) {
          return this.sepOptions[this.sepOptions.length - 1].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 0) {
          const seq_ele = this.sepOptions.find(i => {
            return i.budget_id === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }

        const fy_code = this.fyCode

        const seq = this.newSeq()

        const budget_id = this.form.budget_id || undefined
        const budget_type = 'D'
        const budget_code = this.form.budget_code
        const budget_IE = this.form.budget_IE
        const name_cn = this.form.name_cn
        const name_en = this.form.name_en
        const budget_active = this.form.budget_active || 'N'
        const budget_stage = this.form.budget_stage || undefined
        const parent_budget_id = this.form.parent_budget_id || undefined

        const proposed_amount = Number(this.form.proposed_amount)
        const approved_amount = Number(this.form.approved_amount)
        const last_proposed_amount = Number(this.form.last_proposed_amount)
        const last_approved_amount = Number(this.form.last_approved_amount)
        const budget_account = this.accountList.map(i => i.account_id).join(',')
        if (this.editObject) {
          // 編輯
          editBudget({
            fy_code,
            budget_id,
            budget_code,
            budget_IE,
            name_cn,
            name_en,
            budget_active,
            budget_stage,
            parent_budget_id,
            seq,
            proposed_amount,
            approved_amount,
            last_proposed_amount,
            last_approved_amount,
            budget_account,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createBudget({
            fy_code,
            budget_type,
            budget_code,
            budget_IE,
            name_cn,
            name_en,
            budget_active,
            budget_stage,
            parent_budget_id,
            seq,
            proposed_amount,
            approved_amount,
            last_proposed_amount,
            last_approved_amount,
            budget_account,
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    parentDisable(item) {
      console.log(item.name_cn, item.budget_stage)
      if (this.editObject) {
        return item.budget_type === 'F' || this.form.budget_stage !== item.budget_stage
      } else {
        return item.budget_type === 'F'
      }
    },
    showAmountInput(key) {
      if (this.form.parent_budget_id) {
        const parent = this.parentList.find(i => i.budget_id === this.form.parent_budget_id)
        if (parent) {
          const parent_stage = parent.budget_stage
          return this.amountShow[key].includes(parent_stage)
        }
      }
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
[v-cloak] {
  opacity: 0.1;
}
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
