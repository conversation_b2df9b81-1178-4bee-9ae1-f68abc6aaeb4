<template>
  <!--會計科目彈窗-->
  <el-dialog
    :visible.sync="showDialog"
    :title="$t('daily.dialog.account')"
    @open="onOpenDialogAccount"
  >
    <addPage
      v-if="dialogView === 'add'"
      :default-parent="searchFilter.fund_id"
      :edit-parent="addParent"
      class="voucher-dialog-add"
      @onCancel="onCancel"
    />
    <div v-else class="selectAccount">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.allType')">
            <el-select v-model="searchFilter.fund_id" style="width: 100px">
              <el-option
                v-for="item in fund_list_f"
                :key="item.value"
                :label="conversionParentAccountType(item)"
                :value="item.fund_id"
                v-html="conversionParentAccountType(item, true)"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('daily.label.name')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear(true)">
              {{ $t('button.clear') }}
            </el-button>
            <el-button v-if="hasPermission_Add" size="mini" type="primary" @click="onAdd">
              {{ $t('button.add') }}
            </el-button>
          </el-form-item>
          <el-form-item :label="$t('daily.label.type')">
            <el-select v-model="searchFilter.type" style="width: 70px" @change="fetchData">
              <el-option
                v-for="item in accountTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-table">
        <el-table
          ref="table"
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="100%"
          @current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" />
          <el-table-column :label="$t('daily.label.nameCN')" property="fund_abbr_cn" width="200">
            <template slot-scope="scope">
              <span :style="{marginLeft: `${(scope.row.level - 1) * 10}px`}">{{ scope.row.fund_abbr_cn }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('daily.label.nameEN')" property="fund_abbr_en" />
        </el-table>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <div class="selected">
        <span style="color: #606266;white-space: nowrap;">{{ $t('customReport.selected') }}</span>
        <div class="selected-list">
          <div v-for="item in selectedList" :key="item.id" class="selected-item">
            <span>{{ item.ac_code }}</span>
            <span style="margin: 0 4px;">{{ item[f('fund_abbr')] }}</span>
            <i class="el-icon-close action-icon" @click="onDelete(item)" />
          </div>
        </div>
      </div>
      <div style="white-space: nowrap;">
        <el-button size="mini" type="primary" @click="onConfirm">{{ $t('button.confirm') }}</el-button>
        <el-button size="mini" @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import addPage from '@/views/master/account/add'
import mixinPermission from '@/views/mixins/permission'

import { fetchFunds } from '@/api/master/funds'
import { getAccountTree } from '@/api/master/account'

export default {
  name: 'DialogAccount',
  components: {
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    defaultFundId: {
      type: [String, Number],
      default: '',
    },
    fy_code: {
      type: [String, Object],
      default: '',
    },
    vtCategory: {
      type: String,
      default: '',
    },
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      p_code: 'ac.master.account',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      currentRowItem: null,
      currentRowIndex: -1,
      showDialog: this.dialogVisible,
      dialogSelectAccount: null,
      dialogView: 'list',
      fund_list_f: [],
      fund_list_g: [],
      tableData: [],
      searchFilter: {
        fund_id: '',
        fund_id_target: '',
        name: '',
        type: '',
      },
      accountTypes: [
        {
          label: 'ALL',
          value: '',
        },
        {
          label: 'B',
          value: 'B',
        },
        {
          label: 'I',
          value: 'I',
        },
        {
          label: 'E',
          value: 'E',
        },
      ],

      loading: false,
      tableLoading: false,

      addParent: null,
      selectedList: [],
      // temp
    }
  },
  computed: {
    TQOptions() {
      return [
        {
          label: 'N/A',
          value: '',
        },
        {
          label: 'Quotation',
          value: 'Q',
        },
        {
          label: 'Tender',
          value: 'T',
        },
      ]
    },
    AllFund() {
      return {
        label: this.$t('daily.label.all'),
        value: '',
      }
    },
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },

    onOpenDialogAccount() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.searchFilter = {
        fund_id: '',
        fund_id_target: '',
        name: '',
        type: '',
      }
      let type = ''
      switch (this.vtCategory) {
        case 'P':
        case 'C':
          type = 'E'
          break
        case 'R':
          type = 'I'
          break
        default:
          type = ''
          break
      }
      this.searchFilter.type = type
      fetchFunds({ fund_type: 'F', fy_code: this.fy_code })
        .then(res => {
          this.fund_list_f = res
          if (res.length) {
            const fund = res.find(i => i.fund_id === this.defaultFundId)
            if (fund) {
              this.searchFilter.fund_id = fund.fund_id
            } else {
              this.searchFilter.fund_id = res[0].fund_id
            }
          }
          if (this.searchFilter.fund_id === '') {
            return Promise.reject()
          }
        })
        .then(this.fetchData)
    },
    fetchData() {
      fetchFunds({
        fund_type: 'G',
        parent_fund_id: this.searchFilter.fund_id,
        fy_code: this.fy_code,
      }).then(res => {
        this.tableData = res
        this.$nextTick(() => {
          this.selectedData.forEach(item => {
            const index = this.tableData.findIndex(row => row.fund_id === item.fund_id)
            if (index !== -1) {
              this.$refs.table.toggleRowSelection(this.tableData[index], true)
            }
          })
        })
      })
    },
    onAdd() {
      const fund_id = this.searchFilter.fund_id
      if (fund_id) {
        if (this.searchFilter.fund_id_target) {
          this.addParent = { fund_id: this.searchFilter.fund_id_target }
        }
        this.dialogView = 'add'
      } else {
        this.$message.error(this.$t('message.pleaseSelectFund'))
      }
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$refs.table.toggleRowSelection(currentRow, true)
    },
    handleSelectionChange(selection) {
      this.$set(this, 'selectedList', selection)
    },
    onDelete(item) {
      this.$refs.table.toggleRowSelection(item, false)
    },
    onClear(update) {
      this.searchFilter = {
        fund_id: '',
        fund_id_target: '',
        name: '',
        type: '',
      }
      if (this.fund_list_f.length) {
        this.searchFilter.fund_id = this.fund_list_f[0].fund_id
      }
      if (update) this.fetchData()
    },
    async onConfirm() {
      const res = await getAccountTree(this.searchFilter.fund_id)
      console.log(res)
      const filterAccountData = this.getFilterAccountData(res, this.selectedList)
      console.log(filterAccountData)
      this.$emit('selectRow', {
        selectedList: this.selectedList,
        filterAccountData,
      })
      this.showDialog = false
    },

    getFilterAccountData(res, selectedList) {
      const filterAccountData = []
      res.forEach(item => {
        console.log(item, selectedList, 'selectedList')
        const isMatching = selectedList.some(i => i.fund_id === item.fund_id || i.fund_id === item.parent_fund_id)
        if (isMatching) {
          console.log(item.children, 'item')
          let newSelectedList = selectedList
          if (!item.account_id) {
            newSelectedList = item.children
          }
          if (item.children) {
            filterAccountData.push(...this.getFilterAccountData(item.children, newSelectedList))
          }
          if (item.account_id) {
            filterAccountData.push({
              ...item,
            })
          }
        } else {
          if (item.children) {
            filterAccountData.push(...this.getFilterAccountData(item.children, selectedList))
          }
        }
      })
      return filterAccountData
    },

    /**
     * 獲取指定節點的所有父級 fund_id 鏈
     * @param {Array} data - 完整的樹形數據
     * @param {number} targetFundId - 目標節點的 fund_id
     * @returns {Array} 返回從根節點到目標節點的所有 fund_id 數組
     */
    getParentFundChain(data, targetFundId) {
      // 建立 fund_id 到節點的映射
      const fundMap = new Map()

      // 遞歸遍歷樹形結構，建立映射
      const buildMap = (nodes) => {
        nodes.forEach(node => {
          if (node.fund_id) {
            fundMap.set(node.fund_id, node)
          }
          if (node.children && node.children.length > 0) {
            buildMap(node.children)
          }
        })
      }

      buildMap(data)

      // 從目標節點開始，向上查找所有父級
      const getParentChain = (fundId) => {
        const chain = []
        let currentFundId = fundId

        while (currentFundId) {
          const currentNode = fundMap.get(currentFundId)
          if (!currentNode) break

          chain.unshift(currentFundId) // 添加到數組開頭，保持從根到葉的順序
          currentFundId = currentNode.parent_fund_id
        }

        return chain
      }

      return getParentChain(targetFundId)
    },

    /**
     * 獲取指定節點的根節點 fund_id
     * @param {Array} data - 完整的樹形數據
     * @param {number} targetFundId - 目標節點的 fund_id
     * @returns {number|null} 返回根節點的 fund_id
     */
    getRootFundId(data, targetFundId) {
      const fundMap = new Map()

      const buildMap = (nodes) => {
        nodes.forEach(node => {
          if (node.fund_id) {
            fundMap.set(node.fund_id, node)
          }
          if (node.children && node.children.length > 0) {
            buildMap(node.children)
          }
        })
      }

      buildMap(data)

      let currentFundId = targetFundId
      let rootFundId = currentFundId

      while (currentFundId) {
        const currentNode = fundMap.get(currentFundId)
        if (!currentNode) break

        rootFundId = currentFundId
        currentFundId = currentNode.parent_fund_id
      }

      return rootFundId
    },

    /**
     * 獲取指定節點的所有父級節點完整信息
     * @param {Array} data - 完整的樹形數據
     * @param {number} targetFundId - 目標節點的 fund_id
     * @returns {Array} 返回從根節點到目標節點的所有節點對象數組
     */
    getParentNodes(data, targetFundId) {
      const fundMap = new Map()

      const buildMap = (nodes) => {
        nodes.forEach(node => {
          if (node.fund_id) {
            fundMap.set(node.fund_id, node)
          }
          if (node.children && node.children.length > 0) {
            buildMap(node.children)
          }
        })
      }

      buildMap(data)

      const parentChain = []
      let currentFundId = targetFundId

      while (currentFundId) {
        const currentNode = fundMap.get(currentFundId)
        if (!currentNode) break

        parentChain.unshift(currentNode) // 添加到數組開頭
        currentFundId = currentNode.parent_fund_id
      }

      return parentChain
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
      padding: 20px 15px 0;
    }
    .el-dialog__footer {
      padding: 10px 15px 20px ;
    }
  }
}
.selectAccount {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .selected {
    display: flex;
    align-items: center;
  }
  .selected-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px 0;
    .selected-item {
      display: flex;
      align-items: center;
      border-radius: 4px;
      padding: 4px;
      background: #E4E7ED;
      margin-left: 4px;
      font-size: 12px;
    }
    .el-icon-close {
      width: 12px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #94979E;
      color: #fff;
      border-radius: 50%;
      cursor: pointer;
    }
  }
}
.dialog-table {
  flex: 1;
}
</style>
