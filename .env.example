# 環境變量配置示例
# 複製此文件為 .env 或 .env.development 並修改相應的配置

# 生產環境配置
NODE_ENV=production
VUE_APP_IS_PREVIEW=false

# 開發環境配置示例
# VUE_APP_DEV_HOST=**************
# VUE_APP_DEV_PROT=9527

# 服務器配置
VUE_APP_SERVER_NAME="*************"
VUE_APP_PROTOCOL=http
VUE_APP_IP=*************
VUE_APP_PORT=80
VUE_APP_PROJECT_ROOT=edac
VUE_APP_REMOTE_PROJECT_NAME=edac
VUE_APP_URI=public/storage

# 代理配置（開發環境）
VUE_APP_PROXY_PATH=/api
VUE_APP_PROXY_TARGET=http://*************/edac/public/api

# 報表服務器配置
VUE_APP_RPT_PROTOCOL=http
VUE_APP_RPT_IP=*************
VUE_APP_RPT_PORT=8080
VUE_APP_RPT_URL=EDReport/run

# 其他配置
VUE_APP_WEB_CODE=AC
VUE_APP_TUTORIAL_PATH=https://www.hkschoolsoftware.com/loading/tutorial?tmp=464ei1Qv8oFENolBHwzRkkp%2FnQqorf7sG2CYjq1RhQ

# API配置
VUE_APP_ENV_CONFIG=prod
VUE_APP_BASE_API=http://*************:80/edac/public/api

# 資源路徑配置
# 開發環境通常使用 '/'
# 生產環境需要設置為正確路徑 '/subpath/'
VUE_APP_ASSETS_PUBLIC_PATH=./
