import Vue from 'vue'
import VueI18n from 'vue-i18n'
// import Cookies from 'js-cookie'
import storage from '@/utils/store'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-TW' // element-ui lang
// import elementEsLocale from 'element-ui/lib/locale/lang/es'// element-ui lang
import vxeZhTW from 'vxe-table/lib/locale/lang/zh-TW'
import vxeEnUS from 'vxe-table/lib/locale/lang/en-US'
import enLocale from './en'
import zhLocale from './zh-hk'
// import esLocale from './es'

Vue.use(VueI18n)

const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale,
    ...vxeEnUS,
  },
  'zh-hk': {
    ...zhLocale,
    ...elementZhLocale,
    ...vxeZhTW,
  },
  // es: {
  //   ...esLocale,
  //   ...elementEsLocale
  // }
}

const i18n = new VueI18n({
  // set locale
  // options: en | zh | es
  locale: storage.getLocation('language') || 'en',
  // set locale messages
  messages,
})

export default i18n
