import Layout from '@/views/layout/Layout'

const settingRouter = {
  path: '/purchase-setting',
  component: Layout,
  redirect: 'noredirect',
  name: 'purchase-setting',
  meta: {
    title: 'router.setting',
    p_code: 'pc.setting',
    icon: 'setting',
  },
  children: [
    {
      path: 'screen',
      component: () => import('@/views/setting/screen/index.vue'),
      name: 'settingScreen',
      redirect: 'screen/basic', // 基本設定
      meta: {
        title: 'router.settingScreen',
        p_code: 'pc.setting.screen',
        noCache: true,
      },
      children: [
        {
          path: 'basic',
          component: () => import('@/views/setting/screen/page/basic.vue'),
          name: 'settingScreenBasic',
          meta: {
            title: 'router.settingScreenBasic',
            p_code: 'pc.setting.screen',
            noCache: true,
          },
        },
        {
          path: 'table_header',
          component: () => import('@/views/setting/screen/page/tableHeader.vue'),
          name: 'settingScreenTableHeader',
          meta: {
            title: 'router.settingScreenTableHeader',
            p_code: 'pc.setting.screen',
            noCache: true,
          },
        },
        {
          path: 'menu',
          component: () => import('@/views/setting/screen/page/menu.vue'),
          name: 'settingScreenMenu',
          meta: {
            title: 'router.settingScreenMenu',
            p_code: 'pc.setting.screen',
            noCache: true,
          },
        },
        {
          path: 'content',
          component: () => import('@/views/setting/screen/page/content.vue'),
          name: 'settingScreenContent',
          meta: {
            title: 'router.settingScreenContent',
            p_code: 'pc.setting.screen',
            noCache: true,
          },
        },
      ],
    },
  ],
}

export default settingRouter
