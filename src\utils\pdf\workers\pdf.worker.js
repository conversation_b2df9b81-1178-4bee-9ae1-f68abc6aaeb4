import pdfMake from 'pdfmake/build/pdfmake'
import { PDF_MSG_TYPE } from '@/utils/pdf/workers/constant'
// import vfs from '../../../../static/js/vfs_fonts.jh'

import PdfMerge from '../merge'

pdfMake.vfs = null
const normal = 'PingFangTC-Light.ttf'
const bold = 'PingFangTC-Semibold.ttf'
pdfMake.fonts = {
  cn: {
    normal: normal,
    bold: bold,
    italics: normal,
    bolditalics: bold,
  },
}
let working = false

function generatePdf(content) {
  return new Promise((resolve, reject) => {
    pdfMake.createPdf(content).getBuffer(cb => {
      resolve(cb)
    })
  })
}
function parseJson(str) {
  // eslint-disable-next-line no-eval
  return eval('(' + str + ')')
}

function waitVfsInit() {
  return new Promise((resolve, reject) => {
    const interval = setInterval(() => {
      if (pdfMake.vfs) {
        clearInterval(interval)
        resolve()
      }
    }, 100)
  })
}

async function genList(list) {
  try {
    console.log('genList')
    const pdf = new PdfMerge()
    console.log('genList2')
    await pdf.init(await generatePdf(parseJson(list[0])))
    console.log('genList3')
    self.postMessage({ type: PDF_MSG_TYPE.PAGE_DONE })
    console.log('genList4')
    for (let i = 1; i < list.length; i++) {
      await pdf.addPdf(await generatePdf(parseJson(list[i])))
      self.postMessage({ type: PDF_MSG_TYPE.PAGE_DONE })
    }
    const url = await pdf.getBlobUrl()
    console.log(url, 'genList5')
    return url
  } catch (e) {
    console.error(e)
    return Promise.reject(e)
  }
}

(function() {
  self.onmessage = async e => {
    const data = e.data
    switch (data.type) {
      case PDF_MSG_TYPE.VFS:
        pdfMake.vfs = data.vfs
        self.postMessage({ type: PDF_MSG_TYPE.VFS_INIT })
        break
      case PDF_MSG_TYPE.PRINT: {
        if (working) {
          console.log('1')
          self.postMessage({ type: PDF_MSG_TYPE.BUSY })
          return
        }
        working = true
        await waitVfsInit()
        // 使用正则表达式提取printListIndex的值
        const regex = /"printListIndex":(\d+)/
        const match = data.list[0].match(regex)

        // 获取printListIndex的值
        let printListIndex
        if (match) {
          printListIndex = parseInt(match[1], 10)
        } else {
          printListIndex = null // 如果没有找到匹配项
        }
        genList(data.list)
          .then(url => {
            self.postMessage({
              type: 'DONE',
              url,
              index: data.index,
              printListData: { index: printListIndex, url: url },
            })
            working = false
          })
          .catch(e => {
            self.postMessage({
              type: PDF_MSG_TYPE.ERROR,
              error: e,
              printListData: { index: printListIndex },
            })
            working = false
          })
        break
      }
    }
  }
})()
