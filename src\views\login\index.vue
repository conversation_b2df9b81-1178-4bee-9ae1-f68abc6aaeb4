<template>
  <div
    v-loading="loading"
    :style="{ backgroundImage: 'url(' + background + ')', backgroundSize: 'cover' }"
    class="login-container"
  >
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      autocomplete="off"
      class="login-form"
      label-position="left"
    >
      <div class="school-info">
        <img v-if="school.sch_logo" :src="schoolLogo" class="logo" style="width: 110px">
        <occupy v-else class="logo" width="110px" height="110px" style="margin: 0 auto" />
        <div style="padding-top: 0px; width: 100%">
          <h3
            v-if="school && school.sch_name_cn"
            ref="school_name_cn"
            :style="{ 'font-size': nameSizeCn + 'px' }"
            class="school-name cn"
          >
            {{ school.sch_name_cn }}
          </h3>
          <occupy v-else class="school-name cn" width="98%" height="1em" />
          <h3
            v-if="school && school.sch_name_en"
            ref="school_name_en"
            :style="{ 'font-size': nameSizeEn + 'px' }"
            class="school-name"
          >
            {{ school.sch_name_en }}
          </h3>
          <occupy v-else class="school-name" height="1em" />
        </div>
      </div>

      <el-tabs v-model="loginForm.system" type="border-card" class="system-tabs">
        <el-tab-pane
          v-for="item in systems"
          :key="item.name"
          :label="$t('system.' + item.name)"
          :name="item.name"
          class="system-tab"
        >
          <div v-if="loginForm.system === item.name">
            <!-- 帳號 -->
            <el-form-item prop="username">
              <span class="svg-container">
                <svg-icon icon-class="user" />
              </span>
              <el-input
                v-model="loginForm.username"
                :placeholder="$t('login.username')"
                auto-complete="off"
                name="username"
                class="input"
                onfocus="this.removeAttribute('readonly');"
                type="text"
              />
            </el-form-item>
            <!-- 密碼 -->
            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <el-input
                v-model="loginForm.password"
                :type="pwdType"
                :placeholder="$t('login.password')"
                autocomplete="off"
                name="password"
                onfocus="this.removeAttribute('readonly');"
                readonly
                class="input"
                @keyup.enter.native="handleLogin"
              />
              <span class="show-pwd" @click="showPwd">
                <svg-icon v-if="pwdType === 'password'" icon-class="eye" />
                <svg-icon v-else icon-class="eye_open" />
              </span>
            </el-form-item>
            <!-- 登錄 -->
            <el-form-item>
              <el-button
                :loading="loading"
                style="width: 100%"
                type="primary"
                @click.native.prevent="handleLogin"
              >
                {{ $t('login.login') }}
              </el-button>
            </el-form-item>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 忘記密碼 -->
      <div class="tips">
        <span
          style="margin-right: 20px"
          @click="$notify.info({ title: 'Tips', message: $t('login.tips') })"
        >
          {{ $t('login.forgetPassword') }}
        </span>
      </div>
    </el-form>
    <img :src="company" class="company-logo" alt="">
  </div>
</template>
<script>
import { isvalidUsername } from '@/utils/validate'
import { mapGetters } from 'vuex'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!isvalidUsername(value)) {
        // callback(new Error(this.$t('error.noneUser')))
        // this.$message.error(this.$t('error.noneUser'))
      } else {
        callback()
      }
    }
    const validatePass = (rule, value, callback) => {
      if (value.length < 1) {
        // callback(new Error(this.$t('error.nonePwd')))
        // this.$message.error(this.$t('error.nonePwd'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      loginForm: {
        username: '',
        password: '',
        system: 'AC',
      },

      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePass }],
      },

      redirect: '/dashboard',

      pwdType: 'password',
      background: require('@/assets/login/login-bg.jpg'),
      company: require('@/assets/login/norray_logo.png'),
    }
  },
  computed: {
    ...mapGetters(['school', 'addRouters', 'remoteServerInfo', 'lastLoginSystem']),
    systems() {
      if (this.school && this.school.systems && Array.isArray(this.school.systems)) {
        return this.school.systems.map(s => ({
          name: s,
        }))
        // return ['AC']
      }
      return []
    },
    schoolLogo() {
      const { protocol, ip, port, remoteProjectName, uri } = this.remoteServerInfo
      const { sch_logo } = this.school
      if (!sch_logo) return ''
      return (
        protocol + '://' + ip + ':' + port + '/' + remoteProjectName + '/' + uri + '/' + sch_logo
      )
    },
    nameSizeCn() {
      if (this.school && this.school.sch_name_cn && this.$refs.school_name_cn) {
        const name = this.school.sch_name_cn
        const len = name.length
        const width = this.$refs.school_name_cn.offsetWidth
        const size = parseInt(width / len)
        if (size < 32) {
          return size
        }
      }
      return 32
    },
    nameSizeEn() {
      if (this.school && this.school.sch_name_en && this.$refs.school_name_en) {
        const name = this.school.sch_name_en
        const len = name.length
        const width = this.$refs.school_name_en.offsetWidth
        const size = parseInt((width / len) * 1.5)
        if (size < 17) {
          return size
        }
      }
      return 17
    },
  },
  watch: {
    $route: {
      handler: function() {
        this.redirect = '/'
        // this.redirect = route.query && route.query.redirect
      },
      immediate: true,
    },
    systems: {
      handler: function() {
        if (this.lastLoginSystem) {
          if (this.systems.findIndex(s => s.name === this.lastLoginSystem) !== -1) {
            this.loginForm.system = this.lastLoginSystem
            return
          }
        }
        if (this.systems.length > 0) {
          this.loginForm.system = this.systems[0].name
          return
        }
        this.loginForm.system = ''
      },
      immediate: true,
    },
  },
  created() {
    // if (this.lastLoginSystem) {
    //   if (this.systems.findIndex(s => s.name === this.lastLoginSystem) !== -1) {
    //     this.loginForm.system = this.lastLoginSystem
    //     return
    //   }
    // }
    // if (this.systems.length > 0) {
    //   this.loginForm.system = this.systems[0].name
    //   return
    // }
    // this.loginForm.system = ''
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const data = Object.assign({}, this.loginForm) // 提取表單
          data.password = this.$md5(data.password) // 密碼MD5
          this.$store
            .dispatch('LoginByUsername', data)
            .then(() => {
              // this.$router.push({ path: this.redirect || '/' })
              this.$router.push({ path: '/' }) // this.redirect ||
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = ''
      } else {
        this.pwdType = 'password'
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss">
$bg: rgb(202, 209, 199);
$light_gray: rgb(46, 45, 45);

/* reset element-ui css */
.login-container {
  .el-form-item {
    margin-bottom: 22px !important;
  }
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      font-size: 14px !important;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px $bg inset !important;
        -webkit-text-fill-color: rgb(0, 0, 0) !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.06);
    border-radius: 5px;
    color: #454545;

    .el-form-item__error {
      font-size: 16px;
      font-weight: bold;
      text-align: center;
    }
  }

  ::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #7c7c7c;
  }

  :-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #7c7c7c;
  }

  ::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #7c7c7c;
  }

  :-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #7c7c7c;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #333;
$light_gray: #eee;
.login-container {
  position: fixed;
  height: 100%;
  width: 100%;
  min-height: 400px;

  .login-form {
    position: absolute;
    left: 0;
    right: 0;
    width: 520px;
    max-width: 100%;
    padding: 35px 35px 15px 35px;
    margin: 100px auto;
    background: #fbfbfb;
    border-radius: 5px;

    .input {
      height: 48px;
      line-height: 48px;
      /deep/ {
        input {
          height: 48px;
          line-height: 48px;
        }
      }
    }
  }

  .tips {
    margin-top: 15px;
    font-size: 16px !important;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: rgba(0, 0, 0, 0.9);
    }

    span {
      font-size: 16px !important;

      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    height: 14px;
    line-height: 14px;
    /*padding: 0 5px 0 15px;*/
    font-size: 14px;
    vertical-align: sub;
    padding: 17px 5px 15px 15px;
  }

  .school-info {
    color: #000;
    text-align: center;
    /*display: flex;*/
    align-items: center;
    justify-content: center;
    padding-bottom: 0px;

    .school-name {
      font-size: 17px;
      line-height: 18px;
      font-weight: 400;
      color: $dark_gray;
      margin: 0px auto 20px auto;
      text-align: center;
      font-weight: bold;

      &.cn {
        line-height: 32px;
        font-size: 32px;
      }
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 8px;
    font-size: 16px !important;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}

.company-logo {
  position: fixed;
  right: 16px;
  bottom: 16px;
}
</style>

<style rel="stylesheet/scss" lang="scss">
.login-form {
  .system-tabs {
    /deep/ {
      .el-tabs__header {
        .el-tabs__nav {
          .el-tabs__item {
            &:first-child {
              border-left: none;
            }

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }
  }
  .el-tabs__nav {
    /*float: right;*/
    width: 100%;
    text-align: center;

    display: flex;
    justify-content: space-around;

    #tab-AC {
      /*width: 50%;*/
    }

    #tab-BG {
      /*width: 51%;*/
    }
    .el-tabs__item {
      font-size: 16px !important;
      font-weight: normal;
      width: 100%;
    }
    .el-tabs__item.is-top.is-active {
      color: rgba(0, 0, 0, 0.6);
    }
    .el-tabs__item:not(.is-disabled):hover {
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .svg-icon {
    font-size: 14px !important;
    width: 1em;
    height: 1em;
  }
  .show-pwd .svg-icon {
    font-size: 16px !important;
  }

  .el-tabs--border-card {
    -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    border: none;
  }
}
</style>
