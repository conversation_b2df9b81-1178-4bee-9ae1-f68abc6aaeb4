<template>
  <el-table
    ref="treeTableGroup"
    :data="formatData"
    :row-class-name="isExpanded"
    :show-summary="true"
    :summary-method="getSummaries"
    v-bind="$attrs"
    :row-key="rowKey"
    class="tree-table"
    height="100%"
    @header-dragend="onHeaderDragend"
  >
    <el-table-column
      :align="firstFieldAlign"
      :width="firstFieldWidth"
      :min-width="150"
      label-class-name="index-header"
      column-key="_index"
      highlight-current-row
    >
      <template slot="header">
        <div v-for="i in getMaxLevel()" :key="i" class="level-btn" @click="showLevel(i)">
          {{ i - 1 }}
        </div>
      </template>
      <template v-if="scope && scope.row" slot-scope="scope">
        <div
          v-if="!loading"
          :class="{
            'first-column-has-children': scope.row.children && scope.row.children.length > 0,
          }"
          @click="toggleExpanded(scope.$index)"
        >
          <span v-for="space in scope.row._level" :key="space" class="ms-tree-space" />
          <span v-if="iconShow(0, scope.row)" class="tree-ctrl">
            <i v-if="!scope.row._expanded" class="el-icon-caret-right" />
            <i v-else class="el-icon-caret-bottom" />
          </span>
          <slot :scope="scope" name="firstField">
            <!--文件夾圖標-->
            <i v-if="!scope.row[folderField]" :class="'edac-icon edac-icon-folder'" />
            <!--文件圖標-->
            <i v-else class="edac-icon edac-icon-file" />
          </slot>
        </div>
      </template>
    </el-table-column>
    <template v-for="(item, gIndex) in columns">
      <el-table-column
        v-if="drGroup.hasOwnProperty(item.ss_key)"
        :key="item.ss_key + gIndex"
        :label="columnsGroup[item.ss_key]"
        align="center"
      >
        <el-table-column
          v-if="drGroup[item.ss_key]"
          :label="$t(langKey + drGroup[item.ss_key])"
          :formatter="formatter"
          :prop="drGroup[item.ss_key]"
          :align="item.alignment"
          :width="item.width"
          :label-class-name="drGroup[item.ss_key]"
          :class-name="drGroup[item.ss_key]"
          :column-key="drGroup[item.ss_key]"
        />
        <el-table-column
          v-if="crGroup.hasOwnProperty(item.ss_key)"
          :label="$t(langKey + crGroup[item.ss_key])"
          :formatter="formatter"
          :prop="crGroup[item.ss_key]"
          :align="item.alignment"
          :width="item.width"
          :label-class-name="crGroup[item.ss_key]"
          :class-name="crGroup[item.ss_key]"
          :column-key="drGroup[item.ss_key]"
        />
      </el-table-column>
    </template>
    <el-table-column
      :resizable="false"
      label-class-name="last"
      class-name="last"
      label=""
      min-width="1"
    />
    <slot />
  </el-table>
</template>

<script>
/**
 Auth: Lei.j1ang
 Created: 2018/1/19-13:59
 */
import { mapGetters } from 'vuex'
import treeToArray from './eval'
import { amountFormat, max } from '@/utils'

export default {
  name: 'TreeTableGroup',
  props: {
    /* eslint-disable */
    data: {
      type: [Array, Object],
      required: true,
    },
    /*
    {
      name: 'name',
      children: [
        c1: 'c1',
        c2: 'c2'
      ]
    }
    */
    columnsGroup: {
      type: Object,
      default: () => ({}),
    },
    drGroup: {
      type: Object,
      default: () => [],
    },
    crGroup: {
      type: Object,
      default: () => [],
    },
    dataTypes: {
      type: Object,
      default: () => {
        ;[]
      },
    },
    summary: {
      type: Object,
      default: () => {
        ;[]
      },
    },
    sumLabel: {
      type: String,
      default: '',
    },
    columns: {
      type: Array,
      default: () => [],
    },
    evalFunc: Function,
    evalArgs: Array,
    expandAll: {
      type: Boolean,
      default: false,
    },
    langKey: {
      type: String,
      default: '',
    },
    firstField: {
      type: String,
      default: '',
    },
    firstFieldAlign: {
      type: String,
      default: 'left',
    },
    firstFieldWidth: {
      type: [String, Number],
      default: '150',
    },
    folderField: {
      type: String,
      default: '',
    },
    numberField: {
      type: String,
      default: '',
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    actionsMinWidth: {
      type: Number,
      default: 150,
    },
  },
  data() {
    return {
      maxLevel: 0,
      loading: false,
      sumLabelIndex: -1,
    }
  },
  watch: {
    data() {},
  },
  updated() {},
  computed: {
    ...mapGetters(['language', 'styles', 'currentDate']),
    // 格式化数据源
    formatData: function () {
      let tmp
      if (!Array.isArray(this.data)) {
        tmp = [this.data]
      } else {
        tmp = this.data
      }
      const func = this.evalFunc || treeToArray
      const args = this.evalArgs
        ? [tmp, this.expandAll].concat(this.evalArgs)
        : [tmp, this.expandAll]
      this.reload()
      return func.apply(null, args)
    },
  },
  mounted() {},
  methods: {
    getAmountType(key) {
      switch (key) {
        case 'bf_dr':
        case 'bf_cr':
          return 'bf' // this.dateStart
        case 'dr':
        case 'cr':
          return 'mid' // this.dateRangeStr
        case 'end_dr':
        case 'end_cr':
          return 'end' // this.dateEnd
      }
      return key
    },
    reload() {
      return new Promise(resolve => {
        this.loading = true
        this.$nextTick(() => {
          this.loading = false
          this.$emit('changeView')
          resolve()
        })
      })
      // setTimeout(()=>{
      //     this.hockReload = false
      // },100)
    },
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    showRow: function (row) {
      return ''
      const show = row.row.parent ? row.row.parent._expanded && row.row.parent._show : true
      row.row._show = show
      return show
        ? 'animation:treeTableShow 1s;-webkit-animation:treeTableShow 1s;'
        : 'display:none;'
    },
    // 切换下级是否展开
    toggleExpanded: function (trIndex) {
      const record = this.formatData[trIndex]
      record._expanded = !record._expanded
      this.$emit('changeExpanded', this.getExpandedList())
    },
    // 图标显示
    iconShow(index, record) {
      return index === 0 && record.children && record.children.length > 0
    },
    showLevel(level) {
      this.getMaxLevel()
      let maxLevel = 0
      this.data.forEach(item => {
        const cLevel = this.expanded(item, level)
        maxLevel = maxLevel < cLevel ? cLevel : maxLevel
      })
      this.$emit('changeLevel', level)
      return maxLevel
    },
    getMaxLevel() {
      if (this.data.length === 0) {
        return 0
      }
      const getLevel = item => {
        if (item.children) {
          const cList = item.children.map(getLevel)
          const cLevel = max(cList)
          return max([item._level, cLevel])
        }
        return item._level
      }
      return max(this.data.map(getLevel))
    },
    expanded(data, level) {
      let maxLevel = data._level
      data._expanded = data._level < level
      if (data.children) {
        data.children.forEach(item => {
          const cLevel = this.expanded(item, level)
          maxLevel = maxLevel < cLevel ? cLevel : maxLevel
        })
      }
      return maxLevel
    },
    /**
     * 修改列寬提交到父組件
     * @param newWidth
     * @param oldWidth
     * @param column
     * @param event
     */
    onHeaderDragend(newWidth, oldWidth, column, event) {
      const key = column.columnKey
      // this.$emit('changeWidth', key, newWidth)
      this.$emit('changeWidth', this.getAmountType(key), newWidth)
    },
    getExpandedList() {
      const getList = item => {
        let s = item._expanded ? [item[this.numberField]] : []
        if (item.children) {
          s = [...s, ...item.children.map(getList)]
        }
        return s.filter(i => i).join(',')
      }
      return this.data
        .map(getList)
        .filter(i => i !== '')
        .join(',')
    },
    setExpandItem(listStr) {
      const list = listStr.split(',')
      if (list.length === 0) {
        return
      }
      const setExpanded = item => {
        item._expanded = list.indexOf(item.fund_id + '') !== -1
        if (item.children) {
          item.children.forEach(setExpanded)
        }
      }
      this.data.forEach(setExpanded)
    },
    formatter(row, column, cellValue, index) {
      switch (column.property) {
        case 'dr':
        case 'cr':
        case 'bf_dr':
        case 'bf_cr':
        case 'end_dr':
        case 'end_cr':
          return amountFormat(cellValue)
      }
      return cellValue
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      let sumLabelIndex = -1
      const summary = this.summary
      columns.forEach((column, index) => {
        if (summary.hasOwnProperty(column.property)) {
          if (sumLabelIndex === -1) {
            sumLabelIndex = index - 1
          }
          sums[index] = amountFormat(summary[column.property])
        } else {
          sums[index] = ''
        }
      })
      if (sumLabelIndex > 0) {
        sums[sumLabelIndex] = this.sumLabel
      }

      this.sumLabelIndex = sumLabelIndex
      return sums
    },
    isExpanded({ row }) {
      let text = ''
      const show = row.parent ? row.parent._expanded && row.parent._show : true
      row._show = show
      text = show ? 'show-row' : 'hidden-row'

      if (row._expanded && !row[this.folderField]) {
        text += ' folder-expanded'
      } else {
        text += ' folder-collapse'
      }
      return text
    },
    rowKey(row) {
      return row.fund_id + '-' + row.account_id
    },
  },
}
</script>
<style rel="stylesheet/css" lang="scss">
@keyframes treeTableShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes treeTableShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.tree-table {
  /*height: calc(100vh - 210px);*/
  .el-table__body-wrapper {
    /*height: calc(100vh - 240px);*/
    overflow-y: auto;
  }

  .number-field {
    vertical-align: middle;
    color: #fff;
    background-color: #b9b7b8;
    padding: 0 3px;
    &.selected {
      background-color: #3e97dd;
    }
  }
  .key {
    vertical-align: middle;
  }
  .edac-icon {
    vertical-align: middle;
    color: #707070;

    &.activate {
      color: #3e97dd;
    }
  }
}
</style>

<style lang="scss" rel="stylesheet/scss" scoped>
$color-blue: #2196f3;
$space-width: 18px;
.ms-tree-space {
  position: relative;
  top: 1px;
  display: inline-block;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  width: $space-width;
  height: 14px;
  &::before {
    content: '';
  }
}
.processContainer {
  width: 100%;
  height: 100%;
}
table td {
  line-height: 26px;
}

.tree-ctrl {
  position: relative;
  cursor: pointer;
  /*color: $color-blue;*/
  margin-left: -$space-width;
}
.level-btn {
  background-color: white;
  color: #707070;
  border: 1px solid #707070;
  border-radius: 4px;
  padding: 0 3px;
  margin: 0 2px;
  text-align: center;
  cursor: pointer;
  width: 20px;
  height: 20px;
  line-height: 20px;
  display: inline-block;
}

.tree-table {
  /deep/ {
    .show-row {
      animation: treeTableShow 1s;
      -webkit-animation: treeTableShow 1s;
    }
    .hidden-row {
      display: none;
    }
  }

  i.tree-ctrl {
    font-size: 16px !important;
    color: #707070;
  }
  .svg-icon {
    vertical-align: -0.2em;
    font-size: 16px !important;
    color: #707070;
  }
  .first-column-has-children {
    cursor: pointer;
  }
  .cell {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    .key {
      -webkit-touch-callout: unset;
      -webkit-user-select: text;
      -khtml-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
    }
  }
  /deep/ {
    tr.el-table__row {
      .cell {
        .el-table__expand-icon {
          display: none !important;
        }
      }
    }
    .folder-expanded {
      background-color: #f3f3f3;
    }
    .folder-collapse {
      background-color: #fff;
    }
  }
}
</style>
