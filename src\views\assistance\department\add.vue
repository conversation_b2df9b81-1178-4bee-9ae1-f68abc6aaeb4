<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 部門編號 -->
      <el-form-item
        :rules="codeRules"
        :class="{ 'code-rules-error': haveExist }"
        :label="$t('assistance.department.label.dept_code')"
        prop="dept_code"
      >
        <el-input v-model="form.dept_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.department.label.dept_name_cn')"
        prop="dept_name_cn"
      >
        <el-input v-model="form.dept_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.department.label.dept_name_en')"
        prop="dept_name_en"
      >
        <el-input v-model="form.dept_name_en" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :label="$t('assistance.department.label.parent_dept_type_id')"
        prop="parent_dept_type_id"
      >
        <el-select
          v-model="form.parent_dept_type_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in departmentTypes"
            :key="item.value"
            :value="item.dept_type_id"
            :label="conversionParentDepartmentType(item)"
            v-html="conversionParentDepartmentType(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置 -->
      <el-form-item :label="$t('assistance.department.label.seq')" prop="seq">
        <el-select v-model="form.seq" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in sepOptions"
            :key="item.dept_type_id"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.dept_type_id ? item.dept_type_id : item.department_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.department.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_code"
            :value="item.fy_code"
          >
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  createDepartment,
  updateDepartment,
  getDepartment,
  getDepartmentsTreeNode,
} from '@/api/assistance/department'
import { fetchDepartmentTypes } from '@/api/assistance/department/departmentType'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'AssistanceDepartmentAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      system: 'BG',
      form: {
        department_id: '',
        dept_code: '',
        dept_name_cn: '',
        dept_name_en: '',
        active_year: '',
        parent_dept_type_id: '',
        seq: '',
      },
      defaultForm: {
        department_id: '',
        dept_code: '',
        dept_name_cn: '',
        dept_name_en: '',
        active_year: '',
        parent_dept_type_id: '',
        seq: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      sepOptions: [
        {
          dept_type_id: '',
          department_id: '',
          seq: '',
          name_cn: this.$t('assistance.dept_type.label.seq_default'),
          name_en: this.$t('assistance.dept_type.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      departmentTypes: [],
      active_year_arr: [],
      loading: true,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    defaultSepOptions() {
      return [
        {
          dept_type_id: '',
          department_id: '',
          seq: '',
          name_cn: this.$t('assistance.dept_type.label.seq_default'),
          name_en: this.$t('assistance.dept_type.label.seq_default'),
        },
      ]
    },
    defaultDepartmentTypes() {
      return [
        {
          dept_type_id: '',
          dept_type_cn: this.$t('assistance.dept_type.label.parent_default'),
          dept_type_en: this.$t('assistance.dept_type.label.parent_default'),
        },
      ]
    },
    codeRules() {
      return [{ required: true, validator: this.checkCode, trigger: 'blur' }]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.code === i.code) {
          return
        }
        codeArr.push(i.code)
      })
      return codeArr
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentDepartmentType(departmentType, html, startLevel = 1) {
      let text = this.language === 'en' ? departmentType.dept_type_en : departmentType.dept_type_cn
      if (html) {
        text = '&nbsp;'.repeat((departmentType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    changeParentType() {
      // if (!this.form.parent_dept_type_id) {
      //   this.form.seq = ''
      //   return
      // }
      getDepartmentsTreeNode(
        '',
        this.form.parent_dept_type_id,
        this.form.dept_type_id,
        this.form.department_id,
      ).then(res => {
        this.sepOptions = [...res, ...this.defaultSepOptions]
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].dept_type_id ? res[i].dept_type_id : res[i].department_id
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.dept_type_id) {
        this.form.parent_dept_type_id = this.editParent.dept_type_id
      } else if (this.editObject && this.editObject.parent && this.editObject.parent.dept_type_id) {
        this.form.parent_dept_type_id = this.editObject.parent.dept_type_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getDepartment(this.editObject.department_id)
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.setParent()
              this.form.parent_dept_type_id =
                res.departmentTypeRelation && res.departmentTypeRelation.parent_dept_type_id
                  ? res.departmentTypeRelation.parent_dept_type_id
                  : ''
              this.form.seq_i = res.departmentTypeRelation ? res.departmentTypeRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : []
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() =>
          fetchDepartmentTypes({
            dept_type_id: this.form.dept_type_id,
          }),
        )
        .then(res => {
          this.departmentTypes = [...this.defaultDepartmentTypes, ...res]
          this.changeParentType(this.form.parent_dept_type_id)
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject && res.length && this.fyCode) {
            const year = res.find(i => i.fy_code === this.fyCode)
            if (year) this.active_year_arr.push(year.fy_code)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.dept_type_id ? 'dept_type_id' : 'department_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const department_id = this.form.department_id
        const dept_name_cn = this.form.dept_name_cn
        const dept_name_en = this.form.dept_name_en
        const dept_code = this.form.dept_code
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const parent_dept_type_id = this.form.parent_dept_type_id
        const seq = this.newSeq()

        if (this.editObject) {
          // 編輯
          updateDepartment(
            department_id,
            dept_name_cn,
            dept_name_en,
            dept_code,
            active_year,
            parent_dept_type_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createDepartment(
            dept_code,
            dept_name_cn,
            dept_name_en,
            active_year,
            parent_dept_type_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
