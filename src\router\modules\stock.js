import Layout from '@/views/layout/Layout'

const stockRouter = {
  path: '/stock',
  component: Layout,
  redirect: 'noredirect',
  name: 'stock',
  meta: {
    title: 'router.stock',
    p_code: 'ac.stock',
    icon: 'inventory',
  },
  children: [
    {
      path: 'purchase',
      component: () => import('@/views/stock/purchase/index.vue'),
      name: 'stockPurchase',
      meta: {
        title: 'router.stockPurchase',
        p_code: 'ac.stock.purchase',
        noCache: true,
      },
    },
    {
      path: 'sales',
      component: () => import('@/views/stock/sales/index.vue'),
      name: 'stockSales',
      meta: {
        title: 'router.stockSales',
        p_code: 'ac.stock.sales',
        noCache: true,
      },
    },
    {
      path: 'monthly_sales',
      component: () => import('@/views/stock/monthlySales/index.vue'),
      name: 'stockMonthlySales',
      meta: {
        title: 'router.stockMonthlySales',
        p_code: 'ac.stock.monthly_sales',
        noCache: true,
      },
    },
    {
      path: 'stock_balance',
      component: () => import('@/views/stock/stockBalance/index.vue'),
      name: 'stockBalance',
      meta: {
        title: 'router.stockBalance',
        p_code: 'ac.stock.stock_balance',
        noCache: true,
      },
    },
    {
      path: 'stock_io',
      component: () => import('@/views/stock/stockIO/index.vue'),
      name: 'stockIO',
      meta: {
        title: 'router.stockIO',
        p_code: 'ac.stock.stock_io',
        noCache: true,
      },
    },
    {
      path: 'profit_report',
      component: () => import('@/views/stock/profitReport/index.vue'),
      name: 'stockProfitReport',
      meta: {
        title: 'router.stockProfitReport',
        p_code: 'ac.stock.profit_report',
        noCache: true,
      },
    },
    {
      path: 'item_setup',
      component: () => import('@/views/stock/itemSetup/index.vue'),
      name: 'stockItemSetup',
      meta: {
        title: 'router.stockItemSetup',
        p_code: 'ac.stock.item_setup',
        noCache: true,
      },
    },
  ],
}

export default stockRouter
