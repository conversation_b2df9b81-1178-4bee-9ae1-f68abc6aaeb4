<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <!-- <div slot="pane-right-filters" class="filter">
      </div>  -->
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="icon add"
            @click="onAddContra"
          >
            <svg-icon icon-class="add" class="action-icon" />
          </div>
          <!-- 按鈕 -->
          <!-- <div v-if="hasPermission_Input" class="icon import" @click="importDialog = true">
            <svg-icon icon-class="import"/>
          </div> -->
          <!--  -->
          <!-- <div v-if="hasPermission_Output" class="icon export" @click="onExport">
            <svg-icon icon-class="export"/>
          </div> -->
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :data="contras"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <!--          <template slot="columns">-->
          <!--            <el-table-column-->
          <!--              v-for="item in styleColumns"-->
          <!--              :key="item.ss_id"-->
          <!--              :label="$t(langKey + item.ss_key)"-->
          <!--              :align="item.alignment"-->
          <!--              :width="item.width"-->
          <!--              :property="item.ss_key"-->
          <!--              :column-key="item.ss_key"-->
          <!--            />-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditContra(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDeleteContra(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-contra="editContra"
        :table-data="contras"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      class="upload-dialog"
      width="450px"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import UploadExcel from '@/components/UploadExcel/index'
import customStyle from '@/views/customStyle/index.vue'
import addPage from './add'
import { deleteContra, fetchContras } from '@/api/assistance/contra'
import { mapGetters } from 'vuex'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import mixinPermission from '@/views/mixins/permission'

export default {
  name: 'AssistanceContraIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    addPage,
    UploadExcel,
  },
  mixins: [loadCustomStyle, mixinPermission],
  data() {
    return {
      showDialog: false,
      importDialog: false,
      loading: false,
      leftView: '',
      contras: [],
      editContra: null,
      langKey: 'assistance.contra.label.',
      tableColumns: ['contra_code', 'contra_name_cn', 'contra_name_en'],
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      // console.log(row)
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onAddContra() {
      this.editContra = null
      this.leftView = 'add'
    },
    onEditContra(scope) {
      this.editContra = scope.row
      this.leftView = 'edit'
      console.log('onEditContra', scope)
    },
    onDeleteContra(scope) {
      console.log('onDelete', scope)
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.contra_name_en : scope.row.contra_name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const contra_id = scope.row.contra_id
          return new Promise((resolve, reject) => {
            deleteContra(contra_id)
              .then(res => {
                if (this.editContra && this.editContra.contra_id === contra_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('confirm.deleteSuccess') })
        })
    },
    fetchData() {
      fetchContras()
        .then(res => {
          console.log('fetchContras', res)
          this.contras = res
          console.log(this.contras)
        })
        .catch(err => {
          console.log('fetchContras', err)
        })
    },
    onViewCancel(update) {
      this.editContra = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      // this.loading = true
      // exportStaffs()
      //   .then(res => exportExcel(res, this.exportFileName))
      //   .then(() => {
      //     this.$message.success(this.$t('file.exportSuccess'))
      //   }).catch(() => {
      //     this.$message.error(this.$t('file.exportError'))
      //   }).finally(() => {
      //     this.loading = false
      //   })
    },
    onImport({ results, header }) {
      // this.loading = true
      // importExcel(this, importStaffs, results, header)
      //   .then(() => this.fetchTree())
      //   .catch(() => {})
      //   .finally(() => {
      //     this.loading = false
      //     this.importDialog = false
      //   })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
