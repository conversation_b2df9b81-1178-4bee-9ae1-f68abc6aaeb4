<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <div v-if="!loading">
      <header v-if="false">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ $t('router.settingScreenBasicSetting') }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            {{ $t($route.meta.title) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </header>
      <div class="fliter">
        <el-form :inline="true" class="mini-form">
          <!-- 會計週期 -->
          <el-form-item :label="$t('stock.years')">
            <el-select
              v-model="preferences.filters.selectedYear"
              class="year"
              style="width: 110px"
              @change="fetchMonthlySales"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>

          <!-- 月份 -->
          <el-form-item
            v-if="preferences.filters.selectedStyle !== 'Y'"
            :label="$t('stock.months')"
          >
            <el-select
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 110px"
              @change="reloadTable"
            >
              <el-option :label="allMonth.label" :value="allMonth.value" />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport"
          />
        </div>
      </div>
      <!-- 全年 -->
      <div v-if="preferences.filters.is_all_month" v-cloak class="salesTable">
        <el-table
          :data="data"
          :row-class-name="isStripe"
          :span-method="spanMethod"
          stripe
          class="elTable"
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('stock.monthlySales.label.sk_code')"
            prop="sk_code"
            min-width="100"
            fixed
            align="center"
          />
          <el-table-column
            :formatter="formatColumnName"
            :label="$t('stock.monthlySales.label.desc')"
            :prop="language === 'en' ? 'sk_name_en' : 'sk_name_cn'"
            min-width="150"
            fixed
            align="center"
          />
          <el-table-column
            v-for="(item, index) in tableData"
            :key="item.value"
            :label="conversionMonth(item.pd_code)"
            :fixed="
              index === tableData.length - 1 && tableData.length > maxColumn ? 'right' : false
            "
            width="260"
            align="center"
          >
            <el-table-column
              :label="$t('stock.monthlySales.label.quantity')"
              width="60"
              align="center"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                {{ tableData[index].items[scope.$index].qty }}
              </template>
            </el-table-column>
            <el-table-column
              :formatter="formatAmount"
              :label="$t('stock.monthlySales.label.averagePrice')"
              width="100"
              header-align="center"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <!--              {{ tableData[index].items[scope.$index].avg }}-->
                <!--                {{ tableData[index].items[scope.$index].avg == null ? '-' : tableData[index].items[scope.$index].avg }}-->

                {{
                  tableData[index].items[scope.$index].avg
                    ? thisAmountFormat(tableData[index].items[scope.$index].avg)
                    : '-'
                }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('stock.monthlySales.label.amount')"
              width="100"
              header-align="center"
              align="right"
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <!--                {{ scope.row }}-->
                {{ thisAmountFormat(tableData[index].items[scope.$index].amount) }}
                <!--                {{ thisAmountFormat(tableData[index].items[scope.$index].amount) }}-->
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>

      <!-- 月份 -->
      <div v-else class="salesTable">
        <el-table
          :data="data"
          :row-class-name="isStripe"
          :span-method="spanMethod"
          stripe
          height="100%"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('stock.monthlySales.label.sk_code')"
            prop="sk_code"
            width="100"
            fixed
            align="center"
          />
          <el-table-column
            :formatter="formatColumnName"
            :label="$t('stock.monthlySales.label.desc')"
            :prop="language === 'en' ? 'sk_name_en' : 'sk_name_cn'"
            width="100"
            fixed
            align="center"
          />
          <el-table-column
            v-for="(item, index) in tableData"
            :key="item.value"
            :label="item.voucher_no"
            :fixed="
              index === tableData.length - 1 && tableData.length > maxColumn ? 'right' : false
            "
            :render-header="handleRenderHeader"
            min-width="260"
            align="center"
          >
            <el-table-column
              :label="tableData[index].date"
              :render-header="handleRenderHeader2"
              :label-class-name="tableData[index].date === 'TOTAL' ? 'empty-column' : ''"
              align="center"
            >
              <el-table-column
                :label="$t('stock.monthlySales.label.quantity')"
                width="60"
                align="center"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  {{ tableData[index].items[scope.$index].qty }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('stock.monthlySales.label.averagePrice')"
                width="100"
                header-align="center"
                align="right"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <!--                  {{ tableData[index].items[scope.$index].avg == null ? '-' : tableData[index].items[scope.$index].avg }}-->

                  {{
                    tableData[index].items[scope.$index].avg
                      ? thisAmountFormat(tableData[index].items[scope.$index].avg)
                      : '-'
                  }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('stock.monthlySales.label.amount')"
                width="100"
                header-align="center"
                align="right"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  {{ thisAmountFormat(scope.row.amount) }}
                  <!--                  {{ tableData[index].items[scope.$index].amount }}-->
                </template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear } from '@/api/master/years'
import { getMonthlySales } from '@/api/stock/monthlySales'
import loadPreferences from '@/views/mixins/loadPreferences'
import permission from '@/views/mixins/permission'
import { amountFormat } from '@/utils'
import { exportExcel } from '@/utils/excel'
import { stockMonthlySalesExport } from '@/api/report/excel'

export default {
  name: 'StockMonthlySalesIndex',
  mixins: [loadPreferences, permission],
  data() {
    return {
      loading: false,
      years: [],
      tableData: [],
      data: [],
      maxColumn: this.calcMaxColumn(),

      monthList: [],
      preferences: {
        filters: {
          selectedYear: '',
          selectedMonth: '',
          is_all_month: true,
        },
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
    allMonth() {
      return {
        label: this.$t('stock.monthlySales.allMonth'),
        value: '',
      }
    },
    // maxColumn() {
    //   const w = document.body.offsetWidth
    //   return (w - 80 - 150) / 260
    // }
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    window.addEventListener('resize', this.resize)
  },
  destroyed() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    calcMaxColumn() {
      const w = document.body.offsetWidth
      return (w - 80 - 150) / 260
    },
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      if (!row.sk_code) {
        return 'count-row'
      }
    },
    resize(c) {
      console.log('resize', c)
      this.maxColumn = this.calcMaxColumn()
    },
    conversionMonth(pd_code) {
      if (!pd_code) return ''
      if (pd_code === 'TOTAL') {
        return this.$t('stock.monthlySales.label.yearTotal')
      } else {
        const item = this.monthList.find(i => i.pd_code === pd_code)
        if (!item) return ''
        return item.pd_name
      }
    },
    getMonthList(val) {
      // 月份的名稱可以手動修改的，不能自己生成
      const item = this.years.find(i => i.fy_code === val)
      if (item) {
        return new Promise((resolve, reject) => {
          getYear(item.fy_id)
            .then(res => {
              const periods = res.periods
              this.monthList = periods
              if (periods.length) {
                const pd = periods.find(i => i.pd_code === this.preferences.filters.selectedMonth)
                if (pd === -1 && periods && res.periods.length) {
                  this.preferences.filters.selectedMonth = ''
                }
              } else {
                this.preferences.filters.selectedMonth = ''
              }
              resolve(res)
            })
            .catch(err => {
              reject(err)
            })
        })
      } else {
        return Promise.reject()
      }
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length) {
            const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
            if (year) {
              return
            }
            this.preferences.filters.selectedYear = this.years[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => this.getMonthList(this.preferences.filters.selectedYear))
        .then(this.reloadTable)
        .finally(() => {
          this.loading = false
        })
    },
    fetchMonthlySales() {
      return new Promise((resolve, reject) => {
        getMonthlySales(this.preferences.filters.selectedYear, '')
          .then(res => {
            this.tableData = res
            res.forEach(ele => {
              this.data = res[0].items
            })
            this.preferences.filters.selectedMonth = ''
            // this.$forceUpdate()
          })
          .then(() => this.getMonthList(this.preferences.filters.selectedYear))
          .then(() => {
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    reloadTable() {
      if (this.preferences.filters.selectedMonth === '') {
        this.$nextTick(() => {
          this.preferences.filters.is_all_month = true
        })
      } else {
        this.$nextTick(() => {
          this.preferences.filters.is_all_month = false
        })
      }
      this.loading = true
      this.tableData = []
      this.data = []

      return new Promise((resolve, reject) => {
        getMonthlySales(
          this.preferences.filters.selectedYear,
          this.preferences.filters.selectedMonth,
        )
          .then(res => {
            console.log('getMonthlySales', res)
            this.tableData = res
            res.forEach(ele => {
              this.data = res[0].items
            })
            resolve()
          })
          .finally(() => {
            this.loading = false
            this.$forceUpdate()
          })
      })
    },
    //
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (!row.sk_code) {
        if (columnIndex === 0) {
          return {
            rowspan: 0,
            colspan: 0,
          }
        } else if (columnIndex === 1) {
          return {
            rowspan: 1,
            colspan: 2,
          }
        }
      }
      return {
        rowspan: 1,
        colspan: 1,
      }
    },
    formatColumnName(row, column, cellValue, index) {
      if (!row.sk_code) {
        return cellValue + ' ' + this.$t('stock.monthlySales.label.total') + '：'
      }
      return cellValue
    },
    formatAmount(row, column, cellValue, index) {
      console.log(amountFormat(cellValue))
      return amountFormat(cellValue)
    },
    handleRenderHeader(h, { column, $index }) {
      if (column.label) {
        return h('span', column.label)
      }
      let label // = column.children[0].label
      if (label === 'TOTAL') {
        label = this.$t('stock.monthlySales.label.monthTotal')
      }
      return h('span', label)
    },
    handleRenderHeader2(h, { column, $index }) {
      let label = column.label
      if (this.preferences.filters.is_all_month && column.label === 'TOTAL') {
        label = this.$t('stock.monthlySales.label.yearTotal')
      } else {
        label = this.$t('stock.monthlySales.label.monthTotal')
      }
      return h('span', label)
    },
    thisAmountFormat(val) {
      return amountFormat(val)
    },
    /**
     * Button Export
     */
    onExport() {
      if (this.loading) {
        return
      }
      const fy_code = this.preferences.filters.selectedYear
      const pd_code = this.preferences.filters.selectedMonth || undefined
      if (!fy_code) {
        // this.$message.error('')
        return
      }
      this.loading = true
      stockMonthlySalesExport({ fy_code, pd_code })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
<style lang="scss" scoped>
.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe {
  background: rgb(232, 245, 254);
}
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .fliter {
    width: 670px;
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
  }
  .salesTable {
    height: calc(100vh - 210px);
    .el-table {
      height: 100%;
    }
    /deep/ table {
      thead tr:nth-child(1) th[colspan='3'] {
        border-bottom: none;
      }
      tbody {
        .cell {
          white-space: nowrap;
          .el-input-number--medium {
            width: 100%;
          }
          .el-input {
            border-radius: 0;
          }
        }
        .count-row {
          td {
            background-color: #e2e2e2;
            color: #404246;
            &:first-child {
              text-align: right;
            }
          }
          .cell {
            @extend .count-row;
          }
        }
      }
    }
  }
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
</style>
