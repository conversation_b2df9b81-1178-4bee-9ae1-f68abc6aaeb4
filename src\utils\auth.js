// import Cookies from 'js-cookie'
import storage from '@/utils/store'

const TokenKey = 'edac-token'

export function getToken() {
  // Cookies.get(TokenKey)
  return storage.get(TokenKey)
}

export function setToken(token) {
  // Cookies.set(Token<PERSON><PERSON>, token)
  return storage.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  // Cookies.remove(TokenKey)
  return storage.remove(Token<PERSON>ey)
}
