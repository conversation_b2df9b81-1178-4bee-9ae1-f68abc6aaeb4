<template>
  <div ref="page" v-loading="loading" class="list">
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :ss_code="ss_code"
      :title="$t('style.defaultTitle')"
      table-type="full-screen-with-first-field"
      @reloadStyleSheets="loadUserStyle"
      @close="onCloseCustomStyleDialog"
    />
    <div ref="filters" class="filter">
      <el-form :inline="true" class="form-inline">
        <el-form-item :label="$t('daily.voucher.label.voucherType')">
          <el-select
            v-model="preferences.filters.vt_code"
            :placeholder="$t('placeholder.select')"
            style="width: 270px"
            @change="onChangeVoucherType"
          >
            <el-option :label="allVoucherType.label" :value="allVoucherType.value" />
            <el-option
              v-for="item in voucherTypeList"
              :key="item.vt_code"
              :label="getVoucherTypeLabel(item)"
              :value="item.vt_code"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('daily.voucher.label.year')">
          <el-select
            v-model="preferences.filters.fy_id"
            :placeholder="$t('placeholder.select')"
            @change="onChangeYear"
          >
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('daily.voucher.label.month')">
          <el-select
            v-model="preferences.filters.pd_code"
            :placeholder="$t('placeholder.select')"
            @change="onChangeMonth"
          >
            <el-option
              v-for="item in month_list"
              :key="item.pd_id"
              :label="item.pd_name"
              :value="item.pd_code"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="OptionShow" class="icon-button">
          <i
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="edac-icon action-icon edac-icon-add1"
            @click="onAdd"
          />
        </el-form-item>
        <el-form-item class="icon-button">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onShowSetting"
          />

          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
          <i
            v-if="hasPermission_Input"
            :title="$t('btnTitle.multipleVoucher')"
            class="edac-icon action-icon edac-icon-export1"
            @click="onMultipleExport"
          />
          <import-dialog
            v-if="importDialogByMultipleVoucher"
            :visible.sync="importDialogByMultipleVoucher"
            :title="
              $t('voucher_type_category.' + vt_category) +
                ' - ' +
                $t('daily.dialog.multipleVoucherImport')
            "
            :on-success="handleImportByMultipleVoucher"
            :on-template="onExportDataByMultipleVoucher"
            :multiple="true"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="app-content">
      <BTable
        ref="table"
        v-loading="!loading && tableLoading"
        :data="tableData"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :show-index="false"
        :show-checkbox="true"
        :default-top="230"
        :height="pageHeight - filtersHeight - 30"
        :sort-config="{ trigger: 'cell', orders: ['asc', 'desc', null] }"
        border
        @changeWidth="changeColumnWidth"
        @checkbox-change="onSelectionChange"
        @checkbox-all="onSelectionChange"
      >
        <template slot="columns">
          <vxe-table-column
            v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
            :key="item.ss_key"
            :title="$t(langKey + item.ss_key)"
            :align="item.alignment"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :field="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :params="{ key: item.ss_key }"
            :sort-method="$refs.table.sortAmountMethod(item.ss_key)"
            sortable
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <ENumeric
                v-if="item.ss_key === 'vc_amount'"
                :read-only="true"
                :value="scope.row.vc_amount"
                style="text-align: right; width: 100%"
              />
              <span v-else-if="item.ss_key === 'vc_method'">
                {{
                  scope.row.vc_method ? voucherMethodList[scope.row.vc_method.toLowerCase()] : ''
                }}
              </span>
              <span
                v-else-if="item.ss_key === 'vc_payee' && scope.row['company_id'] === null"
                class="no_company"
              >{{ scope.row[item.ss_key] }}</span>
              <span
                v-else-if="item.ss_key === 'vc_no' && scope.row['vc_receipt'] === 'X'"
                class="vc_receipt_X"
              >{{ scope.row[item.ss_key] }}</span>
              <span
                v-else-if="
                  item.ss_key === 'vc_no' &&
                    (scope.row['vc_receipt'] === 'N' || !scope.row['vc_receipt'])
                "
                class="vc_receipt_Not_X"
              >{{ scope.row[item.ss_key] }}</span>
              <span
                v-else-if="
                  item.ss_key === 'vc_no' &&
                    scope.row['vc_receipt'].length > 0 &&
                    scope.row['vc_receipt'] !== 'X'
                "
              >{{ scope.row[item.ss_key] }}</span>
              <span v-else-if="item.ss_key === 'ref'">
                <span v-if="scope.row.vc_method === 'CASH'">{{ $t('voucher_method.cash') }}</span>
                <span v-else-if="scope.row.vc_method === 'AUTO'">{{
                  $t('voucher_method.auto')
                }}</span>
                <span v-else-if="scope.row.vc_method === 'TRANSF'">{{
                  $t('voucher_method.transf')
                }}</span>
                <span v-else>{{ scope.row[item.ss_key] }}</span>
              </span>
              <span v-else>{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)],
                  customDateFormat
                )
              }}</span>
            </template>
          </vxe-table-column>
        </template>
        <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
          <div class="operation_icon">
            <i
              :title="$t('btnTitle.view')"
              class="el-icon-view action-icon"
              @click="onView(scope)"
            />
            <i
              v-if="hasPermission_Edit && canEditVoucher(scope.row)"
              :class="{ 'icon-disable': scope.row.vc_rdate }"
              :title="$t('btnTitle.edit')"
              class="el-icon-edit action-icon"
              @click="onEdit(scope.row)"
            />
            <i v-else />
            <i
              v-if="hasPermission_Delete && canEditVoucher(scope.row) && !scope.row.vc_rdate"
              :title="$t('btnTitle.delete')"
              class="el-icon-close action-icon"
              @click="onDelete(scope.row)"
            />
            <i v-else />
          </div>
        </template>
      </BTable>
    </div>
    <div class="app-footer">
      <el-row>
        <el-col :span="10">
          <div class="icon-button-bottom">
            <i
              :class="[{ disable: !showPrintVoucher && !printVoucherLoading }, voucherIcon(1)]"
              :title="$t('btnTitle.voucher')"
              @click="onPrintVoucher"
            />
            <i
              :class="[{ disable: !showPrintVoucher && !printVoucherListLoading }, voucherIcon(2)]"
              :title="$t('btnTitle.voucherList')"
              @click="onPrintVoucherList"
            />
            <i
              :class="[{ disable: !showPrintVoucher && !printConsolidateLoading }, voucherIcon(3)]"
              :title="$t('btnTitle.consolidateVoucherList')"
              @click="onPrintConsolidate"
            />
            <i
              :class="[{ disable: !showPrintReceipt && !printReceiptLoading }, voucherIcon(4)]"
              :title="$t('btnTitle.receipt')"
              @click="onPrintReceipt"
            />
            <i
              :class="[{ disable: !showPrintReceipt && !printPaymentSlipLoading }, voucherIcon(7)]"
              :title="$t('btnTitle.paymentSlip')"
              @click="onPrintPaymentSlip"
            />
            <i
              :class="[{ disable: !showPrintReceipt && !printMailingLabelLoading }, voucherIcon(5)]"
              :title="$t('btnTitle.mailLabel')"
              @click="onPrintMailingLabel"
            />
            <i
              :class="[{ disable: !showPrintReceipt && !printEnvelopeLoading }, voucherIcon(6)]"
              :title="$t('btnTitle.envelope')"
              @click="onPrintEnvelope"
            />
            <!--<i :class="{ 'disable': !showPrintReceipt }" class="edac-icon action-icon edac-icon-icon-test1"/>-->
            <i
              :class="{ disable: !hasPermission_Output || !showPrintVoucher }"
              class="edac-icon action-icon edac-icon-export"
              style="border: 1px solid; font-size: 14px"
              @click="onExportVoucherDetail"
            />
            <i
              :class="{ disable: !hasPermission_Output || !showPrintVoucher }"
              class="edac-icon action-icon edac-icon-jing"
              style="border: 1px solid; font-size: 14px"
              @click="onReorderSummons"
            />
          </div>
        </el-col>
        <el-col :span="7" class="total">
          {{ $t('daily.voucher.label.voucherCount') }}{{ tableData.length }}
        </el-col>
        <el-col :span="7" class="total">
          {{ $t('daily.voucher.label.voucherTotal') }}{{ sum }}
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import ETable from '@/components/ETable'
import BTable from '@/components/BTable'
import ENumeric from '@/components/ENumeric'
import ImportDialog from '@/components/ImportDialog/index'
// API
import { fetchYears, getYear } from '@/api/master/years'
import { exportVoucher, fetchVouchers } from '@/api/daily/payment'
import { fetchVoucherTypes } from '@/api/master/voucherType'
import { deleteVoucher } from '@/api/daily/payment'

import addPage from './add'

// 樣式
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
// 權限
import mixinPermission from '@/views/mixins/permission'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'

import { numberFormat, decimalFormat, toDecimal } from '@/utils'

import handlePDF from './handlePDF'
import { voucherListExport } from '@/api/report/excel'
import { exportExcel } from '@/utils/excel'
import { listenTo } from '@/utils/resizeListen'

const { importVoucherMultiple } = require('@/api/daily/payment')

const { importExcelMultiple } = require('@/utils/excel')

const { importVouchersTemplate } = require('@/api/daily/payment')

const { exportVoucherMultiple } = require('@/api/daily/payment')

const { fastExportVouchers, fastImportVouchers } = require('@/api/daily/payment')

export default {
  name: 'DailyList',
  components: {
    ETable,
    BTable,
    ENumeric,
    addPage,
    customStyle,
    ImportDialog,
  },
  mixins: [mixinPermission, loadPreferences, loadCustomStyle, handlePDF],
  props: {
    vt_category: {
      type: String,
      required: true,
    },
    hasRight: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableData: [],
      loading: false,
      tableLoading: false,

      OptionShow: true,
      OptionObj: {},
      OptionShowList: [],

      periodsList: [],

      // 下拉選項
      years: [],
      month_list: [],
      voucherTypeList: [],

      // 偏好設置
      preferences: {
        filters: {
          vt_code: '', // vt_code
          pd_code: '', // pd_code
          fy_id: '', // fy_id
          fy_code: '', // fy_code
        },
      },
      childPreferences: ['pd_code'],
      currentView: 'list',
      editObject: null,
      isView: false,

      tableColumns: [
        'vc_no',
        'vc_date',
        'vc_summary',
        'vc_amount',
        'vc_method',
        'ref',
        'vc_payee',
        'st_name_',
      ],
      amountColumns: ['vc_amount'],
      langKey: 'daily.payment.label.',
      customDateFormat: 'dd/MM/yy',
      // 勾選的傳票,
      selectedVoucherList: [],

      // 窗口適應
      resizeListen: {},
      filtersHeight: 30,
      pageResizeListen: {},
      pageHeight: 500,

      importDialogByMultipleVoucher: false,
      multipleVoucherLoading: false,
      printVoucherLoading: false,
      printVoucherListLoading: false,
      printConsolidateLoading: false,
      printReceiptLoading: false,
      printMailingLabelLoading: false,
      printEnvelopeLoading: false,
      printPaymentSlipLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentDate', 'user_type', 'user_id']),
    vt_category_list() {
      return ['P', 'R', 'C', 'T', 'J', 'A'].map(i => {
        return {
          label: this.$t('voucher_type_category.' + i),
          value: i,
        }
      })
    },
    ss_code() {
      return this.$route.meta.p_code + (this.hasRight ? '' : '_full')
    },
    allMonth() {
      return {
        pd_code: '',
        pd_id: '',
        pd_name: this.$t('daily.payment.allMonth'),
      }
    },
    voucherMethodList() {
      return {
        transf: this.$t('voucher_method.transf'),
        cash: this.$t('voucher_method.cash'),
        cheque: this.$t('voucher_method.cheque'),
        auto: this.$t('voucher_method.auto'),
        other: this.$t('voucher_method.other'),
      }
    },
    allVoucherType() {
      return {
        value: '',
        label: this.$t('master.voucher_type.all'),
      }
    },
    sum() {
      let num = 0
      this.tableData.forEach(item => {
        num += toDecimal(parseFloat(item.vc_amount))
      })
      num = decimalFormat(num, 2)
      return numberFormat(num)
    },
    pathAdd() {
      return this.$route.matched[1].name + 'Add'
    },
    pathEdit() {
      return this.$route.matched[1].name + 'Edit'
    },
    pathView() {
      return this.$route.matched[1].name + 'View'
    },
    pathReorderSummons() {
      return this.$route.matched[1].name + 'ReorderSummons'
    },
    showPrintVoucher() {
      return this.selectedVoucherList.length
    },
    showPrintReceipt() {
      return this.selectedVoucherList.length && 'PCR'.includes(this.vt_category)
    },
  },
  watch: {
    $route(to, from) {
      this.init()
    },
    defaultScale(val) {
      this.$bus.emit('reloadVoucherPane', val)
    },
    ss_code() {
      this.loadUserStyle()
    },
  },
  created() {
    this.init()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.resizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
  },
  methods: {
    init() {
      this.fetchData()
      this.$bus.emit('enquirySetCurrentRow') // 取消查詢選中
      // this.loadUserStyle()
    },
    fetchData() {
      this.loading = true

      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (
            (!this.preferences.filters.fy_id || !this.preferences.filters.fy_code) &&
            this.years.length > 0
          ) {
            const year = this.years[0]
            this.preferences.filters.fy_id = year.fy_id
            this.preferences.filters.fy_code = year.fy_code
          }
          if (this.preferences.filters.fy_id) {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.fy_id === ele.fy_id) {
                this.preferences.filters.fy_id = ele.fy_id
                this.preferences.filters.fy_code = ele.fy_code
                bool = true
                return
              }
            })
            if (!bool && this.years.length > 0) {
              const year = this.years[0]
              this.preferences.filters.fy_id = year.fy_id
              this.preferences.filters.fy_code = year.fy_code
            }

            return getYear(this.preferences.filters.fy_id)
          } else {
            return null
          }
        })
        .then(res => {
          if (res) {
            this.OptionObj = res
            if (res.fy_status === 'O') {
              this.OptionShow = true
            } else {
              this.OptionShow = false
            }
            this.month_list = [this.allMonth, ...res.periods]
          }
        })
        .then(() =>
          fetchVoucherTypes({
            vt_category: this.vt_category,
            fy_code: this.preferences.filters.fy_code,
          }),
        )
        .then(res => {
          this.voucherTypeList = res
          const haveData = this.voucherTypeList.find(
            i => i.vt_code === this.preferences.filters.vt_code,
          )
          if (!haveData) {
            this.preferences.filters.vt_code = ''
          }
        })
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.month_list &&
            this.month_list.length > 0 &&
            this.preferences.filters.pd_code !== ''
          ) {
            let bool = false
            this.month_list.forEach(i => {
              if (i.pd_code === this.preferences.filters.pd_code) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.pd_code = this.month_list[0].pd_code
            }
          } else {
            !this.preferences.filters.pd_code && (this.preferences.filters.pd_code = '')
          }
        })
        .then(this.loadTableData)
        .then(() => {
          // 刷新右邊津貼查詢
          if (this.voucherTypeList.length) {
            this.enquiryGrantFetch()
          }
        })
        .finally(() => {
          this.$bus.emit('reloadVoucherPane', this.defaultScale)
          this.loading = false
        })

      // this.loadUserPreference()
      //   .then(fetchYears)
      //   .then(res => {
      //     this.years = res
      //     if ((!this.preferences.filters.fy_id || !this.preferences.filters.fy_code) && this.years.length > 0) {
      //       const year = this.years[0]
      //       // this.$set(this.preferences.filters, 'fy_id', year.fy_id)
      //       // this.$set(this.preferences.filters, 'fy_code', year.fy_code)
      //       this.preferences.filters.fy_id = year.fy_id
      //       this.preferences.filters.fy_code = year.fy_code
      //     }
      //   })
      //   .then(() => {
      //     if (this.preferences.filters.fy_id) {
      //       return getYear(this.preferences.filters.fy_id)
      //     } else {
      //       return null
      //     }
      //   })
      //   .then(res => {
      //     if (res) {
      //       this.month_list = [this.allMonth, ...res.periods]
      //       !this.preferences.filters.pd_code && (this.preferences.filters.pd_code = '')
      //     }
      //   })
      //   .then(() => fetchVoucherTypes({ vt_category: this.vt_category }))
      //   .then(res => {
      //     this.voucherTypeList = res
      //   })
      //   .then(this.loadTableData)
      //   .finally(() => {
      //     this.loading = false
      //   })
      // .then(() => {
      //   if (!this.preferences.filters.vt_code && this.voucherTypeList.length > 0) {
      //     this.preferences.filters.vt_code = this.voucherTypeList[0].value
      //   }
      // })
    },
    enquiryGrantFetch() {
      if (!this.voucherTypeList || !this.voucherTypeList.length) return
      let item
      if (this.preferences.filters.vt_code) {
        // 修改右邊津貼查詢
        item = this.voucherTypeList.find(i => i.vt_code === this.preferences.filters.vt_code)
      }
      if (!item) {
        item = this.voucherTypeList[0]
      }

      const ac_code = item.vt_ac_code
      const fund_id = item.fund_id
      if (!ac_code) return

      const fy_id = this.preferences.filters.fy_id
      const fy_code = this.preferences.filters.fy_code
      if (!fy_id || !fy_code) return

      const pd_code = this.preferences.filters.pd_code
      this.$bus.emit('enquiryGrantFetch', fund_id, ac_code, fy_id, fy_code, pd_code)
      this.$bus.emit('enquiryBudgetFetch', fy_id, fy_code, pd_code)
    },
    onChangeYear(val) {
      const item = this.years.find(i => i.fy_id === val)
      if (item) this.preferences.filters.fy_code = item.fy_code

      if (this.preferences.filters.fy_id) {
        getYear(this.preferences.filters.fy_id)
          .then(res => {
            // console.log(res);
            if (res.fy_status === 'O') {
              this.OptionShow = true
            } else {
              this.OptionShow = false
            }
            this.OptionObj = res
            this.periodsList = res.periods
            this.month_list = [this.allMonth, ...res.periods]
            this.preferences.filters.pd_code = ''
            // 重新獲取類別
            fetchVoucherTypes({
              vt_category: this.vt_category,
              fy_code: this.preferences.filters.fy_code,
            }).then(res => {
              this.voucherTypeList = res
              const haveData = this.voucherTypeList.find(
                i => i.vt_code === this.preferences.filters.vt_code,
              )
              if (!haveData) {
                this.preferences.filters.vt_code = ''
              }
            })
          })
          .then(() => {
            this.enquiryGrantFetch()
            this.loadTableData()
          })
      } else {
        this.enquiryGrantFetch()
        this.loadTableData()
      }
    },
    onChangeMonth(val) {
      this.enquiryGrantFetch()
      this.loadTableData()
    },
    onChangeVoucherType(val) {
      if (val) {
        // 修改右邊津貼查詢
        const item = this.voucherTypeList.find(i => i.vt_code === val)
        if (item) {
          // this.$bus.emit('enquiryGrantFetch', item.fund_id, item.vt_ac_code)
        }
      }
      this.enquiryGrantFetch()
      this.loadTableData()
    },
    loadTableData() {
      const fy_code = this.preferences.filters.fy_code
      if (!fy_code) {
        return null
      }
      this.tableLoading = true
      const pd_code = this.preferences.filters.pd_code
      const vt_code = this.preferences.filters.vt_code
      return new Promise((resolve, reject) => {
        fetchVouchers({
          fy_code,
          pd_code,
          vt_code,
          vt_category: this.vt_category,
        })
          .then(res => {
            this.tableData = res
            this.OptionObj.periods.forEach(item => {
              this.tableData.forEach(i => {
                if (item.pd_name === this.getYearMonth(i.vc_date)) {
                  if (item.pd_status === 'O') {
                    i.IfShow = true
                  } else {
                    i.IfShow = false
                  }
                }
                i.canEdit = !i.vc_rdate
                if (['P', 'R', 'C', 'T', 'J'].includes(this.vt_category) && i.vc_rdate) {
                  i.canEdit = true
                }
              })
            })
            resolve(res)
          })
          .catch(() => {
            reject()
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    getYearMonth(date) {
      var dateStr = date
      var reg = /^(\d{4})-(\d{1,2})-(\d{1,2})$/
      dateStr.match(reg)
      return RegExp.$2 + '/' + RegExp.$1
    },
    onAdd() {
      // this.currentView = 'add'
      // this.editObject = null
      // this.isView = false
      this.$router.push({
        name: this.pathAdd,
        params: {
          action: 'add',
          vt_category: this.vt_category,
          vt_code: this.preferences.filters.vt_code,
        },
      })
    },
    onView(scope) {
      //
      // this.currentView = 'view'
      // this.editObject = scope.row
      // this.isView = true
      this.$router.push({
        name: this.pathView,
        params: {
          action: 'view',
          isView: true,
          parentFyCode: this.preferences.filters.fy_code,
          parentPdCode: this.preferences.filters.pd_code,
          editObject: scope.row,
          vt_category: this.vt_category,
        },
      })
    },
    onEdit(row) {
      //
      // this.currentView = 'edit'
      // this.editObject = row
      // this.isView = false
      if (row.vc_rdate) return
      this.$router.push({
        name: this.pathEdit,
        params: {
          action: 'edit',
          isView: false,
          parentFyCode: this.preferences.filters.fy_code,
          editObject: row,
          vt_category: this.vt_category,
        },
      })
    },
    onDelete(row) {
      //
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${row.vc_no}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const vc_id = row.vc_id
          const fy_code = this.preferences.filters.fy_code
          return new Promise((resolve, reject) => {
            deleteVoucher({ fy_code, vc_id })
              .then(res => {
                // this.loadTableData()
                this.onCancel(true)
                this.handleReloadAllEnquiryPage()
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    onCancel(update) {
      this.currentView = 'list'
      this.editObject = null
      this.isView = false
      if (update) this.loadTableData()
    },
    onCopy() {},
    handlePrintValidate() {
      const len = this.selectedVoucherList.length
      if (len === 0) {
        return { state: 0 }
      }
      const vt_category = this.vt_category
      const vl = this.selectedVoucherList
      const fy_code = this.preferences.filters.fy_code
      if (!fy_code) {
        console.error('fy_code error')
        return { state: 0 }
      }
      const vc_id_list = vl.map(i => i.vc_id)
      if (len === 1) {
        return { state: 1, vt_category, fy_code, vc_id_list }
      } else {
        return { state: 2, vt_category, fy_code, vc_id_list }
      }
    },
    onPrintVoucher() {
      if (this.printVoucherLoading || !this.showPrintVoucher) return
      const data = this.handlePrintValidate()
      this.printVoucherLoading = true
      if (data.state === 0) {
        return
      } else if (data.state === 1) {
        this.onPrintVoucherById(data.fy_code, data.vc_id_list[0]).finally(() => {
          this.printVoucherLoading = false
        })
      } else {
        this.handlerPrintVoucherByIdList(this.vt_category, data.fy_code, data.vc_id_list).finally(
          () => {
            this.printVoucherLoading = false
          },
        )
      }
    },
    onPrintVoucherList() {
      // let win = open('about:blank')
      // win.document.body.innerHTML="111"
      // return
      const data = this.handlePrintValidate()
      if (data.state === 0 || this.printVoucherListLoading || !this.showPrintVoucher) {
        return
      }
      this.printVoucherListLoading = true
      const pd_code = this.preferences.filters.pd_code
      let month = ''
      if (pd_code) {
        const m = this.month_list.find(i => i.pd_code === pd_code)
        if (m) {
          month = m.pd_name
        }
      }
      let voucherType = {}
      const v = this.voucherTypeList.find(i => i.vt_code === this.preferences.filters.vt_code)
      if (v) {
        voucherType = v
      }
      this.handlerPrintVoucherList(
        data.vt_category,
        data.fy_code,
        month,
        data.vc_id_list,
        data,
        voucherType,
      ).finally(() => {
        this.printVoucherListLoading = false
      })
    },
    onPrintConsolidate() {
      const data = this.handlePrintValidate()
      if (data.state === 0 || this.printConsolidateLoading || !this.showPrintVoucher) {
        return
      }
      this.printConsolidateLoading = true
      const vt_code = this.preferences.filters.vt_code
      const bank = vt_code ? this.voucherTypeList.find(i => i.vt_code === vt_code) : null
      // const fund = vt_code ? this.voucherTypeList.find(i => i.vt_code === vt_code) : null
      const year = this.years.find(i => i.fy_code === data.fy_code)
      if (!year) {
        console.error('year error')
        return
      }
      const date = this.currentDate

      this.handlerPrintConsolidate(data.vt_category, year, bank, date, data.vc_id_list).finally(
        () => {
          this.printConsolidateLoading = false
        },
      )
    },
    onPrintReceipt() {
      if (!'PCR'.includes(this.vt_category) || this.printReceiptLoading || !this.showPrintVoucher) {
        return
      }
      const data = this.handlePrintValidate()
      if (data.state === 0) {
        return
      }
      this.printReceiptLoading = true
      this.handlerPrintReceipt(data.vt_category, data.fy_code, data.vc_id_list).finally(() => {
        this.printReceiptLoading = false
      })
    },
    onPrintPaymentSlip() {
      if (
        !'PCR'.includes(this.vt_category) ||
        this.printPaymentSlipLoading ||
        !this.showPrintVoucher
      ) {
        return
      }
      const data = this.handlePrintValidate()
      if (data.state === 0) {
        return
      }
      this.printPaymentSlipLoading = true
      this.handlerPrintPaymentSlip(data.vt_category, data.fy_code, data.vc_id_list).finally(() => {
        this.printPaymentSlipLoading = false
      })
    },
    onPrintMailingLabel() {
      if (
        !'PCR'.includes(this.vt_category) ||
        this.printMailingLabelLoading ||
        !this.showPrintVoucher
      ) {
        return
      }
      const company_id_list = this.selectedVoucherList.map(i => i.company_id).filter(i => i)
      if (company_id_list.length === 0) {
        this.$message.error(this.$t('daily.voucher.message.voucherNotHasPayee'))
        return
      }
      this.loading = true
      this.printMailingLabelLoading = true
      this.handlerPrintMailingLabel(company_id_list)
        .then(() => {})
        .finally(() => {
          this.loading = false
          this.printMailingLabelLoading = false
        })
    },
    onPrintEnvelope() {
      if (
        !'PCR'.includes(this.vt_category) ||
        this.printEnvelopeLoading ||
        !this.showPrintVoucher
      ) {
        return
      }
      const company_id_list = this.selectedVoucherList.map(i => i.company_id).filter(i => i)
      if (company_id_list.length === 0) {
        this.$message.error(this.$t('daily.voucher.message.voucherNotHasPayee'))
        return
      }
      this.loading = true
      this.printEnvelopeLoading = true
      this.handlerPrintEnvepole(company_id_list)
        .then(() => {})
        .finally(() => {
          this.loading = false
          this.printEnvelopeLoading = false
        })
    },
    onSelectionChange({
      records,
      reserves,
      indeterminates,
      checked,
      row,
      rowIndex,
      $rowIndex,
      column,
      columnIndex,
      $columnIndex,
      $event,
    }) {
      console.log('onSelectionChange', {
        records,
        reserves,
        indeterminates,
        checked,
        row,
        rowIndex,
        $rowIndex,
        column,
        columnIndex,
        $columnIndex,
        $event,
      })
      this.selectedVoucherList = records
    },
    onCloseCustomStyleDialog() {
      this.$bus.emit('reloadVoucherPane', this.defaultScale)
    },
    onExport(export_type) {
      const user_id = this.user_id
      const fy_code = this.preferences.filters.fy_code
      const pd_code = this.preferences.filters.pd_code || undefined
      const vt_category = this.vt_category
      const vt_code = this.preferences.filters.vt_code || undefined
      this.loading = true
      voucherListExport({ user_id, export_type, fy_code, pd_code, vt_category, vt_code })
        .then(res => exportExcel(res))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    async onExportVoucherDetail() {
      if (!this.hasPermission_Output || !this.showPrintVoucher) return
      try {
        const fy_code = this.preferences.filters.fy_code
        const vc_no = this.selectedVoucherList.map(i => i.vc_no).join(',')
        const show_voucher_info = '1'
        const res = await exportVoucher({
          fy_code,
          vc_no,
          show_voucher_info,
        })
        await exportExcel(res)
        this.$message.success(this.$t('file.exportSuccess'))
        console.log(res)
      } catch (e) {
        console.error(e)
      }
    },
    /* ---------------- 组件通信 ---------------- */
    handleReloadAllEnquiryPage() {
      this.$bus.emit('reloadAllEnquiryPage')
    },
    sortMethod(a, b) {
      // 例如：名称不区分大小写的排序
      console.log(a, b)
      var v1 = (a.name || '').toLowerCase()
      var v2 = (b.name || '').toLowerCase()
      return v1 < v2 ? -1 : v1 > v2 ? 1 : 0
    },
    canEditVoucher(row) {
      if (this.user_type === 'G') {
        if (this.user_id !== row.entry_user_id) {
          return false
        }
      }
      return row.IfShow && row.canEdit
    },
    onMultipleExport() {
      console.log('onMultipleExport')
      this.importDialogByMultipleVoucher = true
    },
    async handleImportByMultipleVoucher(data) {
      console.log(data)
      this.multipleVoucherLoading = true
      const fy_code = this.preferences.filters.fy_code
      const vt_category = this.vt_category
      console.log('data', {
        fy_code,
        user_id: this.user_id,
        voucher_ledger_json: data['sheet'].results,
      })

      return importExcelMultiple(
        this,
        fastImportVouchers,
        {
          sheet: data['sheet'],
        },
        { fy_code, user_id: this.user_id },
      )
        .then(this.init)
        .then(() => {
          this.multipleVoucherLoading = false
          this.importDialogByMultipleVoucher = false
        })
        .catch(() => {
          this.multipleVoucherLoading = false
        })
        .finally(() => {})
    },
    async onExportDataByMultipleVoucher(with_data = 'N') {
      try {
        this.multipleVoucherLoading = true
        const fy_code = this.preferences.filters.fy_code
        const vt_category = this.vt_category
        if (!fy_code || !vt_category) return
        const res = await fastExportVouchers({ fy_code, vt_category, with_data })
        await exportExcel(res, '')
        this.$message.success(this.$t('file.exportSuccess'))

        // this.loading = true
        // exportDepartments()
        //   .then(res => exportExcel(res, this.exportFileName))
        //   .then(() => {
        //     this.$message.success(this.$t('file.exportSuccess'))
        //   }).catch(() => {
        //   this.$message.error(this.$t('file.exportError'))
        // }).finally(() => {
        //   this.loading = false
        // })
      } catch (e) {
        // this.$message.error(this.$t('file.exportError'))
        console.log(e)
      }
      this.multipleVoucherLoading = false
    },
    getVoucherTypeLabel(item) {
      const { vt_code, vt_ac_code } = item
      const name = this.language === 'en' ? item.vt_description_en : item.vt_description_cn
      let label = `[${vt_code}]`
      if (vt_ac_code) {
        label += `[${vt_ac_code}]`
      }
      return label + ' ' + name
    },
    onReorderSummons() {
      if (!this.hasPermission_Output || !this.showPrintVoucher) return
      this.$router.push({
        name: this.pathReorderSummons,
        params: {
          fy_code: this.preferences.filters.fy_code,
          selectData: this.selectedVoucherList,
        },
      })
    },
    voucherIcon(type) {
      switch (type) {
        case 1:
          return this.printVoucherLoading ? 'el-icon-loading' : 'edac-icon action-icon edac-icon-V'
        case 2:
          return this.printVoucherListLoading
            ? 'el-icon-loading'
            : 'edac-icon action-icon edac-icon-L'
        case 3:
          return this.printConsolidateLoading
            ? 'el-icon-loading'
            : 'edac-icon action-icon edac-icon-C'
        case 4:
          return this.printReceiptLoading ? 'el-icon-loading' : 'edac-icon action-icon edac-icon-R'
        case 5:
          return this.printMailingLabelLoading
            ? 'el-icon-loading'
            : 'edac-icon action-icon edac-icon-M'
        case 6:
          return this.printEnvelopeLoading ? 'el-icon-loading' : 'edac-icon action-icon edac-icon-E'
        case 7:
          return this.vt_category !== 'P'
            ? ''
            : this.printPaymentSlipLoading
              ? 'el-icon-loading'
              : 'edac-icon action-icon edac-icon-P'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.list {
  /* 為了高度自適應 */
  display: flex;
  position: relative;
  flex-direction: column;
  /*height: calc(100% - 22px);*/
  height: calc(100% - 10px);
}
.filter {
  .el-select {
    width: 120px;
  }
  .icon-button {
    i {
      background: transparent;
      color: #68afff;
      border-radius: 4px;
      font-size: 20px;
      text-align: center;
      vertical-align: middle;
    }
    .edac-icon-setting1 {
      color: #b9b6b6;
    }
  }
}

.app-content {
  flex: 1;
  .operation_icon {
    user-select: none;
    line-height: inherit;
    i {
      padding: 0 5px;
      vertical-align: middle;
      height: 10px;
      line-height: 10px;
    }
  }
  /deep/ {
    .el-table {
      /*height: 100%;*/
      .el-table__body-wrapper {
        /*height: calc(100% - 30px) !important;*/
      }
    }
    .no_company {
      color: #169a55;
    }
    .vc_receipt_X {
      color: #8c8c8c;
    }
    .vc_receipt_Not_X {
      color: #f56c6c;
    }
  }
}
.app-footer {
  margin-top: 5px;
  color: #606266;
  .icon-button-bottom {
    user-select: none;
    /*float: right;*/
  }
  .total {
    text-align: right;
  }
  i {
    background: transparent;
    color: #68afff;
    border-radius: 4px;
    font-size: 16px;
    text-align: center;
    vertical-align: middle;
  }
}
.icon-disable {
  cursor: not-allowed;
}
::v-deep {
  .vxe-table--fixed-right-wrapper {
    .col--fixed,
    .col--ellipsis {
      padding: 2px 0 !important;
    }
  }
}
</style>
