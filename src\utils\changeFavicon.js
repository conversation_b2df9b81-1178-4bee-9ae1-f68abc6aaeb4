/**
 *Created by ji<PERSON><PERSON><PERSON> on 16/11/29.
 * @param {Sting} url
 * @param {Sting} title
 * @param {Number} w
 * @param {Number} h
 */

function setFavicon(url) {
  const link = document.querySelector("link[rel*='icon']") || document.createElement('link')
  link.type = 'image/x-icon'
  link.rel = 'shortcut icon'
  link.href = url
  document.getElementsByTagName('head')[0].appendChild(link)
}

export default function changeFavicon(icon) {
  let url = ''
  switch (icon) {
    case 'AC':
      url = require('@/assets/favicon/ac.png')
      break
    case 'BG':
      url = require('@/assets/favicon/bg.png')
      break
    case 'PC':
      url = require('@/assets/favicon/pc.png')
      break
  }
  if (url) {
    setFavicon(url)
  }
}
