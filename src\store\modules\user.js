import { login, logout, getUserInfo, getUserInfoByStaff } from '@/api/login'
import { getStaffPreference, getUserPreference } from '@/api/settings/user/preference'
import { getToken, setToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    user_id: '',
    username: '',
    user_type: '',
    grade: '',
    token: getToken(),
    name: '',
    name_en: '',
    name_cn: '',
    roles_id: '',
    permissions: {},
    roles: [],
    lastLoginSystem: window.localStorage.getItem('edacLastLoginSystem') || '',
  },

  mutations: {
    SET_USER_ID: (state, user_id) => {
      state.user_id = user_id
    },
    SET_USER_TYPE: (state, user_type) => {
      state.user_type = user_type
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_NAME_CN: (state, name_cn) => {
      state.name_cn = name_cn
    },
    SET_NAME_EN: (state, name_en) => {
      state.name_en = name_en
    },
    SET_USERNAME: (state, username) => {
      state.username = username
    },
    SET_ROLE: (state, role_id) => {
      state.roles = [role_id]
      state.role_id = role_id
    },
    SET_ROLES: (state, role) => {
      state.roles = role
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_LAST_LOGIN_SYSTEM: (state, system) => {
      state.lastLoginSystem = system
      window.localStorage.setItem('edacLastLoginSystem', system)
    },
  },

  actions: {
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      const username = userInfo.username
      commit('SET_SYSTEM', userInfo.system)
      return new Promise((resolve, reject) => {
        login(username, userInfo.password, userInfo.system)
          .then(data => {
            commit('SET_TOKEN', data.token)
            commit('SET_SYSTEM', userInfo.system)
            commit('SET_LAST_LOGIN_SYSTEM', userInfo.system)
            setToken(data.token)
            resolve()
          })
          .catch(() => {
            resolve()
          })
      })
    },

    // 获取用户信息
    GetUserInfo({ commit, getters, state }) {
      let api = getUserInfoByStaff
      if (getters.system === 'AC') {
        api = getUserInfo
      }
      return new Promise((resolve, reject) => {
        api(state.token)
          .then(response => {
            // 由于mockjs 不支持自定义状态码只能这样hack
            if (response.error) {
              reject('Verification failed, please login again.')
            }
            const data = response

            if (data) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLE', data.role_id)
              // console.log(data.role_id)
            } else {
              reject('getInfo: roles must be a non-null array!')
            }
            if (getters.system === 'AC') {
              commit('SET_NAME_CN', data.name_cn)
              commit('SET_NAME_EN', data.name_en)
              commit('SET_USERNAME', data.username)
              commit('SET_USER_ID', data.user_id)
              commit('SET_USER_TYPE', data.user_type)
              commit('SET_PERMISSIONS', data.permissions)
            } else {
              commit('SET_NAME_CN', data.st_name_cn)
              commit('SET_NAME_EN', data.st_name_en)
              commit('SET_USERNAME', data.username)
              commit('SET_USER_ID', data.staff_id)
              commit('SET_USER_TYPE', data.st_type)
              commit('SET_PERMISSIONS', data.permissions)
            }
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 第三方验证登录
    // LoginByThirdparty({ commit, state }, code) {
    //   return new Promise((resolve, reject) => {
    //     commit('SET_CODE', code)
    //     loginByThirdparty(state.status, state.email, state.code).then(response => {
    //       commit('SET_TOKEN', response.data.token)
    //       setToken(response.data.token)
    //       resolve()
    //     }).catch(error => {
    //       reject(error)
    //     })
    //   })
    // },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
            removeToken()
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resolve()
      })
    },

    // 动态修改权限
    ChangeRoles({ commit, dispatch }, role) {
      return new Promise(resolve => {
        commit('SET_TOKEN', role)
        setToken(role)
        getUserInfo(role).then(response => {
          const data = response.data
          commit('SET_ROLES', data.roles)
          commit('SET_NAME', data.name)
          commit('SET_AVATAR', data.avatar)
          commit('SET_INTRODUCTION', data.introduction)
          dispatch('GenerateRoutes', data) // 动态修改权限后 重绘侧边菜单
          resolve()
        })
      })
    },

    getLastPage({ commit, getters }) {
      return new Promise((resolve, reject) => {
        const pf_code = getters.system.toLowerCase() + '.home'
        const system = getters.system
        const user_id = getters.user_id

        let api = getStaffPreference
        if (system === 'AC') {
          api = getUserPreference
        }

        api(user_id, system, pf_code, 'tab')
          .then(res => {
            // debugger
            if (res && res.page) {
              resolve(res.page)
            } else {
              reject()
            }
          })
          .catch(err => {
            reject(err)
          })
      })
    },
  },
}

export default user
