<template>
  <div class="userInfo">
    <el-form ref="paperForm" :model="paperForm" label-position="right" label-width="100px">
      <!-- 紙編號 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'number',
            message: ' ',
          },
        ]"
        :label="$t('setting.settingPageSet.ps_code')"
      >
        <el-input v-model="paperForm.ps_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('setting.settingPageSet.ps_name_cn')"
      >
        <el-input v-model="paperForm.ps_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('setting.settingPageSet.ps_name_en')"
      >
        <el-input v-model="paperForm.ps_name_en" clearable />
      </el-form-item>
      <!-- 寬度 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'number',
            message: ' ',
          },
        ]"
        :label="$t('setting.settingPageSet.width')"
      >
        <el-input v-model="paperForm.width" clearable />
      </el-form-item>
      <!-- 高度 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'number',
            message: ' ',
          },
        ]"
        :label="$t('setting.settingPageSet.height')"
      >
        <el-input v-model="paperForm.height" clearable />
      </el-form-item>
      <!-- 方向 -->
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('setting.settingPageSet.orientation')"
      >
        <el-select v-model="paperForm.orientation" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in orientation"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editUser ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createPaper, editPaper } from '@/api/settings/paper'
export default {
  name: 'MasterUserAdd',
  props: {
    editUser: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      system: 'AC',
      paperForm: {
        ps_id: 1,
        ps_code: '',
        ps_name_cn: '',
        ps_name_en: '',
        width: '',
        height: '',
        orientation: 'L',
      },
      defaultForm: {
        ps_id: '',
        ps_code: '',
        ps_name_cn: '',
        ps_name_en: '',
        width: '',
        height: '',
        orientation: 'L',
      },

      roles: [],

      orientation: [
        {
          value: 'L',
          label: this.$t('setting.settingPageSet.transverse'),
        },
        {
          value: 'P',
          label: this.$t('setting.settingPageSet.vertical'),
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    editUser() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkRequired(rule, value, callback) {},
    clearPassword() {
      this.paperForm.password = ''
      this.paperForm.confirmPassword = ''
    },
    initData() {
      if (this.editUser) {
        // 編輯
        this.paperForm = Object.assign({}, this.editUser)
        this.paperForm.ps_id = this.editUser.paper_set_id
      } else {
        // 新增
        this.paperForm = Object.assign({}, this.defaultForm)
      }
      this.paperForm.role_id = this.paperForm.role_id ? parseInt(this.paperForm.role_id) : ''
    },
    onSave() {
      this.$refs['paperForm'].validate(valid => {
        if (!valid) {
          return false
        }
        const paper_set_id = this.paperForm.ps_id
        const ps_code = this.paperForm.ps_code ? this.paperForm.ps_code : null
        const ps_name_cn = this.paperForm.ps_name_cn
        const ps_name_en = this.paperForm.ps_name_en
        const width = parseInt(this.paperForm.width)
        const height = parseInt(this.paperForm.height)
        const orientation = this.paperForm.orientation

        if (this.editUser) {
          // 編輯
          editPaper(paper_set_id, ps_code, ps_name_cn, ps_name_en, width, height, orientation)
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createPaper(ps_code, ps_name_cn, ps_name_en, width, height, orientation)
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style scoped></style>
