<script>
import { getUserPreference } from '@/api/settings/user/preference'
import store from '@/store'

export default {
  beforeRouteEnter(to, from, next) {
    const user_id = store.getters.user_id
    const system = store.getters.system
    let defaultPage = 'settingPrintout.daily.pdfvoucherp'
    if (system === 'BG') {
      defaultPage = 'settingPrintout.daily.pdfbudget'
    }
    const pf_code = system.toLowerCase() + '.setting'
    const module_code = 'printoutPage'
    getUserPreference(user_id, system, pf_code, module_code)
      .then(res => {
        if (res && res.page) {
          next({ name: res.page })
        } else {
          next({ name: defaultPage })
        }
      })
      .catch(() => {
        next({ name: defaultPage })
      })
  },
  render: function(h) {
    return h() // avoid warning message
  },
}
</script>
