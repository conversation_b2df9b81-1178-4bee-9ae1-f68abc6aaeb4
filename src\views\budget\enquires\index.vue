<template>
  <div class="budget-enquires">
    <enquire
      :show-title="false"
      :all-permission="true"
      :jump="false"
      :row-selected="false"
      class-name="enquiry-general-budget"
    />
  </div>
</template>

<script>
import enquire from '@/views/daily/enquiry/general.vue'
export default {
  name: 'BudgetEnquires',
  components: {
    enquire,
  },
}
</script>

<style lang="scss" scoped>
.budget-enquires {
  padding: 10px;
  position: relative;
  height: 100%;
  width: 100%;
}
</style>
