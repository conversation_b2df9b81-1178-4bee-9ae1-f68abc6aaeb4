# 開發環境配置
NODE_ENV=development
VUE_APP_IS_PREVIEW=true

# 從 config/global.js 遷移的開發環境配置
VUE_APP_DEV_HOST=
VUE_APP_DEV_PROT=9527
VUE_APP_DEV_OPEN=true
VUE_APP_PROXY_PATH=/api
VUE_APP_PROXY_TARGET=http://*************/edac/public/api

# 從 config/global.js 遷移的生產環境配置（開發環境也可能需要）
VUE_APP_SERVER_NAME="*************"
VUE_APP_PROTOCOL=http
VUE_APP_IP=*************
VUE_APP_PORT=80
VUE_APP_PROJECT_ROOT=edac
VUE_APP_REMOTE_PROJECT_NAME=edac
VUE_APP_URI=public/storage

# 報表服務器配置
VUE_APP_RPT_PROTOCOL=http
VUE_APP_RPT_IP=*************
VUE_APP_RPT_PORT=8080
VUE_APP_RPT_URL=EDReport/run

# 其他配置
VUE_APP_WEB_CODE=AC
VUE_APP_TUTORIAL_PATH=https://www.hkschoolsoftware.com/loading/tutorial?tmp=464ei1Qv8oFENolBHwzRkkp%2FnQqorf7sG2CYjq1RhQ

# API配置（從 config/dev.env.js 遷移）
VUE_APP_BASE_API=/api

# 資源路徑配置
VUE_APP_ASSETS_PUBLIC_PATH=/
