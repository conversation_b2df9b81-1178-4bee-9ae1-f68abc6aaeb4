import request from '@/utils/request'

/**
 * 返回採購申請記錄列表
 * @param {string} fy_code 會計週期編號
 * @param {string} [pro_no] 採購編號
 * @param {string} [apply_staff_id] 當前用戶
 * @param {string} [apply_begin_date] 申請日期開始
 * @param {string} [apply_end_date] 申請日期結束
 * @param {string} [pro_level_code] 採購等級編號
 * @param {string} [g_budget_code] 預算群組編號
 * @param {string} [staff_name] 申請人
 * @param {string} [title_or_remark] 描述
 * @param {string} [status] 狀態篩選條件有:A=未遞交,G=覆核中,GR=覆核拒絕,GA=覆核退回,I=報價中,K=選擇報價,M=審批中,MR=審批拒絕,MI,MA=審批退回,O=通過
 * @param {number} [page_size] 每頁數量
 * @param {number} [page] 查找哪一頁
 * @return {Promise}
 */
export function fetchProcurementApplications({
  fy_code,
  pro_no,
  apply_begin_date,
  apply_end_date,
  pro_level_code,
  g_budget_code,
  apply_staff_id,
  staff_name,
  title_or_remark,
  status,
  page_size,
  page,
}) {
  return request({
    url: '/procurement-applications',
    method: 'get',
    params: {
      fy_code,
      pro_no,
      apply_begin_date,
      apply_end_date,
      pro_level_code,
      g_budget_code,
      apply_staff_id,
      staff_name,
      title_or_remark,
      status,
      page_size,
      page,
    },
  })
}

/**
 * 返回採購申請記錄詳情
 * @param {number} pro_application_id 申請id
 * @return {Promise}
 */
export function getProcurementApplication(pro_application_id) {
  return request({
    url: '/procurement-applications/actions/inquire',
    method: 'get',
    params: {
      pro_application_id,
    },
  })
}

/**
 * 新增採購申請
 * @param {string} fy_code 會計週期編號
 * @param {string} pro_level_id 採購等級id
 * @param {string} pro_no 採購編號
 * @param {string} pro_title 採購申請標題
 * @param {string} [pro_remark] 採購申請備註
 * @param {string} apply_date 申請日期
 * @param {string} invite_date 邀請供應商日期
 * @param {string} type 採購類新: P=貨品,S=服務
 * @param {string} contact 聯繫人
 * @param {string} contact_title 聯繫人稱呼
 * @param {string} contact_tel 聯繫人電話
 * @param {string} [budget_code] 預算編號
 * @param {string} reply_end_date 供應商回復截止日期
 * @param {number} quoted_valid_days 供應商報價有效日期(供應商回復截止日期開始計算)
 * @param {number} apply_staff_id 申請人staff_id
 * @param {string} apply_review_handlers_json 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
 * @param {string} apply_approve_handlers_json 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
 * @return {Promise}
 */
export function createProcurementApplications({
  fy_code,
  pro_level_id,
  pro_no,
  pro_title,
  pro_remark,
  apply_date,
  invite_date,
  type,
  contact,
  contact_title,
  contact_tel,
  budget_code,
  reply_end_date,
  quoted_valid_days,
  apply_staff_id,
  apply_review_handlers_json,
  apply_approve_handlers_json,
}) {
  return request({
    url: '/procurement-applications/actions/create',
    method: 'post',
    data: {
      fy_code,
      pro_level_id,
      pro_no,
      pro_title,
      pro_remark,
      apply_date,
      invite_date,
      type,
      contact,
      contact_title,
      contact_tel,
      budget_code,
      reply_end_date,
      quoted_valid_days,
      apply_staff_id,
      apply_review_handlers_json: JSON.stringify(apply_review_handlers_json),
      apply_approve_handlers_json: JSON.stringify(apply_approve_handlers_json),
    },
  })
}

/**
 * 新增採購申請
 * @param {number} pro_application_id 申請id
 * @param {string} fy_code 會計週期編號
 * @param {string} pro_no 採購編號
 * @param {string} pro_title 採購申請標題
 * @param {string} [pro_remark] 採購申請備註
 * @param {string} apply_date 申請日期
 * @param {string} invite_date 邀請供應商日期
 * @param {string} type 採購類新: P=貨品,S=服務
 * @param {string} contact 聯繫人
 * @param {string} contact_title 聯繫人稱呼
 * @param {string} contact_tel 聯繫人電話
 * @param {string} [budget_code] 預算編號
 * @param {string} reply_end_date 供應商回復截止日期
 * @param {number} quoted_valid_days 供應商報價有效日期(供應商回復截止日期開始計算)
 * @param {number} apply_staff_id 申請人staff_id
 * @param {string} apply_review_handlers_json 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
 * @param {string} apply_approve_handlers_json 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
 * @return {Promise}
 */
export function editProcurementApplications({
  pro_application_id,
  fy_code,
  pro_no,
  pro_title,
  pro_remark,
  apply_date,
  invite_date,
  type,
  contact,
  contact_title,
  contact_tel,
  budget_code,
  reply_end_date,
  quoted_valid_days,
  apply_staff_id,
  apply_review_handlers_json,
  apply_approve_handlers_json,
}) {
  return request({
    url: '/procurement-applications/actions/update',
    method: 'post',
    data: {
      pro_application_id,
      fy_code,
      pro_no,
      pro_title,
      pro_remark,
      apply_date,
      invite_date,
      type,
      contact,
      contact_title,
      contact_tel,
      budget_code,
      reply_end_date,
      quoted_valid_days,
      apply_staff_id,
      apply_review_handlers_json: JSON.stringify(apply_review_handlers_json),
      apply_approve_handlers_json: JSON.stringify(apply_approve_handlers_json),
    },
  })
}

/**
 * 新增採購申請
 * @param {number} pro_application_id 申請id
 * @param {number} status 狀態 A->G,GA->A,GA->G,I->K,K->M,MI->I,MA->A
 * @return {Promise}
 */
export function editProcurementApplicationsStatus({ pro_application_id, status }) {
  return request({
    url: '/procurement-applications/actions/update-status',
    method: 'post',
    data: {
      pro_application_id,
      status,
    },
  })
}

/**
 * 更新採購申請物品/服務
 * @param {number} pro_application_id 申請id
 * @param {string} items [{"item_name":"aaa","item_index":0,"item_remark":"aaa","item_qty":1},{...}]
 * @return {Promise}
 */
export function editProcurementApplicationsItems({ pro_application_id, items }) {
  return request({
    url: '/procurement-applications/actions/update-items',
    method: 'post',
    data: {
      pro_application_id,
      items_json: JSON.stringify(items),
    },
  })
}

/**
 * 更新採購供應商信息
 * @param {number} pro_application_id 申請id
 * @param {string} supplier_json [{"supplier_name":"aaa","supplier_type":"COM","supplier_index":0,"supplier_tel":"2223333","supplier_fax":"1111222","supplier_contact","aaa"},{...}]
 * @param {string} [not_enough_suppliers_reason] 供應商數量不足原因
 * @return {Promise}
 */
export function editProcurementApplicationsSuppliers({
  pro_application_id,
  suppliers,
  not_enough_suppliers_reason,
}) {
  return request({
    url: '/procurement-applications/actions/update-suppliers',
    method: 'post',
    data: {
      pro_application_id,
      supplier_json: JSON.stringify(suppliers),
      not_enough_suppliers_reason,
    },
  })
}

/**
 * 覆核或審批採購
 * @param {number} pro_apply_handler_id 複核人或者審批人的pro_apply_handler_id
 * @param {string} status 審核狀態,W=未審核,A=通過,B=退回,C=退回到開始,R=拒絕
 * @param {string} [comment] 意見
 * @return {Promise}
 */
export function reviewApproveProcurementApplications({ pro_apply_handler_id, status, comment }) {
  return request({
    url: '/procurement-applications/actions/review-approve',
    method: 'post',
    data: {
      pro_apply_handler_id,
      status,
      comment,
    },
  })
}

/**
 * 返回等待審核/審批的申請記錄列表
 * @param {string} fy_code 會計週期編號
 * @param {number} [handle_staff_id] 處理人staff_id,管理員不需要傳
 * @param {string} [pro_no] 採購編號
 * @param {string} [title_or_remark] 描述
 * @param {number} [page_size] 每頁數量
 * @param {number} [page] 查找哪一頁
 * @return {Promise}
 */
export function fetchProcurementApplicationsUnhandledList({
  fy_code,
  handle_staff_id,
  pro_no,
  title_or_remark,
  page_size,
  page,
}) {
  return request({
    url: '/procurement-applications/actions/unhandled-list',
    method: 'get',
    params: {
      fy_code,
      handle_staff_id,
      pro_no,
      title_or_remark,
      page_size,
      page,
    },
  })
}

/**
 * 審核/審批的申請記錄
 * @param {string} fy_code 會計週期編號
 * @param {number} [handle_staff_id] 處理人staff_id,管理員不需要傳
 * @param {string} [pro_no] 採購編號
 * @param {string} [title_or_remark] 描述
 * @param {number} [page_size] 每頁數量
 * @param {number} [page] 查找哪一頁
 * @return {Promise}
 */
export function fetchProcurementApplicationshandledList({
  fy_code,
  handle_staff_id,
  pro_no,
  title_or_remark,
  page_size,
  page,
}) {
  return request({
    url: '/procurement-applications/actions/handled-list',
    method: 'get',
    params: {
      fy_code,
      handle_staff_id,
      pro_no,
      title_or_remark,
      page_size,
      page,
    },
  })
}

/**
 * 更新採購供應商報價信息
 * @param {number} pro_application_id 申請id
 * @param {string} quote_json [{"pro_apply_supplier_id":"1","supplier_staus":"Q","items":[{"pro_apply_item_id":"1","price":100,"amount":"500"},{...}]},{...}]
 * @return {Promise}
 */
export function editProcurementApplicationsQuotes({ pro_application_id, quotes }) {
  return request({
    url: '/procurement-applications/actions/update-quotes',
    method: 'post',
    data: {
      pro_application_id,
      quote_json: JSON.stringify(quotes),
    },
  })
}

/**
 * 選擇報價
 * @param {number} pro_apply_supplier_id 要選擇的報價的供應商id
 * @param {string} [not_select_lowest_reason] 不選擇最低報價原因
 * @return {Promise}
 */
export function selectProcurementApplicationsQuotes({
  pro_apply_supplier_id,
  not_select_lowest_reason,
}) {
  return request({
    url: '/procurement-applications/actions/select-quotes',
    method: 'post',
    data: {
      pro_apply_supplier_id,
      not_select_lowest_reason,
    },
  })
}

/**
 * 上傳附件信息
 * @param {string} pro_apply_supplier_id 供應商id
 * @param file
 * @return {Promise}
 */
export function uploadProcurementApplicationsFile({ pro_apply_supplier_id, file }) {
  const data = new FormData()
  data.append('pro_apply_supplier_id', pro_apply_supplier_id)
  data.append('attachment_file', file)
  return request({
    url: '/procurement-applications/actions/quotation-upload',
    method: 'post',
    data,
  })
}

/**
 * 删除报价附件
 * @param {number} pro_attachment_id 附件id
 * @return {Promise}
 */
export function deleteProcurementApplicationsFile(pro_attachment_id) {
  return request({
    url: '/procurement-applications/actions/quotation-delete',
    method: 'post',
    data: {
      pro_attachment_id,
    },
  })
}

/**
 * 獲取過往供應商列表
 * @param {string} [name] 供應商模糊搜索
 * @return {Promise}
 */
export function fetchSupplierList(name) {
  return request({
    url: '/procurement-applications/actions/supplier-list',
    method: 'post',
    data: {
      name,
    },
  })
}
