import request from '@/utils/request'

/**
 * 返回新增后的部門id
 * @param {string} dept_code 部門編號
 * @param {string} dept_name_cn  中文名
 * @param {string} dept_name_en  英文名
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_dept_type_id 父部門類別組id((不傳則為根節點)
 * @param {integer} seq 在組別中的順序
 */
export function createDepartment(
  dept_code,
  dept_name_cn,
  dept_name_en,
  active_year,
  parent_dept_type_id,
  seq,
) {
  return request({
    url: '/departments/actions/create',
    method: 'post',
    data: {
      dept_code,
      dept_name_cn,
      dept_name_en,
      active_year,
      parent_dept_type_id,
      seq,
    },
  })
}

/**
 * 修改部門
 * @param {integer} department_id 部門id
 * @param {string} dept_name_cn 中文名
 * @param {string} dept_name_en 英文名
 * @param {string} dept_code 職員編號
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_dept_type_id 父職員類別組id((不傳則為根節點)
 * @param {integer} seq 在組別中的順序
 */
export function updateDepartment(
  department_id,
  dept_name_cn,
  dept_name_en,
  dept_code,
  active_year,
  parent_dept_type_id,
  seq,
) {
  return request({
    url: '/departments/actions/update',
    method: 'post',
    data: {
      department_id,
      dept_name_cn,
      dept_name_en,
      dept_code,
      active_year,
      parent_dept_type_id,
      seq,
    },
  })
}

/**
 * 刪除部門
 * @param {integer} department_id 部門id
 */
export function deleteDepartment(department_id) {
  return request({
    url: '/departments/actions/delete',
    method: 'post',
    data: {
      department_id,
    },
  })
}

/**
 * 返回部門樹
 * @param {string} fy_code 選擇的會計週期
 */
export function getDepartmentsTree(fy_code) {
  return request({
    url: '/departments/tree',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 返回部門樹節點
 * @param {string} fy_code 選擇的會計週期
 * @param {integer} parent_dept_type_id 上級部門類別id
 * @param {integer} except_dept_type_id 排除部門類別id
 * @param {integer} except_department_id 排除部門id
 */
export function getDepartmentsTreeNode(
  fy_code,
  parent_dept_type_id,
  except_dept_type_id,
  except_department_id,
) {
  return request({
    url: '/departments/tree/node',
    method: 'get',
    params: {
      fy_code,
      parent_dept_type_id,
      except_dept_type_id,
      except_department_id,
    },
  })
}

/**
 * 獲取某職員詳情
 * @param {integer} department_id 職員id
 */
export function getDepartment(department_id) {
  return request({
    url: '/departments/actions/inquire',
    method: 'get',
    params: {
      department_id,
    },
  })
}

/**
 * 返回部門及部門類別excel數據

 */
export function exportDepartments() {
  return request({
    url: '/departments/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入部門及部門類別excel數據
 * @param {string} data_json 匯入的數據
 */
export function importDepartments(data_json) {
  return request({
    url: '/departments/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

/**
 * 返回符合條件的部門
 * @param {integer} fy_code 會計週期編號
 * @param {integer} parent_dept_type_id 父部門類別id
 * @param {string} name 模糊搜索名字
 * @return {Promise}
 */
export function searchDepartment({ fy_code, parent_dept_type_id, name }) {
  return request({
    url: '/departments/actions/search',
    method: 'get',
    params: {
      fy_code,
      parent_dept_type_id,
      name,
    },
  })
}
