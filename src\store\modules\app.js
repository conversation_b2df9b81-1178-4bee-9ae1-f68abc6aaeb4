// import Cookies from 'js-cookie'
import storage from '@/utils/store'
import { getSchoolInfo } from '@/api/school/index.js'
import { editUserPreference, getUserPreference } from '@/api/settings/user/preference'
import {
  getStaffStyleSheets,
  getStaffStyleSheetsMulti,
  getUserStyleSheets,
  getUserStyleSheetsMulti,
} from '@/api/settings/user/style'
import { searchByDate } from '@/api/master/years'
import { getCycleDate } from '@/api/getCycleDate'
import dateUtil from '@/utils/date'
// import { login } from '@/api/login'
// import { setToken } from '@/utils/auth'

// 從環境變量中獲取配置
// 注意：這些變量在編譯時會被 webpack DefinePlugin 替換為實際值
// 如果環境變量不存在，webpack 構建會失敗
const PROTOCOL = process.env.VUE_APP_PROTOCOL
const IP = process.env.VUE_APP_IP
const PORT = process.env.VUE_APP_PORT
const REMOTE_PROJECT_NAME = process.env.VUE_APP_REMOTE_PROJECT_NAME
const URI = process.env.VUE_APP_URI
const TUTORIAL_PATH = process.env.VUE_APP_TUTORIAL_PATH
const app = {
  state: {
    sidebar: {
      opened: storage.get('sidebarStatus') ? !!+storage.get('sidebarStatus') : true,
      withoutAnimation: false,
    },
    device: 'desktop',
    language: storage.getLocation('language') || 'en',
    size: storage.get('size') || 'medium',

    school: {
      sch_chq_type: '',
      sch_code: '',
      sch_pre: '',
      sch_encrypt: '',
      sch_logo: '',
      sch_gray_logo: '',
      sch_name_cn: '',
      sch_name_en: '',
      sch_start_month: '',
      sch_years: '',
      school_id: 0,
      systems: [],
    },
    schCode: '',
    system: storage.get('system') || 'AC',
    currentDate: dateUtil.format(new Date(), 'yyyy-MM-dd'),
    currentYear: null,
    styles: {
      globalFontFamily: 'Microsoft YaHei',
      globalFontSize: '10',

      menuMainHeight: 50,
      menuSecondHeight: 30,
      menuOptionsHeight: 20,
      menuFontFamily: 'Microsoft YaHei',
      menuMainFontSize: 12,
      menuSecondFontSize: 12,
      menuMainBackgroundColor: '#3e97dd',
      menuSecondBackgroundColor: '#ffffff',
      menuMainHoverColor: '#3e97dd',
      menuSecondHoverColor: '#2e9eff',

      tableHeaderFontColor: '#909399',
      tableHeaderBackgroundColor: '#ffffff',

      contentBackgroundColor: '#e8f5fe',
      contentLineHeight: 23,
      dateFormat: 'dd/MM/yyyy',

      grantTheme: '#3e97dd',
      budgetTheme: '#3e97dd',
      contraTheme: '#3e97dd',
      deptTheme: '#3e97dd',
      staffTheme: '#3e97dd',
      generalTheme: '#3e97dd',
    },
    remoteServerInfo: {
      protocol: PROTOCOL,
      ip: IP,
      port: PORT,
      remoteProjectName: REMOTE_PROJECT_NAME,
      uri: URI,
      remoteFilePath: `${PROTOCOL}://${IP}:${PORT}/${REMOTE_PROJECT_NAME}/${URI}/`,
      tutorialPath: TUTORIAL_PATH,
    },
  },
  mutations: {
    TOGGLE_SIDEBAR: state => {
      state.sidebar.opened = !state.sidebar.opened
      state.sidebar.withoutAnimation = false
      if (state.sidebar.opened) {
        storage.set('sidebarStatus', 1)
      } else {
        storage.set('sidebarStatus', 0)
      }
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
      storage.set('sidebarStatus', 0)
      state.sidebar.opened = false
      state.sidebar.withoutAnimation = withoutAnimation
    },
    TOGGLE_DEVICE: (state, device) => {
      state.device = device
    },
    SET_LANGUAGE: (state, language) => {
      state.language = language
      storage.setLocation('language', language)
    },
    SET_SIZE: (state, size) => {
      state.size = size
      storage.set('size', size)
    },
    // 設置學校信息
    SET_SCHOOL: (state, info) => {
      state.school.sch_chq_type = info.sch_chq_type
      state.school.sch_code = info.sch_code
      state.school.sch_pre = info.sch_pre
      state.school.sch_encrypt = info.sch_encrypt
      state.school.sch_logo = info.sch_logo
      state.school.sch_gray_logo = info.sch_gray_logo
      state.school.sch_name_cn = info.sch_name_cn
      state.school.sch_name_en = info.sch_name_en
      state.school.sch_start_month = info.sch_start_month
      state.school.sch_years = info.sch_years
      state.school.school_id = info.school_id
      state.school.edbg_second_approve = info.edbg_second_approve
      state.school.systems = info.systems.split(',')
    },
    SET_SYSTEM: (state, system) => {
      state.system = system
      storage.set('system', system)
    },
    SET_SCHOOL_CODE: (state, schCode) => {
      state.schCode = schCode
    },
    SET_CURRENT_DATE: (state, date) => {
      state.currentDate = date
    },
    SET_CURRENT_YEAR: (state, yearObject) => {
      state.currentYear = yearObject
    },

    SET_STYLES_GLOBAL_FONT_FAMILY: (state, fontFamily) => {
      state.styles.globalFontFamily = fontFamily
    },
    SET_STYLES_GLOBAL_FONT_SIZE: (state, fontSize) => {
      state.styles.globalFontSize = fontSize
    },
    SET_STYLES_MENU_MAIN_HEIGHT: (state, height) => {
      state.styles.menuMainHeight = height
    },
    SET_STYLES_MENU_SECOND_HEIGHT: (state, height) => {
      state.styles.menuSecondHeight = height
    },
    SET_STYLES_MENU_OPTION_HEIGHT: (state, height) => {
      state.styles.menuOptionsHeight = height
    },
    SET_STYLES_MENU_FONT_FAMILY: (state, fontFamily) => {
      state.styles.menuFontFamily = fontFamily
    },
    SET_STYLES_MENU_MAIN_FONT_SIZE: (state, fontSize) => {
      state.styles.menuMainFontSize = fontSize
    },
    SET_STYLES_MENU_SECOND_FONT_SIZE: (state, fontSize) => {
      state.styles.menuSecondFontSize = fontSize
    },
    SET_STYLES_MENU_MAIN_BACKGROUND_COLOR: (state, color) => {
      state.styles.menuMainBackgroundColor = color
    },
    SET_STYLES_MENU_SECOND_BACKGROUND_COLOR: (state, color) => {
      state.styles.menuSecondBackgroundColor = color
    },
    SET_STYLES_MENU_MAIN_HOVER_COLOR: (state, color) => {
      state.styles.menuMainHoverColor = color
    },
    SET_STYLES_MENU_SECOND_HOVER_COLOR: (state, color) => {
      state.styles.menuSecondHoverColor = color
    },
    SET_STYLES_TABLE_HEADER_FONT_COLOR: (state, color) => {
      state.styles.tableHeaderFontColor = color
    },
    SET_STYLES_TABLE_HEADER_BACKGROUND_COLOR: (state, color) => {
      state.styles.tableHeaderBackgroundColor = color
    },
    SET_STYLES_CONTENT_BACKGROUND_COLOR: (state, color) => {
      state.styles.contentBackgroundColor = color
    },
    SET_STYLES_CONTENT_LINE_HEIGHT: (state, height) => {
      state.styles.contentLineHeight = height
    },
    SET_DATE_FORMAT: (state, dateFormat) => {
      state.styles.dateFormat = dateFormat
    },
    SET_STYLES_ENQUIRY: (state, { key, value }) => {
      state.styles[key] = value
    },
  },
  actions: {
    toggleSideBar({ commit }) {
      commit('TOGGLE_SIDEBAR')
    },
    closeSideBar({ commit }, { withoutAnimation }) {
      commit('CLOSE_SIDEBAR', withoutAnimation)
    },
    toggleDevice({ commit }, device) {
      commit('TOGGLE_DEVICE', device)
    },
    setLanguage({ commit }, language) {
      commit('SET_LANGUAGE', language)
    },
    setSize({ commit }, size) {
      commit('SET_SIZE', size)
    },
    setSystem({ commit }, system) {
      commit('SET_SYSTEM', system)
    },
    getSchool({ commit }) {
      return new Promise((resolve, reject) => {
        getSchoolInfo()
          .then(res => {
            const schoolInfo = res
            const language = storage.getLocation('language') || 'en'
            // Object.keys(res.data.language[0])[0]

            commit('SET_SCHOOL', schoolInfo)
            commit('SET_LANGUAGE', language)
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    setCurrentDate({ commit, getters }, date) {
      return new Promise((resolve, reject) => {
        const pf_code = getters.system.toLowerCase() + '.home'
        editUserPreference(getters.user_id, getters.system, pf_code, 'date', { date: date })
          .then(res => {
            commit('SET_CURRENT_DATE', date)
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    getCurrentDate({ commit, getters }, ignore = false) {
      return new Promise((resolve, reject) => {
        const pf_code = getters.system.toLowerCase() + '.home'
        getUserPreference(getters.user_id, getters.system, pf_code, 'date', ignore)
          .then(res => {
            if (ignore) {
              resolve(res.date)
              return Promise.reject('ignore')
            }
            let date
            if (res && res.date) {
              date = res.date
            } else {
              date = dateUtil.format(new Date(), 'yyyy-MM-dd')
            }
            commit('SET_CURRENT_DATE', date)
            return date
          })
          .then(searchByDate)
          .then(res => {
            commit('SET_CURRENT_YEAR', res)
            resolve(getters.currentDate)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    getCycleDate({ commit }, date) {
      return new Promise((resolve, reject) => {
        searchByDate(date)
          .then(year => {
            getCycleDate(year)
              .then(res => {
                resolve(Object.assign(year, res))
              })
              .catch(error => {
                reject(error)
              })
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    fetchGlobalStyle({ commit, getters, dispatch }) {
      return new Promise((resolve, reject) => {
        // if (getters.system !== 'AC') {
        //   resolve(getters.styles)
        //   return
        // }
        const api = getUserStyleSheetsMulti
        console.log(api, 'getters.user_id, getters.system')
        api({
          user_id: getters.user_id,
          staff_id: getters.user_id,
          system: getters.system,
          ss_code_str:
            'screen_basic_basic,screen_basic_content,screen_basic_menu,screen_basic_table_header',
        })
          .then(res => {
            res.forEach(item => {
              const key = item.ss_key
              const value = item.value
              switch (key) {
                case 'font_family':
                  commit('SET_STYLES_GLOBAL_FONT_FAMILY', value)
                  break
                case 'font_size':
                  commit('SET_STYLES_GLOBAL_FONT_SIZE', value)
                  break

                case 'main_menu_height':
                  commit('SET_STYLES_MENU_MAIN_HEIGHT', value)
                  break
                case 'second_menu_height':
                  commit('SET_STYLES_MENU_SECOND_HEIGHT', value)
                  break
                case 'options_height':
                  commit('SET_STYLES_MENU_OPTION_HEIGHT', value)
                  break
                case 'menu_font_family':
                  commit('SET_STYLES_MENU_FONT_FAMILY', value)
                  break
                case 'main_menu_font_size':
                  commit('SET_STYLES_MENU_MAIN_FONT_SIZE', value)
                  break
                case 'second_menu_font_size':
                  commit('SET_STYLES_MENU_SECOND_FONT_SIZE', value)
                  break
                case 'main_menu_background_color':
                  commit('SET_STYLES_MENU_MAIN_BACKGROUND_COLOR', value)
                  break
                case 'second_menu_background_color':
                  commit('SET_STYLES_MENU_SECOND_BACKGROUND_COLOR', value)
                  break
                case 'main_menu_hover_color':
                  commit('SET_STYLES_MENU_MAIN_HOVER_COLOR', value)
                  break
                case 'second_menu_hover_color':
                  commit('SET_STYLES_MENU_SECOND_HOVER_COLOR', value)
                  break

                case 'table_header_font_color':
                  commit('SET_STYLES_TABLE_HEADER_FONT_COLOR', value)
                  break
                case 'table_header_background_color':
                  commit('SET_STYLES_TABLE_HEADER_BACKGROUND_COLOR', value)
                  break

                case 'content_background_color':
                  commit('SET_STYLES_CONTENT_BACKGROUND_COLOR', value)
                  break
                case 'content_line_height':
                  commit('SET_STYLES_CONTENT_LINE_HEIGHT', value)
                  break
                case 'date_format':
                  commit('SET_DATE_FORMAT', value)
                  break
              }
            })
            // resolve(getters.styles)
          })
          .then(() => dispatch('fetchEnquiryStyle'))
          .then(() => {
            resolve(getters.styles)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    async fetchEnquiryStyle({ commit, getters }) {
      // return new Promise((resolve, reject) => {
      //   const api = getters.system === 'AC' ? getUserStyleSheets : getStaffStyleSheets
      //   api({
      //     user_id: getters.user_id,
      //     staff_id: getters.user_id,
      //     system: getters.system,
      //     ss_code: 'ac.enquiry.grant'
      //   }).then(res => {
      //     const theme = res.find(i => i.ss_key === 'theme' && i.ss_type === 'screen')
      //     if (theme) {
      //
      //     }
      //     resolve(getters.styles)
      //   }).catch(error => {
      //     reject(error)
      //   })
      // })
      if (getters.system !== 'AC') {
        return
      }
      try {
        // const api = getters.system === 'AC' ? getUserStyleSheets : getStaffStyleSheets
        const api = getUserStyleSheetsMulti
        const user_id = getters.user_id
        const staff_id = getters.staff_id
        const system = getters.system

        const res = await api({
          user_id,
          staff_id,
          system,
          ss_code_str:
            'ac.enquiry.grant,ac.enquiry.budget,ac.enquiry.staff,ac.enquiry.department,ac.enquiry.contra,ac.enquiry.general',
          ss_key: 'theme',
          ss_type: 'screen',
        })
        const grantTheme = res.find(
          i => i.ss_code === 'ac.enquiry.grant' && i.ss_key === 'theme' && i.ss_type === 'screen',
        )
        if (grantTheme) {
          commit('SET_STYLES_ENQUIRY', { key: 'grantTheme', value: grantTheme.value })
        }
        const budgetTheme = res.find(
          i => i.ss_code === 'ac.enquiry.budget' && i.ss_key === 'theme' && i.ss_type === 'screen',
        )
        if (budgetTheme) {
          commit('SET_STYLES_ENQUIRY', { key: 'budgetTheme', value: budgetTheme.value })
        }
        const staffTheme = res.find(
          i => i.ss_code === 'ac.enquiry.staff' && i.ss_key === 'theme' && i.ss_type === 'screen',
        )
        if (staffTheme) {
          commit('SET_STYLES_ENQUIRY', { key: 'staffTheme', value: staffTheme.value })
        }
        const deptTheme = res.find(
          i =>
            i.ss_code === 'ac.enquiry.department' && i.ss_key === 'theme' && i.ss_type === 'screen',
        )
        if (deptTheme) {
          commit('SET_STYLES_ENQUIRY', { key: 'deptTheme', value: deptTheme.value })
        }
        const contraTheme = res.find(
          i => i.ss_code === 'ac.enquiry.contra' && i.ss_key === 'theme' && i.ss_type === 'screen',
        )
        if (contraTheme) {
          commit('SET_STYLES_ENQUIRY', { key: 'contraTheme', value: contraTheme.value })
        }
        const generalTheme = res.find(
          i => i.ss_code === 'ac.enquiry.general' && i.ss_key === 'theme' && i.ss_type === 'screen',
        )
        if (generalTheme) {
          commit('SET_STYLES_ENQUIRY', { key: 'generalTheme', value: generalTheme.value })
        }
      } catch (e) {
        console.error(e)
      }
    },
    switchSystem({ commit }, system) {
      commit('SET_SYSTEM', system)
      window.location.reload()
    },
  },
}

export default app
