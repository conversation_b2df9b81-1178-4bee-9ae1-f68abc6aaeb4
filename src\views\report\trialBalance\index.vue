<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <div style="height: 100%">
      <VBreadCrumb class="breadcrumb" />
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="auto" class="mini-form">
          <!-- 撥款 -->
          <el-form-item :label="$t('report.trialBalance.label.fund')">
            <el-select
              v-model="preferences.filters.selectedFundId"
              class="bank"
              style="width: 150px"
            >
              <el-option
                v-for="item in funds"
                :key="item.fund_id"
                :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
                :value="item.fund_id"
              />
            </el-select>
          </el-form-item>
          <!-- 年份 -->
          <el-form-item :label="$t('report.trialBalance.label.year')">
            <el-select
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
          </el-form-item>
          <!-- 時期 -->
          <el-form-item :label="$t('report.trialBalance.label.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :picker-options="pickerOptions"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              type="daterange"-->
            <!--              value-format="yyyy-MM-dd"-->
            <!--              style="width: 250px"-->
            <!--              @change="onChangeDateRange"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>

          <!--          &lt;!&ndash; 年份 &ndash;&gt;-->
          <!--          <el-form-item>-->
          <!--            <i class="el-icon-date" @click="showYearPicker = !showYearPicker"/>-->

          <!--            <el-select-->
          <!--              v-if="showYearPicker"-->
          <!--              v-model="preferences.filters.selectedMonth"-->
          <!--              class="year"-->
          <!--              style="width: 100px"-->
          <!--              @change="onChangeMonth"-->
            <!--            >-->
          <!--              <el-option-->
          <!--                v-for="item in monthList"-->
          <!--                :key="item.pd_id"-->
          <!--                :label="item.pd_name"-->
          <!--                :value="item.pd_code"/>-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <!-- 模式 -->
          <el-form-item :label="$t('report.trialBalance.label.mode')">
            <el-select v-model="preferences.filters.mode" style="width: 150px" @change="reloadData">
              <el-option :label="$t('report.trialBalance.content.individualCode')" :value="1" />
              <el-option :label="$t('report.trialBalance.content.TreeStructure')" :value="2" />
            </el-select>
          </el-form-item>
          <!--  跳過零值  -->
          <el-form-item>
            <el-checkbox
              v-model="preferences.filters.ignore_zero"
              :label="$t('report.trialBalance.label.skipZero')"
              :true-label="1"
              :false-label="0"
            />
          </el-form-item>
          <!-- 審計修正 -->
          <el-form-item v-if="showAudit" :label="$t('report.trialBalance.label.auditAdjust')">
            <el-select v-model="preferences.filters.auditAdjust" style="width: 100px">
              <el-option :label="$t('report.trialBalance.content.all')" :value="0" />
              <el-option :label="$t('report.trialBalance.content.none')" :value="1" />
              <el-option :value="2" :label="$t('report.trialBalance.option1')" />
              <el-option :value="3" :label="$t('report.trialBalance.option2')" />
              <el-option :value="4" :label="$t('report.trialBalance.option3')" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              size="mini"
              type="primary"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
          </el-form-item>

          <div
            class="actions-icon"
            style="display: inline-block; vertical-align: middle; line-height: 25px"
          >
            <i
              :title="$t('btnTitle.pageSetting')"
              class="edac-icon action-icon edac-icon-setting1"
              @click="onSetting"
            />
            <i
              v-if="hasPermission_Output"
              :title="$t('btnTitle.exportExcelPage')"
              class="edac-icon action-icon edac-icon-excel"
              @click="onExport('PAGE')"
            />
            <i
              v-if="hasPermission_Output"
              :title="$t('btnTitle.exportExcelAll')"
              class="edac-icon action-icon edac-icon-excel_add"
              @click="onExport('ALL')"
            />
          </div>
        </el-form>
      </div>
      <div :style="`height: calc(100% - ${filtersHeight}px - 20px)`" class="trial-balance-table">
        <el-table
          v-if="tableShow && preferences.filters.mode === 1"
          ref="codeTable"
          :data="tableDataCode"
          :show-summary="true"
          :summary-method="getSummaries"
          :row-class-name="isStripe"
          height="100%"
          border
          @header-dragend="onHeaderDragend"
        >
          <template v-for="(item, gIndex) in filteredStyleColumns">
            <el-table-column
              v-if="dataTypes[item.ss_key]"
              :key="item.ss_key + gIndex"
              :label="dataTypes[item.ss_key]"
              :column-key="item.ss_key"
              align="center"
            >
              <!--            :label="group.key ? $t(langKey + group.key) : ''"-->
              <el-table-column
                v-if="drGroup.hasOwnProperty(item.ss_key)"
                :sortable="true"
                :label="$t(langKey + drGroup[item.ss_key])"
                :formatter="formatter"
                :prop="drGroup[item.ss_key]"
                :align="item.alignment"
                :width="item.width"
                :label-class-name="drGroup[item.ss_key]"
                :class-name="drGroup[item.ss_key]"
                :column-key="drGroup[item.ss_key]"
              />
              <el-table-column
                v-if="crGroup.hasOwnProperty(item.ss_key)"
                :sortable="true"
                :label="$t(langKey + crGroup[item.ss_key])"
                :formatter="formatter"
                :prop="crGroup[item.ss_key]"
                :align="item.alignment"
                :width="item.width"
                :label-class-name="crGroup[item.ss_key]"
                :class-name="crGroup[item.ss_key]"
                :column-key="crGroup[item.ss_key]"
              />
              <el-table-column
                v-if="item.ss_key === '_index'"
                :align="item.alignment"
                :width="item.width"
                :label-class-name="drGroup[item.ss_key]"
                :class-name="drGroup[item.ss_key]"
                column-key="_index"
                label="#"
                type="index"
              />
              <el-table-column
                v-else-if="!drGroup.hasOwnProperty(item.ss_key)"
                :sortable="true"
                :label="$t(langKey + item.ss_key)"
                :formatter="formatter"
                :prop="
                  item.ss_key === 'name' ? (language === 'en' ? 'name_en' : 'name_cn') : item.ss_key
                "
                :align="item.alignment"
                :width="item.width"
                :label-class-name="item.ss_key"
                :class-name="item.ss_key"
                :column-key="item.ss_key"
              />
            </el-table-column>
          </template>
          <el-table-column
            :resizable="false"
            label-class-name="last"
            class-name="last"
            label=""
            min-width="1"
          />
        </el-table>
        <tree-table
          v-if="tableShow && preferences.filters.mode !== 1"
          ref="treeTable"
          :data="tableData"
          :summary="summary"
          :sum-label="sumLabel"
          :lang-key="langKey"
          :columns="styleColumns"
          :columns-group="dataTypes"
          :dr-group="drGroup"
          :cr-group="crGroup"
          :data-types="dataTypes"
          :expand-all="true"
          :first-field-width="columnIndexWidth"
          number-field="fund_id"
          first-field="_index"
          first-field-align="left"
          folder-field="account_id"
          border
          @changeWidth="handleChangeWidth"
          @changeLevel="handleChangeLevel"
          @changeExpanded="handleChangeExpanded"
          @changeView="handleChangeView"
        />
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import ETable from '@/components/ETable'
import TreeTable from './TreeTableGroup'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import handlePDF from './handlePDF'

import { amountFormat } from '@/utils'

import dateUtil from '@/utils/date'
import { fetchFunds } from '@/api/master/funds'
import { getReportAccountsTrialBalance } from '@/api/report'
import { exportExcel } from '@/utils/excel'
import { trialBalanceExport } from '@/api/report/excel'
import { listenTo } from '@/utils/resizeListen'
import DateRange from '@/components/DateRange/index'
export default {
  name: 'ReportTrialBalanceIndex',
  components: {
    DateRange,
    ETable,
    TreeTable,
    customStyle,
    VBreadCrumb,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      funds: [],

      isAllMonth: true,
      isMonth: false,
      typeOption: {
        X: this.$t('daily.cheque.typeOption.type_X'),
        N: this.$t('daily.cheque.typeOption.type_N'),
        C: this.$t('daily.cheque.typeOption.type_C'),
      },
      bankList: [],
      chequeBooks: [],
      years: '',
      selectedYearId: '',
      tableData: [],
      summary: {},
      data: [],
      dateList: [],
      monthList: [],
      langKey: 'report.trialBalance.label.',
      tableColumns: ['code', 'name', 'bf', 'mid', 'end'],
      amountColumns: ['amount_dr', 'amount_cr'],
      preferences: {
        filters: {
          selectedFundId: '',
          selectedYearCode: '',
          selectedMonth: '',
          begin_date: '',
          end_date: '',
          // date_range: [],
          wholeYear: 0,
          mode: 1,
          ignore_zero: 0,
          auditAdjust: 0,
          currentLevel: 0,
          expanded_list: '',
        },
      },
      currentFilter: {
        selectedFundId: '',
        selectedYearCode: '',
        selectedMonth: '',
        begin_date: '',
        end_date: '',
        // date_range: [],
        wholeYear: 0,
        mode: 1,
        ignore_zero: 0,
        auditAdjust: 0,
      },
      childPreferences: ['selectedMonth'],
      showYearPicker: false,
      periods: [],
      yearLastDate: '',
      yearStartDate: '',
      // dateFormatStr: 'yyyy-MM-dd',

      tableShow: true,

      // pickerOptions: {
      //   disabledDate(time) {
      //     if (that.yearStartDate && that.yearLastDate) {
      //       return time.getTime() > that.yearLastDate.getTime() ||
      //         time.getTime() < that.yearStartDate.getTime()
      //     }
      //     return true
      //   },
      //   shortcuts: [{
      //     text: that.language === 'en' ? 'bf' : '期初',
      //     onClick(picker) {
      //       const date = that.yearStartDate
      //       date.setTime(date.getTime())
      //       picker.$emit('pick', [date, new Date(that.preferences.filters.date_range[1])])
      //     }
      //   }, {
      //     text: '期末',
      //     onClick(picker) {
      //       const date = that.yearLastDate
      //       date.setTime(date.getTime())
      //
      //       picker.$emit('pick', [new Date(that.preferences.filters.date_range[0]), date])
      //     }
      //   }]
      // },
      sumLabelIndex: -1,

      filtersResizeListen: {},
      filtersHeight: 40,
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'currentDate', 'user_id']),
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    tableDataCode() {
      return this.tableData.filter(i => i.code)
    },
    dateStart() {
      // if (!this.currentFilter.date_range || this.currentFilter.date_range.length !== 2) {
      //   return ''
      // }
      if (!this.currentFilter.begin_date) {
        return ''
      }
      const date = dateUtil.format(new Date(this.currentFilter.begin_date), 'dd/MM/yyyy')
      return this.$t('report.trialBalance.label.dateTo', { date })
    },
    dateEnd() {
      // if (!this.currentFilter.date_range || this.currentFilter.date_range.length !== 2) {
      //   return ''
      // }
      if (!this.currentFilter.end_date) {
        return ''
      }
      const date = dateUtil.format(new Date(this.currentFilter.end_date), 'dd/MM/yyyy')
      return this.$t('report.trialBalance.label.dateTo', { date })
    },
    dateRangeStr() {
      // if (!this.currentFilter.date_range || this.currentFilter.date_range.length !== 2) {
      //   return ''
      // }
      if (!this.currentFilter.begin_date || !this.currentFilter.end_date) {
        return ''
      }
      const date1 = dateUtil.format(new Date(this.currentFilter.begin_date), 'dd/MM/yyyy')
      const date2 = dateUtil.format(new Date(this.currentFilter.end_date), 'dd/MM/yyyy')
      return date1 + ' - ' + date2
    },
    dataTypes() {
      return {
        bf: this.dateStart,
        mid: this.dateRangeStr,
        end: this.dateEnd,
      }
    },
    drGroup() {
      return {
        bf: 'bf_dr',
        mid: 'dr',
        end: 'end_dr',
      }
    },
    crGroup() {
      return {
        bf: 'bf_cr',
        mid: 'cr',
        end: 'end_cr',
      }
    },
    showAudit() {
      // if (!this.preferences.filters.date_range ||
      //   this.preferences.filters.date_range.length !== 2 ||
      //   !this.yearLastDate) return false
      if (!this.preferences.filters.end_date || !this.yearLastDate) {
        return false
      }
      const lastDate = dateUtil.format(
        new Date(this.preferences.filters.end_date),
        this.dateFormatStr,
      )
      const last = dateUtil.format(new Date(this.yearLastDate), this.dateFormatStr)

      // return this.preferences.filters.date_range &&
      //   this.preferences.filters.date_range.length === 2 &&
      //   lastDate === last
      return lastDate === last
    },
    sumLabel() {
      return this.$t('report.trialBalance.label.sum')
    },
    columnIndexWidth() {
      const indexCol = this.styleColumns.find(i => i.ss_key === '_index')
      return indexCol ? indexCol.width : 150
    },
    /* 個體-結構 */
    pickerOptions() {
      const that = this
      return {
        disabledDate(time) {
          if (that.yearStartDate && that.yearLastDate) {
            const lastDate = new Date(that.yearLastDate)
            const startDate = new Date(that.yearStartDate)
            return time.getTime() > lastDate.getTime() || time.getTime() < startDate.getTime()
          }
          return true
        },
        shortcuts: [
          // {
          //   text: this.language === 'en' ? 'bf' : '期初',
          //   onClick(picker) {
          //     const date = that.yearStartDate
          //     date.setTime(date.getTime())
          //     picker.$emit('pick', [date, new Date(that.preferences.filters.date_range[1])])
          //   }
          // }, {
          //   text: '期末',
          //   onClick(picker) {
          //     const date = that.yearLastDate
          //     date.setTime(date.getTime())
          //     picker.$emit('pick', [new Date(that.preferences.filters.date_range[0]), date])
          //   }
          // },
          {
            text: this.$t('report.trialBalance.label.wholeYear'),
            onClick(picker) {
              const sDate = dateUtil.format(new Date(that.yearStartDate), that.dateFormatStr)
              const eDate = dateUtil.format(new Date(that.yearLastDate), that.dateFormatStr)
              picker.$emit('pick', [sDate, eDate])
            },
          },
          ...this.monthList.map(pd => {
            return {
              text: pd.pd_name,
              onClick(picker) {
                const sDate = new Date(
                  20 + pd.pd_code.substring(0, 2),
                  Number(pd.pd_code.substring(2, 4) - 1),
                  1,
                )
                const sDateStr = dateUtil.format(sDate, that.dateFormatStr)
                const eDate = dateUtil.format(
                  new Date(sDate.getFullYear(), sDate.getMonth() + 1, 0),
                  that.dateFormatStr,
                )
                picker.$emit('pick', [sDateStr, eDate])
              },
            }
          }),
        ],
      }
    },
    // ,
    // treeTableData() {
    //   const filters = this.currentFilter
    //   const fund = this.funds.find(i => i.fund_id === filters.selectedFundId)
    //   const sum = this.summary
    //   return [
    //     {
    //       fund_id: fund ? fund.fund_id : 0,
    //       bf_cr: sum.bf_cr,
    //       bf_dr: sum.bf_dr,
    //       cr: sum.cr,
    //       dr: sum.dr,
    //       end_cr: sum.end_cr,
    //       end_dr: sum.end_dr,
    //       name_en: fund ? fund.fund_name_en : '',
    //       name_cn: fund ? fund.fund_name_cn : '',
    //       children: this.tableData
    //     }
    //   ]
    // }
    filteredStyleColumns() {
      return this.styleColumns.filter(item => this.dataTypes[item.ss_key])
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    reshow() {
      return new Promise(resolve => {
        // this.tableShow = false
        // this.$nextTick(() => {
        //   this.tableShow = true
        resolve()
        // })
      })
    },
    getAmountType(key) {
      switch (key) {
        case 'bf_dr':
        case 'bf_cr':
          return 'bf' // this.dateStart
        case 'dr':
        case 'cr':
          return 'mid' // this.dateRangeStr
        case 'end_dr':
        case 'end_cr':
          return 'end' // this.dateEnd
      }
      return key
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      // this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          return Promise.resolve()
        })
        // .then(this.getMonth)
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          this.keepFilter()
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            let bool = false
            this.monthList.forEach(i => {
              if (i.pd_code === this.preferences.filters.selectedMonth) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedMonth = this.monthList[0].pd_code
            }
          } else {
            this.preferences.filters.selectedMonth = this.monthList.length
              ? this.monthList[0].pd_code
              : ''
          }
        })
        .finally(() => {
          this.loading = true
        })
        .then(this.reloadData)
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = this.monthList.length
              ? this.monthList[0].pd_code
              : ''
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    getDataRange() {
      // let begin_date, end_date
      // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
      //   return { begin_date, end_date }
      // } else {
      //   begin_date = this.preferences.filters.date_range[0] || undefined
      //   end_date = this.preferences.filters.date_range[1] || undefined
      // }
      if (!this.preferences.filters.begin_date || !this.preferences.filters.end_date) {
        return { begin_date: undefined, end_date: undefined }
      }
      const begin_date = this.preferences.filters.begin_date
      const end_date = this.preferences.filters.end_date
      return { begin_date, end_date }
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const { begin_date, end_date } = this.getDataRange()
        if (!begin_date || !end_date) {
          this.$message.error(this.$t('date.dateTips'))
          this.loading = false
          return
        }

        const fy_code = this.preferences.filters.selectedYearCode || undefined
        const parent_fund_id = this.preferences.filters.selectedFundId || undefined
        const ignore_zero = this.preferences.filters.ignore_zero
        const audit_adjust = this.showAudit ? this.preferences.filters.auditAdjust : 0
        const data_type = this.preferences.filters.mode === 1 ? 'ARRAY' : 'TREE'

        if (!fy_code) {
          this.$message.error(this.$t('report.trialBalance.message.yearSelect'))
          this.loading = false
          return
        }
        if (!parent_fund_id) {
          this.$message.error(this.$t('report.trialBalance.message.fundSelect'))
          this.loading = false
          return
        }

        this.onChangeDateRange([begin_date, end_date])
        this.loading = true

        getReportAccountsTrialBalance({
          parent_fund_id,
          fy_code,
          begin_date,
          end_date,
          audit_adjust,
          ignore_zero,
          data_type,
        })
          .then(res => {
            this.keepFilter()
            // this.tableData = this.formatData(res.data)
            this.tableData = res.data
            this.summary = Object.assign({}, res.summary)
            this.reshow()

            if (this.preferences.filters.mode === 2) {
              const level = this.preferences.filters.currentLevel
              this.$nextTick(() => {
                this.$nextTick(() => {
                  // if (!this.$refs.treeTable) return
                  if (level) {
                    this.$refs.treeTable.showLevel(level)
                    this.preferences.filters.expanded_list = ''
                  } else {
                    this.$refs.treeTable.setExpandItem(this.preferences.filters.expanded_list)
                    this.preferences.filters.currentLevel = 0
                  }
                })
              })
            }
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      data.forEach(item => {
        const newItem = Object.assign({}, item)
        newData.push(newItem)
      })
      return newData
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    onChangeDateRange(val) {},
    onChangeMonth(val) {
      // const item = this.monthList.find(i => i.pd_code === val)
      // if (item) {
      //   console.log(item)
      // }e
      // beginDate
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, this.dateFormatStr)

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, this.dateFormatStr)
      // this.preferences.filters.date_range = [beginDate, endDate]

      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
      this.onChangeDateRange([beginDate, endDate])
    },
    formatter(row, column, cellValue, index) {
      switch (column.property) {
        case 'dr':
        case 'cr':
        case 'bf_dr':
        case 'bf_cr':
        case 'end_dr':
        case 'end_cr':
          return amountFormat(cellValue)
      }
      return cellValue
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateFormatStr,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateFormatStr,
            )
            this.dateCorrection()
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    dateCorrection() {
      // const date_range = this.preferences.filters.date_range
      // if (date_range && date_range.length === 2) {
      //   const start = new Date(date_range[0]).getTime()
      //   const end = new Date(date_range[1]).getTime()
      //   const startDate = new Date(this.yearStartDate)
      //   const yearStart = startDate.getTime()
      //   const endDate = new Date(this.yearLastDate)
      //   const yearEnd = endDate.getTime()
      //   let newStartDate = date_range[0] ? new Date(date_range[0]) : new Date(this.yearStartDate)
      //   if (!(yearStart < start && start < yearEnd)) {
      //     const e = date_range[1] ? new Date(date_range[1]) : new Date(this.yearLastDate)
      //     newStartDate = new Date(this.yearStartDate)
      //     this.$set(this.preferences.filters, 'date_range', [new Date(this.yearStartDate), e])
      //   }
      //   if (!(yearStart < end && end < yearEnd)) {
      //     this.$set(this.preferences.filters, 'date_range', [newStartDate, new Date(this.yearLastDate)])
      //   }
      // } else {
      //   this.$set(this.preferences.filters, 'date_range', [this.yearStartDate, this.yearLastDate])
      // }
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (begin_date || end_date) {
        const start = new Date(begin_date).getTime()
        const end = new Date(end_date).getTime()
        const startDate = new Date(this.yearStartDate)
        const yearStart = startDate.getTime()
        const endDate = new Date(this.yearLastDate)
        const yearEnd = endDate.getTime()
        let newStartDate = begin_date ? new Date(begin_date) : new Date(this.yearStartDate)
        if (!(yearStart < start && start < yearEnd)) {
          const e = end_date ? new Date(end_date) : new Date(this.yearLastDate)
          newStartDate = new Date(this.yearStartDate)
          begin_date = new Date(this.yearStartDate)
          end_date = new Date(e)
        }
        if (!(yearStart < end && end < yearEnd)) {
          begin_date = new Date(newStartDate)
          end_date = new Date(new Date(this.yearLastDate))
        }
      } else {
        begin_date = this.yearStartDate
        end_date = this.yearLastDate
      }

      if (begin_date !== this.preferences.filters.begin_date) {
        this.preferences.filters.begin_date = dateUtil.format(
          new Date(begin_date),
          this.dateFormatStr,
        )
      }
      if (end_date !== this.preferences.filters.end_date) {
        this.preferences.filters.end_date = dateUtil.format(new Date(end_date), this.dateFormatStr)
      }
    },
    keepFilter() {
      this.currentFilter = Object.assign({}, this.preferences.filters)
      // this.currentFilter.date_range = this.preferences.filters.date_range.map(i => i)
    },
    getColumnParent(key) {
      const dt = this.dataTypes
      for (const t in dt) {
        if (t.hasOwnProperty(dt[t])) {
          return t
        }
      }
      return key
    },
    onHeaderDragend(newWidth, oldWidth, column, event) {
      let key = column.columnKey + ''
      if (key.substring(0, 5) === 'name_') {
        key = 'name'
      }
      this.handleChangeWidth(this.getAmountType(key), newWidth)
    },
    handleChangeWidth(key, width) {
      const col = this.styleData.find(item => {
        return item.ss_key === key // && item.ss_type === 'column'
      })
      if (col) {
        col.width = width
        this.saveUserStyleSheets()
      }
    },
    handleChangeLevel(level) {
      this.$set(this.preferences.filters, 'currentLevel', level)
      this.$set(this.preferences.filters, 'expanded_list', '')
      this.$nextTick(() => {
        this.calcSummary()
      })
    },
    handleChangeExpanded(expandedList) {
      this.$set(this.preferences.filters, 'expanded_list', expandedList)
      this.$set(this.preferences.filters, 'currentLevel', 0)
      this.$nextTick(() => {
        this.calcSummary()
      })
    },
    handleChangeView() {
      this.$nextTick(() => {
        this.calcSummary()
      })
    },
    calcSummary() {
      const data = this.$refs.treeTable.formatData
      const arr = data
        .filter(item => {
          return (!item._expanded || item['account_id']) && item._show
        })
        .map(item => {
          return Object.assign({ bf_dr: 0, bf_cr: 0, dr: 0, cr: 0, end_dr: 0, end_cr: 0 }, item)
        })
      const summary = arr.reduce(
        (res, cur) => {
          res.bf_dr += cur.bf_dr
          res.bf_cr += cur.bf_cr
          res.dr += cur.dr
          res.cr += cur.cr
          res.end_dr += cur.end_dr
          res.end_cr += cur.end_cr
          return res
        },
        { bf_dr: 0, bf_cr: 0, dr: 0, cr: 0, end_dr: 0, end_cr: 0 },
      )
      console.table(summary)
      this.summary = summary
    },
    getSummaries(param) {
      let sumLabelIndex = -1
      const { columns } = param
      const sums = []
      const summary = this.summary
      columns.forEach((column, index) => {
        // if (index === 0) {
        //   sums[index] = this.sumLabel
        //   return
        // }
        if (summary.hasOwnProperty(column.property)) {
          if (sumLabelIndex === -1) {
            sumLabelIndex = index - 1
          }
          sums[index] = amountFormat(summary[column.property])
        } else {
          sums[index] = ''
        }
      })
      if (sumLabelIndex > 0) {
        sums[sumLabelIndex] = this.sumLabel
      }
      this.sumLabelIndex = sumLabelIndex
      return sums
    },
    onExport(export_type) {
      const user_id = this.user_id
      const { begin_date, end_date } = this.getDataRange()
      const fy_code = this.preferences.filters.selectedYearCode || undefined
      const parent_fund_id = this.preferences.filters.selectedFundId || undefined
      const ignore_zero = this.preferences.filters.ignore_zero
      const audit_adjust = this.preferences.filters.auditAdjust
      const data_type = this.preferences.filters.mode === 1 ? 'ARRAY' : 'TREE'
      const current_level =
        this.preferences.filters.mode === 2 ? this.preferences.filters.currentLevel : undefined
      this.loading = true
      trialBalanceExport({
        user_id,
        export_type,
        parent_fund_id,
        fy_code,
        begin_date,
        end_date,
        data_type,
        ignore_zero,
        audit_adjust,
        current_level,
      })
        .then(res => exportExcel(res))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;
/deep/ {
  .el-form--inline .el-form-item {
    margin-right: 1px;
  }
}

.app-container {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .filter {
    margin: 5px 0;
    display: flex;
    /deep/ {
      .el-input--medium .el-input__icon {
      }
      /*line-height: 30px;*/
      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
  }
  .trial-balance-table {
    flex: 1;
    height: calc(100vh - 210px);
    .el-table {
      /*height: 100%;*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
      }
    }
  }
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
</style>
