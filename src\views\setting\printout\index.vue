<template>
  <div class="app-content">
    <el-row class="screen-setting">
      <el-col :xs="8" :sm="6" :md="4" :lg="4" class="left">
        <el-menu
          :unique-opened="true"
          :router="true"
          :default-active="defaultActive"
          class="left-menu"
          background-color="#fff"
          text-color="#707070"
          active-text-color="#707070"
          @open="handleOpen"
          @close="handleClose"
        >
          <el-submenu
            v-for="(group, gIndex) in menus"
            :key="group.ps_group_code"
            :index="group.ps_group_code"
          >
            <template slot="title">
              <span class="no-label">{{ gIndex + 1 }}.</span>
              <span>{{ language === 'en' ? group.ps_group_name_en : group.ps_group_name_cn }}</span>
            </template>
            <el-menu-item-group>
              <template v-for="(item, cIndex) in group.settings">
                <el-menu-item
                  v-if="routeName(group, item) !== 'coding'"
                  :key="item.ps_code"
                  :index="item.ps_code"
                  :route="{ name: routeName(group, item) }"
                >
                  <template>
                    <div class="no-label">
                      {{ sNum(cIndex + 1) }})
                    </div>
                    <span>{{ language === 'en' ? item.ps_name_en : item.ps_name_cn }}</span>
                  </template>
                </el-menu-item>
              </template>
            </el-menu-item-group>
          </el-submenu>
        </el-menu>
      </el-col>
      <el-col :xs="16" :sm="18" :md="20" :lg="20" class="right">
        <router-view />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
import { fetchPrintSettingMenu } from '@/api/settings/printSetting'

export default {
  name: 'SettingPrintoutIndex',
  components: {},
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      menus: [],
    }
  },
  computed: {
    ...mapGetters(['language', 'system']),
    defaultActive() {
      const n = this.$route.name
      const arr = n.split('.')
      return arr[arr.length - 1]
    },
  },
  created() {
    this.saveUserLastPage()
    this.loadMenu()
  },
  methods: {
    handleOpen(key, keyPath) {},
    handleClose(key, keyPath) {},
    sNum(num) {
      if (num > 0 && num <= 122) return String.fromCharCode(96 + num)
    },
    loadMenu() {
      fetchPrintSettingMenu(this.system).then(res => {
        this.menus = res
        // this.menus = [
        //   {
        //     'system': 'AC',
        //     'ps_group_code': 'daily',
        //     'ps_group_name_cn': '日常',
        //     'settings': [
        //       {
        //         'ps_code': 'pdfbgbudgetlist',
        //         'ps_name_cn': '付款傳票'
        //       }
        //     ]
        //   }]
      })
    },
    routeName(group, item) {
      const name = 'settingPrintout.' + group.ps_group_code + '.' + item.ps_code
      const link = this.$router.resolve({ name: name })
      if (link.href === '#/') {
        return 'coding'
      }
      return 'settingPrintout.' + group.ps_group_code + '.' + item.ps_code
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.no-label {
  width: 15px;
  display: inline-block;
}
.app-content {
  height: 100%;
  .screen-setting {
    height: 100%;
    .left {
      height: 100%;
      .left-menu {
        height: 100%;
        .el-menu-item {
          &.is-active,
          &:hover {
            background-color: #e8f5fe !important;
          }
        }
      }
    }
  }
}

.app-content {
  .screen-setting {
    height: 100%;
    /deep/ {
      .left {
        height: 100%;
        .left-menu {
          background: #ffffff !important;
          height: 100%;

          .el-submenu__title {
            line-height: 30px;
            height: 30px;
            vertical-align: inherit;
            * {
              vertical-align: inherit;
            }
            i {
              /*color: #fff;*/
            }
          }

          .el-submenu .el-menu-item {
            line-height: 30px;
            height: 30px;
            min-width: unset;
          }

          .el-menu-item-group__title {
            padding: 0;
          }

          .el-submenu .el-submenu__title:hover {
            background-color: #3e97dd94 !important;
          }
        }
      }
      .right {
        .el-input-number--medium,
        .el-input {
          width: 150px;
        }
        .drag-select /deep/ {
          .el-input {
            width: 300px !important;
          }
        }

        .form-table-box {
          /*padding-left: 272px;*/
          padding-bottom: 0px;
          height: auto !important;
          > .el-form-item__content {
            height: auto;
          }
          .form-table {
            display: inline-table;

            td,
            .form-table-cell {
              width: 100px;
              margin: 1px 2px;
              span {
                display: inline-block;
                text-align: center;
              }
            }
          }
        }
        .form-item-simple {
          /deep/ {
            .el-form-item__content {
              height: auto;
            }
          }
          .el-input-number--medium,
          .el-input {
            width: 80px;
          }
        }
        .actions {
          padding-bottom: 10px;
          padding-top: 10px;
        }
      }
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss"></style>
