<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 組別編號 -->
      <el-form-item
        :rules="codeRules"
        :class="{ 'code-rules-error': haveExist }"
        :label="$t('assistance.payeePayerGroup.label.cg_code')"
        prop="cg_code"
      >
        <el-input v-model="form.cg_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.payeePayerGroup.label.cg_name_cn')"
        prop="cg_name_cn"
      >
        <el-input v-model="form.cg_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.payeePayerGroup.label.cg_name_en')"
        prop="cg_name_en"
      >
        <el-input v-model="form.cg_name_en" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :label="$t('assistance.payeePayerGroup.label.parent_company_group_id')"
        prop="parent_company_group_id"
      >
        <el-select
          v-model="form.parent_company_group_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in companyGroups"
            :key="item.value"
            :value="item.company_group_id"
            :label="conversionParentCompanyGroup(item)"
            v-html="conversionParentCompanyGroup(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置 -->
      <el-form-item :label="$t('assistance.payeePayerGroup.label.seq')" prop="seq">
        <el-select v-model="form.seq">
          <el-option
            v-for="item in sepOptions"
            :key="item.company_group_id"
            :label="language === 'en' ? item.comp_name_en : item.comp_name_cn"
            :value="item.company_group_id ? item.company_group_id : item.company_id"
          >
            {{ language === 'en' ? item.comp_name_en : item.comp_name_cn }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.payeePayerGroup.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getCompaniesTreeNode } from '@/api/assistance/payeePayer'
import {
  createCompanyGroup,
  updateCompanyGroup,
  getCompanyGroup,
  fetchCompanyGroups,
} from '@/api/assistance/payeePayer/payeePayerGroup'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'AssistanceCompanyGroupAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        company_group_id: '',
        cg_code: '',
        cg_name_cn: '',
        cg_name_en: '',
        active_year: '',
        parent_company_group_id: '',
        seq: '',
      },
      defaultForm: {
        company_group_id: '',
        cg_code: '',
        cg_name_cn: '',
        cg_name_en: '',
        active_year: '',
        parent_company_group_id: '',
        seq: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      defaultSepOptions: [
        {
          company_group_id: '',
          company_id: '',
          seq: '',
          comp_name_en: this.$t('assistance.payeePayerGroup.label.seq_default'),
          comp_name_cn: this.$t('assistance.payeePayerGroup.label.seq_default'),
        },
      ],
      sepOptions: [
        {
          company_group_id: '',
          company_id: '',
          seq: '',
          comp_name_en: this.$t('assistance.payeePayerGroup.label.seq_default'),
          comp_name_cn: this.$t('assistance.payeePayerGroup.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      defaultCompanyGroups: [
        {
          company_group_id: '',
          cg_name_cn: this.$t('assistance.payeePayerGroup.label.parent_default'),
          cg_name_en: this.$t('assistance.payeePayerGroup.label.parent_default'),
        },
      ],
      companyGroups: [],
      seqStartLevel: 1,
      active_year_arr: [],
      loading: true,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    codeRules() {
      return [{ required: true, validator: this.checkCode, trigger: 'blur' }]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.code === i.code) {
          return
        }
        if (!i.company_id) {
          codeArr.push(i.code)
        }
      })
      return codeArr
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentCompanyGroup(companyGroup, html, startLevel = 1) {
      let text = this.language === 'en' ? companyGroup.cg_name_en : companyGroup.cg_name_cn
      if (html) {
        text = '&nbsp;'.repeat((companyGroup.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    changeParentType() {
      getCompaniesTreeNode({
        parent_company_group_id: this.form.parent_company_group_id,
        except_company_group_id: this.form.company_group_id,
        except_company_id: this.form.company_id,
      }).then(res => {
        this.sepOptions = [...res, ...this.defaultSepOptions]
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].company_group_id ? res[i].company_group_id : res[i].company_id
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.company_group_id) {
        this.form.parent_company_group_id = this.editParent.company_group_id
      } else if (
        this.editObject &&
        this.editObject.parent &&
        this.editObject.parent.company_group_id
      ) {
        this.form.parent_company_group_id = this.editObject.parent.company_group_id
        // this.form.seq = this.editObject.parent.seq
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getCompanyGroup(this.editObject.company_group_id)
            .then(res => {
              this.form = Object.assign({}, res)
              this.setParent()
              this.form.parent_company_group_id =
                res.companyGroupRelation && res.companyGroupRelation.parent_company_group_id
                  ? res.companyGroupRelation.parent_company_group_id
                  : ''
              this.form.seq_i = res.companyGroupRelation ? res.companyGroupRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => fetchCompanyGroups({ company_group_id: this.form.company_group_id }))
        .then(res => {
          this.companyGroups = [...this.defaultCompanyGroups, ...res]
          this.changeParentType(this.form.parent_company_group_id)
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject && this.fyCode) {
            const year = res.find(i => i.fy_code === this.fyCode)
            if (year) this.active_year_arr.push(year.fy_code)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.company_group_id ? 'company_group_id' : 'company_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const company_group_id = this.form.company_group_id
        const cg_code = this.form.cg_code
        const cg_name_cn = this.form.cg_name_cn
        const cg_name_en = this.form.cg_name_en
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const parent_company_group_id = this.form.parent_company_group_id
        const seq = this.newSeq()
        if (this.editObject) {
          // 編輯
          updateCompanyGroup(
            company_group_id,
            cg_code,
            cg_name_cn,
            cg_name_en,
            active_year,
            parent_company_group_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        } else {
          // 新增
          createCompanyGroup(
            cg_code,
            cg_name_cn,
            cg_name_en,
            active_year,
            parent_company_group_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
