@font-face {
  font-family: "edac-icon"; /* Project id 1118104 */
  src: url('iconfont.woff2?t=1748486605291') format('woff2'),
       url('iconfont.woff?t=1748486605291') format('woff'),
       url('iconfont.ttf?t=1748486605291') format('truetype');
}

.edac-icon {
  font-family: "edac-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.edac-icon-P:before {
  content: "\e66f";
}

.edac-icon-jing:before {
  content: "\e663";
}

.edac-icon-copy-file:before {
  content: "\e662";
}

.edac-icon-shouqi:before {
  content: "\e660";
}

.edac-icon-zhankai:before {
  content: "\e661";
}

.edac-icon-cancel:before {
  content: "\e65f";
}

.edac-icon-data-delete1:before {
  content: "\e65d";
}

.edac-icon-dept_delete1:before {
  content: "\e65e";
}

.edac-icon-dept_delete:before {
  content: "\e65b";
}

.edac-icon-data-delete:before {
  content: "\e65c";
}

.edac-icon-data:before {
  content: "\e65a";
}

.edac-icon-add-folder-full:before {
  content: "\e658";
}

.edac-icon-add-list:before {
  content: "\e659";
}

.edac-icon-down:before {
  content: "\e656";
}

.edac-icon-up:before {
  content: "\e657";
}

.edac-icon-folder-f:before {
  content: "\e653";
}

.edac-icon-folder-c:before {
  content: "\e654";
}

.edac-icon-folder-g:before {
  content: "\e655";
}

.edac-icon-change:before {
  content: "\e651";
}

.edac-icon-Cr:before {
  content: "\e652";
}

.edac-icon-unlock:before {
  content: "\e616";
}

.edac-icon-tq:before {
  content: "\e617";
}

.edac-icon-icon-test:before {
  content: "\e618";
}

.edac-icon-Search:before {
  content: "\e619";
}

.edac-icon-M:before {
  content: "\e61a";
}

.edac-icon-table:before {
  content: "\e61b";
}

.edac-icon-N:before {
  content: "\e61c";
}

.edac-icon-icon-test1:before {
  content: "\e61d";
}

.edac-icon-save:before {
  content: "\e61e";
}

.edac-icon-print:before {
  content: "\e61f";
}

.edac-icon-icon-test2:before {
  content: "\e620";
}

.edac-icon-return_list:before {
  content: "\e621";
}

.edac-icon-add_folder:before {
  content: "\e622";
}

.edac-icon-add1:before {
  content: "\e623";
}

.edac-icon-icon-test3:before {
  content: "\e624";
}

.edac-icon-office_clerk:before {
  content: "\e625";
}

.edac-icon-search_:before {
  content: "\e626";
}

.edac-icon-B:before {
  content: "\e627";
}

.edac-icon-cash:before {
  content: "\e628";
}

.edac-icon-setting1:before {
  content: "\e629";
}

.edac-icon-copy_p:before {
  content: "\e62a";
}

.edac-icon-copy:before {
  content: "\e62b";
}

.edac-icon-budget:before {
  content: "\e62c";
}

.edac-icon-record:before {
  content: "\e62d";
}

.edac-icon-receipt:before {
  content: "\e62e";
}

.edac-icon-E_:before {
  content: "\e62f";
}

.edac-icon-company:before {
  content: "\e630";
}

.edac-icon-copy_up:before {
  content: "\e631";
}

.edac-icon-excel_add:before {
  content: "\e632";
}

.edac-icon-allowance:before {
  content: "\e633";
}

.edac-icon-backup_up:before {
  content: "\e634";
}

.edac-icon-I:before {
  content: "\e635";
}

.edac-icon-icon-test4:before {
  content: "\e636";
}

.edac-icon-delete:before {
  content: "\e637";
}

.edac-icon-folder2:before {
  content: "\e638";
}

.edac-icon-R:before {
  content: "\e639";
}

.edac-icon-L:before {
  content: "\e63a";
}

.edac-icon-add_record:before {
  content: "\e63b";
}

.edac-icon-accessory:before {
  content: "\e63c";
}

.edac-icon-E:before {
  content: "\e63d";
}

.edac-icon-G:before {
  content: "\e63e";
}

.edac-icon-hedging:before {
  content: "\e63f";
}

.edac-icon-inport:before {
  content: "\e640";
}

.edac-icon-buyer:before {
  content: "\e641";
}

.edac-icon-dept:before {
  content: "\e642";
}

.edac-icon-copy_down:before {
  content: "\e643";
}

.edac-icon-update:before {
  content: "\e644";
}

.edac-icon-Y:before {
  content: "\e645";
}

.edac-icon-V:before {
  content: "\e646";
}

.edac-icon-excel:before {
  content: "\e647";
}

.edac-icon-C:before {
  content: "\e648";
}

.edac-icon-backup_down:before {
  content: "\e649";
}

.edac-icon-Dr:before {
  content: "\e64a";
}

.edac-icon-lock:before {
  content: "\e64b";
}

.edac-icon-go:before {
  content: "\e64c";
}

.edac-icon-export1:before {
  content: "\e64d";
}

.edac-icon-edit:before {
  content: "\e64e";
}

.edac-icon-reference:before {
  content: "\e64f";
}

.edac-icon-IE:before {
  content: "\e650";
}

.edac-icon-folder1:before {
  content: "\e612";
}

.edac-icon-file-add1:before {
  content: "\e613";
}

.edac-icon-file:before {
  content: "\e614";
}

.edac-icon-folder-add1:before {
  content: "\e615";
}

.edac-icon-search:before {
  content: "\e604";
}

.edac-icon-add:before {
  content: "\e605";
}

.edac-icon-user:before {
  content: "\e606";
}

.edac-icon-auxiliaryFiles:before {
  content: "\e607";
}

.edac-icon-cycle:before {
  content: "\e608";
}

.edac-icon-eye_open:before {
  content: "\e609";
}

.edac-icon-inventory:before {
  content: "\e60a";
}

.edac-icon-daily:before {
  content: "\e60b";
}

.edac-icon-mainArchives:before {
  content: "\e60c";
}

.edac-icon-eye:before {
  content: "\e60d";
}

.edac-icon-report:before {
  content: "\e60e";
}

.edac-icon-new_file:before {
  content: "\e60f";
}

.edac-icon-password:before {
  content: "\e610";
}

.edac-icon-new_folder:before {
  content: "\e611";
}

.edac-icon-set:before {
  content: "\e602";
}

.edac-icon-setting:before {
  content: "\e603";
}

.edac-icon-export:before {
  content: "\e601";
}

.edac-icon-import:before {
  content: "\e600";
}

.edac-icon-filedone:before {
  content: "\e7b4";
}

.edac-icon-file-add:before {
  content: "\e7b7";
}

.edac-icon-folder:before {
  content: "\e7d1";
}

.edac-icon-folder-open:before {
  content: "\e7d2";
}

.edac-icon-folder-add:before {
  content: "\e7d3";
}

