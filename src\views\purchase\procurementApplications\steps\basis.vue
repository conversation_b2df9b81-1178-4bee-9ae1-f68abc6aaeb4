<template>
  <div class="basis-step">
    <el-form ref="form" :model="form" :inline="true" label-width="200px">
      <el-form-item :rules="requiredRules" :label="t('year')" prop="fy_code">
        <el-select v-model="form.fy_code" :disabled="isReadonly" class="input input-select">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>
      </el-form-item>
      <el-form-item :rules="requiredRules" :label="t('applyDate')" prop="apply_date">
        <el-date-picker
          v-model="form.apply_date"
          :disabled="isReadonly"
          value-format="yyyy-MM-dd"
          class="input input-date"
          type="date"
          @change="onChangeApplyDate"
        />
      </el-form-item>
      <br>
      <el-form-item :rules="requiredRules" :label="t('title')" prop="pro_title">
        <el-input v-model="form.pro_title" :disabled="isReadonly" class="input input-text-long" />
      </el-form-item>
      <br>
      <el-form-item :rules="requiredRules" :label="t('proNo')" prop="pro_no" class="pro-no-item">
        <el-input v-model="form.pro_no" :disabled="isReadonly" class="input input-text">
          <el-button
            v-if="!isReadonly"
            slot="append"
            icon="el-icon-refresh"
            @click="onGenerateNo"
          />
        </el-input>
      </el-form-item>
      <el-form-item :label="t('budget')" prop="budget_code">
        <!--        <el-input v-model="form.budget_code" class="input input-select"/>-->

        <budget-select-tree-vue
          :select-all="true"
          :data="budgetGroupList"
          :budget-id.sync="form.budget_id"
          :budget-code.sync="form.budget_code"
          :language="language"
          :disabled-method="budgetDisabledMethod"
          :disabled="isReadonly"
          placeholder="---"
          all-label-cn="---"
          all-label-en="---"
          class="input input-select"
          @change="onChangeBudgetGroup"
        />
      </el-form-item>
      <br>
      <el-form-item
        v-if="review"
        :rules="requiredRules"
        :label="t('reviewHandler')"
        class="input input-level-select"
        prop="apply_review_handlers"
      >
        <procurement-level-select
          v-if="form.apply_review_handlers"
          :data.sync="form.apply_review_handlers"
          :can-change-type="false"
          :staffs="staffs"
          :is-readonly="isReadonly"
        />
      </el-form-item>
      <br v-if="review">
      <el-form-item
        v-if="approve"
        :rules="requiredRules"
        :label="t('approvalHandler')"
        class="input input-level-select"
        prop="apply_approve_handlers"
      >
        <procurement-level-select
          v-if="form.apply_approve_handlers"
          :data.sync="form.apply_approve_handlers"
          :can-change-type="false"
          :staffs="staffs"
          :is-readonly="isReadonly"
        />
      </el-form-item>
      <br v-if="approve">
      <el-form-item :rules="requiredRules" :label="t('type')" prop="type">
        <el-select v-model="form.type" :disabled="isReadonly" class="input input-select">
          <el-option
            v-for="item in categoryList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('inviteDate')" prop="invite_date">
        <el-date-picker
          v-model="form.invite_date"
          :disabled="isReadonly"
          value-format="yyyy-MM-dd"
          class="input input-date"
          type="date"
        />
      </el-form-item>
      <br>
      <el-form-item :rules="requiredRules" :label="t('contact')" prop="contact">
        <el-input
          v-model="form.contact"
          :placeholder="t('contact_name')"
          :disabled="isReadonly"
          class="input input-text"
          style="width: 120px; display: inline-block"
        />
        <el-input
          v-model="form.contact_title"
          :placeholder="t('contact_title')"
          :disabled="isReadonly"
          style="width: 84px; display: inline-block"
        />
      </el-form-item>
      <el-form-item :rules="requiredRules" :label="t('contactTel')" prop="contact_tel">
        <el-input v-model="form.contact_tel" :disabled="isReadonly" class="input input-text" />
        <!--
          @input.native="onChangeContactTel"
          @keydown.native="onChangeContactTel"
          @keyup.native="onChangeContactTel"
          @change.native="onChangeContactTel"
      -->
      </el-form-item>
      <br>
      <el-form-item :rules="requiredRules" :label="t('replyEndDate')" prop="reply_end_date">
        <el-date-picker
          v-model="form.reply_end_date"
          :disabled="isReadonly"
          value-format="yyyy-MM-dd"
          class="input input-date"
          type="date"
        />
      </el-form-item>
      <el-form-item :rules="requiredRules" :label="t('quotedValidDays')" prop="quoted_valid_days">
        <el-input-number
          v-model="form.quoted_valid_days"
          :disabled="isReadonly"
          :min="0"
          class="input input-text"
        />
      </el-form-item>
      <br>
      <el-form-item :label="t('remark')" prop="pro_remark">
        <el-input
          v-model="form.pro_remark"
          :disabled="isReadonly"
          type="textarea"
          class="input input-text-long"
        />
      </el-form-item>
      <br>
      <el-form-item class="actions">
        <el-button size="mini" type="info" @click="onBack">
          {{ $t('button.back') }}
        </el-button>
        <el-button v-if="!isReadonly" size="mini" type="success" @click="onSave">
          {{ $t('button.saveFile') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onNext">
          {{ $t('button.next') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import ProcurementLevelSelect from '@/views/purchase/procurementLevel/level'
import BudgetSelectTreeVue from '@/views/budget/common/BudgetSelectTreeVue'
import { deepCloneByJSON } from '@/utils'
import { fetchBudgetTree } from '@/api/budget'
import {
  createProcurementApplications,
  editProcurementApplications,
} from '@/api/purchase/procurementApplications'
import { generateProcurementNo } from '@/api/purchase/procurementLevels'
import dayjs from 'dayjs'
export default {
  name: 'BasisStep',
  components: { ProcurementLevelSelect, BudgetSelectTreeVue },
  props: {
    data: {
      type: Object,
      required: true,
    },
    staffs: {
      type: Array,
      required: true,
    },
    years: {
      type: Array,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: true,
    },
    approve: {
      type: Boolean,
      default: true,
    },
    review: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // form: {
      //   fy_code: '', // 會計週期編號
      //   pro_level_id: '', // 採購等級id
      //   pro_no: '', // 採購編號
      //   pro_title: '', // 採購申請標題
      //   pro_remark: '', // 採購申請備註
      //   apply_date: new Date(), // 申請日期
      //   invite_date: new Date(), // 邀請供應商日期
      //   type: 'P', // 採購類新: P=貨品: '',S=服務
      //   contact: '', // 聯繫人
      //   contact_title: '', // 聯繫人稱呼
      //   contact_tel: '', // 聯繫人電話
      //   budget_code: '', // 預算編號
      //   reply_end_date: '', // 供應商回復截止日期
      //   quoted_valid_days: '', // 供應商報價有效日期(供應商回復截止日期開始計算)
      //   apply_staff_id: '', // 申請人staff_id
      //   apply_review_handlers: [[]], // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
      //   apply_review_handlers_json: '', // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
      //   apply_approve_handlers: [[]], // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
      //   apply_approve_handlers_json: '' // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
      // },

      langKey: 'purchase.daily.procurementApplications.',
      budgetGroupList: [],
      dataUpdating: false,
    }
  },
  computed: {
    ...mapGetters(['user_id', 'username']),
    categoryList() {
      return [
        { value: 'P', label: this.$t('purchase.category.P') },
        { value: 'S', label: this.$t('purchase.category.S') },
      ]
    },
    form: {
      get() {
        return this.data
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
  },
  watch: {
    // form: {
    //   deep: true,
    //   handler(val) {
    //     this.dataUpdating = true
    //     this.$emit('update:data', val)
    //   }
    // },
    'form.apply_date': {
      handler(val) {
        if (val) {
          const updatedData = { ...this.data }
          updatedData.invite_date = dayjs(val).add(1, 'year').format('YYYY-MM-DD')
          updatedData.reply_end_date = dayjs(updatedData.invite_date)
            .add(14, 'day')
            .format('YYYY-MM-DD')
          updatedData.quoted_valid_days = 90
          updatedData.contact = this.username
          this.$emit('update:data', updatedData)
        }
      },
    },
    'form.fy_code': {
      deep: true,
      immediate: true,
      handler(val) {
        if (val) {
          this.initBudgetList()
        }
      },
    },
    'form.contact_tel': {
      deep: true,
      immediate: true,
      handler(val) {
        // console.log(val)
        this.onChangeContactTel()
      },
    },
    // data: {
    //   deep: true,
    //   immediate: true,
    //   handler(val) {
    //     if (!this.dataUpdating) {
    //       // this.form = Object.assign(this.form, deepCloneByJSON(val))
    //       this.initForm()
    //     }
    //     this.dataUpdating = false
    //   }
    // }
  },
  created() {
    this.init()
    this.$forceUpdate()
  },
  methods: {
    validateProNo(rule, value, callback) {
      if (!value) {
        callback(new Error(' '))
        // 檢查至少要有一個 #
      } else if (!value.includes('#')) {
        callback(new Error(this.t('rules.proNoRequired')))
      } else {
        callback()
      }
    },
    set(key, data) {
      this.$set(this.form, key, data)
    },
    async init() {
      // this.initBudgetList()
    },
    async initForm() {
      this.form = Object.assign(this.form, deepCloneByJSON(this.data))
    },
    getBudget(tree, code) {
      for (let i = 0; i < tree.length; i++) {
        const item = tree[i]
        if (item.budget_code === code) {
          return item
        }
        if (item.hasOwnProperty('children')) {
          const r = this.getBudget(item.children, code)
          if (r) {
            return r
          }
        }
      }
      return false
    },
    async initBudgetList() {
      const fy_code = this.data.fy_code
      // this.budgetGroupList = await fetchBudgetGroupList({ fy_code, budget_types })
      this.budgetGroupList = await fetchBudgetTree({ fy_code, budget_active: 'Y' })
      if (this.form.budget_code) {
        const budget = this.getBudget(this.budgetGroupList, this.form.budget_code)
        if (budget) {
          this.form.budget_id = budget.budget_id
        }
        console.log(budget)
      }
    },
    onBack() {
      this.$emit('back')
    },
    onSave(showTips = true) {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid, a) => {
          if (!valid) {
            return false
          }
          const review_valid = this.form.apply_review_handlers.every(row => {
            return row.map(item => item.staff_id).every(id => id)
          })
          if (!review_valid && this.review) {
            this.$message.error(this.t('reviewerHandlersInput'))
            return false
          }
          const approve__valid = this.form.apply_approve_handlers.every(row => {
            return row.map(item => item.staff_id).every(id => id)
          })
          if (!approve__valid && this.approve) {
            this.$message.error(this.t('approveHandlersInput'))
            return false
          }

          const pro_application_id = this.form.pro_application_id
          const fy_code = this.form.fy_code
          const pro_level_id = this.form.pro_level_id
          const pro_no = this.form.pro_no
          const pro_title = this.form.pro_title
          const pro_remark = this.form.pro_remark
          const apply_date = this.form.apply_date
          const invite_date = this.form.invite_date
          const type = this.form.type
          const contact = this.form.contact
          const contact_title = this.form.contact_title
          const contact_tel = this.form.contact_tel
          const budget_code = this.form.budget_code
          const reply_end_date = this.form.reply_end_date
          const quoted_valid_days = this.form.quoted_valid_days

          const apply_staff_id = this.user_id
          const apply_review_handlers_json = this.review
            ? this.form.apply_review_handlers.map(row => {
              return row.map(item => item.staff_id)
            })
            : []
          const apply_approve_handlers_json = this.approve
            ? this.form.apply_approve_handlers.map(row => {
              return row.map(item => item.staff_id)
            })
            : []
          if (pro_application_id) {
            // edit
            editProcurementApplications({
              pro_application_id, // ID
              fy_code, // 會計週期編號
              pro_level_id, // 採購等級id
              pro_no, // 採購編號
              pro_title, // 採購申請標題
              pro_remark, // 採購申請備註
              apply_date, // 申請日期
              invite_date, // 邀請供應商日期
              type, // 採購類新: P=貨品,S=服務
              contact, // 聯繫人
              contact_title, // 聯繫人稱呼
              contact_tel, // 聯繫人電話
              budget_code, // 預算編號
              reply_end_date, // 供應商回復截止日期
              quoted_valid_days, // 供應商報價有效日期(供應商回復截止日期開始計算)
              apply_staff_id, // 申請人staff_id
              apply_review_handlers_json, // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
              apply_approve_handlers_json, // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
            })
              .then(res => {
                if (showTips) {
                  this.$message.success(this.t('saveSuccess'))
                  this.onBack()
                }
                console.log(res)
                resolve()
              })
              .catch(err => {
                reject(err)
              })
          } else {
            // add
            createProcurementApplications({
              fy_code, // 會計週期編號
              pro_level_id, // 採購等級id
              pro_no, // 採購編號
              pro_title, // 採購申請標題
              pro_remark, // 採購申請備註
              apply_date, // 申請日期
              invite_date, // 邀請供應商日期
              type, // 採購類新: P=貨品,S=服務
              contact, // 聯繫人
              contact_title, // 聯繫人稱呼
              contact_tel, // 聯繫人電話
              budget_code, // 預算編號
              reply_end_date, // 供應商回復截止日期
              quoted_valid_days, // 供應商報價有效日期(供應商回復截止日期開始計算)
              apply_staff_id, // 申請人staff_id
              apply_review_handlers_json, // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
              apply_approve_handlers_json, // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
            })
              .then(res => {
                if (showTips) {
                  this.$message.success(this.t('saveSuccess'))
                  console.log(res)

                  this.onBack()
                  // this.$emit('reshow')
                } else {
                  this.$router.push({
                    name: 'purchaseDailyProcurementEdit',
                    // path: '/daily/procurement/view/' + row.pro_application_id,
                    params: {
                      view: 'edit',
                      id: res.pro_application_id,
                    },
                  })
                  // this.$emit('reshow')
                }
                resolve()
              })
              .catch(err => {
                reject(err)
              })
          }
        })
      })
    },
    budgetDisabledMethod(item) {
      return this.isReadonly || (item.budget_type !== 'D' && item.budget_id !== '')
    },
    onChangeBudgetGroup(budget) {
      console.log(budget)
    },
    onNext() {
      this.$emit('next')
    },
    onGenerateNo() {
      const pro_level_id = this.form.pro_level_id
      const apply_date = this.form.apply_date
      const budget_code = this.form.budget_code
      generateProcurementNo({ pro_level_id, apply_date, budget_code })
        .then(res => {
          this.form.pro_no = res.no
        })
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.$refs.form.validateField('pro_no')
        })
    },
    onChangeContactTel() {
      this.$nextTick(() => {
        if (this.form.contact_tel !== null) {
          this.form.contact_tel = this.form.contact_tel.replace(/[^\d]/g, '')
        }
      })
    },
    onChangeApplyDate() {
      if (this.form.apply_date) {
        this.form.invite_date = dayjs(this.form.apply_date).add(1, 'year').format('YYYY-MM-DD')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.basis-step {
  .input {
    width: 210px;
    &.input-level-select {
      width: auto;
      /deep/ {
        .el-form-item__content {
          width: 577px;
        }
      }
    }
    &.input-date {
    }
    &.input-text {
    }
    &.input-text-long {
      width: 636px;
    }
    &.input-select {
    }
    /deep/ {
      .e-select-tree {
        width: 210px;
      }
      .el-input-number__decrease,
      .el-input-number__increase {
        top: 2px !important;
        height: 28px !important;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .actions {
    text-align: right;
    width: 777px;
    padding-top: 10px;
  }
}
</style>
