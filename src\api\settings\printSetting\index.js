import request from '@/utils/request'

/**
 * 返回表單列印列表
 * @param {string} system 系統 AC:賬目 BG:預算
 * @return {Promise}
 */
export function fetchPrintSettingMenu(system) {
  return request({
    url: '/print-settings',
    method: 'get',
    params: {
      system,
    },
  })
}

/**
 * 返回表單列印詳情
 * @param {string} ps_code 表單列印編號
 * @param {string} psg_code 指定樣式
 * @return {Promise}
 */
export function getPrintSetting({ ps_code, psg_code, using }) {
  return request({
    url: '/print-setting/actions/inquire',
    method: 'get',
    params: {
      ps_code,
      psg_code,
      using,
    },
  })
}

/**
 * 更新表單列印詳情
 * @param {string} ps_code 表單列印編號
 * @param {string} psg_code 表單列印設定編號,用於區分統一的ps_code下多種樣式,只有一種通用樣式時,無需填寫
 * @param {string} config_json 樣式json
 * @param {integer} using 1=選中,0=沒選中
 * @return {Promise}
 */
export function editPrintSetting({ ps_code, psg_code, config, using }) {
  return request({
    url: '/print-setting/actions/update',
    method: 'post',
    data: {
      ps_code,
      psg_code,
      config_json: JSON.stringify(config),
      using,
    },
  })
}

/**
 * 刪除表單列印設置
 * @param {string} ps_code 表單列印編號
 * @param {string} psg_code 表單列印設定編號,用於區分統一的ps_code下多種樣式
 * @return {Promise}
 */
export function delPrintSetting({ ps_code, psg_code }) {
  return request({
    url: '/print-setting/actions/delete',
    method: 'post',
    data: {
      ps_code,
      psg_code,
    },
  })
}
