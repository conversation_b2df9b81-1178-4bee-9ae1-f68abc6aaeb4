<template>
  <div class="budget-management-left">
    <div class="filter">
      <el-form ref="form" :inline="true" class="mini-form">
        <!-- ---------- 負責人 ---------- -->
        <el-form-item :label="$t('budget.budgetManagement.label.manager')">
          <el-input :readonly="true" :value="manager" />
        </el-form-item>
        <!-- ---------- 審核 ---------- -->
        <el-form-item :label="$t('budget.budgetManagement.label.approve')">
          <el-input :readonly="true" :value="approve" />
        </el-form-item>
        <el-form-item v-if="budgetGroup && budgetGroup.budget_stage" class="stage-action">
          <div>
            <el-button-group>
              <el-button v-if="cancelStage" size="mini" type="danger" @click="handleCancelStage">
                {{ cancelStage }}
              </el-button>
              <el-button :disabled="true" class="status-button" size="mini" type="info">
                {{ stage }}
              </el-button>
              <el-button size="mini" type="primary" @click="handleNextStage">
                {{ nextStage }}
              </el-button>
            </el-button-group>
          </div>
        </el-form-item>
        <el-form-item class="data-action">
          <i class="edac-icon action-icon edac-icon-excel" />
          <!--<svg-icon icon-class="dept-delete" class="action-icon"/>-->
          <!--<svg-icon icon-class="data-delete" class="action-icon"/>-->
          <i
            v-if="budgetGroup && budgetGroup.budget_stage === 'X' && data.length === 0"
            :title="$t(btnKey + 'copyComposition')"
            class="edac-icon action-icon edac-icon-dept"
            @click="handleCopyComposition('ALL')"
          />
          <i
            v-if="budgetGroup && budgetGroup.budget_stage === 'X' && data.length === 0"
            :title="$t(btnKey + 'copyCompositionAndAmount')"
            class="edac-icon action-icon edac-icon-data"
            @click="handleCopyComposition('STRUCTURE')"
          />
          <i
            v-if="budgetGroup && budgetGroup.budget_stage === 'X' && data.length > 0"
            :title="$t(btnKey + 'clearComposition')"
            class="edac-icon action-icon edac-icon-dept_delete1"
            @click="handleCleanComposition('ALL')"
          />
          <i
            v-if="budgetGroup && budgetGroup.budget_stage === 'X' && data.length > 0"
            :title="$t(btnKey + 'clearCompositionAndAmount')"
            class="edac-icon action-icon edac-icon-data-delete"
            @click="handleCleanComposition('CONTENT')"
          />
          <i
            v-if="budgetGroup && 'XR'.includes(budgetGroup.budget_stage)"
            :title="$t(btnKey + 'addItem')"
            class="edac-icon action-icon edac-icon-add-folder-full"
            @click="handleAdd(null, 'C')"
          />
          <i
            v-if="budgetGroup && 'XR'.includes(budgetGroup.budget_stage)"
            :title="$t(btnKey + 'addDetail')"
            class="edac-icon action-icon edac-icon-add-list"
            @click="handleAdd(null, 'D')"
          />
          <!--          <i v-if="'XSTR'.includes(budgetGroup.budget_stage)" :title="$t(btnKey + 'edit')" class="edac-icon action-icon edac-icon-edit"/>-->
          <!--          <i v-if="'XR'.includes(budgetGroup.budget_stage)" :title="$t(btnKey + 'up')" class="edac-icon action-icon edac-icon-up"/>-->
          <!--          <i v-if="'XR'.includes(budgetGroup.budget_stage)" :title="$t(btnKey + 'down')" class="edac-icon action-icon edac-icon-down"/>-->

          <!--          <el-button-group>-->
          <!--            <el-button type="primary" size="mini" icon="edac-icon edac-icon-excel_add">導出</el-button>-->
          <!--            <el-button v-if="budgetGroup.budget_stage === 'X' && data.length === 0" type="primary" size="mini" icon="edac-icon edac-icon-dept">複製結果</el-button>-->
          <!--            <el-button v-if="budgetGroup.budget_stage === 'X' && data.length === 0" type="primary" size="mini" icon="el-icon-search">複製金額</el-button>-->
          <!--            <el-button v-if="budgetGroup.budget_stage === 'X' && data.length > 0" type="primary" size="mini">複製結果</el-button>-->
          <!--            <el-button v-if="budgetGroup.budget_stage === 'X' && data.length > 0" type="primary" size="mini">複製金額</el-button>-->
          <!--            <el-button v-if="'XR'.includes(budgetGroup.budget_stage)" type="primary" size="mini">新增事項</el-button>-->
          <!--            <el-button v-if="'XR'.includes(budgetGroup.budget_stage)" type="primary" size="mini">新增詳細</el-button>-->
          <!--            <el-button v-if="'XSTR'.includes(budgetGroup.budget_stage)" type="primary" size="mini">編輯</el-button>-->
          <!--            <el-button v-if="budgetGroup.budget_stage === 'X'" type="primary" size="mini">刪除</el-button>-->
          <!--            <el-button v-if="'XR'.includes(budgetGroup.budget_stage)" type="primary" size="mini">上移</el-button>-->
          <!--            <el-button v-if="'XR'.includes(budgetGroup.budget_stage)" type="primary" size="mini">下移</el-button>-->
          <!--          </el-button-group>-->
        </el-form-item>
        <!--        <el-form-item class="left-title">-->
        <!--          <div>{{ $t(langKey + 'lastYear') }}</div>-->
        <!--        </el-form-item>-->
      </el-form>
    </div>
    <div :style="tableHeight" class="right-table">
      <el-table
        v-if="false"
        ref="dataTable"
        :data="data"
        :row-class-name="isStripe"
        :show-summary="false"
        :summary-method="getSummaries"
        border
        class="data-table"
        height="100%"
        highlight-current-row
        stripe
        style="width: 100%"
        @current-change="onChangCurrentRow"
      >
        <el-table-column :resizable="false" :width="columnWidths.icon" align="left">
          <template v-if="scope && scope.row" slot-scope="scope">
            <div>
              <span style="display: inline-block; font-size: 16px">{{
                '&nbsp;'.repeat((scope.row.level - 1) * 4)
              }}</span>
              <!--              <span v-if="scope.row.level > 1" style="display: inline-block;font-family: cursive;">{{ "├".repeat((scope.row.level - 2) * 1) }}</span>-->

              <!--              <span v-if="scope.row.level > 1" style="display: inline-block;font-family: cursive;">-->
              <!--                {{ showTreeLevel(scope) }}-->
              <!--              </span>-->
              <span v-if="scope.row.budget_type === 'C'" style="display: inline-block">
                <!--                <i-->
                <!--                  class="el-icon-caret-bottom"-->
                <!--                  style="line-height: 16px"-->
                <!--                />-->
                <i
                  :class="{
                    activate: scope.row.budget_active,
                  }"
                  class="edac-icon edac-icon-folder"
                />
              </span>
              <span v-else-if="scope.row.budget_type === 'D'" style="display: inline-block">
                <i
                  :class="{
                    activate: scope.row.budget_active,
                  }"
                  class="edac-icon edac-icon-file"
                />
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'budget_code')"
          :resizable="false"
          :width="columnWidths.budget_code"
          align="left"
          header-align="center"
          prop="budget_code"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div v-if="scope.row.edit">
              <el-input v-model="scope.row.budget_code" />
            </div>
            <div v-else>
              {{ scope.row.budget_code }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'name_')"
          :prop="languageKey('name_')"
          :resizable="false"
          align="left"
          header-align="center"
          min-width="120"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div v-if="scope.row.edit">
              <el-input v-model="scope.row[languageKey('name_')]" />
            </div>
            <div v-else>
              {{ scope.row[languageKey('name_')] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'budget_IE')"
          :resizable="false"
          :width="columnWidths.budget_IE"
          align="center"
          header-align="center"
          prop="budget_IE"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div v-if="scope.row.edit && 'XR'.includes(budgetGroup.budget_stage)">
              <el-select v-model="scope.row.budget_IE">
                <el-option v-if="'IB'.includes(budgetGroup.budget_IE)" :label="$t('budget.budgetManagement.IE.I')" value="I" />
                <el-option v-if="'IB'.includes(budgetGroup.budget_IE)" :label="$t('budget.budgetManagement.IE.E')" value="E" />
                <el-option v-if="'B'.includes(budgetGroup.budget_IE)" :label="$t('budget.budgetManagement.IE.B')" value="B" />
              </el-select>
            </div>
            <div v-else>
              <span v-if="'IB'.includes(scope.row.budget_IE)">I</span>
              <span v-if="'EB'.includes(scope.row.budget_IE)">E</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'budget_amount')"
          :resizable="false"
          :width="columnWidths.budget_amount"
          align="right"
          header-align="center"
          prop="budget_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_budget_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_budget_amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'approved_amount')"
          :resizable="false"
          :width="columnWidths.approved_amount"
          align="right"
          header-align="center"
          prop="approved_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_approved_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_approved_amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'actual_amount')"
          :resizable="false"
          :width="columnWidths.actual_amount"
          align="right"
          header-align="center"
          prop="actual_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_actual_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_actual_amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'balance')"
          :resizable="false"
          :width="columnWidths.balance"
          align="right"
          header-align="center"
          prop="balance"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_balance) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_balance) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :resizable="false"
          :width="columnWidths.action"
          align="left"
          fixed="right"
          header-align="center"
          :label="$t('table.action')"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div class="row-action">
              <i
                :class="{ disabled: !showUp(scope) }"
                :title="$t(btnKey + 'up')"
                class="edac-icon action-icon edac-icon-up"
                @click="handleUp(scope)"
              />
              <i
                :class="{ disabled: !showDown(scope) }"
                :title="$t(btnKey + 'down')"
                class="edac-icon action-icon edac-icon-down"
                @click="handleDown(scope)"
              />
              <i
                v-if="
                  budgetGroup &&
                    'XR'.includes(budgetGroup.budget_stage) &&
                    scope.row.budget_type === 'C'
                "
                :title="$t(btnKey + 'addItem')"
                class="edac-icon action-icon edac-icon-add-folder-full"
              />
              <i
                v-if="
                  budgetGroup &&
                    'XR'.includes(budgetGroup.budget_stage) &&
                    scope.row.budget_type === 'C'
                "
                :title="$t(btnKey + 'addDetail')"
                class="edac-icon action-icon edac-icon-add-list"
              />
              <i
                v-if="budgetGroup && !scope.row.edit && 'XSTR'.includes(budgetGroup.budget_stage)"
                :title="$t(btnKey + 'edit')"
                class="edac-icon action-icon edac-icon-edit"
                @click="handleEdit(scope)"
              />
              <i
                v-if="scope.row.edit"
                class="edac-icon action-icon edac-icon-save"
                @click="handleUpdate(scope)"
              />
              <i
                v-if="showDelete(scope)"
                :title="$t(btnKey + 'delete')"
                class="edac-icon action-icon edac-icon-delete"
                @click="handleDelete(scope)"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>

      <tree-table
        v-if="showTable"
        ref="dataTable"
        :data="treeData"
        :expand-all="true"
        :first-field="language === 'en' ? 'name_en' : 'name_cn'"
        :first-field-width="columnWidths.icon"
        border
        first-field-align="left"
        folder-field="st_type_id"
        height="100%"
        number-field="budget_code"
      >
        <!--        @changeWidth="changeColumnWidth"-->
        <!--        @changeExpanded="onChangeExpanded"-->
        <!--    編號姓名      -->
        <template slot="firstField" slot-scope="{ scope }">
          <i
            :class="{
              activate: scope.row.budget_active === 'Y',
              'edac-icon-file': scope.row.budget_type === 'D',
              'edac-icon-folder-f': scope.row.budget_type === 'F',
              'edac-icon-folder-c': scope.row.budget_type === 'C',
              'edac-icon-folder-g': scope.row.budget_type === 'G',
            }"
            class="edac-icon"
          />
          <!--          <span-->
          <!--            v-if="!scope.row.st_type_id"-->
          <!--            :class="{-->
          <!--              selected: scope.row.budget_active === 'Y'-->
          <!--            }"-->
          <!--            class="number-field"-->
          <!--          >{{ scope.row.budget_code }}</span>-->
          <!--          <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>-->
        </template>

        <!--   預算編號     -->
        <el-table-column
          :label="$t(langKey + 'budget_code')"
          :resizable="false"
          :width="columnWidths.budget_code"
          align="left"
          header-align="center"
          prop="budget_code"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div
              v-if="
                budgetGroup && budgetGroup && scope.row.edit && 'X' === budgetGroup.budget_stage
              "
            >
              <el-input v-model="scope.row.budget_code" />
            </div>
            <div v-else>
              {{ scope.row.budget_code }}
            </div>
          </template>
        </el-table-column>
        <!--   預算項目     -->
        <el-table-column
          :label="$t(langKey + 'name_')"
          :min-width="columnWidths.name_"
          :prop="languageKey('name_')"
          :resizable="false"
          align="left"
          header-align="center"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div v-if="budgetGroup && scope.row.edit && 'X' === budgetGroup.budget_stage">
              <el-input v-model="scope.row[languageKey('name_')]" />
            </div>
            <div v-else>
              {{ scope.row[languageKey('name_')] }}
            </div>
          </template>
        </el-table-column>
        <!--   分類     -->
        <el-table-column
          :label="$t(langKey + 'budget_IE')"
          :resizable="false"
          :width="columnWidths.budget_IE"
          align="center"
          header-align="center"
          prop="budget_IE"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>{{ scope.row.budget_IE }}</span>
          </template>
        </el-table-column>
        <!--   預算金額     -->
        <el-table-column
          :label="$t(langKey + 'budget_amount')"
          :resizable="false"
          :width="columnWidths.budget_amount"
          align="right"
          header-align="center"
          prop="budget_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div v-if="scope.row.edit && scope.row.budget_type === 'D'">
              <el-input v-model="scope.row[editAmountField]" />
            </div>
            <div v-else>
              <span
                v-if="
                  budgetGroup &&
                    'SA'.includes(budgetGroup.budget_stage) &&
                    scope.row.proposed_amount != scope.row.approved_amount
                "
                class="m-row oldAmount"
              >
                {{ amountFormat(scope.row.proposed_amount) }}
              </span>
              <span>
                {{ amountFormat(scope.row[budgetAmountField]) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <!--   修訂金額   -->
        <el-table-column
          v-if="showRevised"
          :label="$t(langKey + 'revised_amount')"
          :resizable="false"
          :width="columnWidths.revised_amount"
          align="right"
          header-align="center"
          prop="approved_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ amountFormat(scope.row[revisedAmountField]) }}
            </span>
          </template>
        </el-table-column>
        <!--   實際金額     -->
        <el-table-column
          :label="$t(langKey + 'actual_amount')"
          :resizable="false"
          :width="columnWidths.actual_amount"
          align="right"
          header-align="center"
          prop="actual_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ amountFormat(scope.row.actual_amount) }}
            </span>
          </template>
        </el-table-column>
        <!--   結餘     -->
        <el-table-column
          :label="$t(langKey + 'balance')"
          :resizable="false"
          :width="columnWidths.balance"
          align="right"
          header-align="center"
          prop="balance"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ amountFormat(scope.row[budgetAmountField] - scope.row.actual_amount) }}
            </span>
          </template>
        </el-table-column>
        <!--   結餘率     -->
        <el-table-column
          :label="$t(langKey + 'balancePercentage')"
          :resizable="false"
          :width="columnWidths.percentage"
          align="right"
          header-align="center"
          prop="balance"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ calcBalancePercentage(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <!--操作列-->
        <el-table-column
          v-if="showAction"
          :label="$t('table.action')"
          :width="columnWidths.action"
          align="left"
          fixed="right"
          header-align="left"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div class="row-action">
              <i
                v-if="'XR'.includes(budgetGroup.budget_stage) && scope.row.budget_id"
                :class="{ disabled: !showUp(scope) }"
                :title="$t(btnKey + 'up')"
                class="edac-icon action-icon edac-icon-up"
                @click="handleUp(scope)"
              />
              <i
                v-if="'XR'.includes(budgetGroup.budget_stage) && scope.row.budget_id"
                :class="{ disabled: !showDown(scope) }"
                :title="$t(btnKey + 'down')"
                class="edac-icon action-icon edac-icon-down"
                @click="handleDown(scope)"
              />
              <i
                v-if="
                  'XR'.includes(budgetGroup.budget_stage) &&
                    scope.row.budget_type === 'C' &&
                    scope.row.budget_id
                "
                :title="$t(btnKey + 'addItem')"
                class="edac-icon action-icon edac-icon-add-folder-full"
                @click="handleAdd(scope, 'C')"
              />
              <i
                v-if="
                  'XR'.includes(budgetGroup.budget_stage) &&
                    scope.row.budget_type === 'C' &&
                    scope.row.budget_id
                "
                :title="$t(btnKey + 'addDetail')"
                class="edac-icon action-icon edac-icon-add-list"
                @click="handleAdd(scope, 'D')"
              />
              <i
                v-if="!scope.row.edit && 'XSTR'.includes(budgetGroup.budget_stage)"
                :title="$t(btnKey + 'edit')"
                class="edac-icon action-icon edac-icon-edit"
                @click="handleEdit(scope)"
              />
              <i
                v-if="scope.row.edit"
                class="edac-icon action-icon edac-icon-save"
                @click="handleUpdate(scope)"
              />
              <i
                v-if="scope.row.edit"
                class="edac-icon action-icon edac-icon-cancel"
                @click="handleCancel(scope)"
              />
              <i
                v-if="!scope.row.edit && showDelete(scope) && scope.row.budget_id"
                :title="$t(btnKey + 'delete')"
                class="edac-icon action-icon edac-icon-delete"
                @click="handleDelete(scope)"
              />
            </div>
          </template>
        </el-table-column>
      </tree-table>
      <el-table
        v-if="showTable"
        ref="summaryTable"
        :data="[summary]"
        :row-class-name="isStripe"
        :show-header="false"
        :span-method="summarySpanMethod"
        border
        class="summary-table"
        stripe
        style="width: 100%"
      >
        <el-table-column :width="columnWidths.icon" align="right">
          <template v-if="scope && scope.row" slot-scope="scope">
            {{ $t('budget.budgetList.label.total') }}
          </template>
        </el-table-column>
        <el-table-column :width="columnWidths.budget_code" />
        <el-table-column :min-width="columnWidths.name_" />
        <!--   分類   -->
        <el-table-column
          :label="$t(langKey + 'budget_IE')"
          :width="columnWidths.budget_IE"
          align="center"
          header-align="center"
          prop="budget_IE"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span> {{ scope.row.budget_IE }}</span>
          </template>
        </el-table-column>
        <!--   預算金額     -->
        <el-table-column
          :label="$t(langKey + 'budget_amount')"
          :width="columnWidths.budget_amount"
          align="right"
          header-align="center"
          prop="budget_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span
              v-if="
                budgetGroup &&
                  'SA'.includes(budgetGroup.budget_stage) &&
                  scope.row.proposed_amount != scope.row.approved_amount
              "
              class="m-row oldAmount"
            >
              {{ amountFormat(scope.row.proposed_amount) }}
            </span>
            <span>
              {{ amountFormat(scope.row[budgetAmountField]) }}
            </span>
          </template>
        </el-table-column>
        <!--   修訂金額     -->
        <el-table-column
          v-if="showRevised"
          :label="$t(langKey + 'revised_amount')"
          :resizable="false"
          :width="columnWidths.revised_amount"
          align="right"
          header-align="center"
          prop="approved_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ amountFormat(scope.row[revisedAmountField]) }}
            </span>
          </template>
        </el-table-column>
        <!--   實際金額     -->
        <el-table-column
          :label="$t(langKey + 'actual_amount')"
          :width="columnWidths.actual_amount"
          align="right"
          header-align="center"
          prop="actual_amount"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ amountFormat(scope.row.actual_amount) }}
            </span>
          </template>
        </el-table-column>
        <!--   結餘     -->
        <el-table-column
          :label="$t(langKey + 'balance')"
          :resizable="false"
          :width="columnWidths.balance"
          align="right"
          header-align="center"
          prop="balance"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ amountFormat(scope.row[budgetAmountField] - scope.row.actual_amount) }}
            </span>
          </template>
        </el-table-column>
        <!--   結餘率     -->
        <el-table-column
          :label="$t(langKey + 'balancePercentage')"
          :resizable="false"
          :width="columnWidths.percentage"
          align="right"
          header-align="center"
          prop="balance"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span>
              {{ calcBalancePercentage(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <!--操作列-->
        <el-table-column
          v-if="showAction"
          :resizable="false"
          :width="columnWidths.action"
          align="right"
          fixed="right"
          header-align="center"
          :label="$t('table.action')"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { amountFormat, toDecimal } from '@/utils'
import BudgetSelectTree from './../common/BudgetSelectTreeVue'
import { deleteBudget, editBudgetStage } from '@/api/budget'
import ElRadio from 'element-ui/packages/radio/src/radio'
import { cancelScrollBarSync, scrollBarSync } from '@/views/budget/common/utils'
import treeTable from '@/components/TreeTable'
import { listenTo } from '@/utils/resizeListen'

export default {
  name: 'BudgetManagementLeft',
  components: {
    ElRadio,
    BudgetSelectTree,
    treeTable,
  },
  props: {
    years: {
      type: Array,
      default: () => [],
    },
    fyCode: {
      type: String,
      default: '',
    },
    budgetId: {
      type: [String, Number],
      default: '',
    },
    data: {
      type: Array,
      default: () => [],
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    summary: {
      type: Object,
      default: () => ({}),
    },
    budgetGroup: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      langKey: 'budget.budgetManagement.label.',
      btnKey: 'budget.budgetManagement.button.',
      selectRow: '',
      dataTableWrapper: {},
      summaryTableWrapper: {},
      columnWidths: {
        icon: 120,
        budget_code: 100,
        name_: 150,
        budget_IE: 60,
        budget_amount: 100,
        approved_amount: 100,
        revised_amount: 100,
        actual_amount: 100,
        balance: 100,
        percentage: 80,
        action: 150,
      },
      resizeListen: {},
      infoHeight: 70,
      showTable: true,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    currentYearCode: {
      get() {
        return this.fyCode
      },
      set(val) {
        this.$emit('update:fyCode', val)
      },
    },
    tableHeight() {
      // return `height: calc(100% - ${this.summary.budget_IE === 'B' ? 113 : 90}px);`
      return `height: calc(100% - ${this.infoHeight}px - 20px);`
    },
    manager() {
      if (!this.budgetGroup) return ''
      return this.language === 'en'
        ? this.budgetGroup.manager_name_en
        : this.budgetGroup.manager_name_cn
    },
    approve() {
      if (!this.budgetGroup) return ''
      return this.language === 'en'
        ? this.budgetGroup.approve_name_en
        : this.budgetGroup.approve_name_cn
    },
    stage() {
      const key = {
        X: 'notSubmitted',
        S: 'submitted',
        A: 'approved',
        O: 'accepted',
        C: 'confirmed',
        R: 'revising',
        T: 'revised',
        K: 'reApproved',
        B: 'reAccepted',
        D: 'reConfirmed',
      }
      const langKey = 'budget.budgetManagement.button.'
      const stage = this.budgetGroup.budget_stage
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    cancelStage() {
      const key = {
        // X: '',
        S: 'withdrawSubmission',
        A: 'withdrawApproval',
        O: 'withdrawAcceptance',
        C: 'withdrawConfirmation',
        R: 'cancelReviseMode',
        T: 'withdrawRevision',
        K: 'withdrawReApproval',
        B: 'withdrawAcceptance',
        D: 'withdrawConfirmation',
      }
      const langKey = 'budget.budgetManagement.button.'
      const stage = this.budgetGroup.budget_stage
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    nextStage() {
      const key = {
        X: 'submitBudget',
        S: 'approveBudget',
        A: 'acceptApproval',
        O: 'confirmAcceptance',
        C: 'reviseMode',
        R: 'summitRevision',
        T: 'reApproveBudget',
        K: 'acceptReApproval',
        B: 'confirmAcceptance',
        D: 'reviseMode',
      }
      const langKey = 'budget.budgetManagement.button.'
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    cancelStageMessage() {
      const key = {
        // X: '',
        S: 'withdrawSubmittedBudget',
        A: 'withdrawApprovedBudget',
        O: 'withdrawAcceptance',
        C: 'withdrawConfirmation',
        R: 'cancelReviseMode',
        T: 'withdrawSubmittedRevision',
        K: 'withdrawReApprovedBudget',
        B: 'withdrawReAcceptance',
        D: 'withdrawReConfirmation',
      }
      const langKey = 'budget.budgetManagement.message.'
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    nextStageMessage() {
      const key = {
        X: 'submitBudgetForApproval',
        S: 'approveBudget',
        A: 'acceptApproval',
        O: 'confirmAcceptBudget',
        C: 'enterReviseBudgetMode',
        R: 'submitRevisionForApproval',
        T: 'reApproveBudget',
        K: 'reAcceptApproval',
        B: 'confirmReAcceptedBudget',
        D: 'enterReviseBudgetMode',
      }
      const langKey = 'budget.budgetManagement.message.'
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    budgetAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
          return 'proposed_amount'
        case 'S':
        case 'A':
        case 'O':
          return 'approved_amount'
        case 'C':
        case 'R':
        case 'T':
        case 'K':
        case 'B':
        case 'D':
        default:
          return 'budget_amount'
      }
    },
    revisedAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
        case 'S':
        case 'A':
        case 'O':
        case 'C':
          return ''
        case 'R':
          return 'proposed_amount'
        case 'T':
        case 'K':
        case 'B':
          return 'approved_amount'
        case 'D':
        default:
          return 'budget_amount'
      }
    },
    showRevised() {
      if (!this.budgetGroup) return false
      switch (this.budgetGroup.budget_stage) {
        case 'R':
        case 'T':
        case 'K':
        case 'B':
        case 'D':
          return true
        case 'X':
        case 'S':
        case 'A':
        case 'O':
        case 'C':
        default:
          return false
      }
    },
    editAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
        case 'R':
          return 'proposed_amount'
        case 'S':
        case 'T':
          return 'approved_amount'
        default:
          return ''
      }
    },
    showAction() {
      if (!this.budgetGroup) return false
      return 'XSRT'.includes(this.budgetGroup.budget_stage)
    },
  },
  watch: {
    treeData: {
      deep: true,
      handler() {
        // setTimeout(() => {
        //   this.$nextTick(() => {
        // this.reshowTable()
        // })
        // }, 1)
      },
    },
  },
  created() {},
  mounted() {
    // scrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    this.scrollBarSync()

    this.$nextTick(() => {
      this.resizeListen = listenTo(this.$refs.form.$el, ({ width, height, ele }) => {
        this.infoHeight = height + 10
      })
    })
  },
  beforeDestroy() {
    // cancelScrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    this.cancelScrollBarSync()
  },
  methods: {
    scrollBarSync() {
      this.$nextTick(() => {
        try {
          this.dataTableWrapper =
            this.$refs['dataTable'].$el.querySelector('.el-table__body-wrapper')
          this.summaryTableWrapper =
            this.$refs['summaryTable'].$el.querySelector('.el-table__body-wrapper')
          scrollBarSync(this.dataTableWrapper, this.summaryTableWrapper)
        } catch (e) {
          // error
        }
      })
    },
    cancelScrollBarSync() {
      this.$nextTick(() => {
        cancelScrollBarSync(this.dataTableWrapper, this.summaryTableWrapper)
      })
    },
    reshowTable() {
      this.cancelScrollBarSync()
      this.showTable = false
      // setTimeout(() => {
      this.$nextTick(() => {
        this.showTable = true
        this.$nextTick(() => {
          this.scrollBarSync()
        })
      })
      // }, 500)
    },
    isStripe() {},
    initData() {},
    conversionTreeOption(item, html, level = 1) {
      let text = this.language === 'en' ? item.name_en : item.name_cn
      if (html) {
        text = '&nbsp;'.repeat((item.level - 1 - level + 1) * 4) + text
      }
      return text
    },
    onChangeBudgetGroup(val) {
      this.$emit('changeBudgetGroup', val)
    },
    languageKey(key) {
      return key + (this.language === 'en' ? 'en' : 'cn')
    },
    getSummaries({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('budget.budgetManagement.label.total')
          return
        } else if (index === 1) {
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          // sums[index] = 'N/A'
        }
      })

      return sums
    },

    amountFormat: amountFormat,
    onChangeYear(val) {
      this.$emit('changeYear', val)
    },
    summarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 4,
        }
      } else if (columnIndex < 4) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    handleChangeStage(stage, message, type) {
      if (!this.budgetGroup) return false

      const fy_code = this.fyCode
      const budget_id = this.budgetGroup && this.budgetGroup.budget_id
      const budget_stage = stage || ''

      if (!fy_code || !budget_id || !budget_stage) {
        this.$message.error(this.$t('message.pleaseReload'))
        return
      }
      this.$alert(message, this.$t('budget.budgetManagement.message.tips'), {
        type,
        showCancelButton: true,
        callback: (action, instance) => {
          if (action === 'confirm') {
            editBudgetStage({ fy_code, budget_id, budget_stage }).then(() => {
              const bg = this.budgetGroup
              bg.budget_stage = budget_stage
              this.$emit('update:budgetGroup', bg)
              this.$message.success(this.$t('message.success'))
              this.$emit('reloadThis')
              this.$nextTick(() => {
                this.reshowTable()
              })
            })
          }
        },
      })
    },
    handleCancelStage() {
      const key = {
        X: '',
        S: 'X',
        A: 'S',
        O: 'A',
        C: 'O',
        R: 'C',
        T: 'R',
        K: 'T',
        B: 'K',
        D: 'B',
      }
      const oldBudgetStage = this.budgetGroup && this.budgetGroup.budget_stage
      const budget_stage = key[oldBudgetStage] || ''
      this.handleChangeStage(budget_stage, this.cancelStageMessage, 'warning')
    },
    handleNextStage() {
      const key = {
        X: 'S',
        S: 'A',
        A: 'O',
        O: 'C',
        C: 'R',
        R: 'T',
        T: 'K',
        K: 'B',
        B: 'D',
        D: 'R',
      }
      const oldBudgetStage = this.budgetGroup && this.budgetGroup.budget_stage
      const budget_stage = key[oldBudgetStage] || ''

      this.handleChangeStage(budget_stage, this.nextStageMessage, 'success')
    },
    onChangCurrentRow(currentRow) {
      this.selectRow = currentRow ? currentRow.budget_id : ''
    },
    /* 複製結構 */
    handleCopyComposition(mode) {
      this.$emit('handleCopyComposition', mode)
    },
    /* 清除結構 */
    handleCleanComposition(mode) {
      this.$emit('handleCleanComposition', mode)
    },
    showDelete(scope) {
      if (!this.budgetGroup) return false
      if ('XR'.includes(this.budgetGroup.budget_stage)) {
        const type = scope.row.budget_type
        if (type === 'D') {
          return true
        } else if (type === 'C') {
          return !scope.row.children || scope.row.children.length === 0
        }
      }
      return false
    },
    showUp(scope) {
      if (!this.budgetGroup) return false
      // const i = scope.$index
      const index = scope.row._index
      if ('XR'.includes(this.budgetGroup.budget_stage)) {
        return index !== 0

        // const p = this.data[i - 1]
        //
        // const type = scope.row.budget_type
        // const level = scope.row.level
        // if (type === 'D') {
        //   if (p.budget_type === 'D' && p.level === level) {
        //     return true
        //   }
        // } else if (type === 'C') {
        //   return p.level !== level - 1
        // }
      }
      return false
    },
    showDown(scope) {
      if (!this.budgetGroup) return false
      // const i = scope.$index
      const index = scope.row._index
      const item = scope.row
      if ('XR'.includes(this.budgetGroup.budget_stage)) {
        if (item._level === 1) {
          for (let j = 0; j < this.data.length; j++) {
            const jItem = this.data[j]
            if (jItem.budget_id === item.budget_id) {
              if (j < this.data.length - 1) {
                return true
              }
            }
          }
        } else {
          if (item.parent && item.parent.children) {
            if (item.parent.children.length - 1 > index) {
              return true
            }
          }
        }
        // if (i === (this.data.length - 1)) {
        //   return false
        // }
        // const n = this.data[i + 1]
        //
        // const type = scope.row.budget_type
        // const level = scope.row.level
        // if (type === 'D') {
        //   if (n.budget_type === 'D' && n.level === level) {
        //     return true
        //   }
        // } else if (type === 'C') {
        //   let canDown = false
        //   for (let j = (i + 1); j < this.data.length; j++) {
        //     if (j.level === level) {
        //       canDown = true
        //       break
        //     } else if (j.level > level) {
        //       break
        //     }
        //   }
        //   return canDown
        // }
      }
      return false
    },
    async handleDelete(scope) {
      if (scope.row.budget_type === 'C') {
        if (scope.row.children && scope.row.children.length > 0) {
          this.$message.success(this.$t('budget.budgetManagement.message.hasChildren'))
        }
      }
      const fy_code = this.fyCode
      try {
        await this.$confirm(
          `${this.$t('confirm.deleteConfirm')}此項嗎` + '?',
          this.$t('confirm.warningTitle'),
          {
            confirmButtonText: this.$t('confirm.confirmButtonText'),
            cancelButtonText: this.$t('confirm.cancelButtonText'),
            type: 'warning',
          },
        )
        const budget_id = scope.row.budget_id
        await deleteBudget({ fy_code, budget_id })
        this.$emit('reloadThis')
        this.$message.success(this.$t('message.success'))
      } catch (e) {
        //
      }
    },
    handleUp(scope) {
      if (this.showUp(scope)) {
        this.$emit('handleUp', scope)
      }
    },
    handleDown(scope) {
      if (this.showDown(scope)) {
        this.$emit('handleDown', scope)
      }
    },
    handleEdit(scope) {
      this.$emit('handleEdit', scope)
    },
    handleCancel(scope) {
      this.$emit('handleCancel', scope)
    },
    handleUpdate(scope) {
      this.$emit('handleUpdate', scope)
    },
    handleAdd(scope, type) {
      this.$emit('handleAdd', scope, type)
    },
    showTreeLevel(scope) {
      const i = scope.$index - 1
      const len = this.data.length
      const level = scope.level
      if (i === 0) {
        if (len > 2) {
          return '┌'
        } else {
          return ''
        }
      } else if (i === len - 1) {
        const s = '└'
        return s
      } else {
        const s = '└'
        s.replace(level - 1)
        return s
      }

      // const p = this.data[i - 1]
      // const n = this.data[i + 1]
    },
    calcBalancePercentage(row) {
      const b = Number(row[this.budgetAmountField])
      const a = Number(row.actual_amount)
      // console.log(b, '-', a)
      if (Number.isNaN(a) || Number.isNaN(b) || a === 0) {
        return 'N/A'
      }
      return toDecimal(((b - a) / a) * 100) + '%'
    },
  },
}
</script>

<style lang="scss" scoped>
.budget-management-left {
  padding: 10px;
  height: 100%;

  .stage-action {
    /*float: right;*/
    line-height: 25px;

    .status-button {
      cursor: auto;
    }

    /deep/ {
      .el-button + .el-button {
        margin-left: 2px;
      }
    }
  }

  .right-table {
    height: calc(100% - 90px);
    /*padding-bottom: 30px;*/
    /deep/ {
      .cell {
        span.m-row {
          display: block;
        }

        span.oldAmount {
          text-decoration: line-through;
        }
      }

      .data-table.el-table {
        height: 100%;
        width: 100%;

        .cell {
          i {
            line-height: 16px;
            vertical-align: middle;
          }
        }

        .el-table__body-wrapper {
          overflow: auto;
        }
      }

      .summary-table.el-table {
        height: auto;

        .el-table__body-wrapper {
          overflow: hidden;
        }

        .el-table__fixed-right {
          height: 50px !important;
        }
      }

      i.disabled {
        cursor: not-allowed;
        color: #c8c9cc;
      }

      tr.current-row td {
        background-color: transparent;
      }

      .row-action {
        user-select: none;
      }
    }
  }

  .data-action {
    .edac-icon {
      width: 25px;
      height: 25px;
      line-height: 25px;
      font-size: 20px;
    }
  }
}
</style>
