import request from '@/utils/request'

/**
 * 導出津貼查詢
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} type A=所有,Y=全年,T=年度截至,M=當月
 * @param {string} fy_code 週期編號
 * @param {string} pd_code 週期月份編號
 * @param {string} ac_code 歸屬會計科目樹-會計科目code
 * @param {string} fund_id 賬目類別id
 * @return {Promise}
 */
export function enquiryGrantExport({
  user_id,
  export_type,
  type,
  fy_code,
  pd_code,
  ac_code,
  fund_id,
}) {
  return request({
    url: '/reports/enquiry-grant/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      type,
      fy_code,
      pd_code,
      ac_code,
      fund_id,
    },
  })
}

/**
 * 導出職員查詢
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} type A=所有,Y=全年,T=年度截至,M=當月
 * @param {string} fy_code 週期編號
 * @param {string} pd_code 週期月份編號
 * @param {string} st_code 職員編號
 * @param {string} st_type_id 職員類別id
 * @param {string} ac_B 資產類，負債類，儲備金類,可填N或Y
 * @param {string} ac_I 收入類,可填N或Y
 * @param {string} ac_E 支出類,可填N或Y
 * @return {Promise}
 */
export function enquiryStaffExport({
  user_id,
  export_type,
  type,
  fy_code,
  pd_code,
  st_code,
  st_type_id,
  ac_B,
  ac_I,
  ac_E,
}) {
  return request({
    url: '/reports/enquiry-staff/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      type,
      fy_code,
      pd_code,
      st_code,
      st_type_id,
      ac_B,
      ac_I,
      ac_E,
    },
  })
}

/**
 * 導出部門查詢
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} type A=所有,Y=全年,T=年度截至,M=當月
 * @param {string} fy_code 週期編號
 * @param {string} pd_code 週期月份編號
 * @param {string} dept_code 部門編號
 * @param {string} dept_type_id 部門類別id
 * @param {string} ac_B 資產類，負債類，儲備金類,可填N或Y
 * @param {string} ac_I 收入類,可填N或Y
 * @param {string} ac_E 支出類,可填N或Y
 * @return {Promise}
 */
export function enquiryDepartmentExport({
  user_id,
  export_type,
  type,
  fy_code,
  pd_code,
  dept_code,
  dept_type_id,
  ac_B,
  ac_I,
  ac_E,
}) {
  return request({
    url: '/reports/enquiry-department/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      type,
      fy_code,
      pd_code,
      dept_code,
      dept_type_id,
      ac_B,
      ac_I,
      ac_E,
    },
  })
}

/**
 * 導出對沖查詢
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} type A=所有,Y=全年,T=年度截至,M=當月
 * @param {string} fy_code 週期編號
 * @param {string} pd_code 週期月份編號
 * @param {string} contra_code 對沖編號
 * @param {string} ac_B 資產類，負債類，儲備金類,可填N或Y
 * @param {string} ac_I 收入類,可填N或Y
 * @param {string} ac_E 支出類,可填N或Y
 * @return {Promise}
 */
export function enquiryContraExport({
  user_id,
  export_type,
  type,
  fy_code,
  pd_code,
  contra_code,
  ac_B,
  ac_I,
  ac_E,
}) {
  return request({
    url: '/reports/enquiry-contra/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      type,
      fy_code,
      pd_code,
      contra_code,
      ac_B,
      ac_I,
      ac_E,
    },
  })
}

/**
 * 導出一般查詢
 * @param {integer} user_id 用戶id
 * @param {integer} staff_id 職員id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} type 日期:A=所有,Y=全年,T=年度截至,M=當月,F=自由輸入,D=日期
 * @param {string} fy_code 日期:週期編號
 * @param {string} pd_code 日期:週期月份編號
 * @param {string} date_from 日期:日期範圍from
 * @param {string} date_to 日期:日期範圍to
 * @param {string} date 日期:日期
 * @param {string} payee 收付款:收款人(公司)描述
 * @param {string} ac_code 賬目:歸屬會計科目樹-會計科目code
 * @param {string} fund_id 賬目:賬目類別id
 * @param {string} ac_I 類別:收入類,可填N或Y
 * @param {string} ac_E 類別:支出類,可填N或Y
 * @param {string} ref 參考號:參考號
 * @param {string} contra_cde 對沖號:對沖編號
 * @param {string} quote 報價:Q(Quotation)或T(Tender)
 * @param {string} qnum 報價:quote=Q(0,1,2,3)|quote=T(自由輸入)
 * @param {string} descr 內容:會計科目描述
 * @param {string} vc_code 傳票號:傳票編號
 * @param {string} st_code 職員:職員編號
 * @param {string} st_type_id 職員:職員類別id
 * @param {string} dept_code 部門:部門編號
 * @param {string} dept_type_id 部門:部門類別id
 * @param {string} sbet 金額: BET=範圍,VAL=數值,GT=大於,LT=小於
 * @param {string} amount_from 金額:sbet=BET時使用
 * @param {string} amount_to 金額:sbet=BET時使用
 * @param {string} amount 金額:sbet=VAL或sbet=GT或sbet=LT時使用
 * @param {string} amount_type 金額:A=±Dr/±Cr,B=+Dr/+Cr,C=+Dr/-Cr,D=-Dr/+Cr,E=-Dr/-Cr,F=±Dr,G=+Dr,H=-Dr,I=±Cr,J=+Cr,K=-Cr
 * @return {Promise}
 */
export function enquiryConditionExport({
  user_id,
  staff_id,
  export_type,
  type,
  fy_code,
  pd_code,
  date_from,
  date_to,
  date,
  payee,
  ac_code,
  fund_id,
  ac_I,
  ac_E,
  ref,
  contra_cde,
  quote,
  qnum,
  descr,
  vc_code,
  st_code,
  st_type_id,
  dept_code,
  dept_type_id,
  sbet,
  amount_from,
  amount_to,
  amount,
  amount_type,
  budget_id,
}) {
  return request({
    url: '/reports/enquiry-condition/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      staff_id,
      export_type,
      type,
      fy_code,
      pd_code,
      date_from,
      date_to,
      date,
      payee,
      ac_code,
      fund_id,
      ac_I,
      ac_E,
      ref,
      contra_cde,
      quote,
      qnum,
      descr,
      vc_code,
      st_code,
      st_type_id,
      dept_code,
      dept_type_id,
      sbet,
      amount_from,
      amount_to,
      amount,
      amount_type,
      budget_id,
    },
  })
}

/**
 * 導出試算表
 * @param export_type
 * @param {string} parent_fund_id 賬目類別(大類)id
 * @param {string} fy_code 選擇的會計週期
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} ignore_zero 跳過零值,傳1=跳過,0=不跳過
 * @param {string} mode LIST=個別-編號,STRUCTURE=個別-結構,TREE=樹狀結構
 * @param {integer} audit_adjust 0=所有(此時必須填寫begin_date和end_date),1=無,2=#1,3=#2,4=#3
 * @return {Promise}
 */
export function trialBalanceExport1({
  export_type,
  parent_fund_id,
  fy_code,
  begin_date,
  end_date,
  ignore_zero,
  mode,
  audit_adjust,
}) {
  return request({
    url: '/reports/trial-balance/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      export_type,
      parent_fund_id,
      fy_code,
      begin_date,
      end_date,
      ignore_zero,
      mode,
      audit_adjust,
    },
  })
}

/**
 * 導出試算表
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} [parent_fund_id] 賬目類別(大類)id
 * @param {string} fy_code 選擇的會計週期
 * @param {string} [begin_date] 開始日期
 * @param {string} [end_date] 結束日期
 * @param {string} data_type 數據格式類型,ARRAY=數組,TREE=樹狀
 * @param {string} ignore_zero 跳過零值,傳1=跳過,0=不跳過
 * @param {number} audit_adjust 0=所有(此時必須填寫begin_date和end_date),1=無,2=#1,3=#2,4=#3
 * @param {number} [current_level] 當前縮進級別，數據格式類型為樹狀時才傳
 * @return {Promise}
 */
export function trialBalanceExport({
  user_id,
  export_type,
  parent_fund_id,
  fy_code,
  begin_date,
  end_date,
  data_type,
  ignore_zero,
  audit_adjust,
  current_level,
}) {
  return request({
    url: '/reports/trial-balance/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      parent_fund_id,
      fy_code,
      begin_date,
      end_date,
      data_type,
      ignore_zero,
      audit_adjust,
      current_level,
    },
  })
}

/**
 * 導出傳票列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} fy_code 會計週期編號
 * @param {string} pd_code 會計週期月份編號
 * @param {string} vt_category 傳票類別類型,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票
 * @param {string} vt_code 傳票類別編號
 * @return {Promise}
 */
export function voucherListExport({
  user_id,
  export_type,
  fy_code,
  pd_code,
  vt_category,
  vt_code,
}) {
  return request({
    url: '/reports/voucher-list/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fy_code,
      pd_code,
      vt_category,
      vt_code,
    },
  })
}

/**
 * 導出期初支票
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} account_id 銀行會計賬號id  (ac_bank=C或者F)
 * @return {Promise}
 */
export function oBChequesExport({ user_id, export_type, account_id }) {
  return request({
    url: '/reports/ob-cheques/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      account_id,
    },
  })
}

/**
 * 導出結餘轉賬列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} fy_code 會計週期編號
 * @param {integer} fund_id 賬目類別(大類)
 * @return {Promise}
 */
export function balanceTransferExport({ user_id, export_type, fy_code, fund_id }) {
  return request({
    url: '/reports/balance-transfer/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fy_code,
      fund_id,
    },
  })
}

/**
 * 導出自動轉賬列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} vt_category P=付款,R=收款
 * @param {string} ac_code 銀行會計賬目編號(ac_bank不等於X)
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function autopayListExport({
  user_id,
  export_type,
  vt_category,
  ac_code,
  begin_date,
  end_date,
}) {
  return request({
    url: '/reports/autopay-list/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      vt_category,
      ac_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 導出購貨記錄列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} fy_code 會計週期編號(如果是期初結餘,則不填)
 * @return {Promise}
 */
export function purchaseInvoicesExport({ user_id, export_type, fy_code }) {
  return request({
    url: '/reports/purchase-invoices/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fy_code,
    },
  })
}

/**
 * 導出銷售(銷貨)記錄列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} fy_code 會計週期編號(如果是期初結餘,則不填)
 * @return {Promise}
 */
export function salesInvoicesExport({ user_id, export_type, fy_code }) {
  return request({
    url: '/reports/sales-invoices/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fy_code,
    },
  })
}

/**
 * 導出支票列印列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} ac_code 銀行會計科目編號
 * @param {string} type 篩選類型,N=未登記,X=無支票號碼,C=支票薄編號
 * @param {string} fy_code 會計週期編號,type=N或X時必填
 * @param {string} pd_code 會計週期月份編號,type=N或X時可填
 * @param {string} chqbk_code 支票薄編號,type=C時必填
 * @return {Promise}
 */
export function chequeListExport({
  user_id,
  export_type,
  ac_code,
  type,
  fy_code,
  pd_code,
  chqbk_code,
}) {
  return request({
    url: '/reports/cheque-list/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      ac_code,
      type,
      fy_code,
      pd_code,
      chqbk_code,
    },
  })
}

/**
 * 導出銀行賬目對沖統計(銀行對賬)
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} ac_code 銀行會計賬目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function bankReconciliationExport({ user_id, export_type, ac_code, begin_date, end_date }) {
  return request({
    url: '/reports/bank-reconciliation/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      ac_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 導出每日收款列表
 * @param {integer} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {integer} fund_id 賬目類別(大類)
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function dailyCollectionExport({ user_id, export_type, fund_id, begin_date, end_date }) {
  return request({
    url: '/reports/daily-collection/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fund_id,
      begin_date,
      end_date,
    },
  })
}

/**
 * 導出傳票報表
 * @param {string} [channel] 指定驗證token通道,可传AC或BG,不傳默認AC
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} [fund_id] 撥款賬目id
 * @param {string} [vt_category] 傳票類別類型,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} [vt_code] 傳票類別編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function vouchersExport({
  user_id,
  export_type,
  fund_id,
  vt_category,
  vt_code,
  begin_date,
  end_date,
}) {
  return request({
    url: '/reports/vouchers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fund_id,
      vt_category,
      vt_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 導出部門報表
 * @param {string} [channel] 指定驗證token通道,可传AC或BG,不傳默認AC
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} [parent_dept_type_id] 部門id
 * @param {string} [dept_code] 部門編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} [BIE] 可傳:B,I,E,IE
 * @return {Promise}
 */
export function departmentLedgersExport({
  user_id,
  export_type,
  parent_dept_type_id,
  dept_code,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/departments-ledgers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      parent_dept_type_id,
      dept_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 導出職員報表
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} [parent_st_type_id] 職員組id
 * @param {string} [st_code] 職員編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} [BIE] 可傳:B,I,E,IE
 * @return {Promise}
 */
export function staffLedgersExport({
  user_id,
  export_type,
  parent_st_type_id,
  st_code,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/staffs-ledgers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      parent_st_type_id,
      st_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 導出預算報表
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} fy_code 會計週期編號
 * @param {string} [budget_id] 预算id
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} [BIE] 可傳:B,I,E,IE
 * @return {Promise}
 */
export function budegetLedgersExport({
  user_id,
  export_type,
  fy_code,
  budget_id,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/budgets-ledgers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      fy_code,
      budget_id,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 導出現金/銀行賬簿報表
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} ac_code 選擇的會計科目(ac_bank=C,S,F,P)
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function cashLedgersExport({ user_id, export_type, ac_code, begin_date, end_date }) {
  return request({
    url: '/reports/cash-ledgers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      ac_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 導出利潤報表
 * @param {string} pd_code 選擇的會計週期月份
 * @param {string} date_mode M=本月,T=從本會計週期開始至選擇的月份
 * @return {Promise}
 */
export function stockProfitsExport({ pd_code, date_mode }) {
  return request({
    url: '/reports/stocks/profits/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      pd_code,
      date_mode,
    },
  })
}

/**
 * 導出結存報表
 * @param {string} pd_code 選擇的會計週期月份
 * @param {string} date_mode M=本月,T=從本會計週期開始至選擇的月份
 * @param {string} mode A=詳細,B=詳細+摘要,C=摘要
 * @return {Promise}
 */
export function stockBalanceExport({ pd_code, date_mode, mode }) {
  return request({
    url: '/reports/stocks/balance/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      pd_code,
      date_mode,
      mode,
    },
  })
}

/**
 * 導出每月銷售報表
 * @param {string} fy_code 選擇的會計週期
 * @param {string} [pd_code] 選擇的會計週期月份
 * @return {Promise}
 */
export function stockMonthlySalesExport({ fy_code, pd_code }) {
  return request({
    url: '/reports/stocks/monthly-sales/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      pd_code,
    },
  })
}

/**
 * 導出進出記錄報表
 * @param {string} fy_code 選擇的會計週期
 * @param {string} sk_code 貨品編號
 * @return {Promise}
 */
export function stockInOutExport({ fy_code, sk_code }) {
  return request({
    url: '/reports/stocks/in-out/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      sk_code,
    },
  })
}

/**
 * 導出會計賬目報表
 * @param {number} user_id 用戶id
 * @param {string} export_type 導出類型, PAGE=根據頁面, ALL=根據設定
 * @param {string} [parent_fund_id] 賬目id
 * @param {string} [ac_code] 會計科目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} style 樣式: A=借方/(貸方)結餘, B=貸方結餘, C=貸方淨值
 * @param {string} mode 顯示: N=正常賬目,A=正常賬目+審計修正,B=正常賬目+審計修正+年結轉賬
 * @return {Promise}
 */
export function ledgersExport({
  user_id,
  export_type,
  parent_fund_id,
  ac_code,
  begin_date,
  end_date,
  style,
  mode,
}) {
  return request({
    url: '/reports/ledgers/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      user_id,
      export_type,
      parent_fund_id,
      ac_code,
      begin_date,
      end_date,
      style,
      mode,
    },
  })
}

/**
 * 導出預算列表
 * @param {string} fy_code 選擇的會計週期
 * @param {number} [parent_budget_id] 父預算id(只能傳budget_type = F的預算id)
 * @param {string} [budget_stage] 預算群組狀態,未遞交(X)=>已遞交(S)=>已審批(A)=>已接納(O)=>已確認(C)=>修訂中(R)=>已修訂(T)=>已重批(K)=>已重接納(B)=>已重確認(D)
 * @param {number} [manager_staff_id] 負責的職員id
 * @param {number} [approve_staff_id] 負責審批的職員id
 * @param {number} show_last_year 是否顯示上年的數據 1-顯示 0=不顯示
 * @return {Promise}
 */
export function budgetSummaryExport({
  fy_code,
  parent_budget_id,
  budget_stage,
  manager_staff_id,
  approve_staff_id,
  show_last_year,
}) {
  return request({
    url: '/reports/budgets-summary/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      parent_budget_id,
      budget_stage,
      manager_staff_id,
      approve_staff_id,
      show_last_year,
    },
  })
}

/**
 * 匯出SWD報表
 * @param {string} fy_code 會計週期code
 * @param {string} pd_code 週期月份編號
 * @return {Promise}
 */
export function exportSwdReport({ fy_code, pd_code }) {
  return request({
    url: '/reports/swd/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      pd_code,
    },
  })
}
