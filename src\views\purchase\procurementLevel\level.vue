<template>
  <div class="procurement-level-select">
    <el-table :show-header="false" :data="currentData">
      <el-table-column width="50">
        <template v-if="scope && scope.row" slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column>
        <template v-if="scope && scope.row" slot-scope="scope">
          <div v-for="(item, index) in scope.row" :key="index" class="item-select">
            <el-select
              v-model="item.auto"
              :disabled="isReadonly || !canChangeType"
              class="item-auto"
            >
              <el-option :label="$t('purchase.master.procurementLevel.auto')" value="Y" />
              <el-option :label="$t('purchase.master.procurementLevel.manual')" value="N" />
            </el-select>
            <el-select
              v-model="item.staff_id"
              :disabled="isReadonly || (!canChangeType && item.type === 'S')"
              class="item-staff"
              :placeholder="$t('placeholder.select')"
            >
              <el-option v-if="allowEmpty" :label="t('free')" value="" />
              <el-option
                v-for="staff in staffs"
                :key="staff.staff_id"
                :label="staff[language === 'en' ? 'st_name_en' : 'st_name_cn']"
                :value="staff.staff_id"
              />
            </el-select>
            <el-button
              v-if="!isReadonly && canChangeType"
              :disabled="!canChangeType"
              size="mini"
              icon="el-icon-close"
              class="item-delete"
              @click="onDeleteItem(scope.row, scope.$index, item, index)"
            />
          </div>
          <svg-icon
            v-if="!isReadonly && canChangeType"
            icon-class="add"
            class-name="add-icon"
            @click="onAddItem(scope.row)"
          />
        </template>
      </el-table-column>
      <template slot="append">
        <svg-icon
          v-if="!isReadonly && canChangeType"
          icon-class="add"
          class-name="add-icon add-row"
          @click="onAddRow"
        />
      </template>
    </el-table>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'ProcurementLevelSelect',
  props: {
    staffs: {
      type: Array,
      required: true,
    },
    canChangeType: {
      type: Boolean,
      default: false,
    },
    canAddRow: {
      type: Boolean,
      default: false,
    },
    isReadonly: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      required: true,
    },
    allowEmpty: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      currentData: [
        [
          {
            type: 'F',
            mandatory: 'Y',
            staff_id: '',
            auto: 'N',
          },
        ],
      ],

      langKey: 'purchase.master.procurementLevel.',
      update: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    currentData: {
      deep: true,
      handler(val) {
        this.update = true
        this.$emit('update:data', val)
      },
    },
    data: {
      deep: true,
      handler(val) {
        if (this.update) {
          this.update = false
          return
        }
        this.currentData = this.data.map(row =>
          row.map(item => {
            item.staff_id === null && (item.staff_id = '')
            return item
          }),
        )
      },
    },
  },
  created() {
    this.init()
  },
  methods: {
    t(key) {
      return this.$t(this.langKey + key)
    },
    init() {
      this.currentData = this.data.map(row =>
        row.map(item => {
          item.staff_id === null && (item.staff_id = '')
          return item
        }),
      )
    },
    onAddItem(row) {
      row.push({
        type: 'F',
        mandatory: 'Y',
        staff_id: '',
        auto: 'N',
      })
    },
    onAddRow() {
      this.currentData.push([
        {
          type: 'F',
          mandatory: 'Y',
          staff_id: '',
          auto: 'N',
        },
      ])
    },
    onDeleteItem(row, gIndex, item, index) {
      row.splice(index, 1)
      if (row.length === 0) {
        this.currentData.splice(gIndex, 1)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.procurement-level-select {
  /deep/ {
    .el-table {
      border: 1px solid #dcdfe6;
      border-radius: 5px;
    }
    .el-table__body-wrapper {
      table {
        tbody {
          tr {
            td {
              > .cell {
                line-height: 35px;
              }
            }
          }
        }
      }
    }

    .add-icon {
      height: 30px;
      width: 20px;
      vertical-align: middle;
      line-height: 35px;
      display: inline-block;
      /*margin: 0 5px;*/
      border-radius: 5px;
      cursor: pointer;

      &.add-row {
        margin-left: 5px;
      }
    }
  }
}
.item-select {
  display: inline-flex;
  /deep/ {
    .item-auto,
    .item-staff {
      height: 10px;
      line-height: 10px;
      .el-input__suffix-inner {
        line-height: 30px;
        i {
          height: inherit;
          line-height: inherit;
        }
      }
    }
    .item-auto {
      width: 60px;
      margin-right: 2px;
      input {
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        border-right-width: 1px;
      }
    }
    .item-staff {
      width: 120px;
      margin-right: 2px;
      input {
        /*border-left: none;*/
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-left-width: 1px;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        border-left-width: 1px;
      }
    }
    .item-delete {
      margin-right: 10px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      border-left-width: 1px;
      padding: 7.5px 7.5px;
    }
  }
}
</style>
