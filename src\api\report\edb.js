import request from '@/utils/request'

/**
 * 匯出EDB報表(教育局版)
 * @param {string} fy_code 會計週期code
 * @param {string} pd_code 截止至會計週期月份編號
 * @param {string} sch_type 格式（Format）小學=PRI 中學=SEC
 * @return {Promise}
 */
export function exportEDBReport({ fy_code, pd_code, sch_type }) {
  return request({
    url: '/reports/edb/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      pd_code,
      sch_type,
    },
  })
}

/**
 * 匯出EDB賬目關係(教育局版)
 * @param {string} fy_code 會計週期code
 * @param {string} sch_type 格式（Format）小學=PRI 中學=SEC
 * @return {Promise}
 */
export function exportReportEdbRelationBySecondarySchool({ fy_code, sch_type }) {
  return request({
    url: '/reports/edb-relation/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      fy_code,
      sch_type,
    },
  })
}

/**
 * 匯入EDB賬目關係(教育局版)
 * @param {string} fy_code 會計週期code
 * @param {string} sch_type 格式（Format）小學=PRI 中學=SEC
 * @param {object} data 匯入的數據,多個sheet,sheet名為鍵名
 * @return {Promise}
 */
export function importReportEdbRelationBySecondarySchool(data, fy_code, sch_type) {
  return request({
    url: '/reports/edb-relation/actions/import',
    method: 'post',
    data: {
      fy_code,
      sch_type,
      data_json: JSON.stringify(data),
    },
  })
}
