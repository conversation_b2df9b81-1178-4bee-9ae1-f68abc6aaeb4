import { toDecimal } from '@/utils'
function add(a, b) {
  const a1 = Number(a)
  const a2 = isNaN(a1) ? 0 : a1
  const b1 = Number(b)
  const b2 = isNaN(b1) ? 0 : b1
  return toDecimal(a2 + b2)
}

export function formatBudgetData(data, isLastYear = false) {
  const newData = []
  const sum = {
    budget_IE: '',
    actual_amount: 0,
    I_actual_amount: 0,
    I_budget_amount: 0,
    I_proposed_amount: 0,
    I_approved_amount: 0,
    I_balance: 0,

    E_actual_amount: 0,
    E_budget_amount: 0,
    E_proposed_amount: 0,
    E_approved_amount: 0,
    E_balance: 0,
  }
  let hasI = false
  let hasE = false
  const cols = [
    'I_actual_amount',
    'I_budget_amount',
    'I_proposed_amount',
    'I_approved_amount',
    'E_actual_amount',
    'E_budget_amount',
    'E_proposed_amount',
    'E_approved_amount',
    'I_balance',
    'E_balance',
    'proposed_amount',
    'approved_amount',
    'budget_amount',
    'actual_amount',
  ]

  for (let i = 0; i < data.length; i++) {
    const item = data[i]
    const newItem = Object.assign({}, item)
    newItem.I_balance = toDecimal(item.I_approved_amount - item.I_actual_amount)
    newItem.E_balance = toDecimal(item.E_approved_amount - item.E_actual_amount)
    newData.push(newItem)
    cols.forEach(key => {
      if (isLastYear) {
        if (item.budget_type === 'D') {
          sum[key] = add(sum[key], newItem[key])
        }
      } else {
        sum[key] = add(sum[key], newItem[key])
      }
    })
    if (!hasI && 'IB'.includes(newItem.budget_IE)) {
      hasI = true
    }
    if (!hasE && 'EB'.includes(newItem.budget_IE)) {
      hasE = true
    }
  }
  if (hasI && hasE) {
    sum.budget_IE = 'B'
  } else if (hasI) {
    sum.budget_IE = 'I'
  } else if (hasE) {
    sum.budget_IE = 'E'
  } else {
    sum.budget_IE = ''
  }

  return {
    data: newData,
    summary: sum,
  }
}

/**
 * 遍歷Tree，獲取第一個符合條件的元素
 * @param tree
 * @param verify
 * @return {*|null}
 */
export function getFirstItem(tree, verify) {
  const getGroup = arr => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (verify(item)) {
        return item
      }
      if (item.children && item.children.length) {
        const child = getGroup(item.children)
        if (child) return child
      }
    }
    return null
  }
  return getGroup(tree)
}

function getTreeBudget(treeArray, key, value) {
  for (let i = 0; i < treeArray.length; i++) {
    const item = treeArray[i]
    if (item[key] === value) {
      return item
    }
    if (item.children && item.children.length > 0) {
      const childResult = getTreeBudget(item.children, key, value)
      if (childResult) return childResult
    }
  }
  return null
}

export function getTreeBudgetById(treeArray, id) {
  return getTreeBudget(treeArray, 'budget_id', id)
}

export function getTreeBudgetByCode(treeArray, code) {
  return getTreeBudget(treeArray, 'budget_code', code)
}

const scrollSyncList = []

/* 同步兩個元素的滾動條 */
function scrollEvent(sourceElement, syncElement) {
  scrollSyncList.forEach((item, i) => {
    if (item[0].scrollLeft !== undefined && item[1].scrollLeft !== undefined) {
      item[1].scrollLeft = item[0].scrollLeft
    }
  })
}
document.body.addEventListener('scroll', scrollEvent, true)

export function scrollBarSync(ele1, ele2) {
  let exist = false
  for (let i = 0; i < scrollSyncList.length; i++) {
    const item = scrollSyncList[i]
    if (ele1 === item[0] || ele2 === item[1]) {
      exist = true
      break
    }
  }
  if (!exist) {
    scrollSyncList.push([ele1, ele2])
  }
}
export function cancelScrollBarSync(ele1, ele2) {
  for (let i = 0; i < scrollSyncList.length; i++) {
    const item = scrollSyncList[i]
    if (ele1 === item[0] || ele2 === item[1]) {
      scrollSyncList.splice(i, 1)
      break
    }
  }
}
