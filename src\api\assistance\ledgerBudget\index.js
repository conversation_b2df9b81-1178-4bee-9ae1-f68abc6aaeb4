import request from '@/utils/request'

/**
 * 獲取账目预算列表
 * @param {string} fy_code 選擇的會計週期
 * @param {string} fund_id 歸屬的fund_id
 */
export function getLedgerBudgets(fy_code, fund_id) {
  return request({
    url: '/ledger-budgets',
    method: 'get',
    params: {
      fy_code,
      fund_id,
    },
  })
}

/**
 * 獲取账目预算列表
 * @param {string} fy_code 選擇的會計週期
 * @param {string} ledger_budgets_json 預算金額,bgt_type=I或E,不活躍的不需要傳,格式: [{"ac_code":"001","bgt_type":"I","bgt_amount":"1000.0O"},...]
 */
export function updateLedgerBugets(fy_code, ledger_budgets_json) {
  return request({
    url: '/ledger-budgets/actions/update',
    method: 'post',
    data: {
      fy_code,
      ledger_budgets_json,
    },
  })
}
