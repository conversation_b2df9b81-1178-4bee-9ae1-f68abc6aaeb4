<template>
  <div class="app-container">
    <VBreadCrumb :show="true" class="breadcrumb" />

    <el-form
      ref="form"
      v-loading="loading"
      :model="preferences.filters"
      :inline="true"
      class="form"
      label-position="right"
      label-width="80px"
    >
      <!-- 學年 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.fundSummaryTypesRelation.year')"
        class="year"
        prop="year"
      >
        <el-select v-model="preferences.filters.year" style="width: 110px" @change="fetchData">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :value="item.fy_code"
            :label="item.fy_name"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-if="hasPermission_Input" size="mini" type="primary" @click="onImport">
          {{ $t('button.import') }}
        </el-button>
        <el-button v-if="hasPermission_Output" size="mini" type="primary" @click="onExport">
          {{ $t('button.export') }}
        </el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button
          v-if="hasPermission_Update"
          size="mini"
          type="primary"
          class="update-btn"
          @click="onUpdate"
        >
          {{ $t('button.update') }}
        </el-button>
      </el-form-item>
      <el-table :data="tableData" border style="width: 651px">
        <el-table-column
          :label="$t('assistance.numberOfSchool.month')"
          :resizable="false"
          prop="month"
          align="center"
          width="130"
        />
        <el-table-column
          :label="$t('assistance.numberOfSchool.num_of_half_day')"
          :resizable="false"
          prop="num_of_half_day"
          align="center"
          width="130"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-input-number
              v-model="scope.row.num_of_half_day"
              :controls="false"
              size="mini"
              class="input-number"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('assistance.numberOfSchool.num_of_whole_day')"
          :resizable="false"
          prop="num_of_whole_day"
          align="center"
          width="130"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-input-number
              v-model="scope.row.num_of_whole_day"
              :controls="false"
              size="mini"
              class="input-number"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('assistance.numberOfSchool.scale_of_whole_day')"
          :resizable="false"
          prop="scale_of_whole_day"
          align="center"
          width="130"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-input-number
              v-model="scope.row.scale_of_whole_day"
              :controls="false"
              size="mini"
              class="input-number"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('assistance.numberOfSchool.num_of_infant')"
          :resizable="false"
          prop="num_of_infant"
          align="center"
          width="130"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <el-input-number
              v-model="scope.row.num_of_infant"
              :controls="false"
              size="mini"
              class="input-number"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <import-dialog
      :visible.sync="importDialog"
      :on-success="handleImport"
      :on-template="onExport"
      :multiple="false"
    />
  </div>
</template>

<script>
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import { mapGetters } from 'vuex'
import loadPreferences from '@/views/mixins/loadPreferences'
import permission from '@/views/mixins/permission'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'
import { exportExcel, importExcel, importExcelMultiple } from '@/utils/excel'
import ImportDialog from '@/components/ImportDialog/index'
import {
  editNumberOfSchool,
  exportNumberOfSchool,
  fetchNumberOfSchool,
  importNumberOfSchool,
} from '@/api/assistance/edb'

export default {
  name: 'AssistanceEDBRelation',
  components: {
    VBreadCrumb,
    ImportDialog,
    UploadExcel,
  },
  mixins: [loadPreferences, permission],
  data() {
    return {
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      preferences: {
        filters: {
          year: '',
        },
      },
      years: [],
      importDialog: false,
      tableData: [],
    }
  },
  computed: {
    ...mapGetters(['system', 'currentYear']),
  },
  created() {
    console.log('initData')
    this.initData()
    this.saveUserLastPage()
  },
  methods: {
    async initData() {
      this.loading = false
      try {
        this.years = await fetchYears()
        await this.loadUserPreference()
        await this.$nextTick()
        if (!this.preferences.filters.year && this.currentYear) {
          this.preferences.filters.year = this.currentYear.fy_code
        }
        await this.fetchData()
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    async onImport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return

      this.importDialog = true
    },
    async handleImport({ header, results }) {
      this.loading = true
      const fy_code = this.preferences.filters.year
      if (!fy_code) return
      return importExcel(this, importNumberOfSchool, results, header, { fy_code })
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
          this.fetchData()
        })
    },
    async onExport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return
      try {
        this.loading = true
        const fy_code = this.preferences.filters.year
        const res = await exportNumberOfSchool({ fy_code })
        await exportExcel(res, '')
        this.$message.success(this.$t('file.exportSuccess'))
      } catch (e) {
        this.$message.error(this.$t('file.exportError'))
        console.log(e)
      }
      this.loading = false
    },
    getMonth(m) {
      if (m < 10) {
        return '0' + m
      } else {
        return '' + m
      }
    },
    async fetchData() {
      try {
        const fy_code = this.preferences.filters.year
        if (fy_code) {
          // const data = [...Array(12)].map((v,i) => {
          //   return {
          //     month: this.getMonth(i + 1),
          //     num_of_half_day: 0,
          //     num_of_infant: 0,
          //     num_of_whole_day: 0,
          //     scale_of_whole_day: 0,
          //   }
          // })
          const data = []
          const res = await fetchNumberOfSchool(fy_code)
          res.forEach(item => {
            data.push({
              month: item.month,
              num_of_half_day: item.num_of_half_day,
              num_of_infant: item.num_of_infant,
              num_of_whole_day: item.num_of_whole_day,
              scale_of_whole_day: item.scale_of_whole_day,
            })
          })
          this.tableData = data
        }
      } catch (e) {
        console.error(e)
      }
    },
    async onUpdate() {
      try {
        const fy_code = this.preferences.filters.year
        const numbers = this.tableData.map(row => {
          return {
            fy_code: fy_code,
            month: row.month,
            num_of_half_day: row.num_of_half_day,
            num_of_infant: row.num_of_infant,
            num_of_whole_day: row.num_of_whole_day,
            scale_of_whole_day: row.scale_of_whole_day,
          }
        })
        if (fy_code) {
          const res = await editNumberOfSchool({
            fy_code,
            numbers,
          })
          console.log(res)
          this.$message.success(this.$t('message.success'))
        }
      } catch (e) {
        console.error(e)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.year {
  width: 200px;
}
.input-number {
  width: 100%;
}
.form {
  width: 660px;

  .update-btn {
  }
}
</style>
