<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 收付款公司編號 -->
      <el-form-item
        :rules="codeRules"
        :class="{ 'code-rules-error': haveExist }"
        :label="$t('assistance.payeePayer.label.comp_code')"
        prop="comp_code"
      >
        <el-input v-model="form.comp_code" clearable />
      </el-form-item>
      <!-- 公司名 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.payeePayer.label.comp_name')"
        prop="comp_name"
      >
        <el-input v-model="form.comp_name" clearable />
      </el-form-item>
      <!-- 簡稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.payeePayer.label.comp_abbr')"
        prop="comp_abbr"
      >
        <el-input v-model="form.comp_abbr" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :label="$t('assistance.payeePayer.label.parent_company_group_id')"
        prop="parent_company_group_id"
      >
        <el-select
          v-model="form.parent_company_group_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in companyGroups"
            :key="item.value"
            :value="item.company_group_id"
            :label="conversionParentCompanyGroup(item)"
            v-html="conversionParentCompanyGroup(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置 -->
      <el-form-item :label="$t('assistance.payeePayer.label.seq')" prop="seq">
        <el-select v-model="form.seq" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in sepOptions"
            :key="item.company_group_id"
            :label="language === 'en' ? item.comp_name_en : item.comp_name_cn"
            :value="item.company_group_id ? item.company_group_id : item.company_id"
          >
            {{ language === 'en' ? item.comp_name_en : item.comp_name_cn }}
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 類別 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.payeePayer.label.comp_group')"
        prop="comp_group"
      >
        <el-select v-model="form.comp_group" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in groupOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            {{ language === 'en' ? item.label : item.label }}
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 供應商 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_supplier')" prop="comp_supplier">
        <el-checkbox v-model="form.comp_supplier" />
      </el-form-item>

      <!-- 地址 -->
      <el-form-item
        id="comp_add"
        :label="$t('assistance.payeePayer.label.comp_add')"
        prop="comp_add"
      >
        <el-input v-model="form.comp_add1" clearable />
        <el-input v-model="form.comp_add2" clearable />
        <el-input v-model="form.comp_add3" clearable />
        <el-input v-model="form.comp_add4" clearable />
      </el-form-item>
      <!-- 電話 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_tel')" prop="comp_tel">
        <el-input v-model="form.comp_tel" clearable />
      </el-form-item>
      <!-- 傳真 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_fax')" prop="comp_fax">
        <el-input v-model="form.comp_fax" clearable />
      </el-form-item>
      <!-- 聯絡人 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_attention')" prop="comp_attention">
        <el-input v-model="form.comp_attention" clearable />
      </el-form-item>
      <!-- 電郵 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_email')" prop="comp_email">
        <el-input v-model="form.comp_email" clearable />
      </el-form-item>
      <!-- 備註 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_remark')" prop="comp_remark">
        <el-input v-model="form.comp_remark" clearable />
      </el-form-item>
      <!-- 供應商 -->
      <el-form-item :label="$t('assistance.payeePayer.label.comp_ap')" prop="comp_ap">
        <el-checkbox v-model="form.comp_ap" />
      </el-form-item>
      <!-- credit -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.payeePayer.label.comp_credit')"
        prop="comp_credit"
      >
        <el-input v-model="form.comp_credit" clearable />
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.staff.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_code"
            :value="item.fy_code"
          >
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import { createStaff, editStaff, getStaff, getStaffsTreeNode } from '@/api/assistance/staff'
import {
  createCompany,
  updateCompany,
  getCompany,
  getCompaniesTreeNode,
} from '@/api/assistance/payeePayer'
// import { fetchStaffTypes } from '@/api/assistance/staff/staffType'
import { fetchCompanyGroups } from '@/api/assistance/payeePayer/payeePayerGroup'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'AssistancePayeePayerAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      system: 'BG',
      form: {
        company_id: '',
        company_group_id: '',
        comp_code: '',
        comp_name: '',
        comp_abbr: '',
        comp_group: '',
        comp_supplier: '',
        comp_add1: '',
        comp_add2: '',
        comp_add3: '',
        comp_add4: '',
        comp_tel: '',
        comp_fax: '',
        comp_attention: '',
        comp_email: '',
        comp_remark: '',
        comp_ap: '',
        comp_credit: '0',
        active_year: '',
        parent_company_group_id: '',
        seq: '',
      },
      defaultForm: {
        company_id: '',
        company_group_id: '',
        comp_code: '',
        comp_name: '',
        comp_abbr: '',
        comp_group: '',
        comp_supplier: '',
        comp_add1: '',
        comp_add2: '',
        comp_add3: '',
        comp_add4: '',
        comp_tel: '',
        comp_fax: '',
        comp_attention: '',
        comp_email: '',
        comp_remark: '',
        comp_ap: '',
        comp_credit: '0',
        active_year: '',
        parent_company_group_id: '',
        seq: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      defaultSepOptions: [
        {
          company_group_id: '',
          company_id: '',
          seq: '',
          comp_name_en: this.$t('assistance.payeePayerGroup.label.seq_default'),
          comp_name_cn: this.$t('assistance.payeePayerGroup.label.seq_default'),
        },
      ],
      sepOptions: [
        {
          company_group_id: '',
          company_id: '',
          seq: '',
          comp_name_en: this.$t('assistance.payeePayerGroup.label.seq_default'),
          comp_name_cn: this.$t('assistance.payeePayerGroup.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      defaultCompanyGroups: [
        {
          company_group_id: '',
          cg_name_cn: this.$t('assistance.payeePayerGroup.label.parent_default'),
          cg_name_en: this.$t('assistance.payeePayerGroup.label.parent_default'),
        },
      ],
      companyGroups: [],
      seqStartLevel: 1,
      active_year_arr: [],
      loading: true,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
    groupOptions() {
      return [
        {
          value: 'B',
          label: this.$t('assistance.payeePayerGroup.groupOptions.B'),
        },
        {
          value: 'I',
          label: this.$t('assistance.payeePayerGroup.groupOptions.I'),
        },
        {
          value: 'E',
          label: this.$t('assistance.payeePayerGroup.groupOptions.E'),
        },
      ]
    },
    codeRules() {
      return [{ required: true, validator: this.checkCode, trigger: 'blur' }]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.code === i.code) {
          return
        }
        if (i.children) {
          codeArr.push(...this.getCodeArr(i.children))
        }
        console.log('i', i)

        if (i.company_id) {
          codeArr.push(i.code)
        }
      })
      return codeArr
    },

    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentCompanyGroup(companyGroup, html, startLevel = 1) {
      let text = this.language === 'en' ? companyGroup.cg_name_en : companyGroup.cg_name_cn
      if (html) {
        text = '&nbsp;'.repeat((companyGroup.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    changeParentType() {
      getCompaniesTreeNode({
        parent_company_group_id: this.form.parent_company_group_id,
        except_company_group_id: this.form.company_group_id,
        except_company_id: this.form.company_id,
      }).then(res => {
        this.sepOptions = [...res, ...this.defaultSepOptions]
        console.log('seqOption', this.sepOptions)
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].company_group_id ? res[i].company_group_id : res[i].company_id
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.company_group_id) {
        this.form.parent_company_group_id = this.editParent.company_group_id
      } else if (
        this.editObject &&
        this.editObject.parent &&
        this.editObject.parent.company_group_id
      ) {
        this.form.parent_company_group_id = this.editObject.parent.company_group_id
        // this.form.seq = this.editObject.parent.seq
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getCompany(this.editObject.company_id)
            .then(res => {
              this.form = Object.assign(this.defaultForm, res)
              this.setParent()
              res.comp_supplier
                ? (this.form.comp_supplier = true)
                : (this.form.comp_supplier = false)
              res.comp_ap ? (this.form.comp_ap = true) : (this.form.comp_ap = false)
              this.form.parent_company_group_id =
                res.companyTypeRelation && res.companyTypeRelation.parent_company_group_id
                  ? res.companyTypeRelation.parent_company_group_id
                  : ''
              this.form.seq_i = res.companyTypeRelation ? res.companyTypeRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : []
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => fetchCompanyGroups({}))
        .then(res => {
          this.companyGroups = [...this.defaultCompanyGroups, ...res]
          this.changeParentType(this.form.parent_company_group_id)
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject && this.fyCode) {
            const year = res.find(i => i.fy_code === this.fyCode)
            if (year) this.active_year_arr.push(year.fy_code)
          } else if (!this.editObject && !this.fyCode) {
            this.active_year_arr = res.map(i => i.fy_code)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.company_group_id ? 'company_group_id' : 'company_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const company_id = this.form.company_id
        const comp_code = this.form.comp_code
        const comp_name = this.form.comp_name
        const comp_abbr = this.form.comp_abbr
        const comp_group = this.form.comp_group
        // const comp_supplier = this.form.comp_supplier
        let comp_supplier
        this.form.comp_supplier ? (comp_supplier = 'Y') : (comp_supplier = 'N')
        const comp_add1 = this.form.comp_add1
        const comp_add2 = this.form.comp_add2
        const comp_add3 = this.form.comp_add3
        const comp_add4 = this.form.comp_add4
        const comp_tel = this.form.comp_tel
        const comp_fax = this.form.comp_fax
        const comp_attention = this.form.comp_attention
        const comp_email = this.form.comp_email
        const comp_remark = this.form.comp_remark
        // const comp_ap = this.form.comp_ap
        let comp_ap
        this.form.comp_ap ? (comp_ap = 'Y') : (comp_ap = 'N')
        const comp_credit = this.form.comp_credit
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const parent_company_group_id = this.form.parent_company_group_id
        const seq = this.newSeq()
        if (this.editObject) {
          // 編輯
          updateCompany(
            company_id,
            comp_code,
            comp_name,
            comp_abbr,
            comp_group,
            comp_supplier,
            comp_add1,
            comp_add2,
            comp_add3,
            comp_add4,
            comp_tel,
            comp_fax,
            comp_attention,
            comp_email,
            comp_remark,
            comp_ap,
            comp_credit,
            active_year,
            parent_company_group_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        } else {
          // 新增
          createCompany(
            comp_code,
            comp_name,
            comp_abbr,
            comp_group,
            comp_supplier,
            comp_add1,
            comp_add2,
            comp_add3,
            comp_add4,
            comp_tel,
            comp_fax,
            comp_attention,
            comp_email,
            comp_remark,
            comp_ap,
            comp_credit,
            active_year,
            parent_company_group_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.error(err)
            })
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    setName(name) {
      this.form.comp_name = name
      this.form.comp_abbr = name
    },
    setAcCode(code) {
      this.form.comp_code = code
    },
    setYear(yearsStr) {
      this.active_year_arr = yearsStr.split(',')
    },
  },
}
</script>

<style lang="scss" scoped>
#comp_add /deep/ {
  .el-form-item__content {
    height: auto !important;
  }
  .el-input {
    margin-bottom: 2px;
  }
}
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
