<template>
  <div class="app-content">
    <div class="setting-page">
      <div class="header">
        <slot name="header">
          <el-header>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>
                {{ $t('router.settingPrintout') }}
              </el-breadcrumb-item>
              <el-breadcrumb-item>
                {{ $t($route.meta.title) }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </el-header>
        </slot>
      </div>
      <div class="content">
        <slot name="content" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  mixins: [],
  data() {
    return {}
  },
  computed: {},
  created() {},
  methods: {},
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.header,
.el-breadcrumb {
  height: 60px;
  line-height: 60px;
  font-size: 18px !important;
  /*background-color: rgb(84, 92, 100);*/
}
.header > header {
  margin-left: 0;
}
.content /deep/ .el-main {
  padding: 0px 20px 20px 20px;
}
.app-content {
  height: 100%;
  .setting-page {
    height: calc(100vh - 114px);
    /deep/ {
      .content {
        height: calc(100% - 60px);
        .el-main {
          height: 100%;
        }
      }
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss"></style>
