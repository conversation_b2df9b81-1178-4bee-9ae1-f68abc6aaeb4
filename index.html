<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>EDAC</title>
    <style>
      #app #loadingMsg{margin: 5px auto; width: 150px; height: 30px; text-align: center; font-size: 10px;}#app .spinner{margin: 100px auto 20px auto; width: 150px; height: 60px; text-align: center; font-size: 10px;}#app .spinner > div{background-color: #6dacfe; height: 100%; width: 6px; display: inline-block; -webkit-animation: stretchDelay 1.2s infinite ease-in-out; animation: stretchDelay 1.2s infinite ease-in-out; margin-left: 1px; margin-right: 1px;}#app .spinner .rect2{-webkit-animation-delay: -1.1s; animation-delay: -1.1s;}#app .spinner .rect3{-webkit-animation-delay: -1.0s; animation-delay: -1.0s;}.spinner .rect4{-webkit-animation-delay: -0.9s; animation-delay: -0.9s;}#app .spinner .rect5{-webkit-animation-delay: -0.8s; animation-delay: -0.8s;}@-webkit-keyframes stretchDelay{0%, 40%, 100%{-webkit-transform: scaleY(0.4)}20%{-webkit-transform: scaleY(1.0)}}@keyframes stretchDelay{0%, 40%, 100%{transform: scaleY(0.4); -webkit-transform: scaleY(0.4);}20%{transform: scaleY(1.0); -webkit-transform: scaleY(1.0);}}
    </style>
  </head>
  <body>
    <!--<script src=<%= BASE_URL %>/tinymce4.7.5/tinymce.min.js></script>-->
    <div id="app">
      <div class="spinner">
        <div class="rect1"></div>
        <div class="rect2"></div>
        <div class="rect3"></div>
        <div class="rect4"></div>
        <div class="rect5"></div>
      </div>
      <div id="loadingMsg"></div>

      <script type="application/javascript">
        function getCookie(key) {
          var list =document.cookie.split(';')
          for (let i = 0; i < list.length; i++) {
            var item = list[i].replace(/^\s*(.*)\s*$/, "$1")
            var kv = item.split('=')
            if(kv.length === 2) {
              if (kv[0] === key) {
                return kv[1]
              }
            }
          }
          return ''
        }
        var language = getCookie('language')
        delete window.Cookies
        var msg = 'loading...'
        if (language !== 'en') {
          msg = '正在加載...'
        }
        document.querySelector('#loadingMsg').innerText = msg
      </script>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
