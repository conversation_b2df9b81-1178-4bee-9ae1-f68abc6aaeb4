import request from '@/utils/request'

/**
 * 獲取過賬過來但未生成傳票的數據
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @param {string} vt_code 傳票類別編號
 * @param vc_vtcode
 * @param {string} method_of_payment 付款方式 CASH,CHECK,AUTOPAY,FPS,BANKIN,OTHER
 * @param {string} vc_date 傳票日期
 * @return {Promise}
 */
export function fetchPosts({ vt_category, vt_code, vc_vtcode, method_of_payment, vc_date }) {
  return request({
    url: '/vouchers/actions/posted-list',
    method: 'get',
    params: {
      vt_category,
      vt_code,
      vc_vtcode,
      method_of_payment,
      vc_date,
    },
  })
}

/**
 * 將收費的數據過到EDAC
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @param {string} date 截止日期
 * @param {string} voucher_date 傳票日期
 * @return {Promise}
 */
export function postData({ vt_category, date, voucher_date }) {
  return request({
    url: '/vouchers/actions/post',
    method: 'post',
    data: {
      vt_category,
      date,
      voucher_date,
    },
  })
}

/**
 * 獲取過賬過來但未生成傳票的數據詳情
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @param {string} post_row_id id
 * @return {Promise}
 */
export function getPost({ vt_category, post_row_id }) {
  return request({
    url: '/vouchers/actions/posted-inquire',
    method: 'get',
    params: {
      vt_category,
      post_row_id,
    },
  })
}

/**
 * 更新過賬過來但未生成傳票的數據詳情
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @param {array} posted_data 傳票日期
 * @return {Promise}
 */
export function savePostDetailData({ vt_category, posted_data }) {
  return request({
    url: '/vouchers/actions/posted-update',
    method: 'post',
    data: {
      vt_category,
      posted_data,
    },
  })
}

/**
 * 獲取過賬過來但未生成傳票的數據中包含的傳票類別編號
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @return {Promise}
 */
export function getPostVcTypeList({ vt_category }) {
  return request({
    url: '/vouchers/actions/posted-vt-code',
    method: 'get',
    params: {
      vt_category,
    },
  })
}

/**
 * 獲取多條過賬過來但未生成傳票的數據詳情
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @param {string} post_row_ids ids
 * @return {Promise}
 */
export function getMultiplePost({ vt_category, post_row_ids }) {
  return request({
    url: '/vouchers/actions/posted-multiple-inquire',
    method: 'get',
    params: {
      vt_category,
      post_row_ids,
    },
  })
}

/**
 *
 * @param {string} vt_category P=付款傳票,R=收款傳票,C=現金傳票
 * @param {string} post_row_ids ids
 * @param {string} vc_vtcode ids
 * @param {string} ac_code ids
 * @return {Promise}
 */
export function updateVoucherPostVtCode({ vt_category, post_row_ids, vc_vtcode, ac_code }) {
  return request({
    url: '/vouchers/actions/posted-update-vt-code',
    method: 'post',
    data: {
      vt_category,
      post_row_ids,
      vc_vtcode,
      ac_code,
    },
  })
}

export default {
  fetchPosts,
  getPost,
  postData,
  getPostVcTypeList,
  getMultiplePost,
  savePostDetailData,
}
