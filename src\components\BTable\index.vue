<template>
  <vxe-table
    ref="singleTable"
    :data="data"
    :height="height"
    :max-height="height"
    :o-size="10"
    :cell-style="{ height: `${rowHeight}px` }"
    :row-class-name="filterClassFunction"
    :resizable="resizable"
    show-overflow
    :cell-class-name="cellClassName"
    :header-cell-style="headerCellStyle"
    highlight-hover-row
    :border="border"
    v-bind="$attrs"
    class="b-table"
    v-on="$listeners"
    @resizable-change="onHeaderDragend"
  >
    <!--

    :row-class-name="filterClassFunction"
    @header-dragend="onHeaderDragend"

    -->
    <span v-html="styleSheets" />

    <vxe-table-column
      v-if="indexColumn && showIndex"
      :align="indexColumn.alignment"
      :width="indexColumn.width"
      :index="index"
      column-key="_index"
      :params="{ key: '_index' }"
      type="seq"
      title="#"
    />
    <vxe-table-column v-if="showCheckbox" width="40" type="checkbox" />
    <slot name="prefixColumns" />
    <slot name="columns">
      <!--       && i.ss_key !== 'type'-->
      <vxe-table-column
        v-for="(item, index) in styleColumns.filter(i => i.ss_key !== '_index')"
        :key="item.ss_key"
        :title="$t(langKey + item.ss_key)"
        :align="item.alignment"
        :width="lastColumnAutoWidth && index === columnList.length - 1 ? '' : item.width"
        :min-width="lastColumnAutoWidth && index === columnList.length - 1 ? 100 : ''"
        :property="column_property(item)"
        :column-key="item.ss_key"
        :params="{ key: item.ss_key }"
        :field="column_property(item)"
        :formatter="formatters[item.ss_key] || formatter"
        :sortable="sortable"
      />
      <!--
      :property="column_property(item)"
      -->
      <!--      <el-table-column-->
      <!--        v-for="item in styleColumns.filter(i => i.ss_key == 'type')"-->
      <!--        :key="item.ss_key"-->
      <!--        :title="$t(langKey + item.ss_key)"-->
      <!--        :align="item.alignment"-->
      <!--        :width="item.width"-->
      <!--        :property="column_property(item)"-->
      <!--        :column-key="item.ss_key"-->
      <!--        :formatter="typeFormat"-->
      <!--        :sortable="sortable"-->
      <!--      />-->
    </slot>
    <vxe-table-column
      v-if="showActions && styleColumns && styleColumns.length > 0"
      :title="actionLabel ? actionLabel : defaultActionLabel"
      :min-width="actionsMinWidth"
      fixed="right"
      align="left"
      header-align="left"
    >
      <template v-if="scope && scope.row" slot-scope="scope">
        <slot :scope="scope" name="actions" />
      </template>
    </vxe-table-column>
    <vxe-table-column
      v-if="styleColumns.length && fullColumn"
      :min-width="fullColumnWidth"
      align="right"
      header-align="right"
    />
    <template slot="append">
      <slot name="append" />
    </template>
    <template v-if="emptyText" #empty>
      <span>
        <p>{{ emptyText }}</p>
      </span>
    </template>
  </vxe-table>
</template>

<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
import dateUtil from '@/utils/date'

export default {
  name: 'BTable',
  props: {
    /* eslint-disable */
    data: {
      type: [Array, Object],
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    resizable: {
      type: Boolean,
      default: true,
    },
    border: {
      type: Boolean,
      default: true,
    },
    fullColumn: {
      type: Boolean,
      default: false,
    },
    fullColumnWidth: {
      type: Number,
      default: 1,
    },
    actionLabel: {
      type: String,
      default: '',
    },
    showIndex: {
      type: Boolean,
      default: true,
    },
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    actionsMinWidth: {
      type: Number,
      default: 100,
    },
    styleColumns: {
      type: Array,
      default: () => [],
    },
    amountColumns: {
      type: Array,
      default: () => [],
    },
    langKey: {
      type: String,
      default: '',
    },
    defaultTop: {
      type: Number,
      default: 140,
    },
    autoHeight: {
      type: Boolean,
      default: true,
    },
    lastColumnAutoWidth: {
      type: Boolean,
      default: false,
    },
    height: {
      type: [Number, String],
      default: 500,
    },
    sortable: {
      type: Boolean,
      default: false,
    },
    headerCellStyle: {
      type: Function,
      default: () => ({}),
    },
    cellClassName: {
      type: Function,
      default: () => '',
    },
    filterClassFunction: {
      type: Function,
      default: function (row) {
        /**
         * Table斑馬紋
         */
        if (row.$rowIndex % 2 === 0) {
          return 'table-stripe'
        }
      },
    },
    formatters: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Function,
      default: i => i + 1,
    },
    emptyText: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // tableHeight: 400
      styleSheets: '',
      tabHeight: 500,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    indexColumn() {
      return this.styleColumns.find(item => item.ss_key === '_index')
    },
    columnList() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    defaultActionLabel() {
      return this.$t('table.action')
    },
    rowHeight() {
      if (this.styles && this.styles.contentLineHeight) {
        return this.styles.contentLineHeight
      }
      return 24
    },
  },
  watch: {
    // 'data'() {
    //   this.$nextTick(() => {
    //     this.updateHeight()
    //   })
    // }
  },
  updated() {
    this.$nextTick(() => {
      this.updateHeight()
    })
  },
  activated() {},
  mounted() {
    this.$nextTick(() => {
      this.updateHeight()
    })
  },
  methods: {
    getParentTop(el) {
      let top = el.offsetTop

      if (el.parentNode) {
        const pt = this.getParentTop(el.parentNode)
        if (pt) top = top + pt
      }
      console.log(top, el)
      return top
    },
    updateHeight() {
      if (!this.autoHeight) return
      // const t = this.getParentTop(this.$refs['singleTable'].$el)
      // debugger
      const top = this.$refs['singleTable'].$el.parentNode.parentNode.parentNode.offsetTop
      const headerHeight = this.$refs['singleTable'].$el.children[1].offsetHeight
      const defaultTop = this.defaultTop
      // console.log(t)
      this.styleSheets = `
<style>
  .e-table {
    height: calc(100vh - ${defaultTop}px - ${top}px);
  }
  .e-table .el-table__body-wrapper {
    height: calc(100vh - ${defaultTop}px - 2px - ${top}px - ${headerHeight}px);
  }
</style>

      `
    },
    /**
     * 修改列寬提交到父組件
     * @param newWidth
     * @param oldWidth
     * @param column
     * @param event
     */
    onHeaderDragend({ $rowIndex, column, columnIndex, $columnIndex, $event }) {
      console.log('resizable', { $rowIndex, column, columnIndex, $columnIndex, $event })
      const newWidth = column.resizeWidth
      const key = (column.params && column.params.key) || ''
      if (newWidth && key) {
        this.$emit('changeWidth', key, newWidth)

        this.$nextTick(() => {
          this.updateHeight()
        })
      }
    },
    column_property(item) {
      if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
        return item.ss_key + (this.language === 'en' ? 'en' : 'cn')
      } else {
        return item.ss_key
      }
    },
    column_label(item) {
      if (item.ss_key === '_index') {
        return '#'
      }
      // else if (item.ss_key.charAt(item.ss_key.length - 1) === '_') {
      //   return item.ss_key.substring(0, item.ss_key.length-1)
      // }
      else {
        return item.ss_key
      }
    },
    formatter({ row, column, cellValue }) {
      if (cellValue == null || cellValue === '') return ''
      if (this.amountColumns.includes(column.property)) {
        // 金額
        return amountFormat(cellValue)
      }
      if (column.property.includes('date')) {
        // 日期
        return dateUtil.format(new Date(cellValue), this.styles.dateFormat)
      }
      return cellValue
    },
    setCurrentRow(row) {
      if (this.$refs.singleTable) {
        this.$refs.singleTable.setCurrentRow(row)
      } else {
        this.$nextTick(() => {
          this.$refs.singleTable.setCurrentRow(row)
        })
      }
    },
    customFormatter(key, cellValue, customDateFormat) {
      if (cellValue == null || cellValue === '') return ''
      if (this.amountColumns.includes(key)) {
        // 金額
        return amountFormat(cellValue)
      }
      const date = new Date(cellValue)
      if (key.substr(key.length - 4) === 'date' && !isNaN(date.getTime())) {
        // 日期
        return dateUtil.format(
          date,
          customDateFormat !== undefined ? customDateFormat : this.styles.dateFormat
        )
      }
      return cellValue
    },
    methods() {
      let func = ''
      const arr = []
      for (let i = 0; i < arguments.length; i++) {
        if (i === 0) {
          func = arguments[0]
        } else {
          arr.push(arguments[i])
        }
      }
      if (func && this.$refs.singleTable) {
        this.$refs.singleTable[func].apply(this.$refs.singleTable[func], arr)
      }
    },
    sortAmountMethod(key, key2 = null) {
      console.log('sortAmountMethod', key)
      // var v1 = (a.name || '').toLowerCase()
      // var v2 = (b.name || '').toLowerCase()
      // return v1 < v2 ? -1 : v1 > v2 ? 1 : 0
      if (this.amountColumns.includes(key)) {
        if (key2 === null) {
          return function (a, b) {
            console.log(a[key], b[key])

            const n1 = Number(a[key])
            const n2 = Number(b[key])
            return !a[key] ? -1 : n1 < n2 ? -1 : n1 > n2 ? 1 : 0
          }
        } else {
          return function (a, b) {
            const n1 = Number(a[key])
            const n2 = Number(b[key])
            const m1 = Number(a[key2])
            const m2 = Number(b[key2])
            if (m1 === m2) {
              return n1 < n2 ? -1 : n1 > n2 ? 1 : 0
            } else {
              return m1 < m2 ? -1 : m1 > m2 ? 1 : 0
            }
          }
        }
      } else {
        if (key2 === null) {
          return function (a, b) {
            return !a[key] ? -1 : a[key] < b[key] ? -1 : a[key] > b[key] ? 1 : 0
          }
        } else {
          return function (a, b) {
            const g1 = a[key2]
            const g2 = b[key2]
            const v1 = a[key]
            const v2 = b[key]
            if (g1 === g2) {
              return v1 < v2 ? -1 : v1 > v2 ? 1 : 0
            } else {
              return g1 < g2 ? -1 : g1 > g2 ? 1 : 0
            }
          }
        }
      }
    },
    handleAmountSort(a, b) {
      con
    },
    refreshColumn() {
      this.$refs.singleTable.refreshColumn()
    },
    updateFooter() {
      this.$refs.singleTable.updateFooter()
    },
    moveRowTo(lg_id) {
      const row = this.data.find(i => i.lg_id === lg_id)
      setTimeout(() => {
        this.$refs.singleTable && this.$refs.singleTable.scrollToRow(row)
      }, 0)
    },
  },
}
</script>
<style rel="stylesheet/css" lang="scss">
.vxe-table--tooltip-wrapper {
  box-shadow: 0 0 4px #0000006e;
  .vxe-table--tooltip-content {
    text-shadow: 0 0 1px black;
  }
}
</style>

<style lang="scss" rel="stylesheet/scss" scoped>
.e-table {
  /*height: calc(100vh - 210px);*/

  /deep/ {
    .el-table__body-wrapper {
      /*height: calc(100vh - 240px);*/
      overflow-y: auto;
    }
    .el-table-column--selection .cell {
      text-overflow: unset !important;
    }
    /*.el-table--border{*/
    /*  border: 1px solid #ebeef5;*/
    /*}*/
  }
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.b-table {
  /deep/ {
    .vxe-table--main-wrapper {
      .vxe-cell {
        .vxe-cell--checkbox {
          span.vxe-checkbox--icon {
            &:before {
              border-width: 1px;
            }
          }
        }
      }
      .vxe-table--header-wrapper {
        .vxe-table--header {
          .vxe-header--row {
            .vxe-header--column {
              .vxe-cell {
                word-wrap: normal;
                text-overflow: ellipsis;
                vertical-align: middle;
                overflow: hidden;
                white-space: nowrap;
                .vxe-cell--title {
                  line-height: 23px;
                }
                .vxe-cell--sort {
                  i {
                    font-size: inherit;
                    /*color: #FFFFFF;*/
                    &.sort--active,
                    &:hover {
                      // color: #FFFFFF;
                      /*filter: brightness(70%);*/
                    }
                  }
                  .sort--active {
                    color: #409eff !important;
                  }
                }
              }
              &.is--sortable {
                cursor: pointer;
              }
            }
          }
        }
      }
      .vxe-table--body-wrapper {
        height: 100vh;
        .vxe-table--body {
          .vxe-body--row {
            .vxe-body--column {
              vertical-align: middle;
              padding: 2px 0;
              overflow: hidden;
              .vxe-cell {
              }
            }
          }
        }
      }
      .vxe-table--footer-wrapper {
        .vxe-table--footer {
          .vxe-footer--row {
            .vxe-footer--column {
              background-color: #f5f7fa;
              color: #404246;
            }
          }
        }
      }
    }
    .vxe-table--empty-content {
      width: 50%;
      color: #909399;
    }
  }
}
</style>
