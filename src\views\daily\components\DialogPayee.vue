<template>
  <!--收款人彈窗-->
  <el-dialog
    :visible.sync="showDialog"
    :title="$t('daily.dialog.payee')"
    @open="onOpenDialogAccount"
  >
    <addPage
      v-if="dialogView === 'add'"
      :default-parent="searchFilter.fund_id"
      :edit-parent="addParent"
      class="voucher-dialog-add"
      @onCancel="onCancel"
    />
    <div v-else class="selectPayee">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.companyGroups')">
            <el-select v-model="searchFilter.company_group_id" style="width: 150px">
              <el-option :label="allPayee.label" :value="allPayee.value" />
              <el-option
                v-for="item in companyGroupList"
                :key="item.company_group_id"
                :label="language === 'en' ? item.cg_name_en : item.cg_name_cn"
                :value="item.company_group_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('daily.label.name')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear(true)">
              {{ $t('button.clear') }}
            </el-button>
            <el-button v-if="hasPermission_Add" size="mini" type="primary" @click="onAdd">
              {{ $t('button.add') }}
            </el-button>
          </el-form-item>
          <el-form-item :label="$t('daily.label.type')">
            <el-select v-model="searchFilter.type" style="width: 100px" @change="fetchData">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="100%"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="$t('daily.label.no')" property="comp_code" width="150" />
          <el-table-column :label="$t('daily.label.comp_name')" property="comp_name" width="250" />
          <el-table-column :label="$t('daily.label.comp_abbr')" property="comp_abbr" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import addPage from '@/views/assistance/payeePayer/add'

import { searchCompanies } from '@/api/assistance/payeePayer'
import { fetchCompanyGroups } from '@/api/assistance/payeePayer/payeePayerGroup'

export default {
  name: 'DialogPayee',
  components: {
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
  },
  data() {
    return {
      p_code: 'ac.assistance.payee_payer',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      currentRowItem: null,
      currentRowIndex: -1,
      showDialog: this.dialogVisible,
      dialogSelectAccount: null,
      dialogView: 'list',
      companyGroupList: [],
      companyList: [],
      tableData: [],
      searchFilter: {
        company_group_id: '',
        name: '',
        type: '',
      },
      defaultFilter: {
        company_group_id: '',
        name: '',
        type: '',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp
    }
  },
  computed: {
    ...mapGetters(['language']),
    TQOptions() {
      return [
        {
          label: 'N/A',
          value: '',
        },
        {
          label: 'Quotation',
          value: 'Q',
        },
        {
          label: 'Tender',
          value: 'T',
        },
      ]
    },
    allPayee() {
      return {
        label: this.$t('daily.label.allPayee'),
        value: '',
      }
    },

    typeOptions() {
      return [
        {
          label: this.$t('daily.label.allType'),
          value: '',
        },
        {
          label: 'B',
          value: 'B',
        },
        {
          label: 'I',
          value: 'I',
        },
        {
          label: 'E',
          value: 'E',
        },
      ]
    },
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    onClear(update) {
      this.searchFilter = Object.assign({}, this.defaultFilter)
      if (update) this.fetchData()
    },
    onOpenDialogAccount() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.onClear()
      fetchCompanyGroups({ fy_code: this.fy_code })
        .then(res => {
          this.companyGroupList = res
        })
        .then(this.fetchData)
    },
    fetchData() {
      this.tableLoading = true
      searchCompanies({
        fy_code: this.fy_code,
        parent_company_group_id: this.searchFilter.company_group_id,
        comp_group: this.searchFilter.type,
        name: this.searchFilter.name,
      })
        .then(res => {
          this.tableData = res
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    onAdd() {
      // const fund_id = this.searchFilter.fund_id
      // if (fund_id) {
      //   if (this.searchFilter.fund_id_target) {
      //     this.addParent = { fund_id: this.searchFilter.fund_id_target }
      //   }
      this.dialogView = 'add'
      // }
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$emit('selectRow', currentRow)
      this.showDialog = false
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectPayee {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
</style>
