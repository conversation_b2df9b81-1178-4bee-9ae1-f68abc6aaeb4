<template>
  <div
    :style="{
      width: maxWidth + 'px',
      height: page_height + 'px',
      'font-weight': font_weight,
      'font-size': font_size,
      'background-image': 'url(' + chequeImage + ')',
      'background-repeat': 'no-repeat',
      'background-size': '100%',
    }"
    :class="{
      'cheque-preview': true,
      'cheque-preview--loading': loading,
    }"
    class="cheque-preview"
    style="width: 500px; height: 200px"
  >
    <!--  ac_payee_only  -->
    <img
      v-if="showUnderline"
      ref="acpo"
      :src="ac_payee_only"
      :style="{
        left: ac_payee_only_x,
        top: ac_payee_only_y,
        height: ac_payee_only_height,
        width: ac_payee_only_width,
      }"
      alt=""
    >
    <!--  大寫金額  -->
    <div
      :style="{
        top: text_amount_y,
        left: text_amount_x,
        width: text_width,
        lineHeight: line_height,
        'text-indent': text_indent,
        'font-size': font_size,
      }"
      contenteditable="false"
    >
      <img
        v-if="amountImage"
        :src="amountImage"
        alt=""
        style="position: absolute; top: 0; left: 0; pointer-events: none"
      >
      <!--{{ formLanguage === 'en'-->
      <!--  ? text_amount_en-->
      <!--: (formLanguage === 'cn' ? text_amount_cn : amount) }}-->
      <span
        :style="{
          display: 'block',
          opacity: 0,
          wordBreak: 'break-all',
          'font-size': font_size + '!important',
        }"
      >{{ textAmount2 }}</span>
    </div>
    <!--  日期  -->
    <div
      :style="{
        'font-size': font_size,
        top: date_y,
        left: date_x,
      }"
      contenteditable="false"
    >
      {{ textDate }}
    </div>
    <!--  支票頭-日期  -->
    <div
      :style="{
        'font-size': stub_font_size,
        top: stub_date_y,
        left: stub_date_x,
      }"
      contenteditable="false"
    >
      {{ textDate }}
    </div>
    <!--  收款人  -->
    <div
      :style="{
        'font-size': font_size,
        top: payee_y,
        left: payee_x,
      }"
      contenteditable="false"
    >
      {{ textPayee }}
    </div>
    <!--  支票頭-收款人  -->
    <div
      :style="{
        'font-size': stub_font_size,
        width: stub_payee_width,
        top: stub_payee_y,
        left: stub_payee_x,
      }"
      contenteditable="false"
    >
      {{ textPayee }}
    </div>

    <!-- 金額 -->
    <div
      :style="{
        'font-size': font_size,
        top: amount_y,
        left: amount_x,
        width: 'max-content',
      }"
      contenteditable="false"
    >
      {{ textAmount }}
    </div>
    <!-- 支票頭-金額 -->
    <div
      :style="{
        'font-size': stub_font_size,
        top: stub_amount_y,
        left: stub_amount_x,
        width: 'max-content',
      }"
      contenteditable="false"
    >
      {{ textAmount }}
    </div>
    <!--  刪或持票人  -->
    <div
      v-if="showUnderline"
      :style="{
        'font-size': font_size,
        top: crossed_bearer_y,
        left: crossed_bearer_x,
        width: crossed_width,
        background: '#000',
        height: '1px',
        'min-height': '1px',
      }"
    />
    <div
      v-if="showUnderline"
      :style="{
        'font-size': font_size,
        top: crossed_bearer_y2,
        left: crossed_bearer_x,
        width: crossed_width,
        background: '#000',
        height: '1px',
        'min-height': '1px',
      }"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dateUtil from '@/utils/date'
import { mm2pt } from '@/utils/pdf'
export default {
  name: 'ChequePreview',
  props: {
    formLanguage: {
      type: String,
      default: 'cn',
    },
    chq_template_code: {
      type: String,
      default: '',
    },
    pageWidth: {
      type: Number,
      default: 200,
    },
    pageHeight: {
      type: Number,
      default: 100,
    },
    dateX: {
      type: Number,
      default: 50,
    },
    dateY: {
      type: Number,
      default: 130,
    },
    textAmountX: {
      type: Number,
      default: 20,
    },
    textAmountY: {
      type: Number,
      default: 50,
    },
    textWidth: {
      type: Number,
      default: 50,
    },
    textIndent: {
      type: Number,
      default: 50,
    },
    lineHeight: {
      type: Number,
      default: 50,
    },
    payeeX: {
      type: Number,
      default: 20,
    },
    payeeY: {
      type: Number,
      default: 50,
    },
    amountX: {
      type: Number,
      default: 20,
    },
    amountY: {
      type: Number,
      default: 50,
    },
    fontWeight: {
      type: Number,
      default: 12,
    },
    fontSize: {
      type: Number,
      default: 12,
    },
    AcPayeeOnlyX: {
      type: Number,
      default: 12,
    },
    AcPayeeOnlyY: {
      type: Number,
      default: 12,
    },
    crossedBearerX: {
      type: Number,
      default: 12,
    },
    crossedBearerY: {
      type: Number,
      default: 12,
    },
    crossedWidth: {
      type: Number,
      default: 12,
    },
    crossedSpace: {
      type: Number,
      default: 12,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    textAmount2: {
      type: String,
      default: '',
    },
    textAmount: {
      type: String,
      default: '',
    },
    textDate: {
      type: String,
      default: '',
    },
    textPayee: {
      type: String,
      default: '',
    },
    showChequeStub: {
      type: Boolean,
      default: false,
    },
    stubFontSize: {
      type: Number,
      default: 10,
    },
    stubDateX: {
      type: Number,
      default: 0,
    },
    stubDateY: {
      type: Number,
      default: 0,
    },
    stubPayeeX: {
      type: Number,
      default: 0,
    },
    stubPayeeY: {
      type: Number,
      default: 0,
    },
    stubPayeeWidth: {
      type: Number,
      default: 0,
    },
    stubAmountX: {
      type: Number,
      default: 0,
    },
    stubAmountY: {
      type: Number,
      default: 0,
    },
    stubDescX: {
      type: Number,
      default: 0,
    },
    stubDescY: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      //
      text_amount_en:
        'One Million, Two Hundred and Thirty-Four Thousand, Five Hundred and Sixty-Seven Dollars and Eighty-Nine Cents Only',
      // text_amount_en: 'Nine Hundred And Ninety Nine Million Nine Hundred And Ninety Nine Thousand Nine Hundred And Ninety Nine And Cents Ninety Nine Only',
      text_amount_cn: this.$t('chequePreview.amountCn'),
      // text_amount_cn: '玖億玖仟玖佰玖拾玖萬玖仟玖佰玖拾玖元玖角玖分正',
      amount: '** 1,234,567.89 **',

      amountImage: '',
    }
  },
  computed: {
    ...mapGetters(['school', 'remoteServerInfo']),
    maxWidth() {
      return this.showChequeStub ? 650 : 500
    },
    chequeImage() {
      if (!this.chq_template_code) {
        return ''
      }
      const rsi = this.remoteServerInfo
      return `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/cheque/cheque_${this.chq_template_code}.gif`
    },
    ac_payee_only() {
      return require('@/assets/cheque/ac-payee-only-200-3.png')
    },
    date() {
      return dateUtil.format(new Date(), 'dd/MM/yyyy')
    },
    font_weight() {
      return this.fontWeight ? 'bold' : 100
    },
    font_size() {
      return (this.maxWidth / mm2pt(this.pageWidth)) * this.fontSize + 'px'
    },
    stub_font_size() {
      return (this.maxWidth / mm2pt(this.pageWidth)) * this.stubFontSize + 'px'
    },
    page_px() {
      return this.maxWidth / this.pageWidth
    },
    page_height() {
      return this.page_px * this.pageHeight
    },
    date_x() {
      return this.page_px * this.dateX + 'px'
    },
    date_y() {
      return this.page_px * this.dateY + 'px'
    },
    text_amount_x() {
      return this.page_px * this.textAmountX + 'px'
    },
    text_amount_y() {
      return this.page_px * this.textAmountY + 'px'
    },
    text_width() {
      return this.page_px * this.textWidth + 'px'
    },
    text_indent() {
      return this.textIndent + 'px'
    },
    line_height() {
      return this.lineHeight * 1.5 + 'em'
    },
    payee_x() {
      return this.page_px * this.payeeX + 'px'
    },
    payee_y() {
      console.log('payee_y', this.page_px, this.payeeY + 'px')
      return this.page_px * this.payeeY + 'px'
    },
    amount_x() {
      return this.page_px * this.amountX + 'px'
    },
    amount_y() {
      return this.page_px * this.amountY + 'px'
    },
    ac_payee_only_x() {
      return this.page_px * this.AcPayeeOnlyX + 'px'
    },
    ac_payee_only_y() {
      return this.page_px * this.AcPayeeOnlyY + 'px'
    },
    ac_payee_only_height() {
      return (this.page_px * this.pageHeight) / 3 + 'px'
    },
    ac_payee_only_width() {
      return (this.page_px * this.pageHeight) / 3 + 'px'
    },
    crossed_bearer_x() {
      return this.page_px * this.crossedBearerX + 'px'
    },
    crossed_bearer_y() {
      return parseInt(this.page_px * this.crossedBearerY - 2) + 'px'
    },
    crossed_bearer_y2() {
      return parseInt(this.page_px * (this.crossedBearerY + this.crossedSpace) - 2) + 'px'
    },
    crossed_width() {
      return this.page_px * this.crossedWidth + 'px'
    },
    crossed_space() {
      return this.page_px * this.crossedSpace + 'px'
    },
    stub_date_x() {
      return this.page_px * this.stubDateX + 'px'
    },
    stub_date_y() {
      return this.page_px * this.stubDateY + 'px'
    },
    stub_payee_x() {
      return this.page_px * this.stubPayeeX + 'px'
    },
    stub_payee_y() {
      return this.page_px * this.stubPayeeY + 'px'
    },
    stub_payee_width() {
      return this.page_px * this.stubPayeeWidth + 'px'
    },
    stub_amount_x() {
      return this.page_px * this.stubAmountX + 'px'
    },
    stub_amount_y() {
      return this.page_px * this.stubAmountY + 'px'
    },
    stub_desc_x() {
      return this.page_px * this.stubDescX + 'px'
    },
    stub_desc_y() {
      return this.page_px * this.stubDescY + 'px'
    },
    showAmount() {
      switch (this.formLanguage) {
        case 'en':
          return this.text_amount_en
        case 'cn':
          return this.text_amount_cn
        case 'auto':
          return this.language === 'en' ? this.text_amount_en : this.text_amount_cn
        default:
          return this.amount
      }
    },
    showUnderline() {
      return this.chq_template_code && !this.chq_template_code.includes('_')
    },
  },
  watch: {
    amount() {
      this.generateAmount()
    },
    textAmountY() {
      this.generateAmount()
    },
    textAmountX() {
      this.generateAmount()
    },
    textWidth() {
      this.generateAmount()
    },
    lineHeight() {
      this.generateAmount()
    },
    formLanguage() {
      this.generateAmount()
    },
    language() {
      this.generateAmount()
    },
    textAmount2() {
      this.generateAmount()
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.acpo &&
        (this.$refs.acpo.ondragstart = function(e) {
          e.preventDefault()
        })
    })
    this.generateAmount()
  },
  methods: {
    generateAmount() {
      console.log('generateAmount')
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const width = +(this.page_px * this.textWidth)
      const fontSize = (this.maxWidth / mm2pt(this.pageWidth)) * this.fontSize
      const fontFamily = 'SimSun'
      const fontColour = 'black'

      const lines = this.fragmentText(ctx, this.textAmount2, width, this.textIndent * 1.2)
      const height = lines.length * this.lineHeight * 1.5
      canvas.width = width + 100
      canvas.height = fontSize * height
      console.log('width', width)
      console.log('fontSize', fontSize)
      console.log('lines', lines)

      ctx.save()
      ctx.clearRect(0, 0, width, height)
      ctx.font = 'normal ' + fontSize + 'px ' + fontFamily
      ctx.textAlign = 'left'
      ctx.lineHeight = 1
      ctx.fillStyle = fontColour
      lines.forEach((line, i) => {
        ctx.fillText(
          line,
          i === 0 ? this.textIndent : 0,
          i * this.lineHeight * fontSize * 1.2 + fontSize * 1.2,
        )
      })
      ctx.restore()
      this.amountImage = canvas.toDataURL('image/png')
      canvas.remove()
    },
    fragmentText(ctx, text, maxWidth, textIndent) {
      let currentMaxWidth = maxWidth - textIndent
      console.log({
        text,
        maxWidth,
        textIndent,
        currentMaxWidth,
      })
      let words = text.split('')
      const lines = []
      let line = ''
      if (ctx.measureText(text).width < currentMaxWidth) {
        return [text]
      }
      while (words.length > 0) {
        let count = 1
        if (
          this.formLanguage === 'en' ||
          (this.formLanguage === 'auto' && this.language === 'en')
        ) {
          let lastIndex = words.length
          while (ctx.measureText(words.slice(0, lastIndex).join('')).width > currentMaxWidth) {
            lastIndex = words.lastIndexOf(' ', lastIndex - 1)
          }
          if (lastIndex <= 0) {
            lastIndex = words.indexOf(' ', 1)
            if (lastIndex === -1) {
              lastIndex = words.length
            }
          }
          count = lastIndex
        } else {
          while (
            ctx.measureText(words.slice(0, count).join('')).width < currentMaxWidth &&
            count <= words.length
          ) {
            count++
          }
          if (count > 1 && count <= words.length) {
            count -= 1
          }
        }
        console.log({
          textWidth: ctx.measureText(words.slice(0, count).join('')).width,
        })
        line = words.slice(0, count).join('')
        words = words.slice(count)
        console.log('line', line, 'words', words, 'count', count)
        if (line) {
          lines.push(line)
          currentMaxWidth = maxWidth
        } else if (words.length === 0) {
          lines.push(line)
          currentMaxWidth = maxWidth
          console.log('lines', lines, '換行 重置最大寬度', currentMaxWidth)
        }
      }
      return lines
    },
  },
}
</script>

<style lang="scss" scoped>
.cheque-preview {
  //width: 500px;
  position: relative;
  display: block;
  background: #fff;
  border: 1px solid #a8a8a8;
  user-select: none;
  overflow: hidden;
  transition: all 0.2s ease-out 0s;
  margin-top: 20px;
  box-shadow: 1px 1px 8px 0px #b9b9b9;

  &.cheque-preview--loading {
    > * {
      opacity: 0;
    }
  }
  /*position: absolute;*/
  /*top: 410px;*/
  /*left: 800px;*/
  & > div,
  & > img {
    position: absolute;
    background: #e3e3e373;
    border: 1px solid #b2b2b2;
  }

  /deep/ {
    [contenteditable='true'] * {
      font-size: inherit !important;
    }
  }
}
</style>
