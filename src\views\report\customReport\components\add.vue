<template>
  <div ref="page" v-loading="loading" class="app-container">
    <div class="button-box">
      <el-button size="mini" type="primary" @click="onSave">
        {{ $t('button.save') }}
      </el-button>
      <el-button size="mini" type="primary" @click="onCancel">
        {{ $t('button.abandon') }}
      </el-button>
    </div>
    <div class="title-box">
      <el-form :rules="rules" :model="form" :inline="true" label-width="80px" class="mini-form">
        <el-form-item :label="$t('customReport.reportName')" prop="reportName">
          <el-input v-model="form.reportName" :placeholder="$t('customReport.placeholder.input')" style="width: 300px;" />
        </el-form-item>
      </el-form>
    </div>
    <div class="content-box">
      <div v-for="(item, index) in reportData" :key="index" class="content-item">
        <div class="item-title">
          <span>{{ $t('customReport.sectionTitle', { number: index + 1, title: getTitle(item.section_type) }) }}</span>
          <i class="el-icon-delete action-icon" style="color: #F89696;" @click="onDeleteSection(index)" />
        </div>
        <div v-if="item.section_type !== 'SIGNATURE'" class="item-box">
          <el-form :model="item" :rules="areaRules" :inline="true" label-width="60px" class="mini-form">
            <el-form-item :label="$t('customReport.areaTitle')">
              <el-input v-model="item.section_title" :placeholder="$t('customReport.placeholder.input')" style="width: 300px;" />
            </el-form-item>
            <el-form-item :label="$t('customReport.columnContent')" prop="col">
              <el-checkbox-group v-model="item.col">
                <el-checkbox :label="'TITLE'" disabled>
                  {{ $t('customReport.tableKey.TITLE') }}
                </el-checkbox>
                <el-checkbox :label="'OB'">
                  {{ $t('customReport.tableKey.OB') }}
                </el-checkbox>
                <el-checkbox :label="'BUDGET_IE'">
                  {{ $t('customReport.tableKey.BUDGET_IE') }}
                </el-checkbox>
                <el-checkbox :label="'ACT_IE'">
                  {{ $t('customReport.tableKey.ACT_IE') }}
                </el-checkbox>
                <el-checkbox :label="'BAL'">
                  {{ $t('customReport.tableKey.BAL') }}
                </el-checkbox>
                <el-checkbox :label="'TOTAL_BAL'">
                  {{ $t('customReport.tableKey.TOTAL_BAL') }}
                </el-checkbox>
                <el-checkbox :label="'ACT_IE_PERCENT'">
                  {{ $t('customReport.tableKey.ACT_IE_PERCENT') }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('customReport.rowContent')" prop="row" class="row-form-item">
              <div class="row-box">
                <div class="add-row-box" @click="onAddRow(index)">
                  <i class="edac-icon action-icon edac-icon-add" style="color: #68afff;" />
                  <span>{{ $t('customReport.add1Level') }}</span>
                </div>
                <ETable
                  :data="item.row"
                  :show-actions="false"
                  :row-style="rowStyle"
                  height="auto"
                  border
                  style="width: 100%"
                >
                  <template slot="columns">
                    <el-table-column
                      :label="$t('customReport.title')"
                      prop="report_name"
                      width="350"
                    >
                      <template slot-scope="scope">
                        <div :style="{ marginLeft: `${(scope.row.level - 1) * 10}px`, width: '100%' }">
                          <el-input v-model="scope.row.title" :disabled="scope.row.disabled" :placeholder="$t('customReport.placeholder.input')" style="width: 100%;" />
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('customReport.dataType')"
                      prop="dataType"
                      width="350"
                    >
                      <template slot-scope="scope">
                        <el-radio-group v-model="scope.row.type" :disabled="scope.row.disabled" @change="onChangeType(scope.row.type, index, scope.$index)">
                          <el-radio :label="'TEXT'">
                            {{ $t('customReport.TEXT') }}
                          </el-radio>
                          <el-radio :label="'SUB-TOTAL'">
                            {{ $t('customReport.SUB_TOTAL') }}
                          </el-radio>
                          <el-radio :label="'ACCOUNT-TOTAL'">
                            {{ $t('customReport.ACCOUNT_TOTAL') }}
                          </el-radio>
                          <el-radio :label="'SUB-ACCOUNT'">
                            {{ $t('customReport.SUB_ACCOUNT') }}
                          </el-radio>
                        </el-radio-group>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('customReport.relatedAccountCategory')"
                      prop="relatedAccountCategory"
                      min-width="300px"
                    >
                      <template slot-scope="scope">
                        <div class="related-account-category-box">
                          <template v-if="scope.row.type === 'ACCOUNT-TOTAL'">
                            <div class="related-account-category-list">
                              <div v-for="account in scope.row.accounts" :key="account.account_id" class="related-account-category-item">
                                <div class="related-account-category-item-content">
                                  <span>{{ account.ac_code || account.code }}</span>
                                  <span style="margin: 0 5px;">{{ account[f('ac_name')] || account[f('name')] }}</span>
                                </div>
                                <span v-if="!scope.row.disabled" class="close-box">
                                  <i class="el-icon-close action-icon" @click="onDeleteAccount(index, scope.$index, account.account_id)" />
                                </span>
                              </div>
                            </div>
                          </template>
                          <template v-if="scope.row.type === 'SUB-ACCOUNT'">
                            <div class="related-account-category-list">
                              <div v-for="fund in scope.row.funds" :key="fund.fund_id" class="related-account-category-item">
                                <div class="related-account-category-item-content">
                                  <span style="margin-right: 5px;">{{ fund[f('fund_name')] }}</span>
                                </div>
                                <span v-if="!scope.row.disabled" class="close-box">
                                  <i class="el-icon-close action-icon" @click="onDeleteFund(index, scope.$index, fund)" />
                                </span>
                              </div>
                            </div>
                          </template>
                          <template v-if="['ACCOUNT-TOTAL', 'SUB-ACCOUNT'].includes(scope.row.type) && !scope.row.disabled">
                            <i class="edac-icon action-icon edac-icon-search" style="color: #68afff;" @click="onSearch(index, scope.$index, scope.row.type)" />
                          </template>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('customReport.actions')"
                      prop="actions"
                      width="60"
                    >
                      <template slot-scope="scope">
                        <div v-if="!scope.row.disabled" class="action-box">
                          <i v-if="scope.row.type !== 'SUB-ACCOUNT'" class="edac-icon action-icon edac-icon-add" style="color: #838990;" @click="onAddRowChild(index, scope.$index)" />
                          <i
                            v-if="['ACCOUNT-TOTAL', 'SUB-ACCOUNT'].includes(scope.row.type) && !scope.row.disabled"
                            class="el-icon-close action-icon"
                            :style="{marginLeft: scope.row.type === 'SUB-ACCOUNT' ? '22px' : '10px'}"
                            style="color: #838990;line-height: 23px;"
                            @click="onDeleteRow(index, scope.$index)"
                          />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                </ETable>
                <el-checkbox
                  v-model="item.row_show_total"
                  :label="1"
                  class="show-total-checkbox"
                >
                  {{ $t('customReport.showTotal') }}
                </el-checkbox>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div v-else class="signature-box">
          <ETable
            :data="item.row"
            :show-actions="false"
            :row-style="rowStyle"
            height="auto"
            border
            style="width: 380px"
          >
            <template slot="columns">
              <el-table-column
                prop="report_name"
                width="80"
              >
                <template slot-scope="scope">
                  <div class="signature-title">
                    {{ `${$t('customReport.signature1')} #${scope.$index + 1}` }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('customReport.title')"
                prop="title"
                width="158"
              >
                <template slot-scope="scope">
                  <el-input v-model="scope.row.title" :disabled="scope.row.disabled" :placeholder="$t('customReport.placeholder.input')" style="width: 100%;" />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('customReport.date')"
                prop="date"
                width="80px"
              >
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.show_date" />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('customReport.actions')"
                prop="actions"
                width="60"
              >
                <template slot-scope="scope">
                  <div class="action-box">
                    <i class="el-icon-close action-icon" style="color: #838990;line-height: 23px;" @click="onDeleteSignature(index, scope .$index)" />
                  </div>
                </template>
              </el-table-column>
            </template>
          </ETable>
          <div class="add-signature-box" @click="onAddSignature(index)">
            <i class="edac-icon action-icon edac-icon-add" style="color: #68afff;" />
            <span>{{ $t('customReport.add') }}</span>
          </div>
        </div>
      </div>
      <div class="add-box">
        <el-button size="mini" type="primary" @click="openAddContentArea">
          {{ $t('customReport.addContentArea') }}
        </el-button>
      </div>
    </div>
    <AddAccount :dialog-visible.sync="addAccountData.visible" :selected-data="addAccountData.selectedData" @selectRow="onAddAccount" />
    <AddFund :dialog-visible.sync="addFundData.visible" :selected-data="addFundData.selectedData" @selectRow="onAddFund" />
    <AddContentArea v-if="addContentAreaVisible" :visible.sync="addContentAreaVisible" @addContentArea="onAddContentArea" />
  </div>
</template>

<script>
import AddContentArea from './addContentArea'
import AddAccount from './addAccount'
import AddFund from './addFund'

import ETable from '@/components/ETable'
import { createCustomReport, fetchCustomReportDetail, updateCustomReport } from '../../../../api/report/customReports'
export default {
  name: 'CustomReportAdd',
  components: {
    AddContentArea,
    AddAccount,
    AddFund,
    ETable,
  },
  props: {
    editCustomReportId: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      form: {
        reportName: '',
      },
      loading: false,
      rules: {
        reportName: [
          { required: true, message: ' ', trigger: ['blur', 'change'] },
        ],
      },
      reportData: [],
      areaRules: {
        col: [
          { required: true, message: ' ', trigger: ['blur', 'change'] },
        ],
      },
      addContentAreaVisible: false,
      addAccountData: {
        visible: false,
        selectedData: [],
      },
      addFundData: {
        visible: false,
        selectedData: [],
      },
      // 添加唯一ID计数器
      uniqueIdCounter: 0,
    }
  },
  watch: {
    'form.reportName': {
      handler(newVal) {
        if (newVal) {
          this.$emit('changeRightBox', {
            report_name: newVal,
            section_data: this.makeFinalReportData(),
          })
        }
      },
      deep: true,
    },
    reportData: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.$emit('changeRightBox', {
              report_name: this.form.reportName,
              section_data: this.makeFinalReportData(),
            })
          })
        }
      },
      deep: true,
    },
  },
  created() {
    if (this.editCustomReportId) {
      this.getCustomReportDetail()
    } else {
      this.initializeExistingDataIds()
    }
  },
  methods: {
    makeTableData(data, level = 1, parentId = null, parentType = null) {
      console.log(data, 'data')
      const tableData = []
      data.forEach(item => {
        const id = this.generateUniqueId()
        tableData.push({
          funds: item.funds,
          accounts: item.accounts,
          show_date: item.show_date === 1,
          title: item.title,
          type: item.type,
          fund_ids: item.fund_ids,
          account_ids: item.account_ids,
          level: item.level || level,
          id,
          parentId,
          disabled: parentType === 'SUB-ACCOUNT',
        })
        if (item.sub_row) {
          const parentType = item.type
          tableData.push(...this.makeTableData(item.sub_row, level + 1, id, parentType))
        }
      })
      return tableData
    },
    async getCustomReportDetail() {
      const res = await fetchCustomReportDetail(this.editCustomReportId)
      if (res) {
        this.form.reportName = res.report_name
        res.section_data.forEach(item => {
          item.row = this.makeTableData(item.row)
          item.row_show_total = item.row_show_total === 1
        })
        this.reportData = res.section_data
        console.log(this.reportData, 'this.reportData')
      }
    },
    // 添加生成唯一ID的方法
    generateUniqueId() {
      return ++this.uniqueIdCounter
    },
    // 初始化现有数据的ID
    initializeExistingDataIds() {
      this.reportData.forEach(section => {
        if (section.row) {
          section.row.forEach(row => {
            if (!row.id) {
              row.id = this.generateUniqueId()
            }
          })
        }
      })
    },
    // 添加递归删除子项的辅助方法
    deleteRowAndChildren(rows, targetId) {
      return rows.filter(item => {
        // 如果是目标行本身，删除
        if (item.id === targetId) {
          return false
        }
        // 如果是子行（parentId 指向目标行），删除
        if (item.parentId === targetId) {
          return false
        }
        return true
      })
    },
    // 添加递归构建树形结构的辅助方法
    buildTreeStructure(rows) {
      // 创建一个 id 到项目的映射
      const itemMap = new Map()
      rows.forEach(item => {
        itemMap.set(item.id, { ...item, sub_row: [] })
      })

      // 分离根节点和子节点
      const rootItems = []

      rows.forEach(item => {
        const mappedItem = itemMap.get(item.id)

        if (!item.parentId) {
          // 根节点
          rootItems.push(mappedItem)
        } else {
          // 子节点，添加到父节点的 sub_row 中
          const parent = itemMap.get(item.parentId)
          if (parent) {
            parent.sub_row.push(mappedItem)
          }
        }
      })

      return rootItems
    },
    async onSave() {
      const isOk = this.checkData()
      if (isOk) {
        const saveData = this.makeFinalReportData()
        console.log(saveData, 'saveData')

        saveData.forEach(item => {
          if (item.row && item.row.length > 0) {
            // 使用递归方法构建树形结构
            item.row = this.buildTreeStructure(item.row)
          }
        })

        console.log(saveData, 'processed saveData')
        const api = this.editCustomReportId ? updateCustomReport : createCustomReport
        const apiData = {
          report_name: this.form.reportName,
          section_data: JSON.stringify(saveData),
        }
        if (this.editCustomReportId) {
          apiData.custom_report_id = this.editCustomReportId
        }
        const res = await api(apiData)
        if (res) {
          this.$message.success(this.$t('message.success'))
          this.$emit('refresh')
        }
      }
    },
    /**
     * 檢查並驗證自定義報表數據的完整性和有效性
     * @returns {boolean} 驗證結果，true表示通過，false表示失敗
     */
    checkData() {
      // 第一步：檢查報表標題是否為空
      // 報表標題是必填項，不能為空
      if (!this.form.reportName) {
        this.$message.error(this.$t('customReport.rules.reportName'))
        return false
      }

      // 第二步：檢查是否至少添加了一個內容區域
      // 自定義報表必須包含至少一個內容區域（帳戶狀況、銀行狀況或簽名）
      if (this.reportData.length === 0) {
        this.$message.error(this.$t('customReport.rules.reportData'))
        return false
      }

      // 第三步：詳細驗證每個內容區域的數據完整性
      // 篩選出帳戶狀況(ACCOUNT)和銀行狀況(BANK)類型的區域進行驗證
      const accountOrBankData = this.reportData.filter(item => item.section_type === 'ACCOUNT' || item.section_type === 'BANK')

      for (const item of accountOrBankData) {
        // 3.1 檢查區域標題
        // 每個內容區域必須有標題來標識其用途
        if (!item.section_title) {
          console.log(item, 'item')
          this.$message.error(this.$t('customReport.rules.sectionTitle'))
          return false
        }

        // 3.2 檢查列內容選擇
        // 必須至少選擇一個列來顯示數據（如標題、期初餘額等）
        if (item.col.length <= 1) {
          this.$message.error(this.$t('customReport.rules.col'))
          return false
        }
        // 3.3 檢查行數據
        // 每個區域必須至少有一行數據
        if (item.row.length <= 0) {
          this.$message.error(this.$t('customReport.rules.row'))
          return false
        } else {
          // 3.4 對於帳戶和銀行類型，進行更詳細的行數據驗證
          for (const item1 of item.row) {
            // 3.4.1 檢查行標題
            // 每一行必須有標題來描述該行的內容
            if (!item1.title) {
              this.$message.error(this.$t('customReport.rules.title'))
              return false
            }
            // 3.4.2 檢查數據類型
            // 每一行必須指定數據類型（文字、小計、帳戶總計、子帳戶）
            if (!item1.type) {
              this.$message.error(this.$t('customReport.rules.type'))
              return false
            }

            // 3.4.3 檢查帳戶總計類型的關聯帳戶
            // 如果選擇了帳戶總計，必須關聯至少一個帳戶
            if (item1.type === 'ACCOUNT-TOTAL' && item1.accounts.length <= 0) {
              this.$message.error(this.$t('customReport.rules.accounts'))
              return false
            }

            // 3.4.4 檢查子帳戶類型的關聯基金
            // 如果選擇了子帳戶，必須關聯至少一個基金
            if (item1.type === 'SUB-ACCOUNT' && item1.funds.length <= 0) {
              this.$message.error(this.$t('customReport.rules.funds'))
              return false
            }
          }
        }
      }
      // 第四步：檢查簽名區域
      // 簽名區域必須至少有一個簽名
      const signatureData = this.reportData.filter(item => item.section_type === 'SIGNATURE')
      if (signatureData.length > 0) {
        for (const item of signatureData) {
          // 3.5.1 檢查行標題（簽名行也需要標題）
          for (const item1 of item.row) {
            if (!item1.title) {
              this.$message.error(this.$t('customReport.rules.title'))
              return false
            }
          }
        }
      }

      // 第五步：所有驗證通過，返回true
      return true
    },
    onCancel() {
      this.$emit('cancel')
    },
    openAddContentArea() {
      this.addContentAreaVisible = true
    },
    onAddContentArea(radio) {
      const data = {
        section_type: radio,
        section_title: '',
        row: [],
      }
      if (radio !== 'SIGNATURE') {
        data.col = radio === 'ACCOUNT' ? ['TITLE', 'OB', 'ACT_IE', 'TOTAL_BAL'] : ['TITLE', 'OB', 'TOTAL_BAL']
        data.row_show_total = true
      }
      console.log(data)
      this.reportData.push(data)
    },
    getTitle(sectionType) {
      const data = [
        {
          type: 'ACCOUNT',
          title: this.$t('customReport.accountSituation'),
        },
        {
          type: 'BANK',
          title: this.$t('customReport.bankSituation'),
        },
        {
          type: 'SIGNATURE',
          title: this.$t('customReport.signature'),
        },
      ]
      const result = data.find(item => item.type === sectionType)
      return result.title
    },
    onDeleteSection(index) {
      this.reportData.splice(index, 1)
    },
    onAddRow(index, index1) {
      this.reportData[index].row.push({
        id: this.generateUniqueId(), // 添加唯一ID
        title: '',
        type: '',
        level: 1,
      })
    },
    onAddRowChild(index, parentIndex) {
      console.log(index, parentIndex, 'index, parentIndex')
      if (this.reportData[index].row[parentIndex].level > 4) return
      const parentRow = this.reportData[index].row[parentIndex]
      this.reportData[index].row.splice(parentIndex + 1, 0, {
        id: this.generateUniqueId(), // 添加唯一ID
        title: '',
        type: '',
        level: parentRow.level + 1,
        parentId: parentRow.id, // 使用父項的唯一ID而不是下標
      })
    },
    onDeleteRow(index, targetIndex) {
      const rowToDelete = this.reportData[index].row[targetIndex]
      const filterData = this.deleteRowAndChildren(this.reportData[index].row, rowToDelete.id)
      this.$set(this.reportData[index], 'row', filterData)
      this.$forceUpdate()
    },
    rowStyle({ row, rowIndex }) {
      if (row.level) {
        return {
          backgroundColor: `rgba(62, 151, 221, ${(row.level - 1) * 10}%)`,
        }
      }
      return {}
    },
    onSearch(areaIndex, targetIndex, type) {
      if (type === 'ACCOUNT-TOTAL') {
        this.addAccountData = {
          visible: true,
          selectedData: this.reportData[areaIndex].row[targetIndex].accounts,
          areaIndex,
          targetIndex,
          type,
        }
      } else if (type === 'SUB-ACCOUNT') {
        this.addFundData = {
          visible: true,
          selectedData: this.reportData[areaIndex].row[targetIndex].funds,
          areaIndex,
          targetIndex,
          type,
        }
      }
    },
    onAddAccount(data) {
      this.$set(this.reportData[this.addAccountData.areaIndex].row[this.addAccountData.targetIndex], 'accounts', data)
      this.$forceUpdate()
    },
    onAddFund(data) {
      this.$set(this.reportData[this.addFundData.areaIndex].row[this.addFundData.targetIndex], 'funds', data.selectedList)
      console.log(data.filterAccountData, 'data.filterAccountData')
      data.filterAccountData.map(item => {
        const targetData = this.reportData[this.addFundData.areaIndex].row[this.addFundData.targetIndex]
        this.reportData[this.addFundData.areaIndex].row.splice(this.addFundData.targetIndex + 1, 0, {
          id: this.generateUniqueId(), // 添加唯一ID
          level: targetData.level + 1,
          parentId: targetData.id, // 使用父項的唯一ID而不是下標
          parentFundIds: item.parentFundIds,
          title: item[this.f('name')],
          accounts: [item],
          type: 'ACCOUNT-TOTAL',
          disabled: true,
        })
      })
      this.$forceUpdate()
    },
    onChangeType(type, areaIndex, targetIndex) {
      this.$set(this.reportData[areaIndex].row[targetIndex], 'accounts', [])
      const funds = this.reportData[areaIndex].row[targetIndex].funds
      if (funds && funds.length > 0) {
        funds.forEach(item => {
          this.onDeleteFund(areaIndex, targetIndex, item)
        })
      }
      this.$set(this.reportData[areaIndex].row[targetIndex], 'funds', [])
    },
    onDeleteAccount(areaIndex, targetIndex, accountId) {
      this.reportData[areaIndex].row[targetIndex].accounts = this.reportData[areaIndex].row[targetIndex].accounts.filter(item => item.account_id !== accountId)
      this.$forceUpdate()
    },
    onDeleteFund(areaIndex, targetIndex, fund) {
      // 从当前行的funds数组中删除指定的fund
      this.reportData[areaIndex].row[targetIndex].funds = this.reportData[areaIndex].row[targetIndex].funds.filter(item => item.fund_id !== fund.fund_id)

      // 获取删除后剩余的fund_id列表
      const remainingFundIds = this.reportData[areaIndex].row[targetIndex].funds.map(item => item.fund_id)

      console.log(this.reportData[areaIndex].row, fund, 'this.reportData[areaIndex].row')
      console.log(remainingFundIds, 'remainingFundIds')

      // 过滤行数据：只有当子数据的parentFundIds中没有任何剩余的fund_id时才删除
      const filterData = this.reportData[areaIndex].row.filter(item => {
        // 如果没有parentFundIds，保留
        if (!item.parentFundIds) {
          return true
        }

        // 如果parentFundIds不包含被删除的fund_id，保留
        if (!item.parentFundIds.includes(fund.fund_id)) {
          return true
        }

        // 如果parentFundIds包含被删除的fund_id，检查是否还有其他剩余的parent fund
        const hasRemainingParent = item.parentFundIds.some(parentId => remainingFundIds.includes(parentId))

        // 如果还有其他剩余的parent fund，保留；否则删除
        return hasRemainingParent
      })

      this.$set(this.reportData[areaIndex], 'row', filterData)
      this.$forceUpdate()
    },
    onAddSignature(index) {
      console.log(index, 'index')
      this.reportData[index].row.push({
        id: this.generateUniqueId(), // 添加唯一ID
        title: '',
        show_date: true,
      })
    },
    onDeleteSignature(index, signatureIndex) {
      this.reportData[index].row.splice(signatureIndex, 1)
      this.$forceUpdate()
    },
    makeFinalReportData() {
      // 把checkbox的值转换为1或0
      const data = this.reportData.map(item => {
        return {
          ...item,
          row: item.row.map(item1 => {
            const fundIdData = item1.funds ? item1.funds.map(item2 => item2.fund_id) : []
            const accountIdData = item1.accounts ? item1.accounts.map(item2 => item2.account_id) : []
            const fundIds = fundIdData.join(',')
            const accountIds = accountIdData.join(',')
            const rowItemData = {
              ...item1,
              fund_ids: fundIds,
              account_ids: accountIds,
              show_date: item1.show_date ? 1 : 0,
            }
            return rowItemData
          }),
          row_show_total: item.row_show_total ? 1 : 0,
        }
      })
      console.log(JSON.parse(JSON.stringify(data)), 'data')
      return data
    },
  },
}
</script>

<style scoped lang="scss">
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.el-icon-date {
  cursor: pointer;
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  padding: 0;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 0 0 8px;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    border-collapse: collapse;
    tbody {
      tr.vxe-body--row {
        td.vxe-body--column {
          border: none;
          background: none;
        }
      }
      .sum-row {
        border-bottom: 1px solid #e6e6e6;
      }
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
    .selected {
      background: #E8F5FE;
    }
  }
  .cash-book-table {
    height: calc(100vh - 200px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
.container {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 10px);
  .left-box {
    width: 60%;
    padding: 14px 20px 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      .el-table {
        height: 100%;
      }
    }
  }
  .right-box {
    width: 40%;
    padding: 14px 20px 20px 20px;
    border-left: 1px solid #e6e6e6;
    height: 100%;
    overflow-y: auto;
  }
}
.title-box {
  display: flex;
  align-items: center;
  height: 42px;
  background: #F4F4F4;
  padding-left: 30px;
  margin-top: 12px;
}
.add-box {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #E8EAEC;
  margin-top: 5px;
  height: 48px;
}
.content-box {
  padding-top: 10px;
  .content-item {
    margin-bottom: 5px;
    border: 1px solid #E8EAEC;
    // height: 200px;
    .item-title {
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 33px;
      background: #F5F7FA;
      border-bottom: 1px solid #E8EAEC;
      color: #606266;
      .el-icon-delete {
        font-size: 16px;
      }
    }
    .item-box {
      padding: 15px 20px 15px 32px;
    }
  }
  .row-form-item {
    width: 100%;
    /deep/ {
      .el-form-item__content {
        width: calc(100% - 60px) !important;
      }
    }
    .add-row-box {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #3E97DD;
      cursor: pointer;
    }
  }
  .action-box {
    display: flex;
    align-items: center;
  }
  /deep/ {
    .show-total-checkbox {
      display: flex;
      align-items: center;
      margin-top: 5px;
    }
    .el-table__body-wrapper {
      height: auto !important;
    }
    .el-radio-group {
      .el-radio__original {
        height: auto !important;
        line-height: auto !important;
      }
    }
    .el-radio {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
      .el-radio__label {
        padding-left: 2px;
      }
    }
    .el-form-item {
      margin-bottom: 15px !important;
    }
    .el-checkbox {
      margin-right: 24px !important;
    }
    .el-form-item__content {
      height: auto !important;
      line-height: auto !important;
    }
    .el-table__row {
      .cell {
        min-height: 36px;
        display: flex;
        align-items: center;
      }
      .el-input {
        display: flex;
        align-items: center;
      }
    }
  }
  .related-account-category-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .edac-icon-search {
      margin-left: auto;
      font-size: 20px !important;
    }
  }
  .related-account-category-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .related-account-category-item {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 4px;
    background: #E4E7ED;
    margin-left: 4px;
    font-size: 12px;
    &:first-child {
      margin-top: 5px;
    }
    .related-account-category-item-content {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon-close {
      font-size: 10px !important;
    }
    .close-box {
      width: 12px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #94979E;
      color: #fff;
      border-radius: 50%;
      cursor: pointer;
    }
  }
  .signature-box {
    padding: 12px 80px 15px 80px;
  }
  .add-signature-box {
    display: flex;
    align-items: center;
    margin-top: 5px;
    color: #3E97DD;
    cursor: pointer;
  }
  .signature-title {
    font-size: 12px;
    color: #606266;
  }
}
</style>
