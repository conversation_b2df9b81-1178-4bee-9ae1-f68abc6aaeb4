<template>
  <div ref="page" v-loading="loading" class="app-container">
    <div class="button-box">
      <el-button size="mini" type="primary" @click="onSave">
        {{ $t('button.save') }}
      </el-button>
      <el-button size="mini" type="primary" @click="onCancel">
        {{ $t('button.abandon') }}
      </el-button>
    </div>
    <div class="title-box">
      <el-form :rules="rules" :model="form" :inline="true" label-width="80px" class="mini-form">
        <el-form-item :label="$t('customReport.reportName')" prop="reportName">
          <el-input v-model="form.reportName" :placeholder="$t('customReport.placeholder.input')" style="width: 300px;" @blur="onBlurReportName" />
        </el-form-item>
      </el-form>
    </div>
    <div class="content-box">
      {{ reportData }}
      <div v-for="(item, index) in reportData" :key="index" class="content-item">
        <div class="item-title">
          <span>{{ $t('customReport.sectionTitle', { number: index + 1, title: getTitle(item.section_type) }) }}</span>
          <i class="el-icon-delete action-icon" style="color: #F89696;" @click="onDeleteSection(index)" />
        </div>
        <div class="item-box">
          <el-form :model="item" :rules="areaRules" :inline="true" label-width="60px" class="mini-form">
            <el-form-item :label="$t('customReport.areaTitle')">
              <el-input v-model="item.section_title" :placeholder="$t('customReport.placeholder.input')" style="width: 300px;" />
            </el-form-item>
            <el-form-item :label="$t('customReport.columnContent')" prop="col">
              {{ item.col }}
              <el-checkbox-group v-model="item.col">
                <el-checkbox :label="'TITLE'" disabled>
                  {{ $t('customReport.tableKey.TITLE') }}
                </el-checkbox>
                <el-checkbox :label="'OB'">
                  {{ $t('customReport.tableKey.OB') }}
                </el-checkbox>
                <el-checkbox :label="'BUDGET_IE'">
                  {{ $t('customReport.tableKey.BUDGET_IE') }}
                </el-checkbox>
                <el-checkbox :label="'ACT_IE'">
                  {{ $t('customReport.tableKey.ACT_IE') }}
                </el-checkbox>
                <el-checkbox :label="'BAL'">
                  {{ $t('customReport.tableKey.BAL') }}
                </el-checkbox>
                <el-checkbox :label="'TOTAL_BAL'">
                  {{ $t('customReport.tableKey.TOTAL_BAL') }}
                </el-checkbox>
                <el-checkbox :label="'ACT_IE_PERCENT'">
                  {{ $t('customReport.tableKey.ACT_IE_PERCENT') }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="$t('customReport.rowContent')" prop="row" class="row-form-item">
              <div class="row-box">
                <div class="add-row-box" @click="onAddRow(index)">
                  <i class="edac-icon action-icon edac-icon-add" style="color: #68afff;" />
                  <span>{{ $t('customReport.add1Level') }}</span>
                </div>
                <ETable
                  :data="item.row"
                  :show-actions="false"
                  :row-style="rowStyle"
                  height="auto"
                  border
                  style="width: 100%"
                >
                  <template slot="columns">
                    <el-table-column
                      :label="$t('customReport.title')"
                      prop="report_name"
                      width="350"
                    >
                      <template slot-scope="scope">
                        <div :style="{ marginLeft: `${(scope.row.level - 1) * 10}px`, width: '100%' }">
                          <el-input v-model="scope.row.title" :disabled="scope.row.disabled" :placeholder="$t('customReport.placeholder.input')" style="width: 100%;" />
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('customReport.dataType')"
                      prop="dataType"
                      width="350"
                    >
                      <template slot-scope="scope">
                        <el-radio-group v-model="scope.row.type" :disabled="scope.row.disabled" @change="onChangeType(scope.row.type, index, scope.row.index)">
                          <el-radio :label="'TEXT'">
                            {{ $t('customReport.TEXT') }}
                          </el-radio>
                          <el-radio :label="'SUB-TOTAL'">
                            {{ $t('customReport.SUB_TOTAL') }}
                          </el-radio>
                          <el-radio :label="'ACCOUNT-TOTAL'">
                            {{ $t('customReport.ACCOUNT_TOTAL') }}
                          </el-radio>
                          <el-radio :label="'SUB-ACCOUNT'">
                            {{ $t('customReport.SUB_ACCOUNT') }}
                          </el-radio>
                        </el-radio-group>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('customReport.relatedAccountCategory')"
                      prop="relatedAccountCategory"
                      min-width="300px"
                    >
                      <template slot-scope="scope">
                        <div class="related-account-category-box">
                          <template v-if="scope.row.type === 'ACCOUNT-TOTAL'">
                            <div class="related-account-category-list">
                              <div v-for="account in scope.row.accounts" :key="account.account_id" class="related-account-category-item">
                                <div class="related-account-category-item-content">
                                  <span>{{ account.ac_code }}</span>
                                  <span style="margin: 0 5px;">{{ account[f('ac_name')] }}</span>
                                </div>
                                <span class="close-box">
                                  <i class="el-icon-close action-icon" @click="onDeleteAccount(index, scope.row.index, account.account_id)" />
                                </span>
                              </div>
                            </div>
                          </template>
                          <template v-if="scope.row.type === 'SUB-ACCOUNT'">
                            <div class="related-account-category-list">
                              <div v-for="fund in scope.row.funds" :key="fund.fund_id" class="related-account-category-item">
                                <div class="related-account-category-item-content">
                                  <span style="margin-right: 5px;">{{ fund[f('fund_name')] }}</span>
                                </div>
                                <span v-if="!scope.row.disabled" class="close-box">
                                  <i class="el-icon-close action-icon" @click="onDeleteFund(index, scope.row.index, fund)" />
                                </span>
                              </div>
                            </div>
                          </template>
                          <template v-if="['ACCOUNT-TOTAL', 'SUB-ACCOUNT'].includes(scope.row.type) && !scope.row.disabled">
                            <i class="edac-icon action-icon edac-icon-search" style="color: #68afff;" @click="onSearch(index, scope.row.index, scope.row.type)" />
                          </template>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('customReport.actions')"
                      prop="actions"
                      width="60"
                    >
                      <template slot-scope="scope">
                        <div v-if="!scope.row.disabled" class="action-box">
                          <i class="edac-icon action-icon edac-icon-add" style="color: #838990;" @click="onAddRowChild(index, scope.row.index)" />
                          <i v-if="['ACCOUNT-TOTAL', 'SUB-ACCOUNT'].includes(scope.row.type) && !scope.row.disabled" class="el-icon-close action-icon" style="color: #838990;line-height: 23px;" @click="onDeleteRow(index, scope.row.id)" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                </ETable>
                <el-checkbox
                  v-model="item.row_show_total"
                  :label="1"
                  class="show-total-checkbox"
                >
                  {{ $t('customReport.showTotal') }}
                </el-checkbox>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="add-box">
        <el-button size="mini" type="primary" @click="openAddContentArea">
          {{ $t('customReport.addContentArea') }}
        </el-button>
      </div>
    </div>
    <AddAccount :dialog-visible.sync="addAccountData.visible" :selected-data="addAccountData.selectedData" @selectRow="onAddAccount" />
    <AddFund :dialog-visible.sync="addFundData.visible" :selected-data="addFundData.selectedData" @selectRow="onAddFund" />
    <AddContentArea v-if="addContentAreaVisible" :visible.sync="addContentAreaVisible" @addContentArea="onAddContentArea" />
  </div>
</template>

<script>
import AddContentArea from './addContentArea'
import AddAccount from './addAccount'
import AddFund from './addFund'

import ETable from '@/components/etable'
export default {
  name: 'CustomReportAdd',
  components: {
    AddContentArea,
    AddAccount,
    AddFund,
    ETable,
  },
  data() {
    return {
      form: {
        reportName: '',
      },
      loading: false,
      rules: {
        reportName: [
          { required: true, message: this.$t('customReport.rules.reportName'), trigger: ['blur', 'change'] },
        ],
      },
      reportData: [],
      areaRules: {
        col: [
          { required: true, message: this.$t('customReport.rules.col'), trigger: ['blur', 'change'] },
        ],
      },
      addContentAreaVisible: false,
      addAccountData: {
        visible: false,
        selectedData: [],
      },
      addFundData: {
        visible: false,
        selectedData: [],
      },
    }
  },
  created() {
  },
  methods: {
    onSave() {
      console.log('onSave')
    },
    onCancel() {
      this.$emit('cancel')
    },
    onBlurReportName() {
      this.$emit('changeRightBox', {
        title: this.form.reportName,
      })
    },
    openAddContentArea() {
      this.addContentAreaVisible = true
    },
    onAddContentArea(radio) {
      const data = {
        section_type: radio,
        section_title: '',
        row: [],
      }
      if (radio !== 'SIGNATURE') {
        data.col = ['TITLE', 'OB', 'ACT_IE', 'TOTAL_BAL']
        data.row_show_total = true
      }
      console.log(data)
      this.reportData.push(data)
    },
    getTitle(sectionType) {
      const data = [
        {
          type: 'ACCOUNT',
          title: this.$t('customReport.accountSituation'),
        },
        {
          type: 'BANK',
          title: this.$t('customReport.bankSituation'),
        },
        {
          type: 'SIGNATURE',
          title: this.$t('customReport.signature'),
        },
      ]
      const result = data.find(item => item.type === sectionType)
      return result.title
    },
    onDeleteSection(index) {
      this.reportData.splice(index, 1)
    },
    onAddRow(index, index1) {
      this.reportData[index].row.push({
        title: '',
        type: '',
        level: 1,
        index: this.reportData[index].row.length,
      })
    },
    onAddRowChild(index, parentIndex) {
      console.log(index, parentIndex, 'index, parentIndex')
      if (this.reportData[index].row[parentIndex].level > 4) return
      this.reportData[index].row.splice(parentIndex + 1, 0, {
        title: '',
        type: '',
        level: this.reportData[index].row[parentIndex].level + 1,
        parentId: parentIndex,
        index: parentIndex + 1,
      })
    },
    rowStyle({ row, rowIndex }) {
      if (row.level) {
        return {
          backgroundColor: `rgba(62, 151, 221, ${(row.level - 1) * 10}%)`,
        }
      }
      return {}
    },
    onSearch(areaIndex, targetIndex, type) {
      if (type === 'ACCOUNT-TOTAL') {
        this.addAccountData = {
          visible: true,
          selectedData: this.reportData[areaIndex].row[targetIndex].accounts,
          areaIndex,
          targetIndex,
          type,
        }
      } else if (type === 'SUB-ACCOUNT') {
        this.addFundData = {
          visible: true,
          selectedData: this.reportData[areaIndex].row[targetIndex].funds,
          areaIndex,
          targetIndex,
          type,
        }
      }
    },
    onAddAccount(data) {
      this.$set(this.reportData[this.addAccountData.areaIndex].row[this.addAccountData.targetIndex], 'accounts', data)
      this.$forceUpdate()
    },
    onAddFund(data) {
      this.$set(this.reportData[this.addFundData.areaIndex].row[this.addFundData.targetIndex], 'funds', data.selectedList)
      console.log(data.filterAccountData, 'data.filterAccountData')
      data.filterAccountData.map(item => {
        const targetData = this.reportData[this.addFundData.areaIndex].row[this.addFundData.targetIndex]
        this.reportData[this.addFundData.areaIndex].row.splice(this.addFundData.targetIndex + 1, 0, {
          level: targetData.level + 1,
          parentId: this.addFundData.targetIndex,
          parentFundId: item.parent_fund_id,
          index: this.addFundData.targetIndex + 1,
          title: item[this.f('name')],
          account: [item],
          type: 'ACCOUNT-TOTAL',
          disabled: true,
        })
      })
      this.$forceUpdate()
    },
    onChangeType(type, areaIndex, targetIndex) {
      this.$set(this.reportData[areaIndex].row[targetIndex], 'accounts', [])
      this.$set(this.reportData[areaIndex].row[targetIndex], 'funds', [])
    },
    onDeleteAccount(areaIndex, targetIndex, accountId) {
      this.reportData[areaIndex].row[targetIndex].accounts = this.reportData[areaIndex].row[targetIndex].accounts.filter(item => item.account_id !== accountId)
      this.$forceUpdate()
    },
    onDeleteFund(areaIndex, targetIndex, fund) {
      this.reportData[areaIndex].row[targetIndex].funds = this.reportData[areaIndex].row[targetIndex].funds.filter(item => item.fund_id !== fund.fund_id)
      console.log(this.reportData[areaIndex].row, fund, 'this.reportData[areaIndex].row')
      // const filterData = this.reportData[areaIndex].row.filter(item => item.parentFundId !== fundId)
      // this.$set(this.reportData[areaIndex], 'row', filterData)
      this.$forceUpdate()
    },
  },
}
</script>

<style scoped lang="scss">
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.el-icon-date {
  cursor: pointer;
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  padding: 0;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 0 0 8px;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    border-collapse: collapse;
    tbody {
      tr.vxe-body--row {
        td.vxe-body--column {
          border: none;
          background: none;
        }
      }
      .sum-row {
        border-bottom: 1px solid #e6e6e6;
      }
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
    .selected {
      background: #E8F5FE;
    }
  }
  .cash-book-table {
    height: calc(100vh - 200px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
.container {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 10px);
  .left-box {
    width: 60%;
    padding: 14px 20px 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      .el-table {
        height: 100%;
      }
    }
  }
  .right-box {
    width: 40%;
    padding: 14px 20px 20px 20px;
    border-left: 1px solid #e6e6e6;
    height: 100%;
    overflow-y: auto;
  }
}
.title-box {
  display: flex;
  align-items: center;
  height: 42px;
  background: #F4F4F4;
  padding-left: 30px;
  margin-top: 12px;
}
.add-box {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #E8EAEC;
  margin-top: 5px;
  height: 48px;
}
.content-box {
  .content-item {
    margin-bottom: 5px;
    border: 1px solid #E8EAEC;
    // height: 200px;
    .item-title {
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 33px;
      background: #F5F7FA;
      border-bottom: 1px solid #E8EAEC;
      color: #606266;
      .el-icon-delete {
        font-size: 16px;
      }
    }
    .item-box {
      padding: 15px 20px 15px 32px;
    }
  }
  .row-form-item {
    width: 100%;
    /deep/ {
      .el-form-item__content {
        width: calc(100% - 60px) !important;
      }
    }
    .add-row-box {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #3E97DD;
      cursor: pointer;
    }
  }
  .action-box {
    display: flex;
    align-items: center;
    gap: 0 10px;
  }
  /deep/ {
    .show-total-checkbox {
      display: flex;
      align-items: center;
      margin-top: 5px;
    }
    .el-table__body-wrapper {
      height: auto !important;
    }
    .el-radio-group {
      .el-radio__original {
        height: auto !important;
        line-height: auto !important;
      }
    }
    .el-radio {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
      .el-radio__label {
        padding-left: 2px;
      }
    }
    .el-form-item {
      margin-bottom: 15px !important;
    }
    .el-checkbox {
      margin-right: 24px !important;
    }
    .el-form-item__content {
      height: auto !important;
      line-height: auto !important;
    }
    .el-table__row {
      .cell {
        min-height: 36px;
        display: flex;
        align-items: center;
      }
      .el-input {
        display: flex;
        align-items: center;
      }
    }
  }
  .related-account-category-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .edac-icon-search {
      margin-left: auto;
      font-size: 20px !important;
    }
  }
  .related-account-category-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .related-account-category-item {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 4px;
    background: #E4E7ED;
    margin-left: 4px;
    font-size: 12px;
    &:first-child {
      margin-top: 5px;
    }
    .related-account-category-item-content {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon-close {
      font-size: 10px !important;
    }
    .close-box {
      width: 12px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #94979E;
      color: #fff;
      border-radius: 50%;
      cursor: pointer;
    }
  }
}
</style>
