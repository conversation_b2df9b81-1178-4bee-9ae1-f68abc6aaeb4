<template>
  <div class="quotes-input-step">
    <el-form ref="form" :model="formData">
      <el-table
        :data="form"
        :expand-row-keys="expandRows"
        :cell-class-name="handleCellClassName"
        row-key="pro_apply_supplier_id"
        border
      >
        <el-table-column type="index" />
        <el-table-column type="expand">
          <template v-if="props && props.row" slot-scope="props">
            <el-table
              v-if="props.row.supplier_status === 'Q'"
              :data="props.row.quotes"
              border
              style="padding-left: 120px"
              size="mini"
              class="expand-table"
            >
              <el-table-column type="index" />
              <el-table-column :label="t('itemName')" prop="item_name" width="300" />
              <el-table-column :label="t('qty')" prop="item_qty" width="80" />
              <el-table-column :label="t('price')" prop="price" align="right" width="180">
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span v-if="isReadonly">{{ props.row.quotes[scope.$index].price }}</span>
                  <el-input-number
                    v-else
                    v-model="props.row.quotes[scope.$index].price"
                    :disabled="isReadonly"
                    :controls="false"
                    :precision="2"
                    size="mini"
                    style="width: 100%"
                    class="input input-text align-right"
                    @change="onChangePrice(props.row.quotes[scope.$index])"
                  />
                </template>
              </el-table-column>
              <el-table-column :label="t('amount')" align="right" width="180">
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span>{{ props.row.quotes[scope.$index].amount | amount }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column :label="t('supplierName')" prop="supplier_name" />
        <el-table-column :label="t('supplierTel')" prop="supplier_tel" />
        <el-table-column :label="t('supplierFax')" prop="supplier_fax" />
        <el-table-column :label="t('supplierContact')" prop="supplier_contact" />
        <el-table-column :label="t('status')" prop="supplier_status">
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="isReadonly">{{ getStatus(scope.row.supplier_status) }}</span>
            <el-select
              v-else
              v-model="scope.row.supplier_status"
              :disabled="isReadonly"
              class="input input-select"
              @change="onChangeStatus(scope.row)"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :label="t('total')" prop="supplier_status">
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="scope.row.supplier_status === 'Q'">{{
              form[scope.$index].quotes.map(item => Number(item.amount)).reduce((a, b) => a + b, 0)
                | amount
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('uploadQuotes')" align="center" width="100">
          <template v-if="scope && scope.row" slot-scope="scope">
            <i class="el-icon-upload action-icon" @click="onUpload(scope)" />
          </template>
        </el-table-column>
        <!--        <div slot="append" class="not-enough-suppliers-reason">-->
        <!--          <el-form-item-->
        <!--            v-if="showReason"-->
        <!--            :label="t('notEnoughSuppliersReason')">-->
        <!--            <el-input-->
        <!--              :value="not_enough_suppliers_reason"-->
        <!--              :disabled="true"-->
        <!--              type="textarea"-->
        <!--              autosize-->
        <!--              style="width: 500px"-->
        <!--              resize="none"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </div>-->
      </el-table>
      <el-form-item class="actions">
        <el-button size="mini" type="info" @click="onBack">
          {{ $t('button.back') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onPrev">
          {{ $t('button.prev') }}
        </el-button>
        <el-button
          v-if="!isReadonly"
          :disabled="!(form.length && form.length > 0)"
          size="mini"
          type="success"
          @click="onSave"
        >
          {{ $t('button.saveFile') }}
        </el-button>
        <el-button :disabled="!canNext" size="mini" type="primary" @click="onNext">
          {{ $t('button.next') }}
        </el-button>
      </el-form-item>
    </el-form>
    <!--    <svg-icon v-if="!isReadonly" icon-class="add" class-name="add-icon add-row" @click="onAddRow"/>-->
    <!--    <el-dialog-->
    <!--      :visible.sync="showDialog"-->
    <!--      title="上傳報價單"-->
    <!--      width="600px"-->
    <!--    >-->
    <!--      <div class="upload-area">-->
    <!--&lt;!&ndash;        <el-upload&ndash;&gt;-->
    <!--&lt;!&ndash;          ref="upload"&ndash;&gt;-->
    <!--&lt;!&ndash;          :on-preview="handlePreview"&ndash;&gt;-->
    <!--&lt;!&ndash;          :on-remove="handleRemove"&ndash;&gt;-->
    <!--&lt;!&ndash;          :file-list="fileList"&ndash;&gt;-->
    <!--&lt;!&ndash;          :auto-upload="false"&ndash;&gt;-->
    <!--&lt;!&ndash;          class="upload-demo"&ndash;&gt;-->
    <!--&lt;!&ndash;          action="https://jsonplaceholder.typicode.com/posts/">&ndash;&gt;-->
    <!--&lt;!&ndash;          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>&ndash;&gt;-->
    <!--&lt;!&ndash;          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>&ndash;&gt;-->
    <!--&lt;!&ndash;          <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>&ndash;&gt;-->
    <!--&lt;!&ndash;        </el-upload>&ndash;&gt;-->
    <!--        <upload-p-d-f/>-->
    <!--      </div>-->
    <!--    </el-dialog>-->
    <upload-p-d-f
      :id="uploadId"
      :visible.sync="showDialog"
      :quotations.sync="currentQuotations"
      :is-readonly="isReadonly"
      @reshow="onReshow"
    />
  </div>
</template>
<script>
import { editProcurementApplicationsQuotes } from '@/api/purchase/procurementApplications'
import { uniqueArr, toDecimal } from '@/utils'
import UploadPDF from '@/views/purchase/procurementApplications/steps/uploadPDF'

export default {
  name: 'QuotesInputStep',
  components: { UploadPDF },
  props: {
    id: {
      type: [String, Number],
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    suppliers_at_least: {
      type: Number,
      required: true,
    },
    not_enough_suppliers_reason: {
      type: String,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      langKey: 'purchase.daily.procurementApplications.',
      expandRows: [],
      showDialog: false,
      uploadId: '',
      currentQuotations: [],
      // form: {
      //
      // }
    }
  },
  computed: {
    form: {
      get() {
        return this.data.filter(i => i.supplier_name.length > 0)
      },
      set(val) {
        this.$emit('update:data', val)
      },
    },
    formData() {
      return { form: this.form }
    },
    showReason() {
      return this.form.filter(i => i.supplier_name.length > 0).length < this.suppliers_at_least
    },
    statusList() {
      return [
        {
          value: 'W',
          label: this.$t('purchase.supplierStatus.W'),
        },
        {
          value: 'Q',
          label: this.$t('purchase.supplierStatus.Q'),
        },
        {
          value: 'R',
          label: this.$t('purchase.supplierStatus.R'),
        },
        {
          value: 'N',
          label: this.$t('purchase.supplierStatus.N'),
        },
      ]
    },
    typeList() {
      return [
        {
          value: 'COM',
          label: this.t('com'),
        },
        {
          value: 'ORG',
          label: this.t('org'),
        },
      ]
    },
    canNext() {
      if (this.isReadonly) return true
      const list = this.form.filter(item => item.supplier_status === 'Q')
      return list.length > 0
    },
    totals() {
      if (this.form) {
        return this.form.map(row => {
          return row.quotes.map(item => Number(item.amount)).reduce((a, b) => toDecimal(a + b), 0)
        })
      } else {
        return []
      }
    },
  },
  created() {
    if (!this.isReadonly) {
      this.form = this.form.map(item => {
        if (item.quotes.length === 0) {
          item.quotes = this.items.map(i => {
            return {
              pro_apply_item_id: i.pro_apply_item_id,
              pro_application_id: i.pro_application_id,
              item_name: i.item_name,
              item_index: i.item_index,
              item_remark: i.item_remark,
              item_qty: i.item_qty,
              price: '',
              amount: '',
              pro_apply_supplier_id: item.pro_apply_supplier_id,
            }
          })
        }
        return item
      })
    }
    this.initExpandRows()
  },
  methods: {
    initExpandRows() {
      const arr = [...this.expandRows]
      arr.push(
        ...this.data
          .filter(item => item.supplier_status === 'Q')
          .map(item => item.pro_apply_supplier_id),
      )
      this.expandRows = uniqueArr(arr)
    },
    onAddRow() {
      this.form.push({
        supplier_name: '',
        supplier_index: this.form.length,
        supplier_type: 'COM',
        supplier_tel: '',
        supplier_fax: '',
        supplier_contact: '',
      })
    },
    onDelete(scope) {
      this.form.splice(scope.$index, 1)
    },
    onSave(showTips = true) {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(async(valid, a) => {
          if (!valid) {
            reject()
            return false
          }
          const pro_application_id = this.id
          const quotes = this.form.map((item, index) => {
            return {
              pro_apply_supplier_id: item.pro_apply_supplier_id,
              supplier_status: item.supplier_status,
              items: item.quotes.map(quote => {
                return {
                  pro_apply_item_id: quote.pro_apply_item_id,
                  price: quote.price,
                  amount: toDecimal(quote.price * quote.item_qty),
                }
              }),
            }
          })

          try {
            await editProcurementApplicationsQuotes({
              pro_application_id,
              quotes,
            })
            if (showTips) {
              this.$message.success(this.t('saveSuccess'))
              this.onBack()
            }
            this.$emit('reshow')
            resolve()
          } catch (e) {
            console.log(e)
            reject(e)
          }
        })
      })
    },
    onNext() {
      const hasQuotes = this.form.some(item => {
        return item.supplier_status === 'Q'
      })
      if (!hasQuotes) {
        this.$message.error(this.t('quotesEmpty'))
        return
      }
      this.$emit('next')
    },
    onPrev() {
      this.$emit('prev')
    },
    onBack() {
      this.$emit('back')
    },
    onReshow() {
      this.$emit('reshow')
    },
    onChangeStatus(item) {
      if (item.supplier_status === 'Q') {
        this.expandRows.push(item.pro_apply_supplier_id)
        const l = this.expandRows.length
        const na = uniqueArr(this.expandRows).length
        const nl = na.length
        if (nl < l) {
          this.expandRows = na
        }
      } else {
        const i = this.expandRows.findIndex(i => i === item.pro_apply_supplier_id)
        if (i !== -1) {
          this.expandRows.splice(i, 1)
        }
      }
    },
    handleCellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.type === 'expand' && row.supplier_status === 'Q') {
        return 'has-quotes'
      }
      return ''
    },
    getStatus(status) {
      const item = this.statusList.find(i => i.value === status)
      if (item) {
        return item.label
      } else {
        return status
      }
    },
    onUpload(scope) {
      this.uploadId = scope.row.pro_apply_supplier_id
      this.currentQuotations = scope.row.quotations
      this.showDialog = true
    },
    getTotal(list) {
      return list.reduce((total, item) => {
        const num = Number(item.amount)
        return toDecimal(total + (isNaN(num) ? 0 : num))
      }, 0)
    },
    onChangePrice(q) {
      this.$set(q, 'amount', toDecimal(q.price * q.item_qty))
    },
  },
}
</script>

<style lang="scss" scoped>
.add-icon {
  cursor: pointer;
  &.add-row {
    position: absolute;
    right: -30px;
    top: 3px;
  }
}
.quotes-input-step {
  /deep/ {
    .el-input-number .el-input-number__increase,
    .el-input-number .el-input-number__decrease {
      top: 4px;
    }
    .el-form-item__error {
      display: none;
    }
    .el-table__expanded-cell {
      background-color: #3e97dd1f;
      &:hover {
        background-color: #3e97dd1f !important;
      }
    }

    .el-table__expand-column {
      // 禁止手動展開
      pointer-events: none;
      cursor: not-allowed;
      vertical-align: middle !important;
      &.has-quotes {
        cursor: auto;
        pointer-events: auto;
        i {
          cursor: pointer;
        }
      }
    }
    .expand-table {
      background-color: transparent;
      table thead tr th,
      .el-transfer-panel .el-transfer-panel__header {
        /*color: #ffffff;*/
        color: #909399;
        background-color: transparent !important;
      }
      th,
      tr {
        background-color: transparent !important;
      }
      div.el-table__body-wrapper table > tbody > tr > td .cell {
        line-height: 28px;
      }
      .el-table__body tr:hover > td {
        background-color: transparent !important;
      }
    }
  }
  .actions {
    text-align: right;
    width: 1000px;
    padding-top: 10px;
  }
  .not-enough-suppliers-reason {
    padding: 10px 0;
  }
}
</style>
