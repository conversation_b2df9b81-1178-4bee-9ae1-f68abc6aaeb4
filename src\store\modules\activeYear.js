// const activeYear = {
//   state: {
//     years: ''
//   },
//   mutations: {
//     GET_ACTIVEYEAR: (state, active_year) => {
//       state.years = active_year
//     }
//   },
//   actions: {
//     // 获取用户信息
//     GetUserInfo({ commit, state }) {
//       return new Promise((resolve, reject) => {
//         getUserInfo(state.token).then(response => {
//           commit('GET_ACTIVEYEAR', response.data)
//           resolve(response)
//         }).catch(error => {
//           reject(error)
//         })
//       })
//     }
//   }
// }
