import request from '@/utils/request'

/**
 * 返回相關類型查詢結果
 * @param {string} type 日期:A=所有,Y=全年,T=年度截至,M=當月,F=自由輸入,D=日期
 * @param {string} fy_code 日期:週期編號
 * @param {string} pd_code 日期:週期月份編號
 * @param {string} date_from 日期:日期範圍from
 * @param {string} date_to 日期:日期範圍to
 * @param {string} date 日期:日期
 * @param {string} payee 收付款:收款人(公司)描述
 * @param {string} ac_code 賬目:歸屬會計科目樹-會計科目code
 * @param {string} fund_id 賬目:賬目類別id
 * @param {string} ac_I 類別:收入類,可填N或Y
 * @param {string} ac_E 類別:支出類,可填N或Y
 * @param {string} ref 參考號:參考號
 * @param {string} contra_cde 對沖號:對沖編號
 * @param {string} quote 報價:Q(Quotation)或T(Tender)
 * @param {string} qnum 報價:quote=Q(0,1,2,3)|quote=T(自由輸入)
 * @param {string} descr 內容:會計科目描述
 * @param {string} vc_code 傳票號:傳票編號
 * @param {string} st_code 職員:職員編號
 * @param {string} st_type_id 職員:職員類別id
 * @param {string} dept_code 部門:部門編號
 * @param {string} dept_type_id 部門:部門類別id
 * @param {string} sbet 金額: BET=範圍,VAL=數值,GT=大於,LT=小於
 * @param {string} amount_from 金額:sbet=BET時使用
 * @param {string} amount_to 金額:sbet=BET時使用
 * @param {string} amount 金額:sbet=VAL或sbet=GT或sbet=LT時使用
 * @param {string} amount_type 金額:A=±Dr/±Cr,B=+Dr/+Cr,C=+Dr/-Cr,D=-Dr/+Cr,E=-Dr/-Cr,F=±Dr,G=+Dr,H=-Dr,I=±Cr,J=+Cr,K=-Cr
 * @param {integer} budget_id 預算年度下的預算id,必須同時傳fy_code
 * @return {Promise}
 */
export function enquiryCondition({
  type,
  fy_code,
  pd_code,
  date_from,
  date_to,
  date,
  payee,
  ac_code,
  fund_id,
  ac_I,
  ac_E,
  ac_B,
  ref,
  contra_cde,
  quote,
  qnum,
  descr,
  vc_code,
  st_code,
  st_type_id,
  dept_code,
  dept_type_id,
  sbet,
  amount_from,
  amount_to,
  amount,
  amount_type,
  budget_id,
}) {
  return request({
    url: '/enquiry/condition',
    method: 'get',
    params: {
      type,
      fy_code,
      pd_code,
      date_from,
      date_to,
      date,
      payee,
      ac_code,
      fund_id,
      ac_I,
      ac_E,
      ac_B,
      ref,
      contra_cde,
      quote,
      qnum,
      descr,
      vc_code,
      st_code,
      st_type_id,
      dept_code,
      dept_type_id,
      sbet,
      amount_from,
      amount_to,
      amount,
      amount_type,
      budget_id,
    },
  })
}
