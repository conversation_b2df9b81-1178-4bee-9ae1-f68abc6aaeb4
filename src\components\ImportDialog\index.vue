<template>
  <!-- import 對話框 -->
  <el-dialog
    v-loading="vLoading"
    :title="title || $t('file.excelImport')"
    :visible.sync="dialogVisible"
    class="dialog"
    width="650px"
    @open="onInit"
  >
    <div v-if="$slots.default" class="filters">
      <slot />
    </div>
    <manual-control-upload-excel
      ref="manualControlUploadExcel"
      :loading="vLoading"
      :on-success="onSuccess"
      :on-template="onTemplate"
      :on-submit="onSubmit"
      :on-cancel="onCancel"
      :msg="msg"
      :file-type-tips="fileTypeTips"
      :supports-file-type-tips="supportsFileTypeTips"
      :file-suffix="fileSuffix"
      :excel-file="excelFile"
      :multiple="multiple"
    />
  </el-dialog>
</template>

<script>
// import UploadExcel from '@/components/UploadExcel/index'
import manualControlUploadExcel from '@/components/UploadExcel/manualControl.vue'
export default {
  name: 'ImportDialog',
  components: {
    manualControlUploadExcel,
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    msg: {
      type: String,
      default: '',
    },
    excelFile: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    fileSuffix: {
      type: String,
      default: '.xlsx, .xls',
    },
    fileTypeTips: {
      type: String,
      default: '',
    },
    supportsFileTypeTips: {
      type: String,
      default: '',
    },
    onSuccess: {
      type: Function,
      required: true,
    },
    onTemplate: {
      type: Function,
      default: null,
    },
    onSubmit: {
      type: Function,
      default: async() => true,
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    vLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      },
    },
  },
  methods: {
    onInit() {
      if (this.$refs['manualControlUploadExcel']) {
        this.$refs['manualControlUploadExcel'].init()
      }
    },
    onCancel() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.filters {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 0 20px 0;
}
.dialog {
  /deep/ {
    .el-dialog__body {
      height: auto !important;
    }
  }
}
</style>
