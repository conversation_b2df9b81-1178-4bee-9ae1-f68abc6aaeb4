import request from '@/utils/request'

export function createRole(system, role_name_cn, role_name_en) {
  return request({
    url: '/roles/actions/create',
    method: 'post',
    data: {
      system,
      role_name_cn,
      role_name_en,
    },
  })
}

export function editRole(role_id, role_name_cn, role_name_en) {
  return request({
    url: '/roles/actions/update',
    method: 'post',
    data: {
      role_id,
      role_name_cn,
      role_name_en,
    },
  })
}

/**
 * 返回新增后的用戶id
 * @param user_code
 * @param username
 * @param password
 * @param name_cn
 * @param name_en
 * @param user_type
 * @param role_id
 */
export function createUser(user_code, username, password, name_cn, name_en, user_type, role_id) {
  return request({
    url: '/users/actions/create',
    method: 'post',
    data: {
      user_code,
      username,
      password,
      name_cn,
      name_en,
      user_type,
      role_id,
    },
  })
}

/**
 * 修改用戶
 * @param user_id
 * @param user_code
 * @param username
 * @param password
 * @param name_cn
 * @param name_en
 * @param user_type
 * @param role_id
 */
export function editUser(
  user_id,
  user_code,
  username,
  password,
  name_cn,
  name_en,
  user_type,
  role_id,
) {
  return request({
    url: '/users/actions/update',
    method: 'post',
    data: {
      user_id,
      user_code,
      username,
      password,
      name_cn,
      name_en,
      user_type,
      role_id,
    },
  })
}

/**
 * 刪除用戶
 * @param user_id
 */
export function deleteUser(user_id) {
  return request({
    url: '/users/actions/delete',
    method: 'post',
    data: {
      user_id,
    },
  })
}

/**
 * 查詢所有用戶
 */
export function fetchUsers() {
  return request({
    url: '/users',
    method: 'get',
  })
}

/**
 * 獲取某用戶詳情
 * @param user_id
 */
export function getUser(user_id) {
  return request({
    url: '/users/actions/inquire',
    method: 'get',
    params: {
      user_id,
    },
  })
}

export default { createUser, editUser, deleteUser, fetchUsers, getUser }
