<template>
  <div>
    <el-form-item
      :label="$t('setting.printout.label.fontSize')"
      class="form-item-simple form-table-box"
      style=""
    >
      <table class="form-table font-size-table">
        <tbody>
          <tr>
            <td>
              <span>{{ $t('setting.printout.label.fontSizeSchCN') }}</span>
            </td>
            <td>
              <span>{{ $t('setting.printout.label.fontSizeSchEN') }}</span>
            </td>
            <td>
              <span>{{ $t('setting.printout.label.fontSizeTitle') }}</span>
            </td>
            <td>
              <span>{{ $t('setting.printout.label.fontSizeContent') }}</span>
            </td>
            <td v-if="showSignature">
              <span>{{ $t('setting.printout.label.fontSizeSignature') }}</span>
            </td>
            <td v-if="showPageNum">
              <span>{{ $t('setting.printout.label.fontSizePageNum') }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <el-input-number v-model="font_size_school_name_cn" :min="0" :controls="false" />
            </td>
            <td>
              <el-input-number v-model="font_size_school_name_en" :min="0" :controls="false" />
            </td>
            <td><el-input-number v-model="font_size_title" :min="0" :controls="false" /></td>
            <td><el-input-number v-model="font_size_content" :min="0" :controls="false" /></td>
            <td v-if="showSignature">
              <el-input-number v-model="font_size_signature" :min="0" :max="15" :controls="false" />
            </td>
            <td v-if="showPageNum">
              <el-input-number v-model="font_size_page_num" :min="0" :max="14" :controls="false" />
            </td>
          </tr>
        </tbody>
      </table>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'FontSizeForm',
  props: {
    fontSizeSchoolNameCn: {
      type: [Number, String],
      default: 15,
    },
    fontSizeSchoolNameEn: {
      type: [Number, String],
      default: 13,
    },
    fontSizeTitle: {
      type: [Number, String],
      default: 11,
    },
    fontSizeContent: {
      type: [Number, String],
      default: 11,
    },
    fontSizeSignature: {
      type: [Number, String],
      default: 9,
    },
    fontSizePageNum: {
      type: [Number, String],
      default: 11,
    },
    showSignature: {
      type: Boolean,
      default: true,
    },
    showPageNum: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      //
    }
  },
  computed: {
    font_size_school_name_cn: {
      get() {
        return this.fontSizeSchoolNameCn
      },
      set(val) {
        this.$emit('update:fontSizeSchoolNameCn', val)
      },
    },
    font_size_school_name_en: {
      get() {
        return this.fontSizeSchoolNameEn
      },
      set(val) {
        this.$emit('update:fontSizeSchoolNameEn', val)
      },
    },
    font_size_title: {
      get() {
        return this.fontSizeTitle
      },
      set(val) {
        this.$emit('update:fontSizeTitle', val)
      },
    },
    font_size_content: {
      get() {
        return this.fontSizeContent
      },
      set(val) {
        this.$emit('update:fontSizeContent', val)
      },
    },
    font_size_signature: {
      get() {
        return this.fontSizeSignature
      },
      set(val) {
        this.$emit('update:fontSizeSignature', val)
      },
    },
    font_size_page_num: {
      get() {
        return this.fontSizePageNum
      },
      set(val) {
        this.$emit('update:fontSizePageNum', val)
      },
    },
  },
}
</script>

<style lang="scss" scoped>
.form-table-box {
  .item-font-size .el-input-number {
    width: 80px;
  }

  .form-table.font-size-table {
    td {
      width: 81px !important;
      text-align: center;
      span {
        color: $defaultColor;
      }
    }
  }
}
</style>
