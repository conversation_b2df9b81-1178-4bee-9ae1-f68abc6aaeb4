import store from '../store'
import { Base64 } from 'js-base64'

function getQuery(str) {
  const l = str.indexOf('=')
  const key = str.substring(0, l)
  const value = str.substring(l + 1)
  return { key, value }
}
function getQuerys(queryStr) {
  const queryArr = queryStr.split('&')
  const querys = {}
  queryArr.forEach(item => {
    const { key, value } = getQuery(item)
    querys[key] = value
  })
  return querys
}
export async function autoLogin(next) {
  const hash = location.hash
  const sIndex = location.hash.indexOf('?')
  if (sIndex !== -1) {
    const queryStr = hash.substring(sIndex + 1)
    // const queryArr = queryStr.split('&')
    // const querys = {}
    // queryArr.forEach(item => {
    //   const { key, value } = this.getQuery(item)
    //   querys[key] = value
    // })
    const querys = getQuerys(queryStr)
    if (querys.hasOwnProperty('authorization')) {
      try {
        const authorization = Base64.decode(decodeURIComponent(querys['authorization']))
        console.log(authorization)
        const data = getQuerys(authorization)
        const username = data.username
        const password = data.password
        const system = data.system

        try {
          await store.dispatch('LoginByUsername', { username, password, system })
          location.hash = ''
          await setTimeout(() => {
            // next({ path: '/', replace: true, query: {}})
            location.hash = '#'
            location.reload()
            return true
          }, 10)
        } catch (e) {
          location.hash = '#'
          await setTimeout(() => {
            // next({ path: '/login', replace: true, query: {}})
            // location.hash = ''
            location.hash = '#'
            location.reload()
          }, 10)
          console.log(e)
          return true
        }
      } catch (e) {
        console.log(e)
      }
    }
  }
  return false
}
