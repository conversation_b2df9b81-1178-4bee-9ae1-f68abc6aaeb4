<script>
import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import { fetchLedgersPrint } from '@/api/report'
import dayjs from 'dayjs'

export default {
  // name: 'HandlePDF',
  data() {
    return {
      printData: [],
      printStyle: '',
    }
  },
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      this.loading = true
      return new Promise((resolve, reject) => {
        this.loadPrintData()
          .then(res => {
            this.printData = res
            return this.loadPrintoutSetting('pdfledger', 1)
          })
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(this.formatPrintDataSplit)
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              printSetting,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    loadPrintData() {
      const filters = this.preferences.filters

      const parent_fund_id = filters.select_group ? filters.ac_fund_id : filters.fund_id
      const ac_code = filters.select_group ? undefined : filters.ac_code
      const mode = filters.mode
      this.printStyle = filters.style

      const dateFormat = 'yyyy-MM-dd'
      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, 1)
      const lastDateObj = new Date(year, month + 1, 0)
      // let begin_date, end_date
      // if (!filters.date_range || !filters.date_range.length) {
      //   begin_date = dateUtil.format(beginDateObj, dateFormat)
      //   end_date = dateUtil.format(lastDateObj, dateFormat)
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      //   this.onChangeDateRange([begin_date, end_date])
      // } else {
      //   begin_date = filters.date_range[0]
      //   end_date = filters.date_range[1]
      // }
      // if (!begin_date || !end_date) {
      //   begin_date = dateUtil.format(beginDateObj, dateFormat)
      //   end_date = dateUtil.format(lastDateObj, dateFormat)
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      // }
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!begin_date || !end_date) {
        begin_date = dateUtil.format(beginDateObj, dateFormat)
        end_date = dateUtil.format(lastDateObj, dateFormat)
        // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        this.preferences.filters.begin_date = begin_date
        this.preferences.filters.end_date = end_date
      }
      return new Promise((resolve, reject) => {
        fetchLedgersPrint({
          parent_fund_id,
          ac_code,
          begin_date,
          end_date,
          mode,
        })
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const nc = this.currentFilters.nature_code
        const style = this.preferences.filters.style
        const rawData = this.currentData
        const langKey = this.langKey

        const language = printSetting.language

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const title = this.$t(langKey + 'title', language)
        const date = this.currentFilters.date_range
        let dateStr = ''
        if (date && date.length === 2) {
          dateStr =
            dateUtil.format(new Date(date[0]), 'dd/MM/yyyy') +
            ' - ' +
            dateUtil.format(new Date(date[1]), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title}${dateStr ? ' - ' + dateStr : ''}`,
          data: [
            {
              label: this.$t('filters.period', language) + ':',
              value: dateStr,
            },
          ],
        }

        // 表頭
        const columns = []
        const columnData = printSetting.columnData
          .filter(i => {
            // 模式C 需要過濾列
            if (style === 'C') {
              switch (nc) {
                case 'Dr':
                case 'E':
                  return i.name !== 'amount_cr'
                case 'I/E':
                case 'G':
                  break
                case 'I':
                case 'Cr':
                default:
                  return i.name !== 'amount_dr'
              }
            }
            return true
          })
          .filter(i => i.position > 0)
          .sort((a, b) => a.position - b.position)
        columnData.forEach(col => {
          columns.push({
            text: this.columnLabel(col.name, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: col.alignment,
            width: col.width,
          })
        })
        // 主數據
        const tableData = []
        const sumRow = []
        let sumIndex = 0

        // 初始化行
        columnData.forEach((col, colIndex) => {
          sumRow.push({
            name: col.name,
            text: '',
            alignment: col.alignment,
            bold: true,
            style: 'tableFooter',
          })
        })
        rawData.forEach((item, rowIndex) => {
          if (rowIndex === 0) {
            return
          }

          const row = []
          sumIndex = 0
          columnData.forEach((col, colIndex) => {
            const cell = {
              text: '',
              alignment: col.alignment,
              style: 'tableContent',
              width: col.width,
            }
            switch (col.name) {
              case 'st_name_':
              case 'ac_name_':
              case 'dept_name_': {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = language === 'en' ? key + 'en' : key + 'cn'
                }
                cell.text = item[key]
                break
              }
              case 'vc_date': {
                if (item.vc_date !== null) {
                  let vc_date = ''
                  if (rowIndex === 0) {
                    vc_date = new Date(date[0])
                  } else {
                    vc_date = new Date(item.vc_date)
                  }

                  cell.text = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
                }
                break
              }
              case 'amount_dr':
              case 'amount_cr':
              case 'net_amount':
                {
                  let v = Number(item[col.name])
                  isNaN(v) && (v = 0)
                  cell.text = amountFormat(v)

                  sumRow[sumIndex].alignment = col.alignment
                  sumRow[sumIndex].value = toDecimal(Number(sumRow[sumIndex].value) + v)
                  sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)
                }
                break
              case 'amount_balance':
                {
                  let v = Number(item[col.name])
                  isNaN(v) && (v = 0)
                  v = toDecimal(v)
                  cell.text = amountFormat(v)
                  sumRow[sumIndex].alignment = col.alignment
                  console.log(
                    sumRow[sumIndex].value,
                    v,
                    toDecimal(Number(sumRow[sumIndex].value) + v),
                  )
                  sumRow[sumIndex].value = toDecimal(Number(sumRow[sumIndex].value) + v)
                  sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)
                }
                break
              default: {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = key + (language === 'en' ? 'en' : 'cn')
                }
                const v = item[key]
                cell.text = v === undefined ? '' : v
              }
            }
            row.push(cell)
            sumIndex++
          })
          tableData.push(row)
        })

        const sumFields = ['amount_dr', 'amount_dr', 'amount_dr', 'amount_dr']
        const sumPosition = sumRow.findIndex(i => sumFields.includes(i.name))
        if (sumPosition > 0) {
          sumRow[0].text = this.$t('print.total', language)
          sumRow[0].colSpan = sumPosition
          sumRow[0].alignment = 'right'
        }
        tableData.push(sumRow)

        const ac_code = this.currentFilters.ac_code || ''
        const ac_name = language === 'en' ? this.currentAccountNameEN : this.currentAccountNameCN
        const accountInfo = [...Array(columnData.length)].map(() => '')
        accountInfo[0] = {
          width: '100%',
          style: 'tableExample',
          colSpan: columnData.length,
          border: [false, false, false, false],
          table: {
            dontBreakRows: true,
            keepWithHeaderRows: 1,
            headerRows: 1,
            // widths: ['auto'],
            body: [
              [
                { text: this.$t('report.ledger.label.ac_code', language) + ':' },
                { text: ac_code, margin: [10, 0, 10, 0] },
                { text: this.$t('report.ledger.label.ac_name_', language) + ':' },
                { text: ac_name, margin: [10, 0, 10, 0] },
              ],
            ],
          },
          layout: {
            defaultBorder: false,
            vLineWidth: lineWidth,
            hLineWidth: lineWidth,
            hLineColor: lineColor,
            vLineColor: lineColor,
          },
        }

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
          accountInfo,
        })
      })
    },
    formatPrintDataSplit(printSetting) {
      return new Promise(async(resolve, reject) => {
        const nc = this.currentFilters.nature_code
        const style = this.preferences.filters.style

        const rawData = this.printData

        const langKey = this.langKey

        const language = printSetting.language

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const title = this.$t(langKey + 'title', language)
        const begin_date = this.currentFilters.begin_date
        const end_date = this.currentFilters.end_date
        let dateStr = ''
        if (begin_date && end_date) {
          dateStr =
            dateUtil.format(new Date(begin_date), 'dd/MM/yyyy') +
            ' - ' +
            dateUtil.format(new Date(end_date), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title}${dateStr ? ' - ' + dateStr : ''}`,
          data: [
            {
              label: this.$t('filters.period', language) + ':',
              value: dateStr,
            },
          ],
        }

        // 表頭
        const columns = []
        const columnData = printSetting.columnData
          .filter(i => {
            // 模式C 需要過濾列
            if (style === 'C') {
              switch (nc) {
                case 'Dr':
                case 'E':
                  return i.name !== 'amount_cr'
                case 'I/E':
                case 'G':
                  break
                case 'I':
                case 'Cr':
                default:
                  return i.name !== 'amount_dr'
              }
            }
            return true
          })
          .filter(i => i.position > 0)
          .sort((a, b) => a.position - b.position)
        columnData.forEach(col => {
          columns.push({
            text: this.columnLabel(col.name, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: col.alignment,
            width: col.width,
          })
        })

        // const margin_right = mm2pt(printSetting.margin_right)
        // const title_width = mm2pt(printSetting.title_width)
        // // 學校信息
        // const schoolTable = generateSchoolInfo(
        //   schoolInfo.name_cn,
        //   schoolInfo.name_en,
        //   schoolInfo.logo)
        // // 頁面信息
        // const pageTable = generatePageInfo(
        //   pageInfo.title,
        //   pageInfo.filename,
        //   pageInfo.data,
        //   title_width,
        //   margin_right)
        // // 頁頭，包含LOGO，頁面信息
        // const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

        // 主數據
        const tableData = this.generateContent(
          printSetting,
          rawData,
          columnData,
          columns,
          language,
          schoolInfo,
          pageInfo,
        )
        // const sumRow = []
        // let sumIndex = 0
        //
        // // 初始化行
        // columnData.forEach((col, colIndex) => {
        //   sumRow.push({ name: col.name, text: '', alignment: col.alignment, bold: true, style: 'tableFooter' })
        // })
        // rawData.forEach((item, rowIndex) => {
        //   if (rowIndex === 0) {
        //     return
        //   }
        //
        //   const row = []
        //   sumIndex = 0
        //   columnData.forEach((col, colIndex) => {
        //     const cell = {
        //       text: '',
        //       alignment: col.alignment,
        //       style: 'tableContent',
        //       width: col.width
        //     }
        //     switch (col.name) {
        //       case 'st_name_':
        //       case 'ac_name_':
        //       case 'dept_name_':
        //       {
        //         let key = col.name
        //         if (key[key.length - 1 ] === '_') {
        //           key = language === 'en' ? (key + 'en') : (key + 'cn')
        //         }
        //         cell.text = item[key]
        //         break
        //       }
        //       case 'vc_date':
        //       {
        //         if (item.vc_date !== null) {
        //           let vc_date = ''
        //           if (rowIndex === 0) {
        //             vc_date = new Date(date[0])
        //           } else {
        //             vc_date = new Date(item.vc_date)
        //           }
        //
        //           cell.text = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
        //         }
        //         break
        //       }
        //       case 'amount_dr':
        //       case 'amount_cr':
        //       case 'amount_balance':
        //       case 'net_amount':
        //       {
        //         let v = Number(item[col.name])
        //         isNaN(v) && (v = 0)
        //         cell.text = amountFormat(v)
        //
        //         sumRow[sumIndex].alignment = col.alignment
        //         sumRow[sumIndex].value = Number(sumRow[sumIndex].value) + v
        //         sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)
        //         break
        //       }
        //       default: {
        //         let key = col.name
        //         if (key[key.length - 1 ] === '_') {
        //           key = key + (language === 'en' ? 'en' : 'cn')
        //         }
        //         const v = item[key]
        //         cell.text = v === undefined ? '' : v
        //       }
        //     }
        //     row.push(cell)
        //     sumIndex++
        //   })
        //   tableData.push(row)
        // })
        //
        // const sumFields = ['amount_dr', 'amount_dr', 'amount_dr', 'amount_dr']
        // const sumPosition = sumRow.findIndex(i => sumFields.includes(i.name))
        // if (sumPosition > 0) {
        //   sumRow[0].text = this.$t('print.total', language)
        //   sumRow[0].colSpan = sumPosition
        //   sumRow[0].alignment = 'right'
        // }
        // tableData.push(sumRow)

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    generateContent(printSetting, list, columnData, rawColumns, language, schoolInfo, pageInfo) {
      const data = []
      list.forEach((item, index) => {
        data.push(
          this.generateContentPage(
            printSetting,
            item,
            columnData,
            rawColumns,
            language,
            index,
            schoolInfo,
            pageInfo,
          ),
        )
      })
      return data
    },
    generateHeader(printSetting, schoolInfo, pageInfo, colLength) {
      const margin_right = mm2pt(printSetting.margin_right)
      const title_width = mm2pt(printSetting.title_width)
      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      return generateGeneralHeader(schoolTable, pageTable, colLength)
    },
    generateContentPage(
      printSetting,
      data,
      columnData,
      rawColumns,
      language,
      groupIndex,
      schoolInfo,
      pageInfo,
    ) {
      const pageHeader = this.generateHeader(printSetting, schoolInfo, pageInfo, columnData.length)
      const accountInfo = this.generateAccountInfo(
        data.ac_code,
        language === 'en' ? data.ac_name_en : data.ac_name_cn,
        columnData.length,
        groupIndex,
        language,
      )
      const columns = rawColumns.map(i => ({ ...i }))
      // 表格寬度
      const widths = generateWidths(columns)

      // 表格
      const tableData = this.generateTable(data.data, columnData, printSetting.language)

      // 單個會計項目
      const page = [
        {
          // Content
          headlineLevel: groupIndex > 0 ? 1 : 0,
          pageBreak: groupIndex > 0 ? 'before' : undefined,
          width: '100%',
          style: 'tableExample',
          table: {
            dontBreakRows: true,
            keepWithHeaderRows: 1,
            // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
            widths: widths,
            // heights: tableData.map((e, i) => i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto'),
            headerRows: 3, // columns.length + 1,
            body: [
              pageHeader, // 頁頭
              accountInfo, // 職員信息
              columns, // 數據表頭
              ...tableData, // 數據
            ],
          },
          layout: {
            vLineWidth: lineWidth,
            hLineWidth: lineWidth,
            hLineColor: lineColor,
            vLineColor: lineColor,
          },
        },
      ]
      // 簽名
      if (printSetting.sign_style.toString() === '1') {
        const bottom_sign = printSetting.sign_style.toString() === '2'
        const margin_left = mm2pt(printSetting.margin_left)
        const margin_right = mm2pt(printSetting.margin_right)
        const sign_height = mm2pt(printSetting.sign_height)
        const sign_space = mm2pt(printSetting.sign_space)
        const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
        const signTable = generateSign(
          printSetting.sign_line,
          signColumn,
          printSetting.language,
          sign_height,
          sign_space,
          margin_left,
          margin_right,
          printSetting.font_size_signature,
          bottom_sign,
        )
        page.push(signTable)
      }
      return page
    },
    generateTable(rawData, columnData, language) {
      const nc = this.currentFilters.nature_code
      const style = this.preferences.filters.style
      const tableData = []
      const sumRow = []
      let sumIndex = 0

      // 初始化行
      columnData.forEach((col, colIndex) => {
        sumRow.push({
          name: col.name,
          text: '',
          value: 0,
          alignment: col.alignment,
          bold: true,
          style: 'tableFooter',
        })
      })
      let sum = 0
      rawData.forEach((item, rowIndex) => {
        // if (rowIndex === 0) {
        //   return
        // }
        // if (rowIndex > 0) {
        switch (style) {
          case 'A':
            item.net_amount = toDecimal(Number(item.amount_dr) - Number(item.amount_cr))
            break
          case 'B':
          case 'C':
            switch (nc) {
              case 'Dr':
              case 'E':
                item.net_amount = toDecimal(Number(item.amount_dr) - Number(item.amount_cr))
                break
              case 'I':
              case 'I/E':
              case 'G':
              case 'Cr':
              default:
                item.net_amount = toDecimal(Number(item.amount_cr) - Number(item.amount_dr))
                break
            }
            break
        }
        sum += toDecimal(item.net_amount)
        item.amount_balance = sum
        // } else {
        //   if (style === 'B' || style === 'C') {
        //     sum = item.amount_balance
        //     // item.amount_balance = sum
        //   }
        // }

        const row = []
        sumIndex = 0
        columnData.forEach((col, colIndex) => {
          const cell = {
            text: '',
            alignment: col.alignment,
            style: 'tableContent',
            width: col.width,
          }
          switch (col.name) {
            case 'st_name_':
            case 'ac_name_':
            case 'dept_name_': {
              let key = col.name
              if (key[key.length - 1] === '_') {
                key = language === 'en' ? key + 'en' : key + 'cn'
              }
              cell.text = item[key]
              break
            }
            case 'vc_date': {
              if (item.vc_date !== null) {
                const vc_date = new Date(item.vc_date)

                cell.text = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
              }
              break
            }
            case 'amount_dr':
            case 'amount_cr':
            case 'net_amount': {
              if (rowIndex > 0) {
                let v = Number(item[col.name])
                isNaN(v) && (v = 0)
                cell.text = amountFormat(v)

                sumRow[sumIndex].alignment = col.alignment
                const old = Number(sumRow[sumIndex].value)
                const oldSum = isNaN(old) ? 0 : old
                sumRow[sumIndex].value = toDecimal(oldSum + v)
                sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)
              }
              break
            }
            case 'amount_balance': {
              let v = Number(item[col.name])
              isNaN(v) && (v = 0)
              v = toDecimal(v)
              cell.text = amountFormat(v)

              sumRow[sumIndex].alignment = col.alignment
              switch (this.printStyle) {
                case 'A':
                  sumRow[sumIndex].value = v
                  break
                case 'B':
                case 'C':
                  sumRow[sumIndex].value = toDecimal(v + sumRow[sumIndex].value)
                  break
              }
              sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)
              break
            }
            default: {
              let key = col.name
              if (key[key.length - 1] === '_') {
                key = key + (language === 'en' ? 'en' : 'cn')
              }
              const v = item[key]
              cell.text = v === undefined ? '' : v
            }
          }
          row.push(cell)
          sumIndex++
        })
        tableData.push(row)
      })
      const sumFields = ['amount_dr', 'amount_cr', 'amount_balance', 'net_amount']
      const sumPosition = sumRow.findIndex(i => sumFields.includes(i.name))
      if (sumPosition > 0) {
        sumRow[0].text = this.$t('print.total', language)
        sumRow[0].colSpan = sumPosition
        sumRow[0].alignment = 'right'
      }
      tableData.push(sumRow)
      return tableData
    },
    generateAccountInfo(code, name, span, index, language) {
      const accountInfo = [...Array(span)].map(() => '')
      accountInfo[0] = {
        // headlineLevel: index > 0 ? 1 : 0,
        // pageBreak: index > 0 ? 'before' : undefined,
        width: '*',
        style: 'tableExample',
        colSpan: span,
        border: [false, false, false, false],
        table: {
          // margin: [0, 0, 10, 0],
          width: '*',
          dontBreakRows: true,
          keepWithHeaderRows: 1,
          headerRows: 1,
          widths: ['auto', 'auto', 'auto', '*'],
          body: [
            [
              { text: this.$t('report.ledger.label.ac_code', language) + ':' },
              { text: code },
              { text: this.$t('report.ledger.label.ac_name_', language) + ':' },
              { text: name },
            ],
          ],
        },
        layout: {
          defaultBorder: false,
          vLineWidth: lineWidth,
          hLineWidth: lineWidth,
          hLineColor: lineColor,
          vLineColor: lineColor,
        },
      }
      return accountInfo
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)
      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      // const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 表格寬度
      // const widths = generateWidths(columns)
      //
      // // 學校信息
      // const schoolTable = generateSchoolInfo(
      //   schoolInfo.name_cn,
      //   schoolInfo.name_en,
      //   schoolInfo.logo)
      // // 頁面信息
      // const pageTable = generatePageInfo(
      //   pageInfo.title,
      //   pageInfo.filename,
      //   pageInfo.data,
      //   title_width,
      //   margin_right)
      // 頁頭，包含LOGO，頁面信息
      // const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          tableData,
          // { // Content
          //   width: '100%',
          //   style: 'tableExample',
          //   table: {
          //     dontBreakRows: true,
          //     keepWithHeaderRows: 0,
          //     // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
          //     widths: widths,
          //     heights: tableData.map((e, i) => i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto'),
          //     headerRows: 0,
          //     body: [
          //       // pageHeader, // 頁頭
          //       ...tableData // 數據
          //     ]
          //   },
          //   layout: {
          //     // defaultBorder: false,
          //     vLineWidth: lineWidth,
          //     hLineWidth: lineWidth,
          //     hLineColor: lineColor,
          //     vLineColor: lineColor
          //   }
          // }
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        pageBreakBefore: function(
          currentNode,
          followingNodesOnPage,
          nodesOnNextPage,
          previousNodesOnPage,
        ) {
          return currentNode.headlineLevel === 1
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      // if (printSetting.sign_style.toString() === '1') { // 浮動
      //   docDefinition.content.push(signTable)
      // }
      // console.log(JSON.stringify(docDefinition))
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      await openPdf(docDefinition)
    },
  },
}
</script>
