<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 貨品編號 -->
      <el-form-item :rules="rules" :label="$t('stock.itemSetup.label.sk_code')" prop="sk_code">
        <el-input v-model="form.sk_code" class="skCode" clearable />
      </el-form-item>
      <!-- 名稱（中） -->
      <el-form-item
        :rules="rules"
        :label="$t('stock.itemSetup.label.sk_name_cn')"
        prop="sk_name_cn"
      >
        <el-input v-model="form.sk_name_cn" clearable />
      </el-form-item>
      <!-- 名稱（英） -->
      <el-form-item
        :rules="rules"
        :label="$t('stock.itemSetup.label.sk_name_en')"
        prop="sk_name_en"
      >
        <el-input v-model="form.sk_name_en" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :label="$t('stock.itemSetup.label.parent_stock_group_id')"
        prop="parent_stock_group_id"
      >
        <el-select
          v-model="form.parent_stock_group_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in stockGroups"
            :key="item.value"
            :value="item.stock_group_id"
            :label="conversionParentStockGroup(item)"
            v-html="conversionParentStockGroup(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置 -->
      <el-form-item :label="$t('stock.itemSetup.label.seq')" prop="seq">
        <el-select v-model="form.seq" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in sepOptions"
            :key="item.stock_group_id"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.stock_group_id ? item.stock_group_id : item.stock_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('stock.itemSetup.label.active_year')">
        <el-table
          ref="table"
          :data="tableData"
          :selectable="checkTableSelected"
          border
          tooltip-effect="dark"
          @select="select"
          @select-all="selectAll"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40px" />
          <el-table-column :label="$t('stock.itemSetup.label.date')" width="70px">
            <template v-if="scope && scope.row" slot-scope="scope">
              {{ '20' + scope.row.fy_code }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('stock.itemSetup.label.purchase_price')">
            <template v-if="scope && scope.row" slot-scope="scope">
              <template>
                <ENumeric
                  v-model="scope.row.purchase_price"
                  :controls="false"
                  :precision="2"
                  :disabled="!scope.row.status"
                />
              </template>
            </template>
          </el-table-column>
          <el-table-column :label="$t('stock.itemSetup.label.sales_price')">
            <template v-if="scope && scope.row" slot-scope="scope">
              <template>
                <!-- <el-input v-model="scope.row.sales_price"/> -->
                <ENumeric
                  v-model="scope.row.sales_price"
                  :controls="false"
                  :precision="2"
                  :disabled="!scope.row.status"
                />
              </template>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createStock, updateStock, getStock, getStocksTreeNode } from '@/api/stock/itemSetup'
import { fetchStockGroups } from '@/api/stock/itemSetup/itemSetupGroup'
import { fetchYears } from '@/api/master/years'

import ENumeric from '@/components/ENumeric'

export default {
  name: 'StockItemSetupAdd',
  components: {
    ENumeric,
  },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      system: 'BG',
      tableData: [],
      mid: [],
      status: [],
      multipleSelection: [],
      form: {
        stock_id: '',
        stock_group_id: '',
        sk_code: '',
        sk_name_cn: '',
        sk_name_en: '',
        active_year: '',
        parent_stock_group_id: '',
        seq: '',
        purchase_price: '',
        sales_price: '',
        prices_json: '',
      },
      defaultForm: {
        stock_id: '',
        stock_group_id: '',
        sk_code: '',
        sk_name_cn: '',
        sk_name_en: '',
        active_year: '',
        parent_stock_group_id: '',
        seq: '',
        purchase_price: '',
        sales_price: '',
        prices_json: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      defaultSepOptions: [
        {
          stock_group_id: '',
          stock_id: '',
          seq: '',
          name_cn: this.$t('stock.itemSetup.label.seq_default'),
          name_en: this.$t('stock.itemSetup.label.seq_default'),
        },
      ],
      sepOptions: [
        {
          stock_group_id: '',
          stock_id: '',
          seq: '',
          name_cn: this.$t('stock.itemSetup.label.seq_default'),
          name_en: this.$t('stock.itemSetup.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      defaultStockGroups: [
        {
          stock_group_id: '',
          sg_name_cn: this.$t('stock.itemSetup.label.parent_default'),
          sg_name_en: this.$t('stock.itemSetup.label.parent_default'),
        },
      ],
      stockGroups: [],
      seqStartLevel: 1,
      active_year_arr: [],
      loading: true,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
  },
  watch: {
    editObject() {
      this.initData()
    },
    editParent() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  // mounted(){
  //   this.$nextTick(() => {
  //     this.toggle()
  //   })
  // },
  methods: {
    checked() {
      var x
      // this.$refs.table.toggleRowSelection(this.tableData[1],true)
      for (x in this.tableData) {
        if (this.tableData[x].status) {
          this.$refs.table.toggleRowSelection(this.tableData[x], true)
          // this.$refs.table.toggleAllSelection(this.tableData[x], false)
        }
      }
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentStockGroup(stockGroup, html, startLevel = 1) {
      let text = this.language === 'en' ? stockGroup.sg_name_en : stockGroup.sg_name_cn
      if (html) {
        text = '&nbsp;'.repeat((stockGroup.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    select(rows, row) {
      console.log('rows', rows)
      console.log('row', row)
      const selected = rows.length && rows.indexOf(row) !== -1
      if (selected) {
        row.status = true
      } else {
        row.status = false
      }
    },
    selectAll(rows) {
      console.log(rows)
      // rows.forEach(ele => {
      //   let selected = rows.length && rows.indexOf(ele) !== -1
      //    if(selected){
      //     ele.status = true
      //   }else{
      //    this.$refs.table.toggleAllSelection(this.tableData, false)
      //   }
      // });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('multipleSelection', this.multipleSelection)
    },
    changeParentType() {
      getStocksTreeNode('', this.form.parent_stock_group_id, this.form.stock_group_id, '').then(
        res => {
          this.sepOptions = [...res, ...this.defaultSepOptions]
          if (this.editObject && this.form.seq_i) {
            let isLast = true
            for (let i = 0; i < res.length; i++) {
              if (res[i].seq > this.form.seq_i) {
                isLast = false
                this.form.seq = res[i].stock_group_id ? res[i].stock_group_id : res[i].stock_id
                break
              }
            }
            if (isLast) {
              this.form.seq = ''
            }
          } else {
            this.form.seq = ''
          }
        },
      )
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.stock_group_id) {
        this.form.parent_stock_group_id = this.editParent.stock_group_id
      } else if (
        this.editObject &&
        this.editObject.parent &&
        this.editObject.parent.stock_group_id
      ) {
        this.form.parent_stock_group_id = this.editObject.parent.stock_group_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getStock(this.editObject.stock_id)
            .then(res => {
              this.form = Object.assign(this.defaultForm, res)
              this.setParent()
              this.form.parent_stock_group_id =
                res.stockGroupRelation && res.stockGroupRelation.parent_stock_group_id
                  ? res.stockGroupRelation.parent_stock_group_id
                  : ''
              this.form.seq_i = res.stockGroupRelation ? res.stockGroupRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              // this.tableData = res.stockPrices
              this.mid = res.stockPrices
              this.tableData = []
              resolve()
            })
            .then(fetchYears)
            .then(res => {
              res.forEach(ele1 => {
                if (this.mid.length > 0) {
                  this.mid.forEach(ele2 => {
                    // console.log(ele1)
                    if (ele1.fy_code === ele2.fy_code) {
                      ele1.purchase_price = ele2.purchase_price
                      ele1.sales_price = ele2.sales_price
                      ele1.status = 1
                    } else if (ele1.purchase_price === undefined || ele1.purchase_price === 0) {
                      ele1.purchase_price = 0
                      ele1.status = 0
                    } else if (ele1.sales_price === undefined || ele1.sales_price === 0) {
                      ele1.sales_price = 0
                      ele1.status = 0
                    }
                  })
                } else {
                  ele1.purchase_price = 0
                  ele1.sales_price = 0
                  ele1.status = 0
                }
              })
              this.tableData = res
              this.$nextTick(function() {
                this.checked() // 每次更新了数据，触发这个函数即可。
              })
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.active_year_arr = []
          this.form = Object.assign({}, this.defaultForm)
          this.tableData = []
          this.setParent()
          fetchYears().then(res => {
            res.forEach(ele => {
              ele.purchase_price = 0
              ele.sales_price = 0
              ele.status = false
            })
            this.tableData = res
            this.$nextTick(() => {
              // let selected
              if (this.fyCode) {
                const index = this.tableData.findIndex(i => i.fy_code === this.fyCode)
                if (index !== -1) {
                  this.tableData[index].status = true
                  const selected = this.tableData[index]
                  this.multipleSelection.push(selected)
                  this.$refs.table.toggleRowSelection(selected, true)
                }
              }

              // if (this.currentYear && this.currentYear.fy_code) {
              //   const current = this.tableData.find(i => i.fy_code === this.currentYear.fy_code)
              //   if (current !== selected && current) {
              //     this.multipleSelection.push(current)
              //     this.$refs.table.toggleRowSelection(current, true)
              //   }
              // }
            })
          })
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(fetchStockGroups)
        .then(res => {
          this.stockGroups = [...this.defaultStockGroups, ...res]
          this.changeParentType(this.form.parent_stock_group_id)
        })
        .finally(() => {
          this.loading = false
        })
    },

    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.stock_group_id ? 'stock_group_id' : 'stock_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        // const stock_group_id = this.form.stock_group_id
        const stock_id = this.form.stock_id
        const sk_code = this.form.sk_code
        const sk_name_cn = this.form.sk_name_cn
        const sk_name_en = this.form.sk_name_en
        this.active_year_arr = []
        this.multipleSelection.forEach(ele => {
          this.active_year_arr.push(ele.fy_code)
        })
        console.log(this.active_year_arr)
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const parent_stock_group_id = this.form.parent_stock_group_id
        const prices_json = []
        this.multipleSelection.forEach(ele => {
          prices_json.push({
            fy_code: ele.fy_code,
            purchase_price: ele.purchase_price,
            sales_price: ele.sales_price,
          })
        })
        console.log('prices_json', prices_json)
        const prices_json_string = JSON.stringify(prices_json)
        const seq = this.newSeq()
        if (this.editObject) {
          // 編輯
          updateStock({
            stock_id: stock_id,
            sk_code: sk_code,
            sk_name_cn: sk_name_cn,
            sk_name_en: sk_name_en,
            active_year: active_year,
            parent_stock_group_id: parent_stock_group_id,
            seq: seq,
            prices_json: prices_json_string,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        } else {
          // 新增
          createStock({
            sk_code: sk_code,
            sk_name_cn: sk_name_cn,
            sk_name_en: sk_name_en,
            active_year: active_year,
            parent_stock_group_id: parent_stock_group_id,
            seq: seq,
            prices_json: prices_json_string,
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              this.$message.err(err)
            })
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    checkTableSelected(row, index) {
      console.log(row)
    },
  },
}
</script>

<style scoped></style>
