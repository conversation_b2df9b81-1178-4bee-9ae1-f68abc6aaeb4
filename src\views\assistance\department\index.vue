<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 會計週期 -->
        <el-select v-model="preferences.filters.year" class="year" @change="fetchTree">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>

        <svg-icon v-if="hasPermission_Add" icon-class="new_folder" @click="onAddType" />
        <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd" />
        <div v-for="i in departmentLevel" :key="i" class="selectLevel" @click="expandLevel(i)">
          {{ i }}
        </div>
        <div class="selectLevel" @click="enableYear">
          {{ preferences.filters.isEnableYear ? 'N' : 'Y' }}
        </div>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 按鈕 -->
          <div
            v-if="hasPermission_Input"
            :title="$t('btnTitle.importExcel')"
            class="icon import"
            @click="importDialog = true"
          >
            <svg-icon icon-class="import" class="action-icon" />
          </div>
          <!-- 新增職員類別 -->
          <div
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcel')"
            class="icon export"
            @click="onExport"
          >
            <svg-icon icon-class="export" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <tree-table
          ref="TreeTable"
          :data="departmentTree"
          :eval-func="func"
          :eval-args="args"
          :expand-all="true"
          :first-field="language === 'en' ? 'name_en' : 'name_cn'"
          :first-field-align="firstFieldAlign"
          :first-field-width="firstFieldWidth"
          number-field="code"
          folder-field="dept_type_id"
          border
          @changeWidth="changeColumnWidth"
          @changeExpanded="onChangeExpanded"
        >
          <!--    編號姓名      -->
          <template v-if="scope && scope.row" slot="firstField" slot-scope="{ scope }">
            <i
              v-if="scope.row.dept_type_id"
              :class="
                'edac-icon edac-icon-folder' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' activate'
                    : '')
              "
            />
            <i v-else class="edac-icon edac-icon-file" />
            <span
              v-if="!scope.row.dept_type_id"
              :class="
                'number-field' +
                  (scope.row.active_year && scope.row.active_year.includes(preferences.filters.year)
                    ? ' selected'
                    : '')
              "
            >{{ scope.row.code }}</span>
            <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>
          </template>

          <!-- 操作列 -->
          <el-table-column
            :label="$t('table.action')"
            align="left"
            header-align="left"
            min-width="170"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <div v-if="scope.row.dept_type_id" class="operation_icon">
                <svg-icon
                  v-if="hasPermission_Add"
                  icon-class="new_folder"
                  @click="onAddType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <svg-icon v-if="hasPermission_Add" icon-class="new_file" @click="onAdd(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditType(scope)" />
                <svg-icon v-else class="no-cursor" icon-class="" />
                <i
                  v-if="
                    hasPermission_Delete && (!scope.row.children || scope.row.children.length === 0)
                  "
                  class="el-icon-close"
                  @click="onDeleteType(scope)"
                />
                <svg-icon v-else class="no-cursor" icon-class="" />
              </div>
              <div v-else class="operation_icon">
                <svg-icon class="no-cursor" icon-class="" />
                <svg-icon class="no-cursor" icon-class="" />
                <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
                <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDelete(scope)" />
              </div>
            </template>
          </el-table-column>
        </tree-table>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-object="editObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        :table-data="departmentTree"
        @onCancel="onViewCancel"
      />
      <addTypePage
        v-else-if="leftView === 'addDepartmentType' || leftView === 'editDepartmentType'"
        :edit-object="editTypeObject"
        :edit-parent="editParent"
        :fy-code="preferences.filters.year"
        :table-data="departmentTree"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      table-type="tree"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      class="upload-dialog"
      width="450px"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import addPage from './add'
import addTypePage from './addType'
import {
  deleteDepartment,
  getDepartmentsTree,
  exportDepartments,
  importDepartments,
} from '@/api/assistance/department'
import { deleteDepartmentType } from '@/api/assistance/department/departmentType'
import { mapGetters } from 'vuex'
import treeToArray from '@/components/TreeTable/eval.js'
import treeTable from '@/components/TreeTable'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'

import mixinPermission from '@/views/mixins/permission'
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
import { exportExcel, importExcel } from '@/utils/excel'
export default {
  name: 'AssistanceDepartmentIndex',
  components: {
    LRPane,
    customStyle,
    addPage,
    addTypePage,
    treeTable,
    UploadExcel,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      showDialog: false,
      leftView: '',
      funds: [],
      editObject: null,
      editTypeObject: null,
      func: treeToArray,
      expandAll: false,
      editParent: null,
      args: [null, null, 'timeLine'],
      departmentTree: [],
      years: [],
      selectedYear: '',
      departmentLevel: 0,
      currentLevel: 99,
      isEnableYear: false,
      importDialog: false,
      loading: false,
      exportFileName: 'department_list',

      langKey: 'assistance.department.label.',
      tableColumns: [],

      preferences: {
        filters: {
          year: '',
          currentLevel: 99,
          isEnableYear: false,
          expandedList: '',
        },
      },
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    expandLevel(level) {
      if (level) {
        this.$refs.TreeTable.showLevel(level)
        this.preferences.filters.currentLevel = level
        this.preferences.filters.expandedList = ''
      } else {
        this.$refs.TreeTable.setExpandItem(this.preferences.filters.expandedList)
      }
    },
    handleMaxLevel() {
      this.$nextTick(() => {
        this.departmentLevel = this.$refs.TreeTable.getMaxLevel()
      })
    },
    enableYear() {
      this.preferences.filters.isEnableYear = !this.preferences.filters.isEnableYear
      this.fetchTree()
    },
    onAdd(scope) {
      this.editObject = null
      this.editParent = scope && scope.row
      this.leftView = 'add'
    },
    onAddType(scope) {
      this.editTypeObject = null
      this.editParent = scope && scope.row
      this.leftView = 'addDepartmentType'
    },
    onEdit(scope) {
      this.editObject = scope.row
      this.leftView = 'edit'
    },
    onEditType(scope) {
      this.editTypeObject = scope.row
      this.leftView = 'editDepartmentType'
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const department_id = scope.row.department_id
          return new Promise((resolve, reject) => {
            deleteDepartment(department_id)
              .then(res => {
                if (this.editObject && this.editObject.department_id === department_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    onDeleteType(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${
          this.language === 'en' ? scope.row.name_en : scope.row.name_cn
        }` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const dept_type_id = scope.row.dept_type_id
          return new Promise((resolve, reject) => {
            deleteDepartmentType(dept_type_id)
              .then(res => {
                if (this.editTypeObject && this.editTypeObject.dept_type_id === dept_type_id) {
                  this.onViewCancel()
                }
                this.$refs.TreeTable.delete(scope.row)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchYears()
        .then(res => {
          this.years = res
        })
        // 加載偏好設置
        .then(this.loadUserPreference)
        .then(() => {
          if (!this.preferences.filters.year) {
            this.preferences.filters.year =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          } else {
            if (this.years && this.years.length > 0) {
              let bool = false
              this.years.forEach(i => {
                if (i.fy_code === this.preferences.filters.year) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.year = this.years[0].fy_code
              }
            }
          }
        })
        .then(() => {
          // 獲取部門樹
          return getDepartmentsTree(
            this.preferences.filters.isEnableYear ? this.preferences.filters.year : '',
          )
        })
        .then(res => {
          this.departmentTree = res
          // 處理樹的等級
          this.handleMaxLevel()
          this.$nextTick(() => {
            // 樹節點展開狀態
            this.expandLevel(this.preferences.filters.currentLevel)
          })
        })
        .catch(() => {})
    },
    fetchTree() {
      this.leftView = null
      this.editObject = null
      this.editParent = null
      this.editTypeObject = null
      getDepartmentsTree(
        this.preferences.filters.isEnableYear ? this.preferences.filters.year : '',
      ).then(res => {
        this.departmentTree = res
        this.handleMaxLevel()
        this.$nextTick(() => {
          this.expandLevel(this.preferences.filters.currentLevel)
        })
      })
    },
    onViewCancel(update) {
      this.editObject = null
      this.leftView = null
      if (update) {
        this.fetchTree()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      this.loading = true
      exportDepartments()
        .then(res => exportExcel(res, this.exportFileName))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onImport({ results, header }) {
      this.loading = true
      importExcel(this, importDepartments, results, header)
        .then(() => this.fetchTree())
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.filter {
  .year {
    width: 110px;
  }
  .svg-icon {
    cursor: pointer;
    /*font-size: 17px!important;*/
    width: 1.5em;
    height: 1.5em;
    color: #707070;
    margin: 0 2px;
    vertical-align: middle;
  }
  .selectLevel {
    color: #707070;
    border: 1px solid #707070;
    margin: 1px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    width: 20px;
    line-height: 17px;
    display: inline-grid;
    vertical-align: middle;
    &:hover {
      color: #68afff;
      border: 1px solid #68afff;
    }
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.tree-table {
  .operation_icon {
    .svg-icon {
      cursor: pointer;
      font-size: 14px !important;
      color: #707070;
      margin: 0 10px;
    }
    .svg-icon.no-cursor {
      cursor: auto;
    }
  }
}
</style>
