<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <div slot="pane-right-filters" class="filter">
        <!-- 撥款 -->
        <el-select
          v-model="preferences.filters.selectedFundType"
          class="fund"
          @change="fetchCheque"
        >
          <el-option :label="allFund.label" :value="allFund.value" />
          <el-option
            v-for="item in fundTypes"
            :key="item.value"
            :label="item.fund_name_cn"
            :value="item.fund_id"
          />
        </el-select>
        <!-- 類別 -->
        <el-select
          v-model="preferences.filters.selectedVoucherTypeCode"
          class="voucher-type"
          @change="loadChequeBooks"
        >
          <el-option :label="allVoucherType.label" :value="allVoucherType.value" />
          <el-option
            v-for="item in voucherTypes"
            :key="item.voucher_type_id"
            :label="item.vt_description_cn"
            :value="item.vt_code"
          />
        </el-select>
        <!-- 會計週期 -->
        <el-select v-model="preferences.filters.selectedYear" class="year" @change="fetchCheque">
          <!--          <el-option-->
          <!--            :label="allYear.label"-->
          <!--            :value="allYear.value"/>-->
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>
      </div>
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="icon add"
            @click="onAddCheque"
          >
            <svg-icon icon-class="add" class="action-icon" />
          </div>
          <!-- 按鈕 -->
          <!-- <div v-if="hasPermission_Input" class="icon import" @click="importDialog = true">
            <svg-icon icon-class="import"/>
          </div> -->
          <!--  -->
          <!-- <div v-if="hasPermission_Output" class="icon export" @click="onExport">
            <svg-icon icon-class="export"/>
          </div> -->
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :data="cheques"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <template slot="actions" slot-scope="{ scope }" align="left" header-align="left">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEditCheque(scope)" />
              <i v-if="hasPermission_Delete" class="el-icon-close" @click="onDeleteCheque(scope)" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-cheque="editCheque"
        :default-selected-fund-type="preferences.filters.selectedFundType"
        :default-selected-voucher-type-code="preferences.filters.selectedVoucherTypeCode"
        :default-selected-year="preferences.filters.selectedYear"
        :fy-code="preferences.filters.selectedYear"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
    <!-- import 對話框 -->
    <el-dialog
      v-loading="loading"
      :title="$t('file.excelImport')"
      :visible.sync="importDialog"
      class="upload-dialog"
      width="450px"
    >
      <UploadExcel :on-success="onImport" :on-template="onExport" />
    </el-dialog>
  </div>
</template>

<script>
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'
import UploadExcel from '@/components/UploadExcel/index'
import addPage from './add'
import { deleteChequeBook, fetchChequeBooks } from '@/api/assistance/chequeBook'
import { fetchFunds } from '@/api/master/funds'
import { fetchYears } from '@/api/master/years'
import { fetchVoucherTypes } from '@/api/master/voucherType'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

import mixinPermission from '@/views/mixins/permission'
import loadPreferences from '@/views/mixins/loadPreferences'
import { mapGetters } from 'vuex'

export default {
  name: 'AssistanceChequeBookIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    UploadExcel,
    addPage,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      showDialog: false,
      importDialog: false,
      loading: false,
      leftView: '',
      cheques: [],
      editCheque: null,
      years: [],
      fundTypes: [],
      voucherTypes: [],
      selectedVoucherType: '',
      langKey: 'assistance.chequeBook.label.',
      tableColumns: ['fund_id', 'vt_code', 'chqbk_code', 'chqbk_from', 'chqbk_to'],
      preferences: {
        filters: {
          selectedYear: '',
          selectedFundType: '',
          selectedVoucherTypeCode: '',
        },
      },
      childPreferences: ['selectedG'],
    }
  },
  computed: {
    ...mapGetters(['language']),
    allFund() {
      return {
        label: this.$t('master.fund.all_fund'),
        value: '',
      }
    },
    allVoucherType() {
      return {
        label: this.$t('assistance.chequeBook.allVoucherType'),
        value: '',
      }
    },
    allYear() {
      return {
        label: this.$t('master.year.all_year'),
        value: '',
      }
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onAddCheque() {
      this.editCheque = null
      this.leftView = 'add'
    },
    onEditCheque(scope) {
      this.editCheque = scope.row
      this.leftView = 'edit'
    },
    onDeleteCheque(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${scope.row.chqbk_code}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const cheque_book_id = scope.row.cheque_book_id
          return new Promise((resolve, reject) => {
            deleteChequeBook(cheque_book_id)
              .then(res => {
                if (this.editCheque && this.editCheque.cheque_book_id === cheque_book_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.fundTypes = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYear === '') {
            this.preferences.filters.selectedYear =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          } else {
            if (this.years && this.years.length > 0) {
              let bool = false
              this.years.forEach(i => {
                if (i.fy_code === this.preferences.filters.selectedYear) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedYear = this.years[0].fy_code
              }
            }
          }

          if (
            this.fundTypes &&
            this.fundTypes.length > 0 &&
            this.preferences.filters.selectedFundType !== ''
          ) {
            let bool = false
            this.fundTypes.forEach(i => {
              if (i.fund_id === this.preferences.filters.selectedFundType) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedFundType = this.fundTypes[0].fund_id
            }
          }
        })
        .then(() =>
          fetchVoucherTypes({
            fund_id: this.preferences.filters.selectedFundType,
            vt_category: 'P', // 只關聯付款傳票
            fy_code: this.preferences.filters.selectedYear,
          }),
        )
        .then(res => {
          this.voucherTypes = res
        })
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.voucherTypes &&
            this.voucherTypes.length > 0 &&
            this.preferences.filters.selectedVoucherTypeCode !== ''
          ) {
            let bool = false
            this.voucherTypes.forEach(i => {
              if (i.vt_code === this.preferences.filters.selectedVoucherTypeCode) {
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedVoucherTypeCode = this.voucherTypes[0].vt_code
            }
          }
        })
        .then(() =>
          fetchChequeBooks({
            fund_id: this.preferences.filters.selectedFundType,
            fy_code: this.preferences.filters.selectedYear,
            vt_code: this.preferences.filters.selectedVoucherTypeCode,
          }),
        )
        .then(res => {
          this.cheques = res
        })
    },
    fetchCheque() {
      this.leftView = null
      fetchVoucherTypes({
        fund_id: this.preferences.filters.selectedFundType,
        vt_category: 'P', // 只關聯付款傳票
        fy_code: this.preferences.filters.selectedYear,
      })
        .then(res => {
          this.voucherTypes = res
          // this.selectedVoucherType = res && res.length > 0 ? res[0].vt_description_cn : ''
          // this.selectedVoucherTypeCode = res && res.length > 0 ? res[0].vt_code : ''
          this.selectedVoucherType = ''
          this.preferences.filters.selectedVoucherTypeCode = ''
        })
        .then(this.loadChequeBooks)
    },
    loadChequeBooks() {
      fetchChequeBooks({
        fund_id: this.preferences.filters.selectedFundType,
        fy_code: this.preferences.filters.selectedYear,
        vt_code: this.preferences.filters.selectedVoucherTypeCode,
      }).then(res => {
        this.cheques = res
      })
    },
    onViewCancel(update) {
      this.editCheque = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
    onExport() {
      if (this.loading) {
        return
      }
      // this.loading = true
      // exportStaffs()
      //   .then(res => exportExcel(res, this.exportFileName))
      //   .then(() => {
      //     this.$message.success(this.$t('file.exportSuccess'))
      //   }).catch(() => {
      //     this.$message.error(this.$t('file.exportError'))
      //   }).finally(() => {
      //     this.loading = false
      //   })
    },
    onImport({ results, header }) {
      // this.loading = true
      // importExcel(this, importStaffs, results, header)
      //   .then(() => this.fetchTree())
      //   .catch(() => {})
      //   .finally(() => {
      //     this.loading = false
      //     this.importDialog = false
      //   })
    },
    onChangeExpanded(listStr) {
      this.preferences.filters.expandedList = listStr
      this.preferences.filters.currentLevel = 0
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}

.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.voucher-type {
  width: 250px;
}
.year {
  width: 120px;
}
.fund {
  width: 120px;
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
