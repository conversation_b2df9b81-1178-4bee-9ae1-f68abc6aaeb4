import request from '@/utils/request'

export function createPaper(ps_code, ps_name_cn, ps_name_en, width, height, orientation) {
  return request({
    url: '/paper-sets/actions/create',
    method: 'post',
    data: {
      ps_code,
      ps_name_cn,
      ps_name_en,
      width,
      height,
      orientation,
    },
  })
}

export function editPaper(
  paper_set_id,
  ps_code,
  ps_name_cn,
  ps_name_en,
  width,
  height,
  orientation,
) {
  return request({
    url: '/paper-sets/actions/update',
    method: 'post',
    data: {
      paper_set_id,
      ps_code,
      ps_name_cn,
      ps_name_en,
      width,
      height,
      orientation,
    },
  })
}

// 查詢所有紙設定
export function fetchPaper() {
  return request({
    url: '/paper-sets',
    method: 'get',
  })
}

export function deletePaper(paper_set_id) {
  return request({
    url: '/paper-sets/actions/delete',
    method: 'post',
    data: {
      paper_set_id,
    },
  })
}

export function paperSetsImport(data_json) {
  return request({
    url: '/paper-sets/actions/import',
    responseType: 'blob',
    method: 'post',
    data: {
      data_json,
    },
  })
}

export function paperSetsExport() {
  return request({
    url: '/paper-sets/actions/export',
    method: 'get',
  })
}
