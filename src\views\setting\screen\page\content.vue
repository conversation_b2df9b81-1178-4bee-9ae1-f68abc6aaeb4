<template>
  <div class="setting-basic">
    <right>
      <el-main slot="content">
        <el-form ref="form" :model="form" class="form" label-width="120px">
          <el-form-item :label="$t('setting.basic.content_background_color')">
            <el-color-picker v-model="form.content_background_color" />
          </el-form-item>
          <el-form-item :label="$t('setting.basic.content_line_height')">
            <el-input-number v-model="form.content_line_height" :min="13" :max="23" />
          </el-form-item>
          <!--          <el-form-item :label="$t('setting.basic.date_format')">-->
          <!--            <el-select v-model="form.date_format">-->
          <!--              <el-option-->
          <!--                v-for="item in format_list"-->
          <!--                :key="item"-->
          <!--                :label="formatter(item)"-->
          <!--                :value="item"-->
          <!--              />-->

          <!--            </el-select>-->
          <!--          </el-form-item>-->
          <el-row class="actions">
            <el-button size="mini" type="info" @click="onFetch(0)">
              {{ $t('button.loadDefault') }}
            </el-button>
            <el-button size="mini" type="info" @click="onSave(0)">
              {{ $t('button.saveDefault') }}
            </el-button>
            <el-button size="mini" type="danger" @click="onFetch(1)">
              {{ $t('button.reset') }}
            </el-button>
            <el-button size="mini" type="primary" @click="onSave(1)">
              {{ $t('button.update') }}
            </el-button>
          </el-row>
        </el-form>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'

// import dateUtil from '@/utils/date'

export default {
  name: 'SettingScreenContent',
  components: {
    right,
  },
  mixins: [common],
  data() {
    return {
      // format_list: [
      //   'dd/MM/yy',
      //   'dd/MM/yyyy',
      //   'yy/MM/dd',
      //   'yyyy/MM/dd',
      // ],
      form: {
        content_background_color: '#e8f5fe',
        content_line_height: 23,
        date_format: 'dd/MM/yy',
      },
      // currentDate: new Date(2018, 11, 30),
      ss_code: 'screen_basic_content',
      ss_type: 'content',
    }
  },
  computed: {
    // deteDemo() {
    //   const s = dateUtil.format(new Date(), this.form.date_format)
    //   return s
    // }
  },
  created() {},
  methods: {
    // formatter(val) {
    //   return dateUtil.format(this.currentDate, val)
    // }
  },
}
</script>
