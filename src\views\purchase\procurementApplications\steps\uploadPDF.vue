<template>
  <el-dialog
    :visible.sync="showDialog"
    :title="t('uploadQuotes')"
    width="500px"
    @open="onOpen"
    @close="onClose"
  >
    <div v-loading="loading" class="upload-area">
      <div class="upload-pdf">
        <input
          ref="file-upload-input"
          class="file-upload-input"
          type="file"
          multiple
          accept=".pdf"
          @change="handleChange"
        >
        <div
          v-if="!isReadonly"
          class="drop"
          @drop="handleDrop"
          @dragover="handleDragover"
          @dragenter="handleDragover"
        >
          {{ $t('message.dropPdf') }}
          <el-button style="margin-left: 16px" size="mini" type="primary" @click="handleUpload">
            {{ $t('file.browse') }}
          </el-button>
        </div>
        <div class="upload-tips">
          {{ $t('file.pleaseSelectPdfFile') }}
        </div>
        <el-table
          v-if="showTable"
          :data="fileList"
          class="file-table"
          height="calc(100% - 80px - 30px)"
        >
          <el-table-column :label="t('filename')" prop="name">
            <template v-if="scope && scope.row" slot-scope="scope">
              <span :title="scope.row.name" class="file-name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('status')" width="80" prop="status">
            <template v-if="scope && scope.row" slot-scope="scope">
              <el-tag :type="statusObj[scope.row.status].type">
                {{ statusObj[scope.row.status].label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('action')" width="80" align="center">
            <template v-if="scope && scope.row" slot-scope="scope">
              <i v-if="!isReadonly" class="el-icon-delete action-icon" @click="onDelete(scope)" />
              <i class="el-icon-download action-icon" @click="onDownload(scope)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        v-if="!isReadonly"
        :loading="loading"
        size="mini"
        type="primary"
        @click="onUpload"
      >{{ $t('button.upload') }}</el-button>
      <el-button :disabled="loading" size="mini" @click="showDialog = false">{{
        $t('button.cancel')
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
import { saveAs } from 'file-saver'
import {
  deleteProcurementApplicationsFile,
  uploadProcurementApplicationsFile,
} from '@/api/purchase/procurementApplications'

export default {
  name: 'UploadPDF',
  props: {
    id: {
      type: [Number, String],
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      required: true,
    },
    quotations: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      langKey: 'purchase.daily.procurementApplications.',
      fileList: [],
      reshow: false,
      showTable: true,
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo']),
    showDialog: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    statusObj() {
      return {
        success: {
          label: this.t('uploadStatus.success'),
          type: 'success',
        },
        ready: {
          label: this.t('uploadStatus.ready'),
          type: '',
        },
        error: {
          label: this.t('uploadStatus.error'),
          type: 'danger',
        },
      }
    },
  },
  methods: {
    onDelete(scope) {
      const item = scope.row
      if (item.hasOwnProperty('pro_attachment_id')) {
        this.$confirm(
          `${this.$t('confirm.deleteConfirm')}: ${item.name}` + '?',
          this.$t('confirm.warningTitle'),
          {
            confirmButtonText: this.$t('confirm.confirmButtonText'),
            cancelButtonText: this.$t('confirm.cancelButtonText'),
            type: 'warning',
          },
        )
          .then(() => {
            this.reshow = true
            const pro_attachment_id = scope.row.pro_attachment_id
            return new Promise((resolve, reject) => {
              deleteProcurementApplicationsFile(pro_attachment_id)
                .then(res => {
                  this.fileList.splice(scope.$index, 1)
                  resolve(res)
                })
                .catch(err => {
                  reject(err)
                })
            })
          })
          .then(() => {
            // this.fetchData()
            this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
          })
      } else {
        this.fileList.splice(scope.$index, 1)
      }
    },
    handleDrop(e) {
      console.log('handleDrop', e)
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files

      e.dataTransfer.value = ''

      this.addFiles(files)
      e.stopPropagation()
      e.preventDefault()
    },
    handleDragover(e) {
      console.log('handleDragover', e)
      e.stopPropagation()
      e.preventDefault()
      e.dataTransfer.dropEffect = 'copy'
    },
    handleUpload(e) {
      console.log('handleUpload', e)
      this.$refs['file-upload-input'].click()
    },
    handleChange(e) {
      const files = e.target.files
      this.addFiles(files)
      e.target.value = ''
    },
    addFiles(files) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        if (this.isPdf(file)) {
          file.status = 'ready'
          this.fileList.push(file)
        }
      }
    },
    isPdf(file) {
      return /\.pdf$/.test(file.name.toLowerCase())
    },
    async onUpload() {
      const errorList = []
      this.loading = true
      for (let i = 0; i < this.fileList.length; i++) {
        const file = this.fileList[i]
        if (file.pro_attachment_id) {
          continue
        }
        try {
          const res = await this.handleUploadFile(file)
          if (res.hasOwnProperty('pro_attachment_id')) {
            this.fileList[i]['pro_attachment_id'] = res.pro_attachment_id
            this.$set(this.fileList[i], 'pro_attachment_id', res.pro_attachment_id)
            this.$set(this.fileList[i], 'status', 'success')
            this.$set(this.fileList[i], 'attachment_path', res.attachment_path)
            this.$set(this.fileList[i], 'attachment_type', res.attachment_type)
          }
          // pro_attachment_id: 3
          // system: "PC"
          // attachment_type: "pdf"
          // attachment_name: "04a895da-3979-469f-96a3-ea2d758e9eea.pdf"
          // attachment_path: "PC/20191104/740c781cfeb011e981bc1c1b0dd9b364/04a895da-3979-469f-96a3-ea2d758e9eea.pdf"
          // attachment_size: "461.78"
          // created_at: "2019-11-04 11:09:02"
          // updated_at: "2019-11-04 11:09:02"
          // pro_attachment_id: 2
          // pro_apply_supplier_id: "14"
        } catch (e) {
          this.$set(this.fileList[i], 'status', 'error')
          errorList.push(file)
          console.log(e)
        }
      }
      this.tableReshow()
      if (errorList.length > 0) {
        this.$message.error(this.t('fileUploadFailing', { num: errorList.length }))
        //  `${errorList.length} 個文件上傳失敗`
      } else {
        this.$message.success(this.$t(`message.success`))
      }
      this.reshow = true
      this.loading = false
    },
    async handleUploadFile(file) {
      try {
        const pro_apply_supplier_id = this.id
        const res = await uploadProcurementApplicationsFile({
          pro_apply_supplier_id,
          file,
        })
        console.log(res)
        return res
      } catch (e) {
        return Promise.reject(e)
      }
    },
    onOpen() {
      this.reshow = false
      const arr = []
      this.quotations.forEach(item => {
        arr.push({
          pro_attachment_id: item.pro_attachment_id,
          attachment_path: item.attachment_path,
          name: item.attachment_name,
          attachment_type: item.attachment_type,
          status: 'success',
        })
      })
      this.fileList = arr
    },
    onClose() {
      if (this.reshow) {
        this.$emit('reshow')
        this.reshow = false
      }
    },
    tableReshow() {
      this.showTable = false
      this.$nextTick(() => {
        this.showTable = true
      })
    },
    onDownload(scope) {
      const file = scope.row
      const { remoteFilePath } = this.remoteServerInfo
      const url = remoteFilePath + file.attachment_path
      saveAs(url, file.name)
      // FileSaver.saveAs(url, file.name)
    },
  },
}
</script>

<style lang="scss" scoped>
.upload-pdf {
  max-width: 800px;
}
.upload-wrapper {
  margin: 0;
  width: auto;
}
.file-upload-input {
  display: none;
  z-index: -9999;
}
.drop {
  border: 2px dashed #bbb;
  max-width: 400px;
  height: 80px;
  line-height: 160px;
  margin: 0 auto;
  font-size: 20px !important;
  border-radius: 5px;
  text-align: center;
  color: #bbb;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    border-color: #428bca;
  }
}
.upload-tips {
  align-items: center;
  font-weight: bold;

  max-width: 400px;
  margin: 8px auto;
}
.tem-link {
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: underline;
  color: rgb(42, 125, 207);
  &:hover {
    color: rgb(20, 216, 86);
  }
}
.file-table {
  width: 90%;
  margin: 15px auto;
  .action-icon {
    padding: 0 5px;
  }
  .file-name {
    white-space: nowrap;
  }
}

.upload-area {
  position: relative;
  height: 97%;
  .upload-pdf {
    height: 100%;

    .file-table {
      /*height: calc(100% - 80px - 30px);*/
    }
  }
}
</style>
