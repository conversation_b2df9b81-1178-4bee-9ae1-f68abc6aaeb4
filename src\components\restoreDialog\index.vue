<template>
  <!-- import 對話框 -->
  <el-dialog
    v-loading="vLoading"
    :title="title || $t('file.excelImport')"
    :visible.sync="dialogVisible"
    class="dialog"
    width="550px"
    @open="onInit"
  >
    <div v-if="$slots.default" class="filters">
      <slot />
    </div>
    <div v-show="status === 1">
      <div>{{ $t('setting.dataBackup.backupTips') }}</div>
      <div style="text-align: center; padding: 40px">
        <el-button size="mini" type="primary" @click="onBackup">
          {{ $t('setting.dataBackup.backup') }}
        </el-button>
      </div>
    </div>
    <manual-control-upload-excel
      v-show="status === 2"
      ref="manualControlUploadExcel"
      :loading="vLoading"
      :on-cancel="onCancel"
      :msg="msg"
      :file-type-tips="fileTypeTips"
      :supports-file-type-tips="supportsFileTypeTips"
      :file-suffix="fileSuffix"
      :excel-file="false"
      :multiple="multiple"
      :show-actions="false"
      @change="onChange"
    />

    <span slot="footer" class="dialog-footer">
      <el-button v-if="status === 2" size="mini" type="primary" @click="onSubmit">{{
        $t('button.confirm')
      }}</el-button>
      <el-button size="mini" type="danger" @click="onCancel">{{ $t('button.cancel') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
// import UploadExcel from '@/components/UploadExcel/index'
import manualControlUploadExcel from '@/components/UploadExcel/manualControl.vue'
export default {
  name: 'RestoreDialog',
  components: {
    manualControlUploadExcel,
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    status: {
      type: Number,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    msg: {
      type: String,
      default: '',
    },
    excelFile: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    fileSuffix: {
      type: String,
      default: '.zip',
    },
    fileTypeTips: {
      type: String,
      default: '',
    },
    supportsFileTypeTips: {
      type: String,
      default: '',
    },
    onSubmit: {
      type: Function,
      default: async() => true,
    },
    onBackup: {
      type: Function,
      default: async() => true,
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    vLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      },
    },
  },
  methods: {
    onInit() {
      if (this.$refs['manualControlUploadExcel']) {
        this.$refs['manualControlUploadExcel'].init()
      }
    },
    onCancel() {
      this.$emit('update:visible', false)
    },
    onChange(file) {
      this.$emit('change', file)
    },
  },
}
</script>

<style lang="scss" scoped>
.filters {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 0 20px 0;
}
.dialog {
  /deep/ {
    .el-dialog__body {
      height: auto !important;
    }
  }
}
</style>
