import request from '@/utils/request'

/**
 * 獲取自定義報表列表
 */
export function fetchCustomReports() {
  return request({
    url: '/custom-reports',
    method: 'get',
  })
}

/**
 * 詳情
 */
export function fetchCustomReportDetail(custom_report_id) {
  return request({
    url: `/custom-reports/actions/inquire`,
    method: 'get',
    params: {
      custom_report_id,
    },
  })
}

export default {
  fetchCustomReports,
  fetchCustomReportDetail,
}

/**
 * 新增自定義報表
 */
export function createCustomReport({
  report_name,
  section_data,
}) {
  return request({
    url: '/custom-reports/actions/create',
    method: 'post',
    data: {
      report_name,
      section_data,
    },
  })
}

/**
 * 更新自定義報表
 */
export function updateCustomReport({
  custom_report_id,
  report_name,
  section_data,
}) {
  return request({
    url: '/custom-reports/actions/update',
    method: 'post',
    data: {
      custom_report_id,
      report_name,
      section_data,
    },
  })
}
/**
 * 刪除自定義報表
 */
export function deleteCustomReport(custom_report_id) {
  return request({
    url: `/custom-reports/actions/delete`,
    method: 'post',
    data: {
      custom_report_id,
    },
  })
}

/**
 * 導出自定義報表
 */
export function exportCustomReport({ end_date, start_date, fy_code }) {
  return request({
    url: `/custom-reports/actions/export`,
    method: 'get',
    responseType: 'blob',
    params: {
      end_date,
      start_date,
      fy_code,
    },
  })
}
