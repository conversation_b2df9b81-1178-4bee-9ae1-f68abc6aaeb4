<template>
  <el-select
    v-if="hackReload"
    ref="selectTree"
    v-model="currentItem"
    v-bind="$attrs"
    :value-key="props.code"
    :placeholder="placeholder"
    class="e-select-tree"
    popper-class="e-select-tree-popper"
    @change="onChange"
    @clear="clearHandle"
    @focus="onFocus"
  >
    <!--
      v-on="$listeners"
      -->
    <slot :data="treeOptions">
      <el-option
        v-if="showAllOption"
        :label="allOptionText"
        :value="{
          [props.value]: '',
        }"
      />
      <el-option
        v-for="item in treeOptions"
        :key="item[props.code]"
        :label="
          (!item.children && item[props.code] ? `[${item[props.code]}] ` : '') + item[props.label]
        "
        :value="item"
      >
        <span>{{ repeat('&nbsp;', item.level * 4 + (item.children ? 0 : 2)) }}</span>
        <i v-if="item.children" class="el-icon-caret-bottom" />
        <span v-if="!item.children && item[props.code]">[{{ item[props.code] }}]&nbsp;</span>
        <span>{{ item[props.label] }}</span>
      </el-option>
    </slot>
  </el-select>
</template>
<script>
export default {
  name: 'ElTreeSelect',
  props: {
    options: {
      type: Array, // 必须是树形结构的对象数组
      default: () => {
        return []
      },
    },
    props: {
      type: Object,
      default: () => {
        return {
          value: 'value', // ID字段名
          label: 'label', // 显示名称
          code: 'code', // code名称
          group_id: 'fund_id',
          value_id: 'account_id',
          children: 'children', // 子级字段名
        }
      },
    },
    value: {
      type: [Number, String],
      default: '',
    },
    code: {
      type: [Number, String],
      default: () => {
        return null
      },
    },
    groupId: {
      type: [Number, String],
      default: () => {
        return null
      },
    },
    showAllOption: {
      type: Boolean,
      default: false,
    },
    allOptionText: {
      type: String,
      default: 'All',
    },
    selectGroup: {
      type: Boolean,
      default: false,
    },
    conversion: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: () => {
        return true
      },
    },
    placeholder: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      currentValue: this.value, // 初始值
      currentGroupId: this.groupId, // 初始值
      currentItem: { [this.props.value]: '' },
      treeOptions: [],
      currentSelectGroup: this.selectGroup,

      hackReload: true,
    }
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        this.treeOptions = this.conversion ? this.dataFormat(val) : val
        this.setSelected()
      },
    },
    currentItem: {
      deep: true,
      handler(val) {
        this.$emit('changeItem', val)
      },
    },
    // 提交給父組件
    // currentValue(val) {
    //   this.$emit('input', val)
    // },
    // 提交給父組件
    // currentSelectGroup(val) {
    //   this.$emit('update:selectGroup', val)
    // },
    // 綁定新的選項
    value(val) {
      this.currentValue = val
      this.setSelected()
    },
    // 綁定新的選項
    selectGroup(val) {
      this.currentSelectGroup = val
    },
    // 綁定新的選項
    groupId(val) {
      this.currentGroupId = val
    },
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initHandle()
    // })
  },
  methods: {
    reload() {
      this.hackReload = false
      this.$nextTick(() => {
        this.hackReload = true
      })
    },
    // 初始化值
    initHandle() {
      this.treeOptions = this.conversion ? this.dataFormat(this.options) : this.options
      this.setSelected()
    },
    // 設置當前選中的項
    setSelected() {
      if (
        this.currentValue === '' ||
        this.currentValue === null ||
        this.currentValue === undefined
      ) {
        this.currentItem = null
      }
      if (!this.treeOptions.length) {
        return
      }
      for (let i = 0; i < this.treeOptions.length; i++) {
        const item = this.treeOptions[i]
        if (item[this.props.value] === this.currentValue) {
          this.currentItem = item
          return
        }
      }
      this.clearHandle()
    },
    // 清除选中
    clearHandle() {
      this.currentValue = ''
      this.currentItem = { [this.props.value]: '' }
    },
    dataFormat(data) {
      const newData = []
      for (let i = 0; i < data.length; i++) {
        newData.push(...this.toOption(data[i]))
      }
      // console.log(newData)
      return newData
    },
    toOption(item, level = 0) {
      if (!item) return []
      item.level = level
      const list = [item]
      const children = item[this.props.children]
      if (children && children.length) {
        for (const itemKey in children) {
          list.push(...this.toOption(children[itemKey], level + 1))
        }
      }
      return list
    },
    repeat(str, n) {
      return new Array(n + 1).join(str)
    },
    onChange(val) {
      const group_id = val[this.props.group_id] ? val[this.props.group_id] : '' //
      const selectGroup = !!group_id //
      const value = val[this.props.value] ? val[this.props.value] : '' //
      // const code = val[this.props.code] ? val[this.props.code] : ''//
      this.currentValue = value
      this.currentSelectGroup = selectGroup // 選擇的是否為“組”
      this.$emit('input', value)
      this.$emit('update:groupId', group_id)
      this.$emit('update:selectGroup', selectGroup)
      this.$emit('change', { value, selectGroup, group_id, ...val })
      // this.$forceUpdate()
      // this.reload()
    },
    onFocus() {
      this.$emit('focus')
    },
  },
}
</script>

<style lang="scss" rel="stylesheet/scss">
.e-select-tree-popper {
  .el-select-dropdown__item {
    line-height: 21px;
    height: 21px;
  }

  .el-select-dropdown__wrap.el-scrollbar__wrap {
    max-height: 60vh;
  }
}
</style>
