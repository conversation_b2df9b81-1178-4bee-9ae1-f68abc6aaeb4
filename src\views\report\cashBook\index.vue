<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div>
      <VBreadCrumb class="breadcrumb" />
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 銀行 -->
          <el-form-item :label="$t('filters.bank')">
            <el-select
              v-model="preferences.filters.selectedAcCode"
              class="bank"
              style="width: 300px"
            >
              <el-option
                v-for="item in bankList"
                :key="item.ac_code"
                :label="
                  `[${item.ac_code}] ` + item[language === 'en' ? 'ac_name_en' : 'ac_name_cn']
                "
                :value="item.ac_code"
              />
            </el-select>
          </el-form-item>

          <!-- 時期 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              type="daterange"-->
            <!--              value-format="yyyy-MM-dd"-->
            <!--              style="width: 250px"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>

          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <!--              @change="onChangeMonth"-->
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              type="primary"
              size="mini"
              class="action-button"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i class="edac-icon action-icon edac-icon-setting1" @click="onSetting" />
          <i
            v-if="hasPermission_Output"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
        </div>
      </div>
      <div class="cash-book-table">
        <b-table
          ref="table"
          v-loading="!loading && tableLoading"
          :style-columns="styleColumns"
          :amount-columns="amountColumns"
          :lang-key="langKey"
          :show-index="true"
          :show-actions="true"
          :actions-min-width="5"
          :show-checkbox="false"
          :default-top="230"
          :highlight-current-row="true"
          :sort-config="{
            trigger: 'cell',
            defaultSort: { field: 'vc_date', order: 'asc' },
            orders: ['asc', 'desc', null],
          }"
          :sum-text="$t('enquiry.contra.label.sumText')"
          :height="pageHeight - filtersHeight - 80"
          :footer-method="footerMethod"
          action-label=" "
          show-footer
          show-footer-overflow
          border
          highlight-hover-row
          highlight-hover-column
          @changeWidth="changeColumnWidth"
          @sort-change="sortChangeEvent"
        >
          <template v-if="$refs.table" slot="columns">
            <vxe-table-column
              v-for="item in columns"
              :key="item.ss_key"
              :title="$t(langKey + item.ss_key)"
              :align="item.alignment"
              :class-name="item.ss_key + ' mini-form'"
              :width="item.width"
              :property="$refs.table.column_property(item)"
              :field="$refs.table.column_property(item)"
              :column-key="item.ss_key"
              :params="{ key: item.ss_key }"
              :sort-method="$refs.table.sortAmountMethod(item.ss_key, item._gid)"
              sortable
            >
              <template v-if="scope && scope.row" slot-scope="scope">
                <span>{{
                  $refs.table.customFormatter(
                    item.ss_key,
                    scope.row[$refs.table.column_property(item)]
                  )
                }}</span>
              </template>
            </vxe-table-column>
          </template>
        </b-table>
      </div>

      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'
import { fetchCashOrBankLedgers } from '@/api/report/cashBook'
import { cashLedgersExport } from '@/api/report/excel'
import BTable from '@/components/BTable'
import ENumeric from '@/components/ENumeric'
import DateRange from '@/components/DateRange/index'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'

import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'
import { amountFormat, toDecimal } from '@/utils'

import dateUtil from '@/utils/date'
import { listenTo } from '@/utils/resizeListen'
import { exportExcel } from '@/utils/excel'
export default {
  name: 'ReportCashBookIndex',
  components: {
    DateRange,
    BTable,
    customStyle,
    ENumeric,
    VBreadCrumb,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, loadPrintoutSetting, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      bankList: [],
      chequeBooks: [],
      years: '',
      selectedYearId: '',
      tableData: [],
      footerData: [],
      data: [],
      monthList: [],
      langKey: 'report.cashBook.label.',
      tableColumns: [
        'vc_date',
        'vc_no',
        'ref',
        'vc_payee',
        'descr',
        'vc_method',
        'ac_name_',
        'break_down',
        'amount_dr',
        'amount_cr',
        'amount_balance',
      ],
      amountColumns: ['break_down', 'amount_dr', 'amount_cr', 'amount_balance'],
      preferences: {
        filters: {
          selectedAcCode: '',
          selectedYearCode: '',
          selectedMonth: '',
          begin_date: '',
          end_date: '',
          // date_range: []
        },
      },
      childPreferences: ['selectedMonth'],
      showYearPicker: false,
      periods: [],
      ps_code: 'pdfcashbookcash',

      yearLastDate: dateUtil.format(new Date(), 'yyyy-MM-dd'),
      yearStartDate: dateUtil.format(new Date(), 'yyyy-MM-dd'),

      filtersResizeListen: {},
      filtersHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school', 'remoteServerInfo']),
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
  },
  watch: {
    language: {
      handler() {
        this.footerData = []
        this.$nextTick(() => {
          this.footerData = [this.summaryMethod({ columns: this.columns, data: this.tableData })]
          this.$refs.table.updateFooter()
        })
      },
      deep: true,
    },
  },
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
    })
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({ ac_bank: 'C,S,F,P' }))
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.preferences.filters.selectedAcCode === '') {
            this.preferences.filters.selectedAcCode =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].ac_code : ''
          } else {
            if (this.bankList && this.bankList.length > 0) {
              let bool = false
              this.bankList.forEach(i => {
                if (i.ac_code === this.preferences.filters.selectedAcCode) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
              }
            }
          }
          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth()
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, '1')
        const lastDateObj = new Date(year, month + 1, '0')

        const ac_code = this.preferences.filters.selectedAcCode
        // let begin_date, end_date
        // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
        //   begin_date = dateUtil.format(beginDateObj)
        //   end_date = dateUtil.format(lastDateObj)
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        // } else {
        //   begin_date = this.preferences.filters.date_range[0]
        //   end_date = this.preferences.filters.date_range[1]
        // }
        if (!ac_code) {
          this.$message.error(this.$t('message.bankInvalid'))
          this.loading = false
          return false
        }
        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        if (!begin_date || !end_date) {
          // reject()
          begin_date = dateUtil.format(beginDateObj)
          end_date = dateUtil.format(lastDateObj)
          // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
          this.preferences.filters.begin_date = begin_date
          this.preferences.filters.end_date = end_date
        }
        if (begin_date instanceof Date) {
          begin_date = dateUtil.format(begin_date)
        }
        if (end_date instanceof Date) {
          end_date = dateUtil.format(end_date)
        }
        this.loading = true
        fetchCashOrBankLedgers({
          ac_code,
          begin_date,
          end_date,
        })
          .then(res => {
            // this.tableData = this.formatData(res)
            const tableData = this.formatData(res)

            this.footerData = [this.summaryMethod({ columns: this.columns, data: tableData })]
            this.$refs.table && this.$refs.table.methods('loadData', tableData)
            this.tableData = tableData
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatData(data) {
      let vn = ''
      let gid = 0
      const newData = []
      data.forEach(item => {
        const newItem = Object.assign({}, item)
        if (newItem.vc_no !== vn && newItem.vc_no !== null) {
          gid++
          vn = newItem.vc_no
        }
        newItem.vc_no = vn
        newItem._gid = gid
        newData.push(newItem)
      })
      return newData
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    onChangeMonth(val) {
      if (val === '') {
        // 全年
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateFormatStr)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateFormatStr)
        // this.$set(this.preferences.filters, 'date_range', [sDate, eDate])
        // this.preferences.filters.date_range = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      // beginDate
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d)

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d)
      // this.preferences.filters.date_range = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
    },
    summaryMethod({ columns, data }) {
      const sums = []
      columns.forEach(async(column, index) => {
        const key = column.ss_key || ''
        switch (key) {
          case 'descr': {
            sums.push('C/F')
            break
          }
          case 'amount_cr': {
            let sum_cr = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[key])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum_cr = toDecimal(sum_cr - n)
                }
              }
            })
            sums.push(amountFormat(sum_cr))
            break
          }
          case 'amount_dr': {
            let sum_dr = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[key])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum_dr = toDecimal(sum_dr + n)
                }
              }
            })
            sums.push('+' + amountFormat(sum_dr))
            break
          }
          case 'amount_balance': {
            let sum = 0
            let lastVal = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[column.property])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum = sum + n
                }
              }
              if (typeof item.amount_balance === 'number') {
                lastVal = item.amount_balance
              }
            })
            // sums[index] = '= ' + amountFormat(lastVal)
            sums.push('= ' + amountFormat(lastVal))
            break
          }
          default:
            sums.push('')
            break
        }
      })
      return sums
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateFormatStr,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateFormatStr,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id

      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, '1')
      const lastDateObj = new Date(year, month + 1, '0')

      const ac_code = this.preferences.filters.selectedAcCode
      // let begin_date, end_date
      // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
      //   begin_date = dateUtil.format(beginDateObj)
      //   end_date = dateUtil.format(lastDateObj)
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      // } else {
      //   begin_date = this.preferences.filters.date_range[0]
      //   end_date = this.preferences.filters.date_range[1]
      // }
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!begin_date || !end_date) {
        // reject()
        begin_date = dateUtil.format(beginDateObj)
        end_date = dateUtil.format(lastDateObj)
        // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        this.preferences.filters.begin_date = begin_date
        this.preferences.filters.end_date = end_date
      }
      if (!user_id || !ac_code || !begin_date || !end_date) {
        // this.$message.error('')
        return
      }
      this.loading = true
      cashLedgersExport({ user_id, export_type, ac_code, begin_date, end_date })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    sortChangeEvent({ column, property, order }) {
      console.info(column, property, order)
    },
    sortNameMethod(a, b, key) {
      console.log(a, b, key)
      var v1 = (a.name || '').toLowerCase()
      var v2 = (b.name || '').toLowerCase()
      return v1 < v2 ? -1 : v1 > v2 ? 1 : 0
    },
    footerMethod() {
      return this.footerData
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.el-icon-date {
  cursor: pointer;
}
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
  }
  .cash-book-table {
    /*height: calc(100vh - 200px);*/

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
</style>
