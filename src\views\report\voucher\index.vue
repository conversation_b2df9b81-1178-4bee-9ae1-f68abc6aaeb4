<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container" @resize="resize">
    <div>
      <VBreadCrumb class="breadcrumb" />
      <div ref="filter" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 賬目 -->
          <el-form-item :label="$t('filters.fund')">
            <el-select v-model="preferences.filters.fund_id" class="fund" style="width: 110px">
              <el-option key="all" :label="$t('report.voucher.content.allFund')" value="" />
              <el-option
                v-for="item in funds"
                :key="item.fund_id"
                :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
                :value="item.fund_id"
              />
            </el-select>
          </el-form-item>
          <!-- 分類 -->
          <el-form-item :label="$t('report.voucher.label.category')">
            <el-select
              v-model="preferences.filters.vt_category"
              class="vt-category"
              style="width: 110px"
              @change="onChangeVoucherTypeCategory"
            >
              <el-option :label="$t('report.voucher.content.allCategory')" value="" />
              <el-option :label="$t('report.voucher.content.paymentVoucher')" value="P" />
              <el-option :label="$t('report.voucher.content.cashVoucher')" value="C" />
              <el-option :label="$t('report.voucher.content.receiptVoucher')" value="R" />
              <el-option :label="$t('report.voucher.content.ap')" value="A" />
              <el-option :label="$t('report.voucher.content.transferVoucher')" value="T" />
              <el-option :label="$t('report.voucher.content.journalVoucher')" value="J" />
            </el-select>
          </el-form-item>
          <!-- 賬目 -->
          <el-form-item :label="$t('report.voucher.label.voucherType')">
            <el-select v-model="preferences.filters.vt_code" class="fund" style="width: 200px">
              <el-option key="all" :label="$t('report.voucher.content.allVoucher')" value="" />
              <el-option
                v-for="item in voucherTypeList"
                :key="item.voucher_type_id"
                :label="language === 'en' ? item.vt_description_en : item.vt_description_cn"
                :value="item.vt_code"
              />
            </el-select>
          </el-form-item>

          <!-- 時期 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              type="daterange"-->
            <!--              value-format="yyyy-MM-dd"-->
            <!--              style="width: 250px"-->
            <!--              @change="onChangeDateRange"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>

          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
              @change="onChangeMonth"
            >
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              type="primary"
              size="mini"
              class="action-button"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
        </div>
      </div>
      <div class="cash-book-table">
        <BTable
          ref="table"
          v-loading="!loading && tableLoading"
          :data="tableData"
          :style-columns="styleColumns"
          :amount-columns="amountColumns"
          :lang-key="langKey"
          :show-index="true"
          :show-actions="true"
          :actions-min-width="5"
          :show-checkbox="false"
          :default-top="230"
          :height="pageHeight - filterHeight - 60"
          :highlight-current-row="true"
          :sum-text="$t(langKey + 'total')"
          :filter-class-function="handleTableRowClass"
          action-label=" "
          border
          @changeWidth="changeColumnWidth"
        />
      </div>

      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { getReportVoucher } from '@/api/report/index'
import ETable from '@/components/ETable'
import BTable from '@/components/BTable'
import SelectTree from '@/components/SelectTree'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'

import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'

import { toDecimal } from '@/utils'

import ENumeric from '@/components/ENumeric'
import dateUtil from '@/utils/date'
import { fetchFunds } from '@/api/master/funds'
import { fetchVoucherTypes } from '@/api/master/voucherType'
import { exportExcel } from '@/utils/excel'
import { vouchersExport } from '@/api/report/excel'
import DateRange from '@/components/DateRange/index'
import { listenTo } from '@/utils/resizeListen'
export default {
  name: 'ReportVoucherIndex',
  components: {
    DateRange,
    ETable,
    BTable,
    customStyle,
    ENumeric,
    VBreadCrumb,
    SelectTree,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, loadPrintoutSetting, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      years: '',
      selectedYearId: '',
      tableData: [],
      data: [],
      monthList: [],
      langKey: 'report.voucher.label.',
      tableColumns: [
        'vc_no',
        'vc_date',
        'tx_num',
        'ac_code',
        'ac_name_',
        'descr',
        'vc_payee',
        'ref',
        'st_code',
        'st_name_',
        'budget_code',
        'amount_dr',
        'amount_cr',
        'tx_type',
      ],
      amountColumns: ['amount_dr', 'amount_cr'],
      preferences: {
        filters: {
          selectedYearCode: '',
          selectedMonth: '',
          begin_date: '',
          end_date: '',
          // date_range: [],

          fund_id: '',
          vt_category: '',
          vt_code: '',
        },
      },
      currentFilter: {},
      childPreferences: ['vt_code'],
      showYearPicker: false,
      periods: [],
      ps_code: 'pdfvoucherlist',
      funds: [],
      voucherTypeList: [],

      yearLastDate: new Date(),
      yearStartDate: new Date(),
      // dateFormatStr: 'yyyy-MM-dd'

      pageResizeListen: {},
      pageHeight: 500,
      filterResizeListen: {},
      filterHeight: 80,
      btnLoading: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school', 'remoteServerInfo', 'user_id']),
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    allStaff() {
      return {
        active_year: '',
        children: null,
        code: '',
        level: 1,
        name_cn: this.$t('assistance.staff.label.parent_default'),
        name_en: this.$t('assistance.staff.label.parent_default'),
        parent_st_type_id: null,
        seq: 0,
        st_grade: null,
        staff_id: null,
        username: null,
      }
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filterResizeListen = listenTo(this.$refs.filter, ({ width, height, ele }) => {
        this.filterHeight = height
      })
    })
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.preferences.filters.selectedAcCode === '') {
            this.preferences.filters.selectedAcCode =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].ac_code : ''
          } else {
            if (this.bankList && this.bankList.length > 0) {
              let bool = false
              this.bankList.forEach(i => {
                if (i.ac_code === this.preferences.filters.selectedAcCode) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
              }
            }
          }
          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.loadVoucherType)
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    loadVoucherType() {
      return new Promise((resolve, reject) => {
        const fund_id = this.preferences.filters.fund_id || undefined
        const vt_category = this.preferences.filters.vt_category || undefined
        fetchVoucherTypes({ fund_id, vt_category })
          .then(res => {
            this.voucherTypeList = res
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, 1)
        const lastDateObj = new Date(year, month + 1, 0)

        // let begin_date, end_date
        // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
        //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        // } else {
        //   begin_date = this.preferences.filters.date_range[0]
        //   end_date = this.preferences.filters.date_range[1]
        // }
        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        if (!begin_date || !end_date) {
          begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
          end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
          // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
          this.preferences.filters.begin_date = begin_date
          this.preferences.filters.end_date = end_date
        }
        const fund_id = this.preferences.filters.fund_id || undefined
        const vt_category = this.preferences.filters.vt_category || undefined
        const vt_code = this.preferences.filters.vt_code || undefined
        this.loading = true
        getReportVoucher({
          fund_id,
          vt_category,
          vt_code,
          begin_date,
          end_date,
        })
          .then(res => {
            this.keepFilter()
            this.tableData = this.formatData(res)
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      let drSum = 0
      let crSum = 0
      let index = 1
      const addVoucherSum = function() {
        newData.push({
          ac_code: '',
          amount_cr: crSum,
          amount_dr: drSum,
          descr: '',
          fy_code: '',
          lg_id: '',
          lg_type: '',
          ref: '',
          st_code: '',
          tx_num: '',
          tx_type: '',
          vc_date: '',
          vc_method: '',
          vc_no: '',
          vc_payee: '',
          sumRow: true,
        })
        drSum = 0
        crSum = 0
        index = 1
      }
      data.forEach((item, i) => {
        if (i > 0 && item.vc_no) {
          addVoucherSum()
        }
        const newItem = Object.assign({}, item)
        newItem.amount_net = toDecimal(Number(newItem.amount_dr) - Number(newItem.amount_cr))
        newItem.tx_num = index++
        newData.push(newItem)
        drSum += toDecimal(Number(newItem.amount_dr))
        crSum += toDecimal(Number(newItem.amount_cr))
      })
      if (data.length > 0) {
        addVoucherSum()
      }
      return newData
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    onChangeDateRange(val) {},
    onChangeMonth(val) {
      if (val === '') {
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateFormatStr)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateFormatStr)
        // this.preferences.filters.date_range = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      // beginDate
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, 'yyyy-MM-dd')

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, 'yyyy-MM-dd')
      // this.preferences.filters.date_range = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
    },
    onSelectStaff({ value, selectGroup, group_id, nature_code }) {
      this.preferences.filters.st_code = value
      this.preferences.filters.st_selectGroup = selectGroup
      this.preferences.filters.parent_st_type_id = group_id
      // this.fetchData()
    },
    onChangeVoucherTypeCategory() {
      this.loadVoucherType().then(() => {
        const vt = this.voucherTypeList.find(i => i.vt_code === this.preferences.filters.vt_code)
        if (!vt) {
          this.preferences.filters.vt_code = ''
        }
      })
    },
    handleTableRowClass({ row }) {
      // console.log(row)
      if (row.sumRow) {
        return 'sum-row'
      }
    },
    keepFilter() {
      this.currentFilter = Object.assign({}, this.preferences.filters)
      // this.currentFilter.date_range = this.preferences.filters.date_range.map(i => i)
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateFormatStr,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateFormatStr,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id
      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, 1)
      const lastDateObj = new Date(year, month + 1, 0)

      // let begin_date, end_date
      // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
      //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
      //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      // } else {
      //   begin_date = this.preferences.filters.date_range[0]
      //   end_date = this.preferences.filters.date_range[1]
      // }
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!begin_date || !end_date) {
        begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        this.preferences.filters.begin_date = begin_date
        this.preferences.filters.end_date = end_date
      }
      const fund_id = this.preferences.filters.fund_id || undefined
      const vt_category = this.preferences.filters.vt_category || undefined
      const vt_code = this.preferences.filters.vt_code || undefined

      this.loading = true
      vouchersExport({
        export_type,
        user_id,
        fund_id,
        vt_category,
        vt_code,
        begin_date,
        end_date,
      })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    resize(e) {
      console.log('resize', e)
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.el-icon-date {
  cursor: pointer;
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    border-collapse: collapse;
    tbody {
      tr.vxe-body--row {
        td.vxe-body--column {
          border: none;
          background: none;
        }
      }
      .sum-row {
        border-bottom: 1px solid #e6e6e6;
      }
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
  }
  .cash-book-table {
    height: calc(100vh - 200px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
</style>
