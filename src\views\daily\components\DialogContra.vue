<template>
  <!--對沖編號彈窗-->
  <el-dialog :visible.sync="showDialog" :title="$t('daily.dialog.contra')" @open="onOpenDialog">
    <addPage
      v-if="dialogView === 'add'"
      :default-parent="searchFilter.fund_id"
      :edit-parent="addParent"
      class="voucher-dialog-add"
      @onCancel="onCancel"
    />
    <div v-else class="selectContra">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.name')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear(true)">
              {{ $t('button.clear') }}
            </el-button>
            <el-button v-if="hasPermission_Add" size="mini" type="primary" @click="onAdd">
              {{ $t('button.add') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="100%"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="$t('daily.label.no')" property="contra_code" width="150" />
          <el-table-column
            :label="$t('daily.label.nameCN')"
            property="contra_name_cn"
            width="250"
          />
          <el-table-column :label="$t('daily.label.nameEN')" property="contra_name_en" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import addPage from '@/views/assistance/contraCode/add'

import { fetchContras } from '@/api/assistance/contra'

export default {
  name: 'DialogContra',
  components: {
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
  },
  data() {
    return {
      p_code: 'ac.assistance.contra_code',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      showDialog: this.dialogVisible,
      dialogView: 'list',
      tableData: [],
      searchFilter: {
        name: '',
      },
      defaultFilter: {
        name: '',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    onClear(update) {
      this.searchFilter = Object.assign({}, this.defaultFilter)
      if (update) this.fetchData()
    },
    onOpenDialog() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.onClear()
      this.fetchData()
    },
    fetchData() {
      this.tableLoading = true
      if (!this.fy_code) {
        return
      }
      fetchContras({
        fy_code: this.fy_code,
        name: this.searchFilter.name,
      })
        .then(res => {
          this.tableData = res
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    onAdd() {
      // const fund_id = this.searchFilter.fund_id
      // if (fund_id) {
      //   if (this.searchFilter.fund_id_target) {
      //     this.addParent = { fund_id: this.searchFilter.fund_id_target }
      //   }
      this.dialogView = 'add'
      // }
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$emit('selectRow', currentRow)
      this.showDialog = false
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectContra {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
</style>
