import request from '@/utils/request'

/**
 * 獲取支票列印列表
 * @param {string} ac_code 銀行會計科目編號
 * @param {string} type 篩選類型,N=未登記,X=無支票號碼,C=支票薄編號
 * @param {string} fy_code 會計週期編號,type=N或X時必填
 * @param {string} pd_code 會計週期月份編號,type=N或X時可填
 * @param {string} chqbk_code 支票薄編號,type=C時必填
 * @return {Promise}
 */
export function getChequeList({ ac_code, type, fy_code, pd_code, chqbk_code }) {
  return request({
    url: '/cheques/actions/print-list',
    method: 'get',
    params: {
      ac_code,
      type,
      fy_code,
      pd_code,
      chqbk_code,
    },
  })
}

/**
 * 記錄支票作廢時期
 * @param {string} void_cheque_id 作廢記錄id
 * @param {string} pd_code 作廢週期月份code,傳null=N/A
 * @return {Promise}
 */
export function chequeVoidPeriod({ void_cheque_id, pd_code }) {
  return request({
    url: '/cheques/actions/void-period',
    method: 'post',
    data: {
      void_cheque_id,
      pd_code,
    },
  })
}

/**
 * 返回傳票列表
 * @param {string} chq_no 作廢的支票號碼
 * @param {string} chqbk_code 作廢的號碼歸屬的支票薄編號
 * @return {Promise}
 */
export function chequeVoid({ chq_no, chqbk_code }) {
  return request({
    url: '/cheques/actions/void',
    method: 'post',
    data: {
      chq_no,
      chqbk_code,
    },
  })
}

/**
 * 獲取支票模板列表
 */
export function getCheques() {
  return request({
    url: '/cheques',
    method: 'get',
  })
}

export default {
  getChequeList,
  chequeVoidPeriod,
  chequeVoid,
  getCheques,
}
