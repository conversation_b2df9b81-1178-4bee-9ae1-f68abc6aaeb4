<template>
  <el-dialog
    width="30%"
    :title="t('uploadMonthly')"
    :visible.sync="show"
    custom-class="EDAC"
    append-to-body
    @open="onInit"
  >
    <div v-loading="loading">
      <div class="form-flex bank-select">
        <span>{{ t('bank') }}</span>
        <el-select
          v-model="form.selectedAcCode"
          class="bank"
          style="width: 250px"
          @change="changeBank"
        >
          <el-option
            v-for="item in bankList"
            :key="item.ac_code"
            :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
            :value="item.ac_code"
          />
        </el-select>
      </div>
      <div style="padding: 20px 20px">
        <manual-control-upload-excel
          ref="manualControlUploadExcel"
          :loading="loading"
          :msg="$t('message.dropPdf')"
          :supports-file-type-tips="$t('file.pleaseSelectPdfFile')"
          :file-suffix="'.pdf'"
          :excel-file="false"
          :show-tip="false"
          multiple
          :show-actions="false"
          :on-success="handleSuccess"
          :on-submit="onSubmit"
          @change="onChange"
        />
        <div slot="tip" class="el-upload__tip">
          {{ t('uploadText1') }}
        </div>
        <div slot="tip" class="el-upload__tip">
          {{ t('uploadText2') }}
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { uploadAPI } from '@/api/periodic/bankReconciliation'
import manualControlUploadExcel from '@/components/UploadExcel/manualControl.vue'
export default {
  components: {
    manualControlUploadExcel,
  },
  props: {
    innerVisible: {
      type: Boolean,
      default: false,
    },
    selectedAcCode: {
      type: String,
      default: '',
    },
    bankList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      langKey: 'bankAiReconciliation.',
      show: false,
      loading: false,
      form: {
        selectedAcCode: '',
      },
      uploadUrl: '',
    }
  },
  computed: {
    accountId() {
      return this.form.selectedAcCode
        ? this.bankList.find(item => item.ac_code === this.form.selectedAcCode).account_id
        : ''
    },
  },
  watch: {
    innerVisible: {
      handler: function(val) {
        this.show = val
      },
      deep: true,
    },
    show: {
      handler: function(val) {
        if (!val) {
          this.$emit('closeUpload', false)
        }
      },
      deep: true,
    },
    selectedAcCode: {
      handler: function(val) {
        this.form.selectedAcCode = val
      },
      deep: true,
    },
  },
  created() {
    this.form.selectedAcCode = this.selectedAcCode
  },
  methods: {
    onInit() {
      console.log(this.$refs['manualControlUploadExcel'])

      this.$nextTick(() => {
        if (this.$refs['manualControlUploadExcel']) {
          this.$refs['manualControlUploadExcel'].init()
        }
      })
    },
    changeBank(val) {
      console.log(val)
      this.$emit('changeBank', val)
    },
    async handleSuccess(formData, file) {
      console.log(formData)

      uploadAPI({ file, account_id: this.accountId })
        .then(res => {
          this.$message({
            message: this.$t('message.success'),
            type: 'success',
          })
          this.$emit('uploadSuccess')
        })
        .catch(() => {
          this.handleError()
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleError() {
      this.$message({
        message: this.t('fail'),
        type: 'error',
      })
      this.loading = false
    },
    beforeUpload() {},
    async onSubmit() {
      return true
    },
    onChange() {
      this.loading = true
      this.$refs['manualControlUploadExcel'] &&
        this.$refs['manualControlUploadExcel'].handleSubmit()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-upload {
    width: 100%;
    padding: 20px;
  }
  .el-upload-dragger {
    width: 100%;
    height: 100%;
    border: none;
  }
  .el-upload__tip {
    text-align: left;
  }
}
.bank-select {
  padding: 0 20px;
}
.el-upload__text {
  height: 100px;
  align-content: center;
  border: 1px dashed #bbbbbb;
  border-radius: 10px;
}
</style>
