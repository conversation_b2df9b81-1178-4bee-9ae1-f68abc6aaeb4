import elementResizeDetectorMaker from 'element-resize-detector'

const erd = elementResizeDetectorMaker()

export const listenTo = function(element, callback) {
  if (element) {
    erd.listenTo(element, ele => {
      const width = ele.offsetWidth
      const height = ele.offsetHeight
      // console.log('Size: ' + width + 'x' + height)
      callback && typeof callback === 'function' && callback({ width, height, ele })
    })
    return {
      uninstall: () => {
        erd.uninstall(element)
      },
    }
  } else {
    return null
  }
}
