<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 組別編號 -->
      <el-form-item :rules="rules" :label="$t('stock.itemSetupGroup.label.sg_code')" prop="sg_code">
        <el-input v-model="form.sg_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('stock.itemSetupGroup.label.sg_name_cn')"
        prop="sg_name_cn"
      >
        <el-input v-model="form.sg_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('stock.itemSetupGroup.label.sg_name_en')"
        prop="sg_name_en"
      >
        <el-input v-model="form.sg_name_en" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :label="$t('stock.itemSetupGroup.label.parent_stock_group_id')"
        prop="parent_stock_group_id"
      >
        <el-select
          v-model="form.parent_stock_group_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in stockGroups"
            :key="item.value"
            :value="item.stock_group_id"
            :label="conversionParentStockGroup(item)"
            v-html="conversionParentStockGroup(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置 -->
      <el-form-item :label="$t('stock.itemSetupGroup.label.seq')" prop="seq">
        <el-select
          v-model="form.seq"
          :placeholder="$t('placeholder.select')"
          @change="changePosition"
        >
          <el-option
            v-for="(item, i) in sepOptions"
            :key="i"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.stock_group_id ? item.stock_group_id : item.stock_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('stock.itemSetupGroup.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getStocksTreeNode } from '@/api/stock/itemSetup'
import {
  createStockGroup,
  updateStockGroup,
  getStockGroup,
  fetchStockGroups,
} from '@/api/stock/itemSetup/itemSetupGroup'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'StockItemSetupGroupAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: {
        stock_group_id: '',
        sg_code: '',
        sg_name_cn: '',
        sg_name_en: '',
        active_year: '',
        parent_stock_group_id: '',
        seq: '',
      },
      defaultForm: {
        stock_group_id: '',
        sg_code: '',
        sg_name_cn: '',
        sg_name_en: '',
        active_year: '',
        parent_stock_group_id: '',
        seq: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      defaultSepOptions: [
        {
          stock_group_id: '',
          stock_id: '',
          seq: '',
          name_cn: this.$t('stock.itemSetupGroup.label.seq_default'),
          name_en: this.$t('stock.itemSetupGroup.label.seq_default'),
        },
      ],
      sepOptions: [
        {
          stock_group_id: '',
          stock_id: '',
          seq: '',
          name_cn: this.$t('stock.itemSetupGroup.label.seq_default'),
          name_en: this.$t('stock.itemSetupGroup.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      defaultStockGroups: [
        {
          stock_group_id: '',
          sg_name_cn: this.$t('stock.itemSetupGroup.label.parent_default'),
          sg_name_en: this.$t('stock.itemSetupGroup.label.parent_default'),
        },
      ],
      stockGroups: [],
      seqStartLevel: 1,
      active_year_arr: [],
      loading: true,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
  },
  watch: {
    editObject() {
      console.log('watch editObject', this.editObject)
      this.initData()
    },
    editParent() {
      console.log('watch editObject', this.editObject)
      this.initData()
    },
  },
  created() {
    console.log('editObject', this.editObject)
    this.initData()
  },
  methods: {
    changePosition() {
      this.forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentStockGroup(stockGroup, html, startLevel = 1) {
      let text = this.language === 'en' ? stockGroup.sg_name_en : stockGroup.sg_name_cn
      if (html) {
        text = '&nbsp;'.repeat((stockGroup.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    changeParentType() {
      getStocksTreeNode('', this.form.parent_stock_group_id, this.form.stock_group_id, '').then(
        res => {
          this.sepOptions = [...res, ...this.defaultSepOptions]
          if (this.editObject && this.form.seq_i) {
            let isLast = true
            for (let i = 0; i < res.length; i++) {
              if (res[i].seq > this.form.seq_i) {
                isLast = false
                this.form.seq = res[i].stock_group_id ? res[i].stock_group_id : res[i].stock_id
                break
              }
            }
            if (isLast) {
              this.form.seq = ''
            }
          } else {
            this.form.seq = ''
          }
        },
      )
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.stock_group_id) {
        this.form.parent_stock_group_id = this.editParent.stock_group_id
      } else if (
        this.editObject &&
        this.editObject.parent &&
        this.editObject.parent.stock_group_id
      ) {
        this.form.parent_stock_group_id = this.editObject.parent.stock_group_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getStockGroup(this.editObject.stock_group_id)
            .then(res => {
              this.form = Object.assign({}, res)
              console.log('getStockGroup', res)
              console.log('stock_group_id', this.form.stock_group_id)
              this.setParent()
              this.form.parent_stock_group_id =
                res.stockGroupRelation && res.stockGroupRelation.parent_stock_group_id
                  ? res.stockGroupRelation.parent_stock_group_id
                  : ''
              this.form.seq_i = res.stockGroupRelation ? res.stockGroupRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => fetchStockGroups(this.form.stock_group_id, ''))
        .then(res => {
          console.log('fetchStockGroups', res)
          this.stockGroups = [...this.defaultStockGroups, ...res]
          console.log('stockGroups', this.stockGroups)
          this.changeParentType(this.form.parent_stock_group_id)
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          this.active_year_arr = []
          if (!this.editObject) {
            const selected = this.years.find(i => i.fy_code === this.fyCode)
            if (selected) {
              this.active_year_arr.push(selected.fy_code)
            }
            // if (this.currentYear && this.currentYear.fy_code) {
            //   const current = this.years.find(i => i.fy_code === this.currentYear.fy_code)
            //   if (current !== selected && current) {
            //     this.active_year_arr.push(current.fy_code)
            //   }
            // }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.stock_group_id ? 'stock_group_id' : 'stock_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const stock_group_id = this.form.stock_group_id
        const sg_code = this.form.sg_code
        const sg_name_cn = this.form.sg_name_cn
        const sg_name_en = this.form.sg_name_en
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const parent_stock_group_id = this.form.parent_stock_group_id
        const seq = this.newSeq()
        if (this.editObject) {
          // 編輯
          updateStockGroup({
            stock_group_id: stock_group_id,
            sg_code: sg_code,
            sg_name_cn: sg_name_cn,
            sg_name_en: sg_name_en,
            active_year: active_year,
            parent_stock_group_id: parent_stock_group_id,
            seq: seq,
          })
            .then(res => {
              console.log('Success: updateStockGroup', res)
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              console.log('ERR: updateStockGroup', err)
              this.$message.err(err)
            })
        } else {
          // 新增
          createStockGroup({
            sg_code: sg_code,
            sg_name_cn: sg_name_cn,
            sg_name_en: sg_name_en,
            active_year: active_year,
            parent_stock_group_id: parent_stock_group_id,
            seq: seq,
          })
            .then(res => {
              console.log('Success: createStockGroup', res)
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(err => {
              console.log('ERR:  createStockGroup', err)
              this.$message.err(err)
            })
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style scoped></style>
