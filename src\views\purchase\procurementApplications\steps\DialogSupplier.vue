<template>
  <!--收款人彈窗-->
  <el-dialog :visible.sync="showDialog" :title="$t('daily.dialog.staff')" @open="onOpenDialog">
    <addPage
      v-if="dialogView === 'add'"
      :default-parent="searchFilter.st_type_id"
      :edit-parent="addParent"
      class="voucher-dialog-add"
      @onCancel="onCancel"
    />
    <div v-else class="selectStaff">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.name')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear(true)">
              {{ $t('button.clear') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="400"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="t('pro_no')" property="comp_code" width="150" />
          <el-table-column :label="t('companyName')" property="comp_name" width="250" />
          <el-table-column :label="t('companyShortName')" property="comp_abbr" />
          <el-table-column :label="t('companyTel')" property="comp_tel" />
          <el-table-column :label="t('companyFax')" property="comp_fax" />
          <el-table-column :label="t('companyContact')" property="comp_attention" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import addPage from '@/views/assistance/staff/add'

import { searchCompanies } from '@/api/assistance/payeePayer'
export default {
  name: 'DialogStaff',
  components: {
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fyCode: {
      type: [String, Object],
      required: true,
    },
  },
  data() {
    return {
      langKey: 'purchase.daily.procurementApplications.',
      p_code: 'ac.assistance.staff',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      showDialog: this.dialogVisible,
      dialogView: 'list',
      staffTypeList: [],
      companyList: [],
      tableData: [],
      searchFilter: {
        st_type_id: '',
        name: '',
      },
      defaultFilter: {
        st_type_id: '',
        name: '',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    onClear(update) {
      this.searchFilter = Object.assign({}, this.defaultFilter)
      if (update) this.fetchData()
    },
    onOpenDialog() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.onClear()
      this.fetchData()
    },
    fetchData() {
      this.tableLoading = true
      searchCompanies({
        name: this.searchFilter.name,
      })
        .then(res => {
          this.tableData = res
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    onAdd() {
      // const fund_id = this.searchFilter.fund_id
      // if (fund_id) {
      //   if (this.searchFilter.fund_id_target) {
      //     this.addParent = { fund_id: this.searchFilter.fund_id_target }
      //   }
      this.dialogView = 'add'
      // }
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$emit('selectRow', currentRow)
      this.showDialog = false
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectStaff {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
</style>
