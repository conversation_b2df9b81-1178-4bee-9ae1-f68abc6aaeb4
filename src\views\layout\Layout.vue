<template>
  <div v-if="!loading" :class="'EDAC' + ' font-size-' + globalFontSize">
    <span v-html="styleInner2" />
    <div class="EDAC_header">
      <vheader />
    </div>
    <div class="EDAC_content">
      <router-view />
    </div>
    <div class="EDAC_footer">
      <vfooter />
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import vheader from './components/vheader.vue'
import vfooter from './components/vfooter.vue'
import { getUserStyleSheets } from '@/api/settings/user/style'
import { initPDFFont } from '@/utils/pdf'
import changeFavicon from '@/utils/changeFavicon'

export default {
  components: {
    vheader,
    vfooter,
  },
  data() {
    return {
      color: 'red',
      loading: true,
      keepAliveInterval: null,
    }
  },

  computed: {
    ...mapGetters({
      thisSystem: 'system',
      user_id: 'user_id',
      styles: 'styles',
      hasPDFRouter: 'hasPDFRouter',
    }),
    globalFontSize() {
      if (this.styles && this.styles.globalFontSize) {
        return this.styles.globalFontSize
      }
      return 12
    },
    styleInner2() {
      const globalFontSize = this.styles.globalFontSize
      const globalFontFamily = this.styles.globalFontFamily

      /* 菜單 */
      const menu = {
        mainHeight: this.styles.menuMainHeight,
        secondHeight: this.styles.menuSecondHeight,
        optionsHeight: this.styles.menuOptionsHeight,
        fontFamily: this.styles.menuFontFamily,
        mainFontSize: this.styles.menuMainFontSize,
        secondFontSize: this.styles.menuSecondFontSize,
        mainBackgroundColor: this.styles.menuMainBackgroundColor,
        secondBackgroundColor: this.styles.menuSecondBackgroundColor,
        mainHoverColor: this.styles.menuMainHoverColor,
        secondHoverColor: this.styles.menuSecondHoverColor,
      }

      /* table */
      const table = {
        font_color: this.styles.tableHeaderFontColor,
        background_color: this.styles.tableHeaderBackgroundColor,
      }
      /* table */
      const content = {
        background_color: this.styles.contentBackgroundColor,
        line_height: this.styles.contentLineHeight,
      }

      const menuHeight = 42 + parseInt(menu.mainFontSize)
      let elInputMumberTop = 1

      /* 數字輸入框 - 圖標 */
      switch (parseInt(globalFontSize)) {
        case 13:
          elInputMumberTop = 2
          break
        case 10:
        case 11:
        case 12:
        case 14:
        case 15:
          elInputMumberTop = 3
          break
      }

      const enquiry = {
        grant: this.styles.grantTheme,
        staff: this.styles.staffTheme,
        budget: this.styles.budgetTheme,
        contra: this.styles.contraTheme,
        dept: this.styles.deptTheme,
        general: this.styles.generalTheme,
      }

      const stripe = '14'
      const selected = '66'

      return `


<style>
.EDAC *:not(i):not(.svg-icon):not(.el-input-number__increase),
.el-select-dropdown__item {
  /*color: ${this.color};*/
  font-size: ${globalFontSize}px;
  font-family: "${globalFontFamily}";
}
.EDAC .EDAC_header .EDAC_header_top .EDAC_header_top_left .mainMenu {
  margin: 8px 0 ${globalFontSize / 2}px;
}
.EDAC .EDAC_header .EDAC_header_top{
  min-height: ${menuHeight}px!important;
  max-height: ${menuHeight + (globalFontSize / 3) * 2}px!important;
}
.EDAC_header .EDAC_header_top .EDAC_header_top_left .mainMenu ul .activated:after {
    /*content: "";
    display: flex;
    width: 0px;
    border: ${this.styles.globalFontSize}px solid #ec080800;
    border-bottom: ${this.styles.globalFontSize / 2}px solid #fff;
    position: absolute;
    text-align: center;
    right: 0;
    bottom: -${this.styles.globalFontSize / 2}px;
    left: 50%;
    margin-left: -${this.styles.globalFontSize}px;*/

    bottom: -${globalFontSize / 2}px;
}

.el-input-number .el-input-number__increase, .el-input-number .el-input-number__decrease {
  height: 27px;
  line-height: 30px;
  top: ${elInputMumberTop}px;
}

.EDAC .EDAC_header .EDAC_header_top {
    height: ${menu.mainHeight}px!important;
}
.EDAC .EDAC_header .EDAC_header_top .EDAC_header_top_left *,
.EDAC .EDAC_header .EDAC_header_bottom * {
  font-family: "${menu.fontFamily}"!important;
}
/*主菜單*/
.EDAC .EDAC_header .EDAC_header_top {
  background-color: ${menu.mainBackgroundColor}!important;
  background: ${menu.mainBackgroundColor}!important;
}
.EDAC .EDAC_header .EDAC_header_top .EDAC_header_top_left *:not(i):not(.svg-icon):not(.EDAC_header_Logo) {
  font-size: ${menu.mainFontSize}px!important;
  line-height: 1;
}
.EDAC .EDAC_header .EDAC_header_bottom li:hover {
  color: ${menu.mainHoverColor};
  border-bottom: 2px solid ${menu.mainHoverColor};
}
/*次菜單*/
.EDAC .EDAC_header .EDAC_header_bottom {
  background-color: ${menu.secondBackgroundColor}!important;
  height: ${menu.secondHeight}px!important;
}
.EDAC .EDAC_header .EDAC_header_bottom li {
  font-size: ${menu.secondFontSize}px!important;
}
.EDAC .EDAC_header .EDAC_header_bottom li:hover {
  color: ${menu.secondHoverColor}!important;
  border-bottom: 2px solid ${menu.secondHoverColor}!important;
}

/* 表頭 */
.el-table table thead tr th, .el-transfer-panel .el-transfer-panel__header {
  color: ${table.font_color};
  background-color: ${table.background_color}!important;
}
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label{
  color: ${table.font_color};
  font-size: ${menu.secondFontSize}px!important;
}
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span{
  color: ${table.font_color};
}
/*斑馬紋*/
.table-stripe th,.table-stripe td{
  background: ${content.background_color};
}
/* 內容 */
.EDAC .el-table .el-table__body-wrapper > table .el-table__row > td{
  padding: 0!important;
}
.EDAC .el-table .el-table__body-wrapper > table .el-table__row > td > .cell{
  line-height: ${content.line_height}px;
}
.EDAC .el-table .el-table__fixed-body-wrapper > table .el-table__row > td{
  padding: 0!important;
}
.EDAC .el-table .el-table__fixed-body-wrapper > table .el-table__row > td > .cell{
  line-height: ${content.line_height}px;
}

.vxe-table .vxe-table--header-wrapper th.vxe-header--column {
    padding: 2px 0;
    color: ${table.font_color};
    background-color: ${table.background_color}!important;
    line-height: 23px;
}
.vxe-table .vxe-table--footer-wrapper .vxe-table--footer .vxe-footer--row .vxe-footer--column {
    line-height: ${content.line_height}px;
    height: ${content.line_height}px;
    max-height: 23px;
    padding: 2px 0;
}
.vxe-table .vxe-body--column.col--ellipsis>.vxe-cell, .vxe-table .vxe-footer--column.col--ellipsis>.vxe-cell, .vxe-table .vxe-header--column.col--ellipsis>.vxe-cell {
}

/* 查詢樣式 */
/* 津貼 */
.EDAC .enquiry-grant .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.grant}!important;
}
.EDAC .enquiry-grant .vxe-table .vxe-body--row.table-stripe > td {
  background-color: ${enquiry.grant}${stripe}!important;
}
.EDAC .enquiry-grant .vxe-table .vxe-body--row.row--current > td {
  background-color: ${enquiry.grant}${selected}!important;
}
/* 預算 */
.EDAC .enquiry-budget .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.budget}!important;
}
.EDAC .enquiry-budget .vxe-table .vxe-body--row.table-stripe > td {
  background-color: ${enquiry.budget}${stripe}!important;
}
.EDAC .enquiry-budget .vxe-table .vxe-body--row.row--current > td {
  background-color: ${enquiry.budget}${selected}!important;
}
/* 職員 */
.EDAC .enquiry-staff .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.staff}!important;
}
.EDAC .enquiry-staff .vxe-table .vxe-body--row.table-stripe > td {
  background-color: ${enquiry.staff}${stripe}!important;
}
.EDAC .enquiry-staff .vxe-table .vxe-body--row.row--current > td {
  background-color: ${enquiry.staff}${selected}!important;
}
/* 部門 */
.EDAC .enquiry-department .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.dept}!important;
}
.EDAC .enquiry-department .vxe-table .vxe-body--row.table-stripe > td {
  background-color: ${enquiry.dept}${stripe}!important;
}
.EDAC .enquiry-department .vxe-table .vxe-body--row.row--current > td {
  background-color: ${enquiry.dept}${selected}!important;
}
/* 對沖 */
.EDAC .enquiry-contra .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.contra}!important;
}
.EDAC .enquiry-contra .vxe-table .vxe-body--row.table-stripe > td {
  background-color: ${enquiry.contra}${stripe}!important;
}
.EDAC .enquiry-contra .vxe-table .vxe-body--row.row--current > td {
  background-color: ${enquiry.contra}${selected}!important;
}
/* 綜合 */
.EDAC .enquiry-general .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.general}!important;
}
.EDAC .enquiry-general .vxe-table .vxe-body--row.table-stripe > td {
  background-color: ${enquiry.general}${stripe}!important;
}
.EDAC .enquiry-general .vxe-table .vxe-body--row.row--current > td {
  background-color: ${enquiry.general}${selected}!important;
}

.EDAC .enquiry-grant .el-table table thead tr th {
  background-color: ${enquiry.grant}!important;
}
.EDAC .enquiry-grant .el-table .el-table__body-wrapper > table tr.table-stripe.el-table__row > td {
  background-color: ${enquiry.grant}${stripe}!important;
}
.EDAC .enquiry-grant .el-table .el-table__body-wrapper > table tr.current-row.el-table__row > td {
  background-color: ${enquiry.grant}${selected}!important;
}
/* --- */


.EDAC .enquiry-staff .vxe-table .vxe-table--header-wrapper table.vxe-table--header thead tr th {
  background-color: ${enquiry.staff}!important;
}

.EDAC .enquiry-staff .el-table table thead tr th {
  background-color: ${enquiry.staff}!important;
}
.EDAC .enquiry-staff .el-table .el-table__body-wrapper > table tr.table-stripe.el-table__row > td {
  background-color: ${enquiry.staff}${stripe}!important;
}
.EDAC .enquiry-staff .el-table .el-table__body-wrapper > table tr.current-row.el-table__row > td {
  background-color: ${enquiry.staff}${selected}!important;
}
.EDAC .enquiry-budget .el-table table thead tr th {
  background-color: ${enquiry.budget}!important;
}
.EDAC .enquiry-budget .el-table .el-table__body-wrapper > table tr.table-stripe.el-table__row > td {
  background-color: ${enquiry.budget}${stripe}!important;
}
.EDAC .enquiry-budget .el-table .el-table__body-wrapper > table tr.current-row.el-table__row > td {
  background-color: ${enquiry.budget}${selected}!important;
}
.EDAC .enquiry-contra .el-table table thead tr th {
  background-color: ${enquiry.contra}!important;
}
.EDAC .enquiry-contra .el-table .el-table__body-wrapper > table tr.table-stripe.el-table__row > td {
  background-color: ${enquiry.contra}${stripe}!important;
}
.EDAC .enquiry-contra .el-table .el-table__body-wrapper > table tr.current-row.el-table__row > td {
  background-color: ${enquiry.contra}${selected}!important;
}
.EDAC .enquiry-department .el-table table thead tr th {
  background-color: ${enquiry.dept}!important;
}
.EDAC .enquiry-department .el-table .el-table__body-wrapper > table tr.table-stripe.el-table__row > td {
  background-color: ${enquiry.dept}${stripe}!important;
}
.EDAC .enquiry-department .el-table .el-table__body-wrapper > table tr.current-row.el-table__row > td {
  background-color: ${enquiry.dept}${selected}!important;
}
.EDAC .enquiry-general .el-table table thead tr th {
  background-color: ${enquiry.general}!important;
}
.EDAC .enquiry-general .el-table .el-table__body-wrapper > table tr.table-stripe.el-table__row > td {
  background-color: ${enquiry.general}${stripe}!important;
}
.EDAC .enquiry-general .el-table .el-table__body-wrapper > table tr.current-row.el-table__row > td {
  background-color: ${enquiry.general}${selected}!important;
}
.el-select-dropdown__item span{
  /*color: ${this.color};*/
  font-size: ${globalFontSize}px !important;
  font-family: "${globalFontFamily}" !important;
}
.vxe-cell .action-icon {
  font-size: ${globalFontSize}px !important;
}
.el-table__row .cell .action-icon {
  font-size: ${globalFontSize}px !important;
}
/* 畫面 */
/*
.app-content .screen-setting .left .left-menu .el-menu-item.is-active,
 .app-content .screen-setting .left .left-menu .el-menu-item:hover {
    background-color: ${content.background_color}1c !important;
 }
 .app-content .screen-setting .left .left-menu .el-submenu .el-submenu__title:hover {
    background-color: ${content.background_color}5c !important;
 }

 */

</style>`
    },
  },
  created() {
    document.title = this.$t('system.' + this.thisSystem)

    changeFavicon(this.thisSystem)

    this.fetchGlobalStyle().finally(() => {
      this.loading = false
    })
    if (this.hasPDFRouter) {
      this.$bus.emit('PDFLoading')
      // const notify = this.$notify({
      //   message: this.$t('message.pdfLoading'),
      //   iconClass: 'el-icon-loading',
      //   duration: 0
      // })
      initPDFFont()
        .then(() => {
          // notify.close()
          this.$bus.emit('PDFLoadedSuccessfully')
        })
        .catch(() => {
          // notify.close()
          this.$bus.emit('PDFFailedToLoad')
        })
    }

    document.body.addEventListener(
      'focus',
      function(e) {
        if (e.target.tagName === 'BUTTON') {
          e.target.blur()
        }
      },
      true,
    )
  },
  mounted() {
    this.keepAlive()
  },
  beforeDestroy() {
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval)
      this.keepAliveInterval = null
    }
  },
  methods: {
    ...mapActions(['fetchGlobalStyle']),
    keepAlive() {
      this.keepAliveInterval = setInterval(() => {
        this.$store.dispatch('getCurrentDate', true)
      }, 1000 * 3)
    },
    fetchStyle() {
      const user_id = this.user_id
      const system = this.thisSystem
      getUserStyleSheets({ user_id, system, ss_code: 'screen_basic' })
        .then(res => {
          res.forEach(item => {
            const key = item.ss_key
            const value = item.value
            switch (key) {
              case 'font_family':
                this.styles.globalFontFamily = value
                break
              case 'font_size':
                this.styles.globalFontSize = value
                break
            }
          })
        })
        .finally(() => {})
    },
  },
}
</script>
<style scoped>
.EDAC {
  height: 100%;
  min-width: 1000px;
  min-height: 600px;
}
.EDAC_header {
  /*height: 84px;*/
}
.EDAC_content {
  height: calc(100% - 104px);
  overflow: auto;
}
.EDAC_footer {
  z-index: 2000;
}
</style>
