import request from '@/utils/request'

/**
 * 新增公司組別
 * @param {string} cg_code 公司組別編號
 * @param {string} cg_name_cn 公司組別中文
 * @param {string} cg_name_en 公司組別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_company_group_id 父公司組別id
 * @param {integer} seq 所處層級里的位置
 */
export function createCompanyGroup(
  cg_code,
  cg_name_cn,
  cg_name_en,
  active_year,
  parent_company_group_id,
  seq,
) {
  return request({
    url: '/company-groups/actions/create',
    method: 'post',
    data: {
      cg_code,
      cg_name_cn,
      cg_name_en,
      active_year,
      parent_company_group_id,
      seq,
    },
  })
}

/**
 * 修改公司組別
 * @param {integer} company_group_id 公司組別id
 * @param {string} cg_code 公司組別編號
 * @param {string} cg_name_cn 公司組別中文
 * @param {string} cg_name_en 公司組別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_company_group_id 父公司組別id
 * @param {integer} seq 所處層級里的位置
 */
export function updateCompanyGroup(
  company_group_id,
  cg_code,
  cg_name_cn,
  cg_name_en,
  active_year,
  parent_company_group_id,
  seq,
) {
  return request({
    url: '/company-groups/actions/update',
    method: 'post',
    data: {
      company_group_id,
      cg_code,
      cg_name_cn,
      cg_name_en,
      active_year,
      parent_company_group_id,
      seq,
    },
  })
}

/**
 * 刪除公司組別
 * @param {integer} company_group_id 公司組別id
 */
export function deleteCompanyGroup(company_group_id) {
  return request({
    url: '/company-groups/actions/delete',
    method: 'post',
    data: {
      company_group_id,
    },
  })
}

/**
 * 獲取公司組別列表
 * @param {integer} company_group_id 公司組別id(返回結果將會排除此id)
 * @param {string} fy_code 選擇的會計週期
 */
export function fetchCompanyGroups({ company_group_id, fy_code }) {
  return request({
    url: '/company-groups',
    method: 'get',
    params: {
      company_group_id,
      fy_code,
    },
  })
}

/**
 * 獲取某公司組別詳情
 * @param {integer} company_group_id 公司組別id
 */
export function getCompanyGroup(company_group_id) {
  return request({
    url: '/company-groups/actions/inquire',
    method: 'get',
    params: {
      company_group_id,
    },
  })
}

export default {
  createCompanyGroup,
  updateCompanyGroup,
  deleteCompanyGroup,
  fetchCompanyGroups,
  getCompanyGroup,
}
