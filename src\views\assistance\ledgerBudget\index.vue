<template>
  <!-- 篩選 -->
  <div class="app-container">
    <header>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ $t('router.assistance') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t($route.meta.title) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </header>
    <div class="fliter">
      <el-row>
        <!-- 會計週期 -->
        <el-select
          v-model="preferences.filters.selectedYear"
          class="year"
          style="width: 110px"
          @change="fetchLedgerBudgets"
        >
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_name"
            :value="item.fy_code"
          />
        </el-select>
        <!-- F範圍 -->
        <el-select
          v-model="preferences.filters.selectedF"
          class="year"
          style="width: 130px"
          @change="reloadFfunds"
        >
          <el-option
            v-for="item in F_fundtypes"
            :key="item.value"
            :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
            :value="item.fund_id"
          />
        </el-select>
        <!-- G範圍 -->
        <el-select
          v-model="preferences.filters.selectedG"
          style="width: 300px"
          class="year"
          @change="fetchLedgerBudgets(false)"
        >
          <el-option
            v-for="item in G_fundtypes"
            :key="item.value"
            :label="conversionParentAccountType(item)"
            :value="item.fund_id"
            v-html="conversionParentAccountType(item, true)"
          />
        </el-select>
        <el-button
          v-if="hasPermission_Update"
          type="primary"
          size="mini"
          class="search"
          style="position: relative; top: 2px"
          @click="onSave"
        >
          {{ $t('button.update') }}
        </el-button>
      </el-row>
    </div>
    <ETable
      :data="tableData"
      :v-loading="loading"
      :default-top="215"
      :show-actions="false"
      border
      style="width: 100%"
    >
      <template slot="columns">
        <el-table-column type="index" width="50" />
        <el-table-column
          :label="$t('assistance.ledgerBudget.label.code')"
          prop="ac_code"
          width="100"
        />
        <el-table-column
          :label="$t('assistance.ledgerBudget.label.name')"
          :prop="language === 'en' ? 'ac_name_en' : 'ac_name_cn'"
          min-width="100"
        />
        <el-table-column
          :label="$t('assistance.ledgerBudget.label.income')"
          prop="i_bgt_amount"
          align="right"
          width="133"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <ENumeric
              v-if="hasPermission_Update"
              v-model="scope.row.i_bgt_amount"
              :controls="false"
              :precision="2"
              :min="0"
              :disabled="scope.row.income_active !== 'Y'"
              :disable-update="loading"
            />
            <span v-else> {{ amountFormat(scope.row.i_bgt_amount) }} </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('assistance.ledgerBudget.label.expense')"
          prop="e_bgt_amount"
          align="right"
          width="133"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <ENumeric
              v-if="hasPermission_Update"
              v-model="scope.row.e_bgt_amount"
              :controls="false"
              :precision="2"
              :min="0"
              :disable-update="loading"
              :disabled="scope.row.expense_active !== 'Y'"
            />
            <span v-else> {{ amountFormat(scope.row.i_bgt_amount) }} </span>
          </template>
        </el-table-column>
      </template>
    </ETable>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getLedgerBudgets, updateLedgerBugets } from '@/api/assistance/ledgerBudget'
import { fetchYears } from '@/api/master/years'
import { fetchFunds } from '@/api/master/funds'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

import mixinPermission from '@/views/mixins/permission'
import loadPreferences from '@/views/mixins/loadPreferences'

import ETable from '@/components/ETable'
import ENumeric from '@/components/ENumeric'
import { amountFormat } from '@/utils'

export default {
  name: 'AssistanceLedgerBudgetIndex',
  components: {
    ETable,
    ENumeric,
  },
  mixins: [mixinPermission, loadCustomStyle, loadPreferences],
  data() {
    return {
      years: '',
      F_fundtypes: '',
      G_fundtypes: '',
      tableData: [],
      loading: false,

      preferences: {
        filters: {
          selectedYear: '',
          selectedF: '',
          selectedG: '',
        },
      },
      childPreferences: ['selectedG'],
    }
  },
  computed: {
    ...mapGetters(['language']),
    AllFund() {
      return {
        fund_id: '',
        fund_name_cn: this.$t('assistance.ledgerBudget.allFund'),
        fund_name_en: this.$t('assistance.ledgerBudget.allFund'),
        fund_abbr_cn: this.$t('assistance.ledgerBudget.allFund'),
        fund_abbr_en: this.$t('assistance.ledgerBudget.allFund'),
        fund_type: 'G',
      }
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },

  methods: {
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.F_fundtypes = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYear === '') {
            this.preferences.filters.selectedYear =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          } else {
            if (this.years && this.years.length > 0) {
              let bool = false
              this.years.forEach(i => {
                if (i.fy_code === this.preferences.filters.selectedYear) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedYear = this.years[0].fy_code
              }
            }
          }
          if (this.preferences.filters.selectedF === '') {
            this.preferences.filters.selectedF =
              this.F_fundtypes && this.F_fundtypes.length > 0 ? this.F_fundtypes[0].fund_id : ''
          } else {
            if (this.F_fundtypes && this.F_fundtypes.length > 0) {
              let bool = false
              this.F_fundtypes.forEach(i => {
                if (i.fund_id === this.preferences.filters.selectedF) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedF = this.F_fundtypes[0].fund_id
              }
            }
          }
        })
        .then(this.updateChildPreference)
        .then(() => {
          this.fetchLedgerBudgets(true)
        })
        .finally(() => {
          this.loading = false
        })
    },
    fetchLedgerBudgets(isNeedUpdateChildPreference) {
      this.loading = true
      if (this.preferences.filters.selectedF === '') {
        this.tableData = []
        this.loading = false
        return Promise.reject()
      }
      fetchFunds({
        fund_type: 'G',
        parent_fund_id: this.preferences.filters.selectedF,
        fy_code: this.preferences.filters.selectedYear,
      })
        .then(res => {
          this.G_fundtypes = [this.AllFund, ...res]
        })
        .finally(() => {
          this.loading = false
        })
        .then(() => {
          if (isNeedUpdateChildPreference) {
            this.updateChildPreference()
          }
        })
        .then(() =>
          getLedgerBudgets(
            this.preferences.filters.selectedYear,
            this.preferences.filters.selectedG,
          ),
        )
        .then(res => {
          this.tableData = res
          this.loading = false
        })
    },
    reloadFfunds() {
      this.preferences.filters.selectedG = ''
      getLedgerBudgets(this.preferences.filters.selectedYear, this.preferences.filters.selectedF)
        .then(res => {
          this.tableData = res
        })
        .then(() =>
          fetchFunds({
            fund_type: 'G',
            parent_fund_id: this.preferences.filters.selectedF,
            fy_code: this.preferences.filters.selectedYear,
          }),
        )
        .then(res => {
          this.G_fundtypes = [this.AllFund, ...res]
        })
    },
    onSave() {
      const fy_code = this.preferences.filters.selectedYear
      const dataArray = []
      this.tableData.forEach(ele => {
        const ac_code = ele.ac_code
        if (ele.income_active === 'Y') {
          const bgt_type_I = 'I'
          const bgt_amount_I = ele.i_bgt_amount
          dataArray.push({
            ac_code,
            bgt_type: bgt_type_I,
            bgt_amount: bgt_amount_I,
          })
        }
        if (ele.expense_active === 'Y') {
          const bgt_type_E = 'E'
          const bgt_amount_E = ele.e_bgt_amount
          dataArray.push({
            ac_code,
            bgt_type: bgt_type_E,
            bgt_amount: bgt_amount_E,
          })
        }
      })
      const ledger_budgets_json = JSON.stringify(dataArray)
      updateLedgerBugets(fy_code, ledger_budgets_json)
        .then(res => {
          this.$message.success(this.$t('message.editSuccess'))
          this.fetchLedgerBudgets(false)
        })
        .catch(err => {
          this.$message.err(err)
        })
    },
    amountFormat(val) {
      return amountFormat(val)
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
  .fliter {
    width: 670px;
    margin: 5px 0;
    display: flex;
    /*align-items: center;*/
    /*justify-content: space-around*/
    input {
      line-height: 30px;
      height: 30px;
    }
    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
      }
    }
  }
}
</style>
