// import store from '@/store'
// import lang from '@/lang'
import dateUtil from '@/utils/date'
import { mapGetters } from 'vuex'

const globalMethods = {
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      permissions: 'permissions',
      language: 'language',
      currentSystem: 'system',
      userId: 'user_id',
    }),
    /* Common validation rules - required */
    requiredRules() {
      return this.isReadonly ? [] : [{ required: true, message: ' ', trigger: ['blur'] }]
    },
    requiredRulesOnChange() {
      return this.isReadonly ? [] : [{ required: true, message: ' ', trigger: ['blur', 'change'] }]
    },
    selectPlaceholder() {
      return this.$t('placeholder.select')
    },
    isEnglish() {
      return this.language.toLowerCase() === 'en'
    },
    dateFormatStr() {
      return 'yyyy-MM-dd'
    },
    LangSuffix() {
      const l = this.language.toLowerCase()
      switch (l) {
        case 'zh-cn':
        case 'zh-hk':
          return '_cn'
        case 'zh-tw':
          return 'Hk'
        case 'en':
        case 'en-us':
        default:
          return '_en'
      }
    },
    isAdmin() {
      const permissions = this.permissions
      const system = this.currentSystem
      if (
        system === 'PC' &&
        permissions &&
        permissions['pc.level.admin'] &&
        permissions['pc.level.admin'].p_selected &&
        permissions['pc.level.admin'].p_selected.includes('Y')
      ) {
        return true
      }
      return false
    },
  },
  mounted() {},
  methods: {
    /**
     * Get the corresponding language translation based on the langKey of the current page, data must contain langKey
     * @param {string} key
     * @param data
     * @return {string}
     */
    f(name) {
      return name + this.LangSuffix
    },
    t(key, data = undefined) {
      return this.$t(this.langKey + key, data)
    },
    dateFormatToStr(date, format = 'yyyy-MM-dd') {
      return dateUtil.format(date, format)
    },
  },
}

export { globalMethods }
