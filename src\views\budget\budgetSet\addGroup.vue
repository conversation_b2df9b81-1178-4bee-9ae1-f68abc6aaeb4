<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- ---------- 預算編號 ---------- -->
      <el-form-item
        :rules="codeRules"
        :label="$t(langKey + 'budget_code')"
        :class="{ 'code-rules-error': haveExist }"
        prop="budget_code"
      >
        <el-input v-model="form.budget_code" clearable />
      </el-form-item>
      <!-- ---------- 中文名稱 ---------- -->
      <el-form-item :rules="rules" :label="$t(langKey + 'name_cn')" prop="name_cn">
        <el-input v-model="form.name_cn" clearable />
      </el-form-item>
      <!-- ---------- 英文名稱 ---------- -->
      <el-form-item :rules="rules" :label="$t(langKey + 'name_en')" prop="name_en">
        <el-input v-model="form.name_en" clearable />
      </el-form-item>
      <!-- ---------- 上級 ---------- -->
      <el-form-item :label="$t(langKey + 'parent')" prop="parent_budget_id">
        <el-select
          v-model="form.parent_budget_id"
          :placeholder="$t('placeholder.select')"
          style="width: 100%"
          @change="changeParentType"
        >
          <el-option
            :label="$t('budget.budgetSet.label.parent_default')"
            :disabled="parentDisable({})"
            value=""
          />
          <el-option
            v-for="item in parentList"
            :key="item.budget_id"
            :value="item.budget_id"
            :disabled="parentDisable(item)"
            :label="conversionParentStaffType(item)"
            v-html="conversionParentStaffType(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- ---------- 位置 ---------- -->
      <el-form-item :label="$t(langKey + 'position')" prop="seq">
        <el-select
          v-model="form.seq"
          :placeholder="$t('placeholder.select')"
          style="width: 100%"
          @change="changePosition"
        >
          <el-option
            v-for="(item, i) in sepOptions"
            :key="i"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.budget_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>

          <el-option :label="$t('budget.budgetSet.label.seq_default')" value="" />
        </el-select>
      </el-form-item>
      <!-- ---------- 收支類別 ---------- -->
      <el-form-item :rules="rules" :label="$t(langKey + 'EIType')" prop="budget_IE">
        <el-select
          v-model="form.budget_IE"
          :placeholder="$t('placeholder.select')"
          @change="changePosition"
        >
          <el-option
            v-for="(item, i) in IETypes"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <div v-if="budget_type === 'F' || budget_type === 'G'">
        <!-- ---------- 預算群組 ---------- -->
        <el-form-item :label="$t(langKey + 'budgetGroup')">
          <el-checkbox v-model="isBudgetGroup" :disabled="!canChangeType" />
        </el-form-item>
        <!-- ---------- 組長 ---------- -->
        <el-form-item v-if="isBudgetGroup" :label="$t(langKey + 'manager')">
          <memberSelect
            :fy-code="fyCode"
            :data="managerList"
            :empty-text="$t('budget.budgetSet.label.emptyManager')"
            st_grade="M"
          />
        </el-form-item>
        <!-- ---------- 成員 ---------- -->
        <el-form-item v-if="isBudgetGroup" :label="$t(langKey + 'member')">
          <memberSelect
            :data="memberList"
            :fy-code="fyCode"
            :show-type="false"
            :empty-text="$t('budget.budgetSet.label.emptyMember')"
            st_grade="G"
          />
        </el-form-item>
        <!-- ---------- 審批 ---------- -->
        <el-form-item v-if="isBudgetGroup" :label="$t(langKey + 'audit')" prop="approve_staff_id">
          <el-select
            v-model="form.approve_staff_id"
            :placeholder="$t('placeholder.select')"
            clearable
          >
            <el-option
              v-for="(item, i) in staffList"
              :key="i"
              :label="`[${item.st_code}] ${language === 'en' ? item.st_name_en : item.st_name_cn}`"
              :value="item.staff_id"
            />
          </el-select>
        </el-form-item>
      </div>
      <!-- ---------- 狀態 ---------- -->
      <el-form-item
        v-if="budget_type !== 'F'"
        :rules="form.budget_type === 'G' ? rules : undefined"
        :label="$t(langKey + 'budget_stage')"
        prop="budget_stage"
      >
        <el-select
          v-model="form.budget_stage"
          :disabled="budget_type === 'C'"
          :placeholder="$t('placeholder.select')"
        >
          <el-option
            v-for="item in stageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- ---------- 活躍年度 ---------- -->
      <el-form-item :label="$t(langKey + 'budget_active')">
        <el-checkbox v-model="form.budget_active" true-label="Y" false-label="N" />
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { searchStaffs } from '@/api/assistance/staff'
import { createBudget, editBudget, fetchBudgetTree, getBudget } from '@/api/budget'
import { convertTreeToList } from '@/utils'
import memberSelect from './member'

export default {
  name: 'BudgetSettingAddGroup',
  components: {
    memberSelect,
  },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      langKey: 'budget.budgetSet.label.',
      form: {
        fy_code: '',
        budget_type: '',
        budget_code: '',
        budget_IE: '',
        name_cn: '',
        name_en: '',
        budget_active: 'Y',
        seq: '', // 以上必填
        budget_stage: 'X', // budget_type=G，必填
        amount: 0,
        parent_budget_id: '',
        budget_account: '',
        manager_staffs_json: '',
        member_staffs_json: '',
        approve_staff_id: '',
      },
      defaultForm: {
        fy_code: '',
        budget_type: '',
        budget_code: '',
        budget_IE: '',
        name_cn: '',
        name_en: '',
        budget_active: 'Y',
        seq: '', // 以上必填
        budget_stage: 'X', // budget_type=G，必填
        amount: 0,
        parent_budget_id: '',
        budget_account: '',
        manager_staffs_json: '',
        member_staffs_json: '',
        approve_staff_id: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      sepOptions: [],
      parentList: [],
      seqStartLevel: 1,
      loading: true,

      isBudgetGroup: false,
      canChangeType: false,
      staffList: [],
      managerList: [],
      memberList: [],
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'school']),
    IETypes() {
      const arr = []
      let type = 1
      /*
       * 1 I
       * 2 E
       * 3 IE
       * 4 B
       * */
      // switch (this.budget_type) {
      //   case 'F':
      //     type = 4
      //     break
      //   case 'G':
      //     if (!this.editParent || (this.editParent && this.editParent.budget_IE === 'B')) {
      //       type = 3
      //     } else {
      //       type = this.editParent.budget_IE === 'I' ? 1 : 2
      //     }
      //     break
      //   case 'C':
      //   case 'D':
      //     type = this.editParent.budget_IE === 'I' ? 1 : 2
      //     break
      // }
      const pIE = (this.editParent && this.editParent.budget_IE) || 'B'
      switch (pIE) {
        case 'I':
          type = 1
          break
        case 'E':
          type = 2
          break
        case 'B':
          type = 4
          break
      }
      if (type === 1 || type > 2) {
        arr.push({
          label: this.$t('budget.budgetSet.label.income'),
          value: 'I',
        })
      }
      if (type > 1) {
        arr.push({
          label: this.$t('budget.budgetSet.label.expense'),
          value: 'E',
        })
      }
      if (type === 4) {
        arr.push({
          label: this.$t('budget.budgetSet.label.incomeAndExpenditure'),
          value: 'B',
        })
      }
      if (type < 4 && this.form.budget_IE === 'B') {
        this.$set(this.form, 'budget_IE', '')
      }
      return arr
    },
    stageOptions() {
      let s = ['X', 'S', 'A', 'O', 'C']
      if (this.school.edbg_second_approve === 'Y') {
        s = ['X', 'S', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D']
      }
      return s.map(key => {
        return {
          label: this.$t('budget.budgetSet.label.stage_' + key.toLowerCase()),
          value: key,
        }
      })
    },
    budget_type() {
      if (this.editParent) {
        switch (this.editParent.budget_type) {
          case 'F':
            return this.isBudgetGroup ? 'G' : 'F'
          case 'G':
          case 'C':
            return 'C'
          default:
            throw new Error('budget type error')
        }
      } else {
        return this.isBudgetGroup ? 'G' : 'F'
      }
    },
    codeRules() {
      return [
        {
          required: true,
          validator: this.checkCode,
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    isBudgetGroup: {
      immediate: true,
      handler(value) {
        if (value) {
          this.form.budget_type = this.isBudgetGroup ? 'G' : 'F'
        } else {
          this.form.budget_type = 'F'
        }
      },
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        console.log('checkCode', !value)
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.budget_code === i.budget_code) {
          return
        }
        codeArr.push(i.budget_code)
        if (i.children) {
          codeArr.push(...this.getCodeArr(i.children))
        }
      })
      return codeArr
    },
    changePosition() {
      this.forceUpdate()
    },
    conversionParentStaffType(item, html, startLevel = 1) {
      let text = this.language === 'en' ? item.name_en : item.name_cn
      if (html) {
        text = '&nbsp;'.repeat((item.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    changeParentType() {
      // if (!this.form.parent_budget_id) {
      //   this.form.seq = ''
      //   return
      // }

      if (this.budget_type === 'C') {
        const parent = this.parentList.find(i => i.budget_id === this.form.parent_budget_id)
        if (parent) {
          this.form.budget_stage = parent.budget_stage
        }
      }

      const parent_budget_id = this.form.parent_budget_id
      const budget_id = this.editObject ? this.form.budget_id : undefined
      const fy_code = this.fyCode
      // let type
      // let type
      // switch (this.budget_type) {
      //   case 'F':
      //     type = 'F'
      //     break
      //   case 'G':
      //     type = 'G'
      //     break
      //   case 'C':
      //     type = 'C'
      //     break
      // }
      fetchBudgetTree({
        parent_budget_id,
        budget_id,
        fy_code,
        // type,
        only_node: '1',
      }).then(res => {
        this.sepOptions = res
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].budget_id || ''
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    setParent() {
      if (this.editParent && this.editParent.budget_id) {
        this.form.parent_budget_id = this.editParent.budget_id
      } else if (this.editObject && this.editObject.parent && this.editObject.parent.budget_id) {
        this.form.parent_budget_id = this.editObject.parent.budget_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          const fy_code = this.fyCode
          const budget_id = this.editObject.budget_id
          getBudget({ fy_code, budget_id })
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.managerList = res.manager_staffs
              this.memberList = res.member_staffs
              this.canChangeType = false
              // this.form.parent_st_type_id = res.staffTypeRelation && res.staffTypeRelation.parent_st_type_id ? res.staffTypeRelation.parent_st_type_id : ''
              this.form.seq_i = res.fundAccountRelation ? res.fundAccountRelation.seq : ''
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.form.fy_code = this.fyCode
          // this.form.budget_IE = this.editParent.budget_IE || ''
          this.canChangeType = true
          this.$nextTick(() => {
            // this.form.budget_IE = this.editParent.budget_IE || ''
          })
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => {
          this.setParent()
          this.isBudgetGroup = this.form.budget_type === 'G'
          if (this.editParent) {
            if (this.editParent.budget_type === 'G') {
              this.form.budget_type = 'G'
              this.canChangeType = false
            }
          }
        })
        .then(() => {
          const budget_id = this.editObject ? this.form.budget_id : undefined
          const fy_code = this.fyCode
          // const type = this.budget_type// === 'C' ? 'C' : 'F'
          let type
          switch (this.budget_type) {
            case 'F':
              type = 'F'
              break
            case 'G':
              type = 'G'
              break
            case 'C':
              type = 'C'
              break
          }
          return fetchBudgetTree({ budget_id, fy_code, type })
        })
        .then(res => {
          // function convertTreeToList(treeArray, level = 0) {
          //   const array = []
          //   treeArray.forEach(item => {
          //     const node = Object.assign({}, item)
          //     node.level = level + 1
          //     array.push(node)
          //     if (node.children) {
          //       array.push(...convertTreeToList(node.children, node.level))
          //       node.children = undefined
          //     }
          //   })
          //   return array
          // }
          this.parentList = convertTreeToList(res, 1)
        })
        .then(() => searchStaffs({ fy_code: this.fyCode, st_grade: 'A' }))
        .then(res => {
          this.staffList = res
        })

        .then(() => {
          this.changeParentType(this.form.parent_budget_id)
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 0) {
          return this.sepOptions[this.sepOptions.length - 1].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 0) {
          const seq_ele = this.sepOptions.find(i => {
            return i.budget_id === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }

        const fy_code = this.fyCode

        const seq = this.newSeq()

        const budget_id = this.form.budget_id || undefined
        const budget_type = this.budget_type
        const budget_code = this.form.budget_code
        const budget_IE = this.form.budget_IE
        const name_cn = this.form.name_cn
        const name_en = this.form.name_en
        const budget_active = this.form.budget_active || 'N'
        const budget_stage = this.form.budget_stage || undefined
        const parent_budget_id = this.form.parent_budget_id || undefined

        let manager_staffs_json, member_staffs_json, approve_staff_id
        if (this.isBudgetGroup) {
          manager_staffs_json = JSON.stringify(this.managerList.filter(i => i.staff_id))
          member_staffs_json = JSON.stringify(this.memberList.filter(i => i.staff_id))
          approve_staff_id = this.form.approve_staff_id || undefined
        }

        if (this.editObject) {
          // 編輯
          editBudget({
            fy_code,
            budget_id,
            budget_code,
            budget_IE,
            name_cn,
            name_en,
            budget_active,
            budget_stage,
            parent_budget_id,
            seq,
            manager_staffs_json,
            member_staffs_json,
            approve_staff_id,
          })
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createBudget({
            fy_code,
            budget_type,
            budget_code,
            budget_IE,
            name_cn,
            name_en,
            budget_active,
            budget_stage,
            parent_budget_id,
            seq,
            manager_staffs_json,
            member_staffs_json,
            approve_staff_id,
          })
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    parentDisable(item) {
      switch (this.budget_type) {
        case 'F':
        case 'G':
          if (this.editObject) {
            return item.budget_type !== 'F' || this.form.budget_stage !== item.budget_stage
          } else {
            return item.budget_type !== 'F'
          }
        case 'C':
          if (!item.budget_type) {
            return true
          } else if (this.editObject) {
            return item.budget_type === 'F' || this.form.budget_stage !== item.budget_stage
          } else {
            return item.budget_type === 'F'
          }
        default:
          return true
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
