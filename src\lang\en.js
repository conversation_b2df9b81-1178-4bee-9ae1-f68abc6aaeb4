/* eslint-disable no-dupe-keys */
export default {
  // 系統名稱
  system: {
    AC: 'Account System',
    BG: 'Budget System',
    PC: 'Purchasing System',
  },
  // 路由
  router: {
    home: 'Home',
    enquiry: 'Enquiry',
    enquiryGrant: 'Grant Enquiry',
    enquiryBudget: 'Budget Enquiry',
    enquiryStaff: 'Staff Enquiry',
    enquiryDepartment: 'Department Enquiry',
    enquiryContra: 'Contra Enquiry',
    enquiryGeneral: 'General Enquiry',

    daily: 'Daily',
    voucherAdd: 'Add',
    voucherEdit: 'Edit',
    voucherView: 'View',
    voucherReorderSummons: 'Voucher Renumbering',

    dailyPayment: 'Payment',
    dailyPettyCash: 'Petty Cash',
    dailyReceipt: 'Receipt',
    dailyBankTransfer: 'Bank Transfer',
    dailyJournal: 'Journal',
    dailyCheque: 'Cheque',
    dailyAddress: 'Address',
    dailyPost: 'From SR',

    periodic: 'Periodic',
    periodicBankReconciliation: 'Bank Reconciliation',
    periodicBankAiReconciliation: 'Bank AI Reconciliation',
    periodicDailyCollection: 'Daily Collection',
    periodicAutopayList: 'Autopay List',
    periodicBalanceTransfer: 'Balance Transfer',
    periodicObCheque: 'OB Cheque',
    periodicEDBInput: 'EDB Annual Adjustment',
    periodicDataBackup: 'Data Backup',
    restoreDemoData: 'Restore Demo Data',

    stock: 'Stock',
    stockPurchase: 'Purchase',
    stockSales: 'Sales',
    stockMonthlySales: 'Monthly Sales',
    stockBalance: 'Stock Balance',
    stockIO: 'Stock I/O',
    stockProfitReport: 'Profit Report',
    stockItemSetup: 'Item Setup',

    report: 'Report',
    reportTrialBalance: 'Trial Balance',
    reportLedger: 'Ledger',
    reportCashBook: 'Cash Book',
    reportBudget: 'Budget',
    reportStaff: 'Staff',
    reportDepartment: 'Department',
    reportVoucher: 'Voucher',
    reportExcelReport: 'Excel Report',

    master: 'Master',
    masterFund: 'Fund',
    masterAccount: 'Account',
    masterVoucherType: 'Voucher Type',
    masterUser: 'User',
    masterRoleSet: 'Role Set',
    masterACPeriod: 'A/C Period',

    assistance: 'Assistance',
    assistanceBudget: 'Budget',
    assistanceDescription: 'Description',
    assistancePayeePayer: 'Payee/Payer',
    assistanceStaff: 'Staff',
    assistanceDepartment: 'Department',
    assistanceContraCode: 'Contra Code',
    assistanceChequeBook: 'Cheque Book',
    assistanceBudgetRole: 'Budget Role',
    assistanceLedgerBudget: 'Ledger Budget',
    assistanceNumberOfSchool: 'Number Of Students',
    assistanceFundSummaryTypesRelation: 'Financial Report Set',
    assistanceNorrayEDBRelation: 'EDB Set',
    assistanceEDBRelationBySecondarySchool: 'Secondary School EDB Set',
    assistanceEntrySummary: 'Entry Summary',
    assistanceFixWrongVoucher: 'Data Manage',

    setting: 'Setting',
    settingUserInfo: 'User Info',
    settingScreen: 'Screen',
    settingScreenBasicSetting: 'Basic Setting',
    settingScreenBasic: 'Font',
    settingScreenTableHeader: 'Header',
    settingScreenMenu: 'Menu',
    settingScreenContent: 'Content',

    settingPrintout: 'Printout',
    settingChecking: 'Checking',
    settingDataExport: 'Data Export',
    settingPageSet: 'Page Set',
    settingDataImport: 'Data Import',
    settingBackup: 'Data Backup',

    budget: {
      daily: 'Daily',
      dailyBudgetList: 'Budget List',
      dailyBudgetManagement: 'Budget Management',
      dailyEnquires: 'Enquires',
      systemSettings: 'System Settings',
      systemSettingsFundSetup: 'Fund Setup',
      systemSettingsAccountInput: 'Account Input',
      systemSettingsRoleSet: 'Role Set',
      systemSettingsStaffSet: 'Staff Set',
      systemSettingsBudgetSet: 'Budget Set',
      reports: 'Reports',
      reportsBudgetList: 'Budget List',
      reportsIndividualBudgetReport: 'Individual Budget Report',
      personalSet: 'Personal Set',
      personalSetChangePassword: 'Change Password',
      personalSetScreen: 'Screen',
      personalSetPrintout: 'Printout',
    },
    // Purchase
    purchase: {
      daily: 'Daily',
      dailyProcurement: 'Procurement Application',
      dailyApproval: 'Procurement Approval',

      master: 'Master',
      period: 'A/C Period',
      staff: 'Staff',
      masterProcurementLevel: 'Procurement Level',
      role: 'Role Set',
      systemSetting: 'System Setting',
    },
  },
  navbar: {
    help: 'Help',
    inputDate: 'Entry Date',
    today: 'Today',
    yesterday: 'Yesterday',
    oneWeekAgo: 'One Week Ago',
    edit: 'Edit',
    add: 'Add',
    switchToEnglish: 'Switch to English',
    switchToChinese: 'Switch to Chinese',
    zhHK: 'Traditional Chinese',
    english: 'English',
    switchToBudgetSystem: 'Budget System',
    switchToPurchaseSystem: 'Purchasing System',
  },
  footer: {
    version: 'Version',
    versionNumber: '1.0',
    developer: 'Developer',
    company: '@Norray Professional Computer Ltd.',
    phone: 'TEL',
    phoneNumber: '�?52�?394-4114',
    fax: 'FAX',
    faxNumber: '�?52�?789-4910',
    mail: 'E-mail',
    email: '<EMAIL>',
  },
  // Error Log
  errorLog: {
    title: 'Error Log',
    message: 'Message',
    msg: 'Msg',
    url: 'URL',
    info: 'Info',
    stack: 'Stack',
    errorIn: 'error in',
  },
  // Github Corner
  githubCorner: {
    viewSource: 'View source on Github',
  },
  // Header Search
  headerSearch: {
    search: 'Search',
  },
  // Size Select
  sizeSelect: {
    default: 'Default',
    medium: 'Medium',
    small: 'Small',
    mini: 'Mini',
  },
  // Common
  common: {
    yes: 'Y',
    no: 'N',
  },
  // Language Select
  langSelect: {
    chinese: '中文',
    english: 'English',
    spanish: 'Español',
  },
  // Error Page
  errorPage: {
    back: 'Back',
    oops: 'Oops!',
    orYouCanGo: 'Or you can go to:',
    backToHome: 'Back to Home',
    logout: 'Logout',
    randomLook: 'Random Look',
    randomLookTitle: 'Random Look',
    copyright: 'Copyright',
    checkUrl: 'Please check if the URL you entered is correct, please click the button below to return to the homepage or send an error report',
    returnHome: 'Return Home',
  },
  // Screenfull Component
  screenfull: {
    browserNotSupported: 'Your browser does not support fullscreen',
  },
  // Upload Component
  upload: {
    dragText: 'Drop files here, or',
    clickUpload: 'click to upload',
  },
  // Report
  report: {
    report: 'Report',
    year: 'Year',
    month: 'Month',
    financialStatement: 'Financial Statement',
    edb: 'EDB',
    swd: 'SWD',
    edbSecondarySchool: 'EDB Secondary School',
  },
  // Theme Demo
  themeDemo: {
    primary: 'Primary',
    success: 'Success',
    info: 'Info',
    warning: 'Warning',
    danger: 'Danger',
    search: 'Search',
    upload: 'Upload',
  },
  // 登錄頁面
  login: {
    tips: 'Please contact the system administrator!',
    forgetPassword: 'Forget password',
    username: 'Username',
    password: 'Password',
    login: 'Login',
    logOut: 'Logout',
    systemAdmin: 'System Admin',
  },
  date: {
    dateTips: 'Please entry date',
    days: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    justNow: 'Just now',
    minutesAgo: 'minutes ago',
    hoursAgo: 'hours ago',
    daysAgo: 'days ago',
    month: 'M',
    day: 'D',
    hour: 'H',
    minute: 'm',
    pickerOptions: {
      today: 'Today',
      lastWeek: 'Last Week',
      lastMonth: 'Last Month',
      lastThreeMonths: 'Last 3 Months',
    },
  },
  confirm: {
    deleteConfirm: 'Confirm delete',
    warningTitle: 'Warning',
    confirmButtonText: 'Confirm',
    cancelButtonText: 'Cancel',
    cancelConfirm: 'Confirm cancel',
    tipsTitle: 'Tips',
    cancelOperation: 'Operation cancelled',
    systemError: 'System error, please refresh the page',
  },
  button: {
    save: 'Save',
    saveFile: 'Save',
    keep: 'Save',
    cancel: 'Cancel',
    add: 'Add',
    edit: 'Edit',
    copy: 'Copy',
    delete: 'Delete',
    clear: 'Clear',
    confirm: 'Confirm',
    loadDefault: 'Load Default',
    saveDefault: 'Save as Default',
    reset: 'Reset',
    update: 'Update',
    previewCheque: 'Cheque Preview',
    printCheque: 'Cheque Print',
    fetch: 'Search',
    print: 'Print',
    chequePrint: 'Cheque Print',
    chequePreview: 'Cheque Preview',
    select: 'Select',
    submit: 'Submit',
    back: 'Back',
    next: 'Next',
    prev: 'Prev',
    upload: 'Upload',
    browse: 'Browse',
    import: 'Import',
    export: 'Export',
    backup: 'Backup',
    restore: 'Restore',
    loadData: 'Fetch Data',
    generateVoucher: 'Generate Voucher',
    generateGroupVoucher: 'Generate Voucher (Single)',
    uploadFile: 'Upload File',
    checkConfiguration: 'Check Configuration',
    goTop: 'Go Top',
  },
  enquiry: {
    timeType: {
      all: 'All',
      year: 'Whole Year',
      to: 'Year-to-date',
      month: 'Month',
      free: 'Free',
      day: 'Day',
    },
    grant: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher Num',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        tx_type: 'IE',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        net_amount: 'Net Amount', // 淨金�?        bal: 'Balance', // 結餘

        // dr
        amount_dr_dr: 'Dr Amount',
        amount_cr_dr: 'Cr Amount', // x
        net_amount_dr: 'Net Dr',
        bal_dr: 'Bal Dr',
        // cr
        amount_dr_cr: 'Dr Amount', // x
        amount_cr_cr: 'Cr Amount',
        net_amount_cr: 'Net Cr',
        bal_cr: 'Bal Cr',
        // e
        amount_dr_e: 'Expense',
        amount_cr_e: 'Income', // x
        net_amount_e: 'Net Expense',
        bal_e: 'Cumulative Expense',
        // i
        amount_dr_i: 'Income', // x
        amount_cr_i: 'Income',
        net_amount_i: 'Net Income',
        bal_i: 'Cumulative Income',
        // g
        amount_dr_g: 'Expense',
        amount_cr_g: 'Income',
        net_amount_g: 'Net I/E',
        bal_g: 'Surplus of allowances',
        // ie
        amount_dr_ie: 'Expense',
        amount_cr_ie: 'Income',
        net_amount_ie: 'Net I/E',
        bal_ie: 'Cumulative I/E',

        vc_payee: 'Payee/Payer',
        // 收付款簡�?        // 參考號
        budget_code: 'Budget Code',
        // 預算名稱
        st_code: 'Staff Code',
        staff_name_: 'Staff Name',
        // 職員名稱
        vc_contra: 'Contra',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',

        ref: 'Ref',
        vt_category: 'Type',
        vt_code: 'Category',

        sumText: 'This Period',
        preTotal: 'Last Period',

        LastYearBF: "Last Year's B/F",
        lastPeriod: 'Last Period',
      },
    },
    staff: {
      label: {
        vc_no: 'Voucher Num',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        tx_type: 'IE',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        vc_payee: 'Payee/Payer',
        ref: 'Ref',
        budget_code: 'Budget Code',
        st_code: 'Staff Code',
        staff_name_: 'Staff Name',
        vc_contra: 'Contra',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',
        vc_date: 'Date',
        vt_category: 'Type',
        vt_code: 'Category',
        net_amount: 'Net Amount', // 淨金�?        bal: 'Balance', // 結餘

        sumText: 'Accumulated Amount',
        budgetAmount: 'Budget Amount',
        budgetBalance: 'Budget Balance',
      },
    },
    department: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher Num',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        tx_type: 'IE',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        net_amount: 'Net Amount', // 淨金�?        bal: 'Balance', // 結餘,
        vc_payee: 'Payee/Payer',
        ref: 'Ref',
        budget_code: 'Budget Code',
        st_code: 'Staff Code',
        staff_name_: 'Staff Name',
        vc_contra: 'Contra',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',
        vt_category: 'Type',
        vt_code: 'Category',

        sumText: 'Accumulated Amount',
      },
    },
    contra: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher Num',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        tx_type: 'IE',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        net_amount: 'Net Amount', // 淨金�?        bal: 'Balance', // 結餘,
        vc_payee: 'Payee/Payer',
        ref: 'Ref',
        budget_code: 'Budget Code',
        st_code: 'Staff Code',
        staff_name_: 'Staff Name',
        vc_contra: 'Contra',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',

        pd_code: 'Month',
        vt_category: 'Type',
        vt_code: 'Category',

        sumText: 'Accumulated Amount',
      },
    },
    general: {
      label: {
        allBudget: 'All Budget',

        date: 'Date',
        startDate: 'Begin Date',
        endDate: 'End Date',
        fund: 'A/C',

        allAccountType: 'All',
        amount: 'Amount',

        type: 'Type',
        payee: 'Payment',
        ref: 'Ref',
        contra: 'Contra',
        quote: 'Quote',
        desc: 'Descr.',
        vc_code: 'Vou.No.',
        dept: 'Dept.',
        budget: 'Budget',
        allDept: 'All',

        staff: 'Staff',
        allStaff: 'All',

        vc_no: 'Voucher Num',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        tx_type: 'IE',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        net_amount: 'Net Amount', // 淨金�?        vc_payee: 'Payee/Payer',
        budget_code: 'Budget Code',
        st_code: 'Staff Code',
        staff_name_: 'Staff Name',
        vc_contra: 'Contra',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',
        vc_date: 'Date',
        vt_category: 'Type',
        vt_code: 'Category',
      },
      amountType: {
        bet: 'Range',
        val: 'Numerical',
        gt: 'Greater than',
        lt: 'Less than',
        A: '±Dr/±Cr',
        B: '+Dr/+Cr',
        C: '+Dr/-Cr',
        D: '-Dr/+Cr',
        E: '-Dr/-Cr',
        F: '±Dr',
        G: '+Dr',
        H: '-Dr',
        I: '±Cr',
        J: '+Cr',
        K: '-Cr',
      },
      vc_type: {
        all: 'All',
        i: 'Income',
        e: 'Expenditure',
      },
      timeType: {
        a: 'All',
        y: 'Whole Year',
        t: 'Year-to-date',
        m: 'Month',
        f: 'Free',
        d: 'Day',
      },
      quoteType: {
        N: 'None',
        Q: 'Quotation',
        T: 'Tender',
      },
      button: {
        fetch: 'Go',
        reset: 'Reset',
      },
    },
    budget: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher No',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        tx_type: 'IE',
        amount_dr: 'Expense',
        amount_cr: 'Income',
        net_amount: 'Net EXP.',
        bal: 'Acc.Net Exp',
        vc_payee: 'Payee/Payer',
        ref: 'Ref',
        budget_code: 'Budget Code',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',
        st_code: 'Staff Code',
        staff_name_: 'Staff Name',
        vc_contra: 'Contra',

        sumText: 'This Period',
      },
    },
  },
  daily: {
    label: {
      name: 'Name',
      date: 'Date',
      type: 'Type',
      number: 'Code',
      amt_type: 'Method',
      desc: 'Description',
      staff: 'Staff',
      payee: 'Payee',
      payee_receipt: 'Payee',
      payee_payment: 'Payee',
      payee_c: 'Payee',
      vc_date: 'Vou.Date',
      all: 'All',
      allAccountType: 'All Account',
      allFundType: 'All Fund',
      allType: 'All Type',
      allDepartment: 'All Dept.',
      accountType: 'Account Type',
      crAmtSum: 'Cr.Amt.',
      drAmtSum: 'Dr.Amt.',
      amtSum: 'Total',
      normal: 'Normal Entry',
      correctOne: 'Audit Adjustment 1',
      correctTwo: 'Audit Adjustment 2',
      correctThree: 'Audit Adjustment 3',

      staffType: 'Staff Type',
      allStaff: 'ALl Staff',
      companyGroups: 'Company Groups',
      dept: 'Department',

      no: 'Code',
      staffNameCN: 'Chinese name',
      staffNameEN: 'English name',
      nameCN: 'Chinese name',
      nameEN: 'English name',
      comp_name: 'Name',
      comp_abbr: 'Abbr',
      allPayee: 'All Payee',
      account: 'Account Ledger',
      descr: 'Description',
      amount: 'Amount',

      group: 'Group',
      budgetItem: 'Budget Item',
      category: 'Category',
      ac_code: 'Company Code',
    },
    dialog: {
      voucherNO: 'Voucher No',
      account: 'Account',
      staff: 'Staff',
      contra: 'Contra',
      department: 'Department',
      description: 'Description',
      payee: 'Payee',
      budget: 'Budget',
      multipleVoucherImport: 'Voucher Import',
    },
    payment: {
      allMonth: 'All',
      label: {
        vc_no: 'Voucher Number',
        vc_date: 'Date',
        vc_summary: 'Description',
        vc_amount: 'Amount',
        vc_method: 'Method',
        ref: 'Chq. No.',
        vc_payee: 'Payee',
        st_name_: 'Staff Name',
      },
    },
    cheque: {
      typeOption: {
        type_X: 'No Cheque Number',
        type_N: 'Cheque Book Not Registered',
        type_C: 'Cheque No.',
      },
      label: {
        chq_no: 'Cheque No.',
        vc_chq_date: 'Cheque Date',
        vc_payee: 'Payee',
        vc_amount: 'Cheque Amount',
        vc_no: 'Voucher No.',
      },
      button: {
        chequeVoid: 'Void',
        chequeRestore: 'Restore',
      },
      message: {
        selectChequeBook: 'Please select cheque book',
        selectCheque: 'Please select cheque',
      },
    },
    address: {
      label: {
        allPayee: 'All Payees/Payers',
        payeeGroup: 'Range',
        optional: 'Pending selection',
        selected: 'Selected',
        search: 'Search',
      },
      button: {
        fetch: 'Search',
        printLabel: 'Print Label',
        printEnvelope: 'Print Envelope',
      },
    },
    voucher: {
      voucherMethod: {
        cash: 'CASH',
        // cheque: '支票', // 不顯�?        transf: 'TRANSF',
        // other: '其他', // 不顯�?        auto: 'AUTOPY',
      },
      label: {
        voucherType: 'Voucher Type',
        year: 'Year',
        month: 'Month',
        voucherCount: 'No. of Voucher(s): ',
        voucherTotal: 'Voucher Total: ',
        // column
        index: '#',
        account: 'A/C',
        desc: 'Description',
        amount: 'Amount (HK$)',
        amount_dr: 'Dr Amount\n(HK$)',
        amount_cr: 'Cr Amount\n(HK$)',
        voucherNo: 'No.',
        voucherDate: 'Date',

        paymentVoucher: 'PAYMENT VOUCHER',
        receiptVoucher: 'RECEIPT VOUCHER',
        cashVoucher: 'CASH VOUCHER',
        transferVoucher: 'TRANSFER VOUCHER',
        journalVoucher: 'JOURNAL VOUCHER',

        detailStyle: 'Detail display status',
        staff: 'Staff',
        ref: 'Ref',
        budget: 'Budget',
        quote: 'Quote',
        dept: 'Dept',
        contra: 'Contra Code',
        originalSummonsNumber: 'Old Vou. No.',
        summonsDate: 'Vouc. Date',
        checkNumber: 'Chq. No.',
        payee: 'Payee',
        moneny: 'Amount',
        newSummonsNumber: 'New Vou. No.',
      },
      button: {
        addRow: 'Add Line',
        deleteRow: 'Del Line',
        save: 'Save',
        cancel: 'Abandon',
        voucher: 'Voucher',
        chequePreview: 'Cheque Preview',
        cheque: 'Cheque',
        envelop: 'Envelop',
        reset: 'Reset',
        update: 'Update',
      },
      message: {
        inputVoucherNo: 'Please fill in the voucher number',
        inputVoucherInfo: 'Please fill in the voucher information',
        inputAccountingSubject: 'Please select an accounting subject',
        invalidAcCode: 'Invalid A/C Code',
        invalidStaff: 'Invalid Staff',
        invalidDept: 'Invalid Department',
        invalidContra: 'Invalid Contra',
        theAmountCannotBeZero: 'Amount cannot be zero',

        dcNotEqual: 'Dr/Cr amounts are not equal',

        selectFund: 'Please select account type',
        selectVoucherDate: 'Please select voucher date',

        voucherNotHasPayee: 'Selected voucher does not contain payee company/person',

        importRowErr: 'Row {row}: Missing required field {field}',
        dataLoadingFailed: 'Data loading failed, please refresh and try again!',
        descCannotEmpty: 'The description content cannot be empty',
      },
      printout: {
        ref: 'Ref',
        budget: 'Budget',
        staff: 'Staff',
        dept: 'Department',
        dept2: 'Dept',
        payee_receipt: 'Payee',
        payee_payment: 'Payee',
        voucherType: 'Voucher Type',
        bank: 'Bank',
        chq_no: 'Chq No.',
        chq_date: 'Chq Date',
        summary: 'Summary',
        paidBy: 'Paid By',
      },
    },
    post: {
      label: {
        vt_category: 'Category',
        vc_date: 'Date',
        ac_code: 'A/C Code',
        ac_name: 'A/C Name',
        Descr: 'Description',
        amount: 'Amount($)',
        Amount_Dr: 'Receipt($)',
        Amount_Cr: 'Payment($)',
        vt_description: 'Voucher Type',
        date: 'As At Date',
        voucher_date: 'Voucher Date',
        load_data: 'Load SR Data',
        detail: 'Detail',
        vc_vtcode: 'Voucher Type',
        Ref: 'Chq. No.',
        other: 'Other',
        method_of_payment: {
          CASH: 'Cash',
          CHECK: 'Cheque',
          AUTOPAY: 'Auto Pay',
          FPS: 'FPS',
          BANKIN: 'Bank In',
          OTHER: 'Other',
        },
        vc_summary: 'Description',
        generateVoucher: 'Generate Voucher',
        multipleEdit: 'Set Voucher Type',
        tra_date: 'Sale Date',
      },
    },
  },
  periodic: {
    bankReconciliation: {
      label: {
        vc_date: 'Date',
        vc_no: 'Vou. No.',
        descr: 'Description',
        vc_payee: 'Payee/Payer',
        vc_method: 'Method',
        ref: 'Cheque No.',
        amount_dr: 'Receipt($)',
        amount_cr: 'Payment($)',
        vc_rdate: 'Rec. Date',

        ledgerBalance: 'Ledger Balance',
        unpresentedPayment: 'Unpresented Payment',
        unpresentedReceipt: 'Unpresented Receipt',
        statementBalance: 'Statement Balance',
        CASH: 'Cash',
        TRANSF: 'Transfer',
        CHEQUE: 'Cheque',
        AUTO: 'Auto',
        OTHER: 'Other',
        clearAll: 'Clear All',
        isClearAll: 'Confirm clear all unpresented receipts and payments?',
      },
      button: {
        applyReceipt: 'Apply Receipt',
      },
    },
    dailyCollection: {
      filters: {
        fund: 'Fund',
        all: 'All',
        period: 'Period',
        to: '-',
        date: 'Date',
        begin_date: 'Begin Date',
        end_date: 'End Date',
      },
      label: {
        vc_date: 'Vou. Date',
        vc_no: 'Vou. No.',
        ref: 'Cheque No.',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        ac_name_cn: 'A/C Name(Chinese)',
        ac_name_en: 'A/C Name(English)',
        ac_abbr_cn: 'A/C Abbr(Chinese)',
        ac_abbr_en: 'A/C Abbr(English)',
        descr: 'Description',
        lg_amount: 'Breakdown',
        vc_amount: 'Total',
        vc_receipt: 'Rcpt. No.',
        vc_receipt_date: 'Rept. Date',
        vc_method: 'Method',
      },
      button: {
        updateReceipt: 'Update Receipt',
      },
    },
    autopayList: {
      typeOption: {
        type_P: 'Payment',
        type_R: 'Receipt',
      },
      label: {
        vc_date: 'Date',
        vc_no: 'Vou. No.',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        ac_name_cn: 'A/C Name(Chinese)',
        ac_name_en: 'A/C Name(English)',
        ac_abbr_cn: 'A/C Abbr(Chinese)',
        ac_abbr_en: 'A/C Abbr(English)',
        descr: 'Description',
        lg_amount: 'BreakDown',
        vc_amount: 'Total',
        bank_ac_code: 'Bank A/C',
        vc_receipt: 'Rcpt. No.',
        fund_name_: 'Fund',
        vc_payee: 'Payee/Payer',
        vc_method: 'Method',
        all: 'All',
      },
    },
    balanceTransfer: {
      label: {
        ac_code: 'Code',
        ac_name_: 'A/C Name',
        ac_abbr_: 'A/C Abbr Name',
        ac_B: 'YE TRF',
        bf_amount: 'Last Year Closing',
        ob_amount: 'Expected O/B',
        adj_amount: 'Adjusted O/B',

        lastData: 'Last Year Balance',
        thisData: 'This Year O/B',

        totalDr: 'Total Dr',
        totalCr: 'Total Cr',
      },
      amountType: {
        dr: 'Dr',
        cr: 'Cr',
      },
      button: {
        ob: 'OB',
        cs: 'CS',
        autoTransfer: 'Auto Transfer',
        update: 'Update',
      },
      message: {
        autoTransfer: 'Confirm overwrite of current adjusted O/B?',
      },
    },
    obCheque: {
      label: {
        amount: 'Amount',
        ref: 'Cheque No.',
        vc_date: 'Date',
        vc_payee: 'Payee',
        vc_rdate: 'Presented',

        cheque_date: 'Cheque Date',
        currentCheque: 'this Cheque',
        Unpaid: 'Unpaid',
      },
    },
  },
  master: {
    user: {
      name_cn: 'Chinese Name',
      name_en: 'English Name',
      username: 'Login Name',
      action: 'Operation',
      label: {
        name_cn: 'Chinese Nname',
        name_en: 'English Name',
        username: 'Login Name',
        password: 'Password',
        confirm: 'Confirm Password',
        type: 'Type',
        type_admin: 'Admin',
        type_general: 'general',
        role: 'Role',
        group: 'Group',
        add: 'Add',
        edit: 'Update',
      },
      value: {
        typeAdmin: 'A',
        typeGeneral: 'G',
      },
    },
    role: {
      role_name: 'Role Name',
      role_name_cn: 'Chinese Role',
      role_name_en: 'English Role',
      system: 'System',
      action: 'Operation',
      label: {
        role_name: 'Role Name',
        role_name_: 'Role Name',
        // role_id: 'Role ID',
        system: 'System',
        role_code: 'Role Code',
        role_name_cn: 'Chinese Role',
        role_name_en: 'English Role',
        // show: '用戶是否可見',
        // created_at: '創建時間',
        // updated_at: '更新時間'
      },
    },
    year: {
      all_year: 'All',
      fy_code: 'Code',
      fy_name: 'Year',
      action: 'Operation',
      month: 'Month',
      status: 'Status',
      yymm: 'YYMM',
      label: {
        fy_code: 'Code',
        fy_name: 'Year',
        fy_status: 'Status',
        fy_enquiry: 'Enquiry',
      },
      option: {
        open: 'Open',
        close: 'Close',
      },
    },
    fund: {
      all_fund: 'All Fund',
      all_fund2: 'All Fund',
      // fund_id: '賬目類別id',
      name_cn: 'Name(Chinese)',
      name_en: 'Name(English)',
      username: 'Login Name',
      action: 'Operation',
      label: {
        _index: '#',
        // fund_id: '賬目類別id',
        fund_code: 'Code',
        fund_name: 'Name',
        fund_name_: 'Name',
        fund_name_cn: 'Name(Chinese)',
        fund_name_en: 'Name(English)',
        fund_abbr: 'Abbr Name',
        fund_abbr_: 'Abbr Name',
        fund_abbr_cn: 'Abbr Name(Chinese)',
        fund_abbr_en: 'Abbr Name(English)',
        active_year: 'Active Year',
        parent_default: 'All Fund',
        seq_default: '--- Last Item ---',
      },
    },
    account: {
      all_account: 'All',
      label: {
        _index: '#',
        first_field: 'Code/Name',
        ac_bie: 'BIE',
        ac_code: 'Account Code',
        ac_name_cn: 'Name(Chinese)',
        ac_name_en: 'Name(English)',
        ac_abbr_cn: 'Abbr(Chinese)',
        ac_abbr_en: 'Abbr(English)',
        abbr_cn: 'Abbr(Chinese)',
        abbr_en: 'Abbr(English)',
        code: 'Account Code',
        parent_fund_id: 'Parent',
        parent_default: 'All',
        seq: 'Order (Before)',
        seq_default: '--- Last Item ---',
        nature_code: 'Account Nature',
        code_default: 'Grant',
        ac_category: 'Account Category',
        ac_bank: 'Bank',
        bank_default: 'Non-Bank',
        ac_cf: 'Acount',
        cf_default: '** Select Fund **',
        ac_group: 'Group',
        active_year: 'Active Year',
        income_bg_year: 'Income Budget',
        expense_bg_year: 'Expense Budget',
      },
      category: {
        B: 'B',
      },
      natureOptions: {
        option_G: 'Grant',
        option_IE: 'Income/Expense',
        option_I: 'Income',
        option_E: 'Expense',
        option_Dr: 'Dr Balance',
        option_Cr: 'Cr Balance',
      },
      bankOptions: {
        option_X: 'Non-Bank',
        option_S: 'S/A',
        option_C: 'C/A',
        option_F: 'F/D',
        option_P: 'Cash',
        option_A: 'A/P',
      },
    },
    voucher_type: {
      all: 'All',
      label: {
        // voucher_type_id: '傳票類別id',
        fund_id: 'Fund', // 賬目類別(�?id
        fund_name_: 'Fund', // 賬目類別(�?id
        vt_category: 'Category', // 傳票類別類型
        vt_code: 'Code', // 傳票類別編號
        vt_description_cn: 'Description(Chinese)', // 傳票類別描述中文
        vt_description_en: 'Description(English)', // 傳票類別描述英文
        vt_description_: 'Description',
        vt_format: 'Format', // 傳票類別格式
        vt_ac_code: 'Bank A/C', // 銀行賬�?會計科目編號)
        chq_template_code: 'Cheque Template', // 支票款式編號
        vt_group: 'Group', // 傳票類別組別
        active_year: 'Active Year', // 活躍的會計週期編號
        chq_name_: 'Cheque Name', // 活躍的會計週期編號
      },
      rules: {
        isExist: 'Code already exists',
      },
    },
  },
  assistance: {
    staff: {
      label: {
        first_field: 'Code/Name',
        // staff_id: '職員id',
        username: 'User Name',
        password: 'Password',
        st_name_cn: 'Staff Name(Chinese)',
        st_name_en: 'Staff Name(Englsh)',
        st_code: 'Staff_code',
        st_grade: 'Budget Grade',
        grade: 'Grade',
        active_year: 'Active Year',
        role_id: 'Budget Role',
        budgetRole: 'Budget Role',
        pc_role_id: 'PC Role',
        parent_st_type_id: 'Order (English)',
        seq: 'Order (Before)',
        parent_default: 'All Staff',
        seq_default: '--- Last Item ---',
      },
      grade: {
        A: 'Approver',
        M: 'Manager',
        G: 'Member',
        S: 'Staff',
      },
    },
    staff_type: {
      label: {
        st_type_code: 'Staff Code',
        st_type_cn: 'Name(Chinese)',
        st_type_en: 'Name(English)',
        parent_st_type_id: 'Parent',
        seq: 'Order (Before)',
        active_year: 'Active Year',
        parent_default: 'All',
        seq_default: '--- Last Item ---',
      },
    },
    department: {
      label: {
        _index: '#',
        first_field: 'Code/Name',
        // department_id: '部門id',
        dept_name_cn: 'Name(Chinese)',
        dept_name_en: 'Name(English)',
        dept_code: 'Code',
        active_year: 'Active Year',
        parent_dept_type_id: 'Parent',
        seq: 'Order (Before)',
        parent_default: 'All',
        seq_default: '--- Last Item ---',
      },
    },
    dept_type: {
      label: {
        dept_type_code: 'Dept Code',
        dept_type_cn: 'Name(Chinese)',
        dept_type_en: 'Name(English)',
        active_year: 'Active Year',
        parent_dept_type_id: 'Parent',
        seq: 'Order (Before)',
        parent_default: 'All',
        seq_default: '--- Last Item ---',
      },
    },
    contra: {
      label: {
        _index: '#',
        contra_code: 'Code',
        contra_name_cn: 'Chinese Name',
        contra_name_en: 'English Name',
      },
    },
    chequeBook: {
      allVoucherType: 'All Banks',
      label: {
        _index: '#',
        fund_id: 'Fund',
        fund_name_: 'Fund',
        vt_code: 'Type',
        chqbk_code: 'Code',
        chqbk_from: 'From',
        chqbk_to: 'To',
        active_year: 'Active Year',
        bank_code: 'Bank',
      },
    },
    description: {
      label: {
        multi_ac_code: 'Code',
        // desc_id: '傳票明細id',
        fund_id: 'Fund',
        ac_code: 'Code', // 歸屬會計科目編號
        desc: 'Description', // 描述
        BIE: 'BIE', // 會計科目的BIE歸屬,可多�?例如 B,I,E
        active_year: 'Active Year', // 活躍的會計週期編號
        selectAccount: 'Select Account',
      },
    },
    ledgerBudget: {
      allFund: 'All Fund',
      label: {
        range: 'Range',
        active_year: 'Active Year',
        code: 'Code',
        name: 'Ledger',
        income: 'Income Budget',
        expense: 'Expense Budget',
      },
    },
    fundSummaryTypesRelation: {
      year: 'Year',
      sch_type: 'Format',
      fixWrongVoucher: 'Need to fix voucher no.',
      sch_type_pri: 'Primary School',
      sch_type_sec: 'Secondary School',
    },
    payeePayer: {
      label: {
        first_field: 'Code/Name',
        comp_code: 'Code',
        comp_name: 'Name',
        comp_abbr: 'Abbreviation',
        parent_company_group_id: 'Parent',
        seq: 'Order (Before)',
        comp_group: 'Group',
        comp_supplier: 'Supplier',
        comp_add: 'Address',
        comp_tel: 'Tel.',
        comp_fax: 'Fax.',
        comp_attention: 'Attention',
        comp_email: 'Email',
        comp_remark: 'Remark',
        comp_ap: 'A/P',
        comp_credit: 'credit',
        active_year: 'Active Year',
        seq_default: '--- Last Item ---',
        parent_default: 'All Payees/Payers',
      },
    },
    payeePayerGroup: {
      label: {
        cg_code: 'Account Code',
        cg_name_cn: 'Name(Chinese)',
        cg_name_en: 'Name(English)',
        parent_company_group_id: 'Parent',
        seq: 'Order (Before)',
        active_year: 'Active Year',
        seq_default: '--- Last Item ---',
        parent_default: 'All Payees/Payers',
      },
      groupOptions: {
        B: 'Payment/Receipt',
        I: 'Payment',
        E: 'Receipt',
      },
    },
    numberOfSchool: {
      month: 'Month',
      num_of_half_day: '半日',
      num_of_infant: '幼兒',
      num_of_whole_day: '全日',
      scale_of_whole_day: '分攤比率',
    },
  },
  stock: {
    years: 'Year',
    months: 'Month',
    style: 'Style',
    mode: 'Mode',
    stockItem: 'Stock Item',
    purchase: {
      label: {
        pi_no: 'Inv. No.',
        date: 'Date',
        pi_payee: 'Supplier',
        comp_name: 'Supplier',
        chq_no: 'Cheque No.',
        voucher_no: 'Voucher No.',
        count: 'Count',
        price: 'Unit Price',
        amount: 'Amount',
        items: 'Items',
        openingBalance: 'Opening Balance',
        sk_code: 'Code',
        total: 'Total',
      },
    },
    sales: {
      label: {
        date: 'Date',
        type: 'Type',
        chq_no: 'Cheque No.',
        voucher_no: 'Voucher No.',
        count: 'Count',
        price: 'Unit Price',
        amount: 'Amount',
        items: 'Items',
        openingBalance: 'Opening Balance',
        sk_code: 'Code',
        total: 'Total',
      },
    },
    itemSetup: {
      label: {
        first_field: 'Code/Name',
        sk_code: 'Code',
        sk_name_cn: 'Name(Chinese)',
        sk_name_en: 'Name(English)',
        name_cn: 'Name(Chinese)',
        name_en: 'Name(English)',
        parent_stock_group_id: 'Parent',
        seq: 'Order (Before)',
        active_year: 'Active Year',
        seq_default: '--- Last Item ---',
        parent_default: 'All Stock Items',
        purchase_price: 'Purchase Price',
        sales_price: 'Sales Price',
        date: 'Date',
      },
      button: {
        goodsImport: 'Goods Import',
        priceImport: 'Price Import',
        goodsExport: 'Goods Export',
        priceExport: 'Price Export',
      },
    },
    itemSetupGroup: {
      label: {
        sg_code: 'Code',
        sg_name_cn: 'Name(Chinese)',
        sg_name_en: 'Name(English)',
        parent_stock_group_id: 'Parent',
        seq: 'Order (Before)',
        active_year: 'Active Year',
        seq_default: '--- Last Item ---',
        parent_default: 'All Stock Items',
      },
    },
    monthlySales: {
      allMonth: 'Whole Year',
      label: {
        sk_code: 'Code',
        desc: 'Description',
        quantity: 'Qty',
        averagePrice: 'Sales Price',
        amount: 'Amount',
        total: 'Total',
        monthTotal: 'Monthly Total',
        yearTotal: 'Yearly Total',
      },
    },
    salesProfit: {
      label: {
        sales: 'Sales',
        less: 'Less',
        openingStock: 'Opening Stock',
        purchase: 'Purchase',
        writeOff: 'Write Off',
        self: 'Self',
        DM: 'DM',
        closingStock: 'Closing Stock',
        cost: 'Cost of Goods Sold',
        salesProfit: 'Sales Profit',
        DMSWO: 'D/M,I/U & W/O',
        netProfit: 'Net Profit',
        percentageProfit: 'Percentage Of Profit On Const',
        title: 'Sales Profit',
      },
    },
    stockBalance: {
      label: {
        breakdown: 'Breakdown',
        breakdownAndSummary: 'Breakdown + Summary',
        summary: 'Summary',

        opening: 'opening',
        purchase: 'Purchase',
        sale: 'Sale',
        balance: 'Balance',
        adjustment: 'Adjustment',
        finalBalance: 'Final Balance',

        quantity: 'Qty',
        unitPrice: 'U.Cost',
        amount: 'Amount',
        sk_code: 'Code',
        name: 'Name',
        out_sale_avg: 'Price',
        out_sale_amount: 'S.Amount',
        out_amount: 'C.Amount',
        adj_d_qty: 'D/M',
        adj_u_qty: 'I/U',
        adj_w_qty: 'W/O',
        adj_amount: 'Adj. Cost',

        total: 'Total',
      },
    },
    stockIo: {
      label: {
        date: 'Date',
        inv_chq_no: 'Inv./Chq.',
        voucher_no: 'Voucher',
        type: 'Type',
        in: 'In',
        out: 'Out',
        balance: 'Balance',
        quantity: 'Qty',
        unitPrice: 'U.Price',
        amount: 'Amount',
        total: 'Total',
      },
    },
    profitReport: {},

    label: {
      month: 'This Month',
      year: 'Whole Year',
      to: 'To',

      sales: 'Sales',
      damagedOrLost: 'Damaged/Lost',
      internalUse: 'Internal Use',
      writeOff: 'Write Off',
      purchase: 'Purchase',
    },
  },
  report: {
    trialBalance: {
      pdf: {
        index: '#',
        code: 'Code',
        name: 'Name',
        bf_dr: 'Dr(HK$) ',
        bf_cr: 'Cr(HK$) ',
        dr: 'Dr(HK$) ',
        cr: 'Cr(HK$) ',
        end_dr: 'Dr(HK$) ',
        end_cr: 'Cr(HK$) ',
        sum: 'Total',
        bf: 'OB',
        mid: 'This Period',
        end: 'CS',
      },
      label: {
        fund: 'Fund',
        wholeYear: 'Whole Year',
        year: 'Year',
        period: 'Period',
        mode: 'Mode',
      },
      option1: '#1',
      option2: '#2',
      option3: '#3',
        skipZero: 'skip Zero',
        auditAdjust: 'Audit Adjust',

        dateTo: 'As at {date}',
        bf: 'OB',
        mid: 'This',
        end: 'CS',

        index: '#',
        code: 'Code',
        name: 'A/C Name',
        bf_dr: 'Dr',
        bf_cr: 'Cr',
        dr: 'Dr',
        cr: 'Cr',
        end_dr: 'Dr',
        end_cr: 'Cr',
        sum: 'Total',
      },
      content: {
        individualCode: 'individual-Code',
        // individualStructure: '個別-結構',
        TreeStructure: 'Tree Structure',
        all: 'All',
        none: 'None',
      },
      message: {
        yearSelect: 'Please entry year',
        fundSelect: 'Please entry account',
      },
    },
    ledger: {
      label: {
        title: 'Ledger Report',
        ac_code: 'A/C Code',
        vc_date: 'Date',
        vc_no: 'Voucher No.',
        descr: 'Description',
        vc_payee: 'Payee',
        st_code: 'Staff Code',
        st_name_: 'Staff name',
        budget_code: 'Budget Code',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        net_amount: 'Net Amount',
        amount_balance: 'Balance',
        dept_code: 'Dept Code',
        dept_name_: 'Dept Name',
        ref: 'Ref',
        chq_no: 'Chq. No.',
        ac_name_: 'A/C Name',

        expenditure: 'Expend',
        income: 'Income',

        range: 'Range',
        style: 'Style',
        Show: 'Show',

        order: 'Sort',
      },
      content: {
        drCrBalance: 'Dr/(Cr)Balance',
        drBalance: 'Dr Balance',
        crBalance: 'Cr Balance',

        netDr: 'Net Dr',
        netCr: 'Net Cr',
        netExpenditure: 'Net Expenditure',
        netIncome: 'Net Income',
        NetIncomeAndExpenditure: 'Net Income And Expenditure',
        grantBalance: 'Grant Balance',
        balance: 'Balance',

        drSum: 'Dr Balance',
        crSum: 'Cr Balance',
        cumulativeExpenditure: 'Cumulative Expenditure',
        cumulativeIncome: 'Cumulative Income',
        accumulatedRevenueAndExpenditure: 'Accumulated Revenue And Expenditure',

        normalEntry: 'Normal Entry',
        entryAndAdjustment: 'Entry +Adjustment',
        entryAndAdjustmentAndYearEnd: 'Entry + Adjustment + YearEnd',

        byDate: 'By Date',
        byVcNo: 'By Vc.No.',
        byAcCode: 'By A/C Code',
      },
    },
    cashBook: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher No.',
        ref: 'Cheque No.',
        vc_payee: 'Payee',
        descr: 'Description',
        vc_method: 'Method',
        break_down: 'Breakdown',
        ac_name_: 'A/C Name',
        amount_dr: 'Income',
        amount_cr: 'Expense',
        amount_balance: 'Balance',
        title: 'Petty Cash Payment/Receipt Report',
      },
    },
    cashBookBank: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher No.',
        ref: 'Cheque No.',
        vc_payee: 'Payee',
        descr: 'Description',
        vc_method: 'Method',
        break_down: 'Breakdown',
        ac_name_: 'A/C Name',
        amount_dr: 'Income',
        amount_cr: 'Expense',
        amount_balance: 'Balance',
        title: 'Bank Payment/Receipt Report',
      },
    },
    budget: {
      message: {
        selectBudget: 'Please select a budget',
      },
    },
    staff: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher No.',
        // ref: '支票號碼',
        mark: 'Mark',
        vc_payee: 'Payee',
        descr: 'Description',
        vc_method: 'Method',
        break_down: 'Breakdown',
        ac_name_: 'A/C Name',
        // amount_dr: '收入',
        // amount_cr: '支出',
        // amount_balance: '結餘',
        tx_num: 'Line',
        ac_code: 'A/C Code',
        ref: 'Ref',
        st_code: 'Staff Code',
        st_name_: 'Staff Name',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        amount_net: 'Net Amount',
        amount_balance: 'Balance',
        total: 'Total',
        title: 'Staff Ledger Report',
      },
    },
    department: {
      label: {
        vc_date: 'Date',
        vc_no: 'Voucher No.',
        // ref: '支票號碼',
        mark: 'Mark',
        vc_payee: 'Payee',
        descr: 'Description',
        vc_method: 'Method',
        break_down: 'Breakdown',
        ac_name_: 'A/C Name',
        // amount_dr: '收入',
        // amount_cr: '支出',
        // amount_balance: '結餘',
        tx_num: 'Line',
        ac_code: 'A/C Code',
        ref: 'Ref',
        vc_dept: 'Dept Code',
        dept_name_: 'Dept Name',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        amount_net: 'Net Amount',
        amount_balance: 'Balance',
        total: 'Total',
        title: 'Department Ledger Report',
      },
    },
    voucher: {
      label: {
        title: 'Voucher List ',
        category: 'Type',
        voucherType: 'Fund',
        total: 'Total',
        tx_num: '#',
        vc_no: 'Voucher No.',
        vc_date: 'Date',
        ac_code: 'A/C Code',
        ac_name_: 'A/C Name',
        descr: 'Description',
        vc_payee: 'Payee',
        ref: 'Ref.',
        st_code: 'Staff Code',
        st_name_: 'Staff Name',
        budget_code: 'Budget Code',
        amount_dr: 'Dr Amount',
        amount_cr: 'Cr Amount',
        tx_type: 'IE',
      },
      content: {
        allFund: 'All Fund',
        allCategory: 'All Category',
        paymentVoucher: 'Payment',
        cashVoucher: 'Cash',
        receiptVoucher: 'Receipt',
        ap: 'A/P',
        transferVoucher: 'Transfer',
        journalVoucher: 'Journal',

        allVoucher: 'All Voucher',
      },
    },
    excelReport: {
      report: 'Report',
      year: 'Year',
      month: 'Month',
      financialStatement: 'Financial Report',
      edb: 'Financial Report (EDB)',
      edbSecondarySchool: 'Financial Report (Secondary School EDB)',
      swd: 'SWD Report',
    },
  },
  setting: {
    basic: {
      name: 'Base Setting',
      font: 'Font',
      font_color: 'Font Color',
      background_color: 'Background',

      menu: 'Menu',
      content: 'Content',

      font_family: 'Font Face',
      font_size: 'Font Size',

      content_background_color: 'Theme',
      content_line_height: 'Line Height',
      date_format: 'date Format',

      fontFamilyList: {
        sim_sun: 'SimSun',
        sim_hei: 'SimHei',
        microsoft_yahei: 'Microsoft YaHei',
        kai_ti: 'KaiTi',
      },

      select_font_placeholder: 'Please select a font',
      main_menu_font_size: 'Main Menu Font Size',
      second_menu_font_size: 'Second Menu Font Size',
      main_menu_background_color: 'Main Menu Background Color',
      second_menu_background_color: 'Second Menu Background Color',
      second_menu_hover_color: 'Second Menu Hover Color',
    },
    settingPageSet: {
      ps_code: 'Code',
      ps_name_cn: 'Chinese',
      ps_name_en: 'English',
      width: 'Width',
      height: 'Height',
      orientation: 'Orientation',
      transverse: 'Transverse',
      vertical: 'Vertical',
    },
    user_info: {
      username: 'Username',
      password: 'Password',
      new_password: 'New Password',
      confirm_password: 'Confirm Password',
    },
    printout: {
      label: {
        unitTips: 'unit: mm',
        unitTipsPercentage: 'unit: %',

        style: 'Style',
        styleName: 'Style Name',
        pageStyle: 'Paper style',
        pageSize: 'Paper Size',
        // pageWidth: '紙闊',
        // pageHeight: '紙長',
        pageOrientation: 'Orientation',
        language: 'Language',

        margin: 'Margin',
        // marginTop: '上邊�?,
        // marginBottom: '下邊�?,
        // marginLeft: '左邊�?,
        // marginRight: '右邊�?,
        tableHeaderHeight: 'Header Height',
        titleWidth: 'Title Width',
        descHeight: 'Description Height',

        signStyle: 'Sign Style',
        signNum: 'Sign Num',
        signLine: 'Sign Line',
        stage_cn: 'Stage(Chi)',
        stage_en: 'Stage(Eng)',
        position_cn: 'Position(Chi)',
        position_en: 'Position(Eng)',
        signHeight: 'Sign Height',
        signSpace: 'Sign Space',

        fontSize: 'Font Size',
        fontSizeSchCN: 'Sch.Name(C)',
        fontSizeSchEN: 'Sch.Name(E)',
        fontSizeTitle: 'Title',
        fontSizeContent: 'Content',
        fontSizeSignature: 'Signature',
        fontSizePageNum: 'Page Numbers',

        tableFooterHeight: 'Footer Height',
        groupBreak: 'Group Break',
        'break': 'Break',
        dividingLine: 'Dividing Line',
        fundAbbr: 'Fund Abbr',
        adjustment: 'Adjustment',
        position: 'Position',
        blankLedger: 'Blank Ledger',

        tableHeaderColumns: 'Header Field',
        tableContentColumns: 'Content Field',

        itemNumPerPage: 'Item Num Per Page',
        itemNumPerPageTips:
          'When the number of items exceeds the set value, it will be displayed in multiple pages',

        stepCN: 'Stage(Chi)',
        stepEN: 'Stage(Eng)',
        postCN: 'Position(Chi)',
        postEN: 'Position(Eng)',

        rowNum: 'Row Num',
        colNum: 'Column Num',

        paperPreview: 'Paper Preview',

        offset: 'Offset',
        cheque: 'Cheque',
        fontWeight: 'Font Weight',
        amount: 'Amount',
        text_amount: 'Text Amount',
        text_width: 'Text Width',
        text_indent: 'Text Indent',
        line_height: 'Line Height',
        ac_payee_only: 'A/C Payee Only',
        crossed_bearer: 'Crossed Bearer',
        crossed_width_space: 'Crossed Width/Space',
        date: 'Date',
        payee: 'Payee',
        payee_width: 'Payee Width',

        stub_date: 'Stub Date',
        stub_payee: 'Stub Payee',
        stub_payee_width: 'Stub Payee Width',
        stub_desc: 'Stub Desc',
        stub_amount: 'Stub Amount',
        stubFontSize: 'Stub Font Size',
      },
      content: {
        english: 'English',
        chinese: 'Chinese',
        auto: 'Auto',
        numeric: 'Numeric',

        noSignature: 'No Signature',
        floating: 'Floating',
        fixed: 'Fixed',
        signatureNumber_1: '1 Signatures',
        signatureNumber_2: '2 Signatures',
        signatureNumber_3: '3 Signatures',
        signatureNumber_4: '4 Signatures',
        signatureNumber_5: '5 Signatures',
        signatureNumber_6: '6 Signatures',

        line: 'Line',
        box: 'Box',

        alignment_left: 'Left',
        alignment_center: 'Center',
        alignment_right: 'Right',

        noPageBreak: 'No Page Break',
        pageBreak: 'Page Break',

        toPrint: 'Print',
        notPrint: 'Not Print',

        landscape: 'Landscape',
        portrait: 'Portrait',

        normal: 'Normal',
        bold: 'Bold',
      },
      button: {
        update: 'Update',
        defaultValue: 'default',
      },
      navMenu: {
        daily: 'Daily',
        daily_payment: 'Payment Voucher',
        daily_receipt: 'Receipt Voucher',
        daily_petty_cash: 'Petty Cash Voucher',
        daily_bank_transfer: 'Transfer Voucher',
        daily_journal: 'Journal Voucher',
        daily_voucher_list: 'Voucher Checklist',
        daily_voucher_consolidate: 'ConsolidateVoucher',
        daily_payment_receipt: 'Payment Receipt',
        daily_receipt_receipt: 'Income receipt',
        daily_mailing_label: 'Mailing Label',
        daily_envelope: 'Envelop',
        daily_cheque: 'Cheque',
        periodic: 'Periodic',
        periodic_bank_reconciliation: 'Bank Rec. Statement',
        periodic_daily_collection: 'Daily Collection Report',
        periodic_autopay_list: 'Autopay List',
        periodic_balance_transfer: 'Year-end Transfer Voucher',
        report: 'Report',
        report_trial_balance: 'Trial Balance',
        report_ledger: 'General Ledger List',
        // report_ledger_complex: '總賬賬目綜合列表',
        report_cash_book: 'Petty Cash Book',
        report_cash_book_bank: 'Bank Cash Book',
        report_budget: 'Budget Ledger List',
        report_staff: 'Staff Ledger List',
        report_department: 'Department Ledger List',
        report_voucher: 'Vouchers List',
        budget: 'Budget',
        budget_balance: 'Budget Balance Report',
        stock: 'Stock',
        stock_profit_report: 'Sales Profit Report',
        test: 'Test',
        test_portrait: 'Vertical Test',
        test_landscape: 'Transverse Test',
        test_print: 'Print Debugging',
        stock_sales_proffit: 'Profit Report',

        bg_budget_list: 'Budget List',
        to: '�?,
        enclosed: '謹附上銀行支票一張號�?,
        amount: '港幣',
        in_payment_of: '以付',
        with_invoice: '發票號碼',
        please_issue_receipt: '請寄回收據為荷！',
        year: '�?,
        month: '�?,
        day: '�?,
        address: '收據請寄',
        Please_end_receipts_to: 'Please send receipt(s) to ',
        Tel: '電話Tel. : ',
        Fax: '傳真Fax. : ',
      },
      daily: {
        payment: {
          content: {
            style_standard: '標準付款傳票',
            style_standard_2: '標準付款傳票2',
          },
        },
        voucherList: {
          index: '#',
          vc_no: 'Voucher No.',
          vc_date: 'Date',
          vc_summary: 'Description',
          vc_amount: 'Total',
          vc_amount_detail: 'Breakdown',
          vc_method: 'Method',
          ref: 'Cheque No.',
          vc_payee: 'Payee',
          vc_payer: 'Payer',
          vc_payee_r: 'Payee',
          ac_name: 'A/C Name',
          ac_code: 'A/C Code',
          vc_receipt: 'Receipt No.',
          row: 'Line',

          category: 'Type',
          month: 'Month',
        },
        consolidateVoucher: {
          title_p: 'Consolidation Payment Voucher({year})',
          title_r: 'Consolidation Receipt Voucher({year})',
          title_c: 'Consolidation Cash Voucher({year})',
          title_t: 'Consolidation Bank Transfer({year})',
          title_j: 'Consolidation Journal Voucher({year})',
          cheque_count: 'Tx {count}',
          transf_count: 'Tx {count}',
          other_count: 'Tx {count}',

          index: '#',
          vc_no: 'Voucher No.',
          vc_date: 'Date',
          vc_summary: 'Description',
          ac_name: 'A/C Name',
          payment_info: 'payment Info',
          cheque_date: 'Cheque Date',
          cheque_amount: 'Cheque Amount',
          cheque_no: 'Cheque No.',
          transfer_amount: 'Transfer Amount',
          other_amount: 'Cash Or Other',
          vc_payee: 'Payee',
          st_code: 'Staff Code',
          st_name: 'Staff Name',

          bank: 'Bank A/C',
          account_type: 'Fund Type',
          date: 'Date',
          allBank: 'All Bank',
          allAccount: 'All Funds',
          tips1:
            '* Cheque and relevant documents including formal receipt should be attached for all payment transaction.',
          tips2: '* Seperate forms should be used for different bank accounts',
        },
        receipt: {
          title_payment: 'Payment Receipt',
          title_receipt: 'Income Receipt',
        },
      },
      periodic: {
        label: {
          autoPayList: 'Autopay List',
          bankReconciliation: 'Bank Reconciliation',
          dailyCollection: 'Daily Collection',
          openingBalance: 'Opening Balance',
          closingBalance: 'Closing Balance',

          fund: 'Fund',
          bank: 'Bank',
          period: 'Period',
          year: 'Year',
          category: 'Category',

          all: 'All',
        },
        autopayList: {
          label: {
            // columnsField

            vc_date: 'Date',
            vc_no: 'Voucher No.',
            ref: 'Ref',
            ac_code: 'A/C Code',
            ac_name_: 'A/C Name',
            ac_name_cn: 'A/C Name(Chinese)',
            ac_name_en: 'A/C Name(English)',
            ac_abbr_cn: 'A/C Abbr(Chinese)',
            ac_abbr_en: 'A/C Abbr(English)',
            descr: 'Description',
            lg_amount: 'breakdown',
            vc_amount: 'Total',
            fund_name_: 'Fund',
            bank_ac_code: 'Bank A/C',
            vc_payee: 'Payee/Payer',
            vc_receipt: 'Receipt No.',
            vc_receipt_date: 'Receipt Date',
            vc_method: 'Method',
          },
          content: {
            style_standard: 'Standard Autopay List',

            page_style_a4_landscape: 'A4 Landscape',
            page_style_a4_pdf_landscape: 'A4 PDF Landscape',
            page_style_a4_portrait: 'A4 Portrait',
            page_style_a4_pdf_portrait: 'A4 PDF Portrait',
            page_style_a5_pdf_landscape: 'A5 PDF Landscape',
            page_style_boc_cheque: 'BOC Cheque',
            page_style_dbs_cheque: 'DBS Cheque',
            page_style_bea_cheque: 'BEA Cheque',
            page_style_standard_envelope_1: 'Standard Envelope 1',
            page_style_standard_envelope_2: 'Standard Envelope 2',
            page_style_hsb_cheque: 'HSB Cheque',
            page_style_hsbc_cheque: 'HSBC Cheque',
            page_style_scb_cheque: 'SCB Cheque',
            page_style_wlb_cheque: 'WLB Cheque',
          },
          button: {
            update: 'update',
            defaultValue: 'default',
          },
        },
        bankReconciliation: {
          label: {
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            descr: 'Description',
            vc_payee: 'Payee/Payer',
            ref: 'Cheque No.',
            amount: 'Amount',
            total: 'Total',

            bf_amount: 'Balance as per Cash Book as at {date} : ',
            end_amount: 'Balance as per Bank Statement as at {date} : ',
            unpresentedPayment: 'Add Unpresented Payment�?,
            unpresentedReceipt: 'Less Unpresented Receipt�?,
          },
        },
        dailyCollection: {
          label: {
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            ref: 'Ref',
            ac_code: 'A/C Code',
            ac_name: 'A/C Name',
            descr: 'Description',
            lg_amount: 'Breakdown',
            vc_amount: 'ToTal',
            fund_name_: 'Fund',
            vc_receipt: 'Receipt Code',
            vc_receipt_date: 'Receipt Date',
            vc_payee: 'Payee',
            signature: 'Signature',
          },
        },
        balanceTransfer: {
          label: {
            index: '#',
            ac_code: 'A/C Code',
            ac_name: 'A/C Name',
            descr: 'Description',
            amount_dr: 'Dr Amount',
            amount_cr: 'Cr Amount',
          },
        },
      },
      report: {
        cashBook: {
          label: {
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            ref: 'Cheque No.',
            mark: 'Mark',
            vc_payee: 'Payee/Payer',
            descr: 'Description',
            vc_method: 'Method',
            break_down: 'Breakdown',
            ac_name_: 'A/C Name',
            amount_dr: 'Income',
            amount_cr: 'Expense',
            amount_balance: 'Balance',
          },
        },
        cashBookBank: {
          label: {
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            ref: 'Cheque No.',
            mark: 'Mark',
            vc_payee: 'Payee/Payer',
            descr: 'Description',
            vc_method: 'Method',
            break_down: 'Breakdown',
            ac_name_: 'A/C Name',
            amount_dr: 'Income',
            amount_cr: 'Expense',
            amount_balance: 'Balance',
          },
        },
        budget: {
          label: {
            ac_code: 'A/C Code',
            vc_date: 'Date',
            vc_no: 'Vou. No.',
            tx_num: '#',
            descr: 'Description',
            // ref: '支票號碼',
            vc_payee: 'Payee/Payer',
            st_code: 'Staff Code',
            st_name_: 'Staff Name',
            budget_code: 'Budget Code',
            amount_dr: 'Dr Amt.',
            amount_cr: 'Cr Amt.',
            // amount_net: '凈金�?,
            amount_balance: 'Balance',
            ref: 'Ref',
            vc_dept: 'Dept Code',
            dept_name_: 'Dept Name',
            title: 'Budget Ledger Report',
            budget_name_: 'Budget Name',
            IE: 'I / E',
            I: 'I',
            E: 'E',
            B: 'B',
            all: 'All',
            total: 'Total',
          },
        },
        staff: {
          label: {
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            // ref: '支票號碼',
            vc_payee: 'Payee/Payer',
            descr: 'Description',
            tx_num: '#',
            ac_code: 'A/C Code',
            ref: 'Ref',
            st_code: 'Staff Code',
            st_name_: 'Staff Name',
            amount_dr: 'Dr Amount',
            amount_cr: 'Cr Amount',
            amount_net: 'Net Amount',
            amount_balance: 'Balance',
            title: 'Staff Ledger Report',
          },
        },
        department: {
          label: {
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            // ref: '支票號碼',
            vc_payee: 'Payee/Payer',
            descr: 'Description',
            tx_num: '#',
            ac_code: 'A/C Code',
            ref: 'Ref',
            vc_dept: 'Dept Code',
            dept_name_: 'Dept Name',
            amount_dr: 'Dr Amount',
            amount_cr: 'Cr Amount',
            amount_net: 'Net Amount',
            amount_balance: 'Balance',
            title: 'Department Ledger Report',
          },
        },
        trialBalance: {
          label: {
            index: '#',
            code: 'Code',
            name: 'A/C Name',
            bf: 'OB',
            mid: 'This',
            end: 'CS',
          },
        },
        voucher: {
          label: {
            vc_no: 'Voucher No.',
            vc_date: 'Date',
            tx_num: '#',
            ac_code: 'A/C Code',
            ac_name_: 'A/C Name',
            descr: 'Description',
            vc_payee: 'Payee/Payer',
            ref: 'Ref',
            st_code: 'Staff Code',
            st_name_: 'Staff Name',
            budget_code: 'Budget Code',
            amount_dr: 'Dr Amount',
            amount_cr: 'Cr Amount',
            tx_type: 'IE',
          },
        },
        ledger: {
          label: {
            ac_code: 'A/C Code',
            vc_date: 'Date',
            vc_no: 'Voucher No.',
            descr: 'Description',
            vc_payee: 'Payee/Payer',
            st_code: 'Staff Code',
            st_name_: 'Staff Name',
            budget_code: 'Budget Code',
            amount_dr: 'Dr Amount',
            amount_cr: 'Cr Amount',
            net_amount: 'Net Amount',
            amount_balance: 'Balance',
            dept_code: 'Dept Code',
            dept_name_: 'Dept Name',
            ref: 'Ref',
            ac_name_: 'A/C Name',
            chq_no: 'Chq. No.',
          },
        },
      },
      budget: {
        budgetList: {
          label: {
            budget_code: 'Budget Code',
            name: 'Item',
            manager: 'Manager',
            budget_stage: 'Status',
            budget_IE: '',
            amount: 'Amount',
            lastYear: 'Last Year',
            thisYear: 'This Year',

            budget_amount: 'Budget Amount',
            proposed_amount: 'Prop.Amount',
            approved_amount: 'Appr.Amount',
            actual_amount: 'Act.Amount',
            balance: 'Balance',
          },
        },
      },
    },
    dataImport: {
      year: 'Year',
    },
    dataBackup: {
      dataBackup: 'Data Backup',
      dataRestore: 'Data Restore',
      backupTips: 'It is recommended to backup before restoring data to ensure data security.',
      restoreTips: 'Select a data backup record to restore',
      backup: 'Backup Now',
      backupSuccess: 'Backup Successful',
      restoreSuccess: 'Restore Successful',
      selectFile: 'Please select backup file',
    },
    restoreDemoData: {
      confirmTitle: 'Restore Demo Data',
      confirmContent: 'Confirm all Demo data?',
      restoreSuccess: 'Restore Successful',
      restoreSuccessContent: 'Click confirm to re-login',
    },
  },
  budget: {
    budgetSet: {
      label: {
        first_field: 'Code/Name',
        manager_name_: 'Manager',
        member_name_: 'Member',
        amount: 'Amount',

        budget_code: 'Budget Code',
        name_cn: 'Name(CN)',
        name_en: 'Name(EN)',
        parent: 'Parent',
        position: 'Order(Before)',
        EIType: 'Type',
        budgetGroup: 'Budget Group',
        budget_stage: 'Stage',
        budget_active: 'Active',
        audit: 'Audit',
        member: 'Member',
        manager: 'Manager',
        proposed_amount: 'Proposed Amount',
        approved_amount: 'Approved Amount',
        last_proposed_amount: 'Last Proposed Amount',
        last_approved_amount: 'Last Approved Amount',
        budget_account: 'Budget Account',

        income: 'Income',
        expense: 'Expense',
        incomeAndExpenditure: 'incomeAndExpenditure',

        parent_default: 'All',
        seq_default: '--- Last Item ---',

        stage_x: 'Not Submitted',
        stage_s: 'Submitted',
        stage_a: 'Approved',
        stage_o: 'Accepted',
        stage_c: 'Confirmed',
        stage_r: 'Revising',
        stage_t: 'Revised',
        stage_k: 'Re-approved',
        stage_b: 'Re-accepted',
        stage_d: 'Re-confirmed',

        memberTypeE: 'Extra',
        memberTypeN: 'Normal',
        memberTypeL: 'Limit',

        emptyManager: 'NO Manager',
        emptyMember: 'NO Member',
        cn: 'CN',
        en: 'EN',
      },
    },
    accountInput: {
      label: {
        type: 'Type',
        account: 'Account',
      },
    },
    budgetList: {
      label: {
        category: 'Category',
        budget_stage: 'Status',
        manager: 'Manager',
        approve: 'Approve',

        budget_code: 'Item',
        name_: 'Name',
        manager_staff_name_: 'Manager',
        budget_IE: 'IE',

        budget_amount: 'Budget Amount',

        proposed_amount: 'Prop.Amount',
        approved_amount: 'Appr.Amount',
        actual_amount: 'Act.Amount',
        balance: 'Balance',

        total: 'Total',

        lastYear: 'Last Year',
        thisYear: 'This Year',

        budgetList: 'Budget List',
        year: 'Year',
      },
    },
    budgetManagement: {
      label: {
        budget: 'Budget',
        budget_stage: 'Status',
        manager: 'Manager',
        approve: 'Approve',

        // budget_code: '編號',
        name_: 'Item',
        manager_staff_name_: 'Manager',
        budget_IE: 'IE',

        budget_amount: 'Budget Amount',

        proposed_amount: 'Prop.Amount',
        approved_amount: 'Appr.Amount',
        actual_amount: 'Act.Amount',
        balance: 'Balance',
        balancePercentage: 'Bal.%',

        revised_amount: 'Rev.Amount',

        total: 'Total',

        lastYear: 'Last Year',
        thisYear: 'This Year',

        vc_date: 'Date',
        vc_no: 'Voucher No',
        descr: 'Description',
        vc_payee: 'Payee/Payer',
        ref: 'Invoice No.',
        amount: 'Amount',
        budget_code: 'Budget Code',
        ac_code: 'A/C Code',
        fullList: 'Full List',
        canNextApproval: 'Can Next Approval',
      },
      button: {
        // X 未遞�?        notSubmitted: 'Not Submitted',
        submitBudget: 'Submit Budget',
        // S 已遞�?        withdrawSubmission: 'Withdraw Submission',
        approveBudget: 'Approve Budget',
        submitted: 'Submitted',
        // A 已審�?        withdrawApproval: 'Withdraw Approval',
        acceptApproval: 'Accept Approval',
        approved: 'Approved',
        // O 已接�?        withdrawAcceptance: 'Withdraw Acceptance',
        accepted: 'Accepted',
        confirmAcceptance: 'Confirm Acceptance',
        // C 已確�?        withdrawConfirmation: 'Withdraw Confirmation',
        confirmed: 'Confirmed',
        reviseMode: 'Revise Mode',
        // R 修訂�?        cancelReviseMode: 'Cancel Revise Mode',
        revising: 'Revising',
        summitRevision: 'Summit Revision',
        // T 己修�?        withdrawRevision: 'Withdraw Revision',
        revised: 'Revised',
        reApproveBudget: 'Re-Approve Budget',
        // K 已重�?        withdrawReApproval: 'Withdraw Re-Approval',
        reApproved: 'Re-Approved',
        acceptReApproval: 'Accept Re-Approval',
        // B 已重接納
        reAccepted: 'Re-Accepted',
        // D 己重確認
        reConfirmed: 'Re-Confirmed',

        copyComposition: 'Copy Composition And Amount',
        copyCompositionAndAmount: 'Copy Composition',
        clearComposition: 'Clear Composition',
        clearCompositionAndAmount: 'Clear Amount',
        addItem: 'Add Item',
        addDetail: 'Add Detail',
        edit: 'Edit',
        delete: 'Delete',
        up: 'Up',
        down: 'Down',
      },
      message: {
        tips: 'Tips',
        // X 未遞�?        submitBudgetForApproval: 'Submit Budget For Approval?',
        // S 已遞交
        withdrawSubmittedBudget: 'Withdraw Submitted Budget?',
        approveBudget: 'Approve Budget?',
        // A 已審批
        withdrawApprovedBudget: 'WithDraw Approved Budget?',
        acceptApproval: 'Accept Approval?',
        // O 已接納
        withdrawAcceptance: 'Withdraw Accept?',
        confirmAcceptBudget: 'Confirm Accept Budget?',
        // C 已確認
        withdrawConfirmation: 'Withdraw Confirmation?',
        // enterReviseBudgetMode: '進入修訂預算式？',
        // R 修訂中
        cancelReviseMode: 'Cancel Revise Mode?',
        submitRevisionForApproval: 'Submit Revision For Approval?',
        // T 己修訂
        withdrawSubmittedRevision: 'With Draw Submitted Revision?',
        reApproveBudget: 'Re-Approve Budget?',
        // K 已重批
        withdrawReApprovedBudget: 'Withdraw Re-Approved Budget?',
        reAcceptApproval: 'Re-Accept Approval?',
        // B 已重接納
        withdrawReAcceptance: 'Withdraw Re-Acceptance?',
        confirmReAcceptedBudget: 'Confirm Re-Accepted Budget?',
        // D 己重確認
        withdrawReConfirmation: 'Withdraw Re-Confirmation?',
        enterReviseBudgetMode: 'Enter Revise Budget Mode?',

        noYear: 'Not entered into the accounting period!',
        loadingPageDataFailed: 'Failed to load page data!',
        loadingLastYearDataFailed: 'Loading last year budget failed!',
        loadingThisYearDataFailed: "Loading this year's budget failed!",
        hasChildren: 'The selection contains additional content, please clear it and try again',

        inputBudgetCode: 'Please enter the budget code',
        inputBudgetName: 'Please enter the budget item',
        cleanComposition: 'Are you sure to clear all related budget items for this year?',
        cleanCompositionAndAmount:
          'Are you sure to clear all related budget amounts for this year?',
        addTip: 'Please save the budget item first',
      },
    },
  },
  purchase: {
    master: {
      procurementLevel: {
        pro_level_code: 'Level Code',
        pro_level_name_: 'Level Description',
        pro_level_name_cn: 'Level Description(Chinese)',
        pro_level_name_en: 'Level Description(English)',
        pro_no_format: 'Number Format',
        amount_range: 'Amount Range',
        min_amount: 'Min Amount',
        max_amount: 'Max Amount',
        suppliers_at_least: 'Min Suppliers',
        invitation_letter_code: 'Invitation Letter',
        invitation_letter_type: 'Invitation Letter Type',
        reject_letter_code: 'Rejection Letter',
        accept_letter_code: 'Acceptance Letter',
        reason_for_suppliers: 'Must fill in reason for inviting fewer than required suppliers',
        reason_for_low_price: 'Must fill in reason for not selecting lowest quote',
        review: 'Review',
        reviewSetting: 'Review Setting',
        approve: 'Approve',
        approveSetting: 'Approve Setting',
        free: 'Applicant Choice',
      },
      setting: {
        principal_cn: 'Principal Name(Chinese)',
        principal_en: 'Principal Name(English)',
        sch_tel: 'School Tel',
        sch_fax: 'School Fax',
        sch_addr_cn: 'School Address(Chinese)',
        sch_addr_en: 'School Address(English)',
        no_month_skip: 'Procurement Number Rule(Monthly Reset)',
        no_budget_skip: 'Procurement Number Rule(Independent Budget Count)',
      },
    },
    daily: {
      procurementApplications: {
        level: 'Category',
        filterRemark: 'Description',
        startDate: 'Start Date',
        endDate: 'End Date',
        budgetGroup: 'Budget Group',
        applyStaff: 'Applicant',

        g_budget_name_: 'Department',
        pro_no: 'Number',
        pro_title: 'Description ',
        apply_date: 'Apply Date ',
        invite_date: 'Invitation Date ',
        applicant_name_: 'Applicant',
        r_staff_name_: 'Reviewer',
        a_staff_name_: 'Approver',
        // amount: 'Amount',
        status: 'Status',

        year: 'School Year',
        applyDate: 'Apply Date',
        title: 'Title',
        proNo: 'Number',
        budget: 'Budget Item',
        reviewHandler: 'Reviewer',
        approvalHandler: 'Approver',
        type: 'Category',
        inviteDate: 'Invitation Date',
        contact: 'Contact',
        contactTel: 'Contact Tel',
        replyEndDate: 'Supplier Reply Deadline',
        quotedValidDays: 'Quote Valid Days',
        remark: 'Remark',
        // column
        itemName: 'Item/Service Description',
        itemQty: 'Quantity',
        itemRemark: 'Purpose',
        action: 'Action',

        supplierName: 'Supplier Name',
        supplierType: 'Type',
        supplierTel: 'Tel',
        supplierFax: 'Fax',
        supplierContact: 'Contact',
        letters: 'Invitation Letters',
        notEnoughSuppliersReason: 'Reason for Insufficient Suppliers',
        notSelectLowestReason: 'Reason for Not Selecting Lowest Quote',
        com: 'Company',
        org: 'Organization',

        auto: 'Auto',
        stName: 'Name',
        updatedAt: 'Review Time',
        reviewStatus: 'Review Result',
        reviewComment: 'Review Comment',

        uploadQuotes: 'Upload Quotes',
        filename: 'Filename',

        qty: 'Quantity',
        price: 'Unit Price',
        amount: 'Amount',
        total: 'Total Quote',
        select: 'Accept',
        filePreview: 'File Preview',

        // message
        saveSuccess: 'Save Successful!',
        levelsEmpty: 'Procurement levels not set',
        levelsEmptyInput: 'Please add procurement levels first',
        itemsEmpty: 'Items/Services not filled!',
        suppliersEmpty: 'At least one supplier information required!',
        quotesSelectEmpty: 'Please select a supplier!',
        quotesEmpty: 'Please enter supplier quotes!',
        // itemsEmpty: 'Items/Services cannot be empty!',
        passConfirm: 'Confirm to approve this application?',
        backComment: 'Return Comment',
        rejectComment: 'Reject Comment',
        commentRequired: 'Required',
        reviewerHandlersInput: 'Reviewer not filled!',
        approveHandlersInput: 'Approver not filled',
        fileUploadFailing: '{num} files failed to upload',

        goto: 'Go to',
        back: 'Back',

        stepList: {
          basis: 'Basic Information',
          items: 'Items/Services',
          suppliers: 'Suppliers',
          review: 'Review',
          quotesInput: 'Quote Input',
          quotesSelect: 'Quote Selection',
          approval: 'Approval',
          print: 'Document Print',
        },
        button: {
          pass: 'Pass',
          reviewBack: 'Return',
          reviewBackToQuotes: 'Return to Quotes',
          reviewBackToStart: 'Return to Basic Info',
          reject: 'Reject',
        },
        uploadStatus: {
          success: 'Uploaded',
          ready: 'Ready to Upload',
          error: 'Upload Failed',
        },
      },
      procurementApproval: {
        unhandled: 'Pending',
        handled: 'Processed',

        year: 'Year',
        level: 'Category',
        remark: 'Description',

        step: 'Stage',
        g_budget_name_: 'Department',
        pro_no: 'Number',
        pro_title: 'Description ',
        apply_date: 'Apply Date ',
        invite_date: 'Invitation Date ',
        applicant_name_: 'Applicant',
        handler_name_: 'Handler',
        handle_time: 'Process Date',
        apply_status: 'Application Status',
        handler_status: 'Status',
        comment: 'Comment',
      },
    },
    status: {
      A: 'Not Submitted',
      G: 'Under Review',
      GR: 'Review Rejected',
      GA: 'Review Returned',
      I: 'Quotation Stage',
      K: 'Quote Selection',
      M: 'Under Approval',
      MR: 'Approval Rejected',
      MI: 'Approval Returned',
      MA: 'Approval Returned',
      O: 'Approved',
    },
    approvalStatus: {
      W: 'Pending Review',
      A: 'Approved',
      B: 'Returned',
      C: 'Returned to Start',
      R: 'Rejected',
    },
    supplierStatus: {
      W: 'Awaiting Reply',
      Q: 'Quoted',
      R: 'Not Quoting',
      N: 'No Response',
    },
    reviewStep: {
      R: 'Review',
      A: 'Approval',
    },
    category: {
      P: 'Products',
      S: 'Services',
    },
  },
  permission: {
    Y: 'Use',
    A: 'Add',
    D: 'Delete',
    E: 'Edit',
    F: 'Update',
    O: 'Export',
    I: 'Import',
    V: 'Voucher',
    L: 'List',
    C: 'Consolidate',
    R: 'Receipt',
    M: 'Label',
    N: 'Envelop',
    P: 'Print',
    T: 'Upload',
    W: 'Download',
    U: 'Restore',
    Z: 'Renum',
    B: 'Copy',
  },
  voucher_type_category: {
    ALL: 'All',
    P: 'Payment',
    R: 'Receipt',
    C: 'Cash',
    T: 'Transfer',
    J: 'Jourmal',
    A: 'A/P',
  },
  voucher_method: {
    transf: 'Transfer',
    cash: 'Cash',
    cheque: 'Cheque',
    auto: 'Auto',
    other: 'Other',
    value: {
      transf: 'TRANSF',
      cash: 'CASH',
      cheque: 'CHEQUE',
      auto: 'AUTO',
      other: 'OTHER',
    },
  },
  view: {
    add: 'Add',
    edit: 'Edit',
    copy: 'Copy',
    addStaff: 'Add Staff',
    editStaff: 'Edit Staff',
    addStaffType: 'Add Staff Type',
    editStaffType: 'Edit Staff Type',
    addAccountType: 'Add Account Type',
    addDepartmentType: 'Add Department Type',
    editDepartmentType: 'Edit Department Type',
    editAccountType: 'Edit Account Type',
    addCompanyGroup: 'Add Company Group',
    editCompanyGroup: 'Edit Company Group',
    addStockGroup: 'Add Stock Group',
    editStockGroup: 'Edit Stock Group',
    addBudgetGroup: 'Add Group',
    editBudgetGroup: 'Edit Group',
    addBudgetItem: 'Add Budget Item',
    editBudgetItem: 'Edit Budget Item',
    addBudgetDetail: 'Add Budget Detail',
    editBudgetDetail: 'Edit Budget Detail',
  },
  table: {
    action: 'Action',
  },
  style: {
    left: 'Left',
    center: 'Center',
    right: 'Right',
    position: 'Position',
    leftWidth: 'Left column width',
    defaultTitle: 'Page Settings',
    lineHeight: 'Line Height',
    theme: 'Theme',
  },
  file: {
    import: 'Import',
    export: 'Export',
    excelImport: 'Excel Import',
    excelExport: 'Excel Export',
    browse: 'Browse',
    downTemplate: 'Download Template',
    pleaseSelectExcelFile: "Please Select�?Excel5 ',' Excel 2007 ' type of file",
    pleaseSelectPdfFile: "Please Select�?PDF' type of file",
    exportSuccess: 'Export success',
    exportError: 'Export failed',
  },
  message: {
    dropExcel: 'Drag and drop the Excel file here',
    dropPdf: 'Drag and drop the PDF file here',
    success: 'Successful operation',
    leaveSave: 'Discard current unsaved content�?,
    tips: 'Tips',
    showColumn: 'Show at least one column',
    addSuccess: 'added successfully',
    editSuccess: 'Successful editing',
    modifySuccess: 'Successfully modified',
    deleteSuccess: 'successfully deleted',
    cancelDelete: 'Delete Cancelled',
    confirmPasswordError: 'Inconsistent passwords',
    dateError: 'This date is not within the accounting range, please select again',
    pleaseContactTheAdministrator: 'System error, please contact the administrator',
    pleaseReload: 'System error, please refresh the page',
    pleaseSelectFund: 'Please select an fund',
    theGoodsDoNotExist: 'The goods do not exist',
    theYearDoNotExist: 'Accounting period does not exist',
    theMonthDoNotExist: 'Month does not exist',
    editPaperRepeat: 'Edit number already exists',
    printSettingEmpty: 'Print settings are not configured',
    receiptReceived: 'Received from',
    chequeNumber: 'Cheque No.',
    totalAmount: 'Total Amount',
    payment: 'Payment',
    receiptDate: 'Receipt Date',
    payee: 'Payee',
    receipt: 'Receipt',
    numberNo: 'No.',
    receiptLeft: 'Rec',
    receiptRight: 'eipt',
    workInProgress: 'Work In Progress',
  },
  // Common
  common: {
    notApplicable: 'N/A',
    colon: ':',
  },
  // Component
  component: {
    sticky: 'Sticky',
  },
  // Table
  table: {
    index: 'Index',
    account: 'Account',
    action: 'Action',
    staff: 'Staff',
    type: 'Type',
  },
  // Theme
  theme: {
    optionA: 'Option A',
    optionB: 'Option B',
    optionC: 'Option C',
    noReverseVoucherType: 'No corresponding voucher category found',
    pdfLoading: 'The print component is loading, please wait',
    pdfFailedToLoad: 'The print component failed to load, please refresh and try again!',
    pdfError: 'The print component failed to load, please refresh and try again!',
    bankInvalid: 'Bank invalid',
    chequeSettingWidthInvalid:
      'The width of the cheque must not be larger than {width}mm, please correct it',
    chequeSettingHeightInvalid:
      'The width of the cheque must not be larger than {height}mm, please correct it',

    importExcelError: 'Import Failed!',
    noSelectedFile: 'No file selected�?,

    uninitializedYear: 'Uninitialized accounting period!',

    responseError: 'Something Wrong, Please contact the admin',

    loginAgainMsg: 'Authentication session has expired please log in again',
    loginAgain: 'login Again',
    loginAgainBtn: 'Confirm',
    supportUploadingOneFile: 'Only support uploading one file!',
    supportUploadingExcel: 'Only supports upload .xlsx, .xls, .csv suffix files',

    networkError: 'Network connection failed, refresh the page and try again',
    noVoucherType: 'No voucher category found',
    noAccount: 'No account found',
    noVoucherNo: 'No voucher no. found',
    pleaseSelected: 'Please select record',
    confirmGenerateVoucher: 'Confirm to generate voucher',
    voucherDateError: 'Voucher Date cannot be less than As At Date',

    pdfPrintError: 'Print failed',
    pdfWinBlock: 'The browser blocked the pop-up window, please allow pop-ups',
    confirmDelete: 'Confirm to delete?',
    clearSuccess: 'Clear successfully',
    configurationError: 'Configuration Error',
    hasPermission: 'Has',
    noPermission: 'No',
    receipt: 'Receipt',
    receiptChar1: 'Receipt',
    receiptChar2: '',
    totalWithColon: 'Total�?,
    pageNotAccessible: 'The administrator says you cannot access this page......',
    historicalYearFieldError: 'Historical year field error',
    noCurrentYearRecord: 'No current year record',
  },
  placeholder: {
    desc: 'Description',
    staff: 'Staff',
    dept: 'Department',
    payee: 'Payee',
    ref: 'Ref',
    budget: 'Budget',
    quote: 'Quote',
    ac_code: 'A/C Code',
    ac_name: 'A/C Name',
    contra: 'Contra',
    select: 'Select',
    selectDate: 'Select Date',
    beginDate: 'BeginDate',
    endDate: 'EndDate',
    selectDragSort: 'Please select the display column and drag and drop',
  },
  filters: {
    fund: 'Fund',
    type: 'Type',
    all: 'All',
    bank: 'Bank',
    chequeCode: 'Cheque No.',
    period: 'Period',
    isMatching: 'Show only unmatched vouchers',
    to: '-',
    cheque_book: 'Cheque Book',
    years: 'Year',
    months: 'month',
    all_months: 'All Months',
    budget: 'Budget',
    staff: 'Staff',
    dept: 'Dept',
    group: 'Group',
    range: 'Range',
    date: 'Date',
    style: 'Style',
    wholeYear: 'Whole Year',
    fundRange: 'Fund Range',
  },
  print: {
    footer: 'Page {currentPage} / {pageCount}',
    total: 'Total�?,
    pdfLabel: 'Print',
    title: {},
    left: 'Left',
    center: 'Center',
    right: 'Right',
    top: 'Top',
    bottom: 'Bottom',
    noData: 'No Data',
    companyName: 'Company Name',
    reportTitle: 'Report Title',
    dateRange: 'Date Range',
    printDate: 'Print Date',
    pageNumber: 'Page Number',
    // 签名相关
    prepared: 'Prepared',
    approved: 'Approved',
    checked: 'Checked',
    received: 'Received',
    staff: 'Staff',
    principal: 'Principal',
    supervisor: 'Supervisor',
    // 报表名称
    autoPayList: 'Auto Pay List',
    balanceTransfer: 'Balance Transfer',
    bankReconciliation: 'Bank Reconciliation',
    budget: 'Budget',
    cashBook: 'Cash Book',
    cashBookBank: 'Bank Cash Book',
    cashVoucher: 'Cash Voucher',
    consolidateVoucher: 'Consolidated Voucher',
    dailyCollection: 'Daily Collection',
    department: 'Department',
    envelope: 'Envelope',
    journalVoucher: 'Journal Voucher',
    mailingLabel: 'Mailing Label',
    paymentReceipt: 'Payment Receipt',
    paymentVoucher: 'Payment Voucher',
    receiptReceipt: 'Receipt Receipt',
    receiptVoucher: 'Receipt Voucher',
    reportLedger: 'Report Ledger',
    reportVoucher: 'Report Voucher',
    salesProfit: 'Sales Profit',
    staff_report: 'Staff Report',
    transferVoucher: 'Transfer Voucher',
    trialBalance: 'Trial Balance',
    voucherList: 'Voucher List',
    bgBudgetList: 'BG Budget List',
    cheque: 'Cheque',
  },
  components: {
    accountTreeSelect: {
      label: {
        allAccount: 'All Account',
      },
    },
    imageCropper: {
      hint: 'Click or drag the file here to upload',
      loading: 'Uploading�?,
      noSupported: 'Browser is not supported, please use IE10+ or other browsers',
      success: 'Upload success',
      fail: 'Upload failed',
      preview: 'Preview',
      btn: {
        off: 'Cancel',
        close: 'Close',
        back: 'Back',
        save: 'Save',
      },
      error: {
        onlyImg: 'Image only',
        outOfSize: 'Image exceeds size limit: ',
        lowestPx: "Image's size is too low. Expected at least: ",
      },
      rotate: {
        left: '�?,
        right: '�?,
      },
      title: 'Image Cropper',
      preview: 'Preview',
      btn: {
        reset: 'Reset',
        rotate: 'Rotate',
        cropper: 'Crop',
        scale: 'Scale',
      },
      upload: {
        success: 'Upload successful',
        error: 'Upload failed',
      },
    },
    tinymce: {
      uploadImage: 'Upload Image',
      clickUpload: 'Click Upload',
      cancel: 'Cancel',
      confirm: 'Confirm',
      uploadError: 'Please wait for all images to upload successfully or there is a network problem, please refresh the page and upload again!',
    },
    dropzone: {
      uploadImage: 'Upload Image',
      maxFilesExceeded: 'Only one image allowed',
    },
    themePicker: {
      changeThemeSuccess: 'Theme Changed Successfully',
    },
  },
  btnTitle: {
    add: 'Add',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    pageSetting: 'Page Setting',
    exportExcel: 'Export',
    importExcel: 'Import',
    exportExcelPage: 'Export(Page)',
    exportExcelAll: 'Export(All)',
    multipleVoucher: 'Voucher Import',
    print: 'Print',
    paymentSlip: 'Payment Slip',
    voucher: 'Voucher',
    voucherList: 'Voucher List',
    consolidateVoucherList: 'Consolidate Voucher List',
    receipt: 'Receipt',
    mailLabel: 'Address',
    envelope: 'Envelope',

    enquiry: {
      grant: 'Grant Enquiry',
      budget: 'Budget Enquiry',
      staff: 'Staff Enquiry',
      dept: 'Department Enquiry',
      contra: 'Contra Enquiry',
      general: 'General Enquiry',
    },

    addStockGroup: 'Add Stock Group',
  },
  printList: {
    error: 'Failed',
    success: 'Success',
    ing: 'In Progress',
    name: 'Name',
    status: 'Status',
    startTime: 'Start Time',
    finishTime: 'Finish Time',
    action: 'Action',
    check: 'Check',
    back: 'Back',
    title: 'Print',
  },
  bankAiReconciliation: {
    dataHedging: 'Hedging Date: Use Recommended Options',
    dataHedgingShowBank: 'Hedging Date does not display selected bank transactions',
    bankMonth: 'Bank Statement',
    fileScan: '{number} files are being scanned by AI...',
    vc_date: 'Date',
    vc_no: 'Voucher No.',
    descr: 'Description',
    bank: 'Bank',
    summary: 'Summary',
    vc_payee: 'Payee',
    uploadMonthly: 'Upload Bank Statement',
    ref: 'Cheque No.',
    amount_dr: 'Income',
    amount_cr: 'Expense',
    Unpaid: 'Unpaid',
    vc_rdate: 'Hedging Date',
    uploadOptions: 'Drag and drop PDF files here',
    bower: 'Browse',
    uploadText1: '1. Files with the same bank and same month need to be consecutive',
    uploadText2: '2. Single PDF cannot be more than 50 pages and cannot be larger than 50 MB',
    RUNNING: 'AI Running',
    SUCCESS: 'AI Success',
    FAILURE: 'AI Failure',
    user: 'Account',
    fileName: 'File Name',
    index: '#',
    options: 'Options',
    uploadDate: 'Upload Date',
    result: 'AI Result',
    letsGo: 'Go',
    noData: 'No data',
    letUploadFile: 'Please upload the file first',
    success: 'Successful operation',
    fail: 'Operation failed',
    currentPage: 'Current page: {current} of {total} pages',
    addLedger: 'Add Transaction',
    addPre: 'Add to above',
    enable: 'Selected',
    disable: 'Not Selected',
    addNext: 'Add to below',
    account: 'Account',
    isSelect: 'Selected�?,
    SAVINGS: 'S/A',
    CURRENT: 'C/A',
    FIXED_DEPOSIT: 'F/D',
    OVERDRAFTS: 'O',
    selectUnpaidDate: 'Select Unpaid Date',
    selectUnpaidDateRequired: 'Please select Unpaid Date',
    vc_method: 'Method',
    CASH: 'Cash',
    TRANSF: 'Transfer',
    CHEQUE: 'Cheque',
    AUTO: 'Auto',
    OTHER: 'Other',
    autoSelectHedgingConfirm:
      'Confirm to use recommended options to cover the current hedging date?',
    ABANDONED: 'Abandoned',
    filterAbnormalLedger: 'Show only abandoned files',
    clearAll: 'Clear All',
    undo: 'Undo',
    isClearAll: 'Are you sure to clear all the hedging dates of the current vouchers?',
    isUndo:
      'The last recommended option has successfully matched {num} records, are you sure to undo the last recommended option operation?',
    pairing: 'Successfully matched {num} records',
  },
  paperPreview: {
    view: 'Paper Preview',
  },
  checkConfiguration: 'Check Configuration',
  copySuccess: 'Copy Success',
  copyFailed: 'Copy Failed, Please Copy Manually',
  chequePreview: {
    amountCn: 'One Million, Two Hundred and Thirty-Four Thousand, Five Hundred and Sixty-Seven Dollars and Eighty-Nine Cents Only',
  },
  error: {
    invalidCode: 'Invalid Code!',
    invalidStaff: 'Invalid Staff!',
    invalidDept: 'Invalid Department!',
    invalidContra: 'Invalid Contra Code!',
    duplicateEntry: 'Duplicate Entry!',
  },
  amount: {
    ch: 'zero one two three four five six seven eight nine',
    ch_u: 'unit ten hundred thousand million billion',
    ch_f: 'negative',
    ch_d: 'point',
    m_u: 'dollar dime cent',
    m_t: '',
    m_z: 'only',
  },
}
