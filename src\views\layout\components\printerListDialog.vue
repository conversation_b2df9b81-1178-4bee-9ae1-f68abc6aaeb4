<template>
  <el-dialog
    :title="t('title')"
    :visible.sync="finDialogVisible"
    width="1000px"
    custom-class="printer-list-dialog"
  >
    <div>
      <el-table :data="printList" style="width: 100%">
        <el-table-column prop="name" :label="t('name')" />
        <el-table-column prop="status" :label="t('status')" width="180" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 1" type="success">
              {{ t('success') }}
            </el-tag>
            <el-tag v-else-if="scope.row.status === 2" type="danger">
              {{ t('error') }}
            </el-tag>
            <el-tag v-else-if="scope.row.status === 0" type="info">
              {{ t('ing') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" :label="t('startTime')" width="180" />
        <el-table-column prop="finishTime" :label="t('finishTime')" width="180" />
        <el-table-column :label="t('action')" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              :disabled="scope.row.status !== 1"
              type="text"
              @click="handleView(scope.row)"
            >
              {{ t('check') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="finDialogVisible = false">{{ t('back') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'PrinterListDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters(['printList']),
    finDialogVisible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      },
    },
  },
  data() {
    return {
      langKey: 'printList.',
    }
  },
  methods: {
    handleView(row) {
      window.open(row.url, '_blank')
    },
  },
}
</script>

<style lang="scss">
.printer-list-dialog {
  .el-dialog__body {
    height: 100% !important;
  }
}
</style>
