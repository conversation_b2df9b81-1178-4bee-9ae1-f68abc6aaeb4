{"name": "edac-vue", "version": "0.1.0", "description": "學校賬目管理系統", "author": "norray", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "build": "cross-env NODE_ENV=production node --max_old_space_size=4096 build/build.js", "build:prod": "cross-env NODE_ENV=production node --max_old_space_size=4096 build/build.js", "check-env": "node build/check-env.js", "check-env:dev": "cross-env NODE_ENV=development node build/check-env.js", "check-env:prod": "cross-env NODE_ENV=production node build/check-env.js", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix --quiet", "prettier": "prettier --write \"src/**/*.{js,vue}\"", "format": "prettier --write \"src/**/*.{js,vue}\" && eslint --ext .js,.vue src --fix", "test": "npm run lint", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "keywords": ["vue", "element-ui", "admin", "management-system", "admin-template"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@riophae/vue-treeselect": "0.0.38", "accounting-js": "^1.1.1", "axios": "0.18.0", "clipboard": "1.7.1", "dayjs": "^1.11.11", "driver.js": "0.8.1", "dropzone": "5.2.0", "echarts": "4.1.0", "element-resize-detector": "^1.1.15", "element-ui": "2.4.11", "file-saver": "^2.0.2", "fuse.js": "3.4.2", "html2canvas": "^1.4.1", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.1.5", "md5": "^2.2.1", "mockjs": "1.0.1-beta3", "normalize.css": "7.0.0", "nprogress": "0.2.0", "nzh": "^1.0.4", "panzoom": "^9.4.3", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.10", "screenfull": "4.0.0", "serialize-javascript": "^6.0.2", "showdown": "1.8.6", "sortablejs": "1.7.0", "vue": "^2.6.2", "vue-count-to": "1.0.13", "vue-i18n": "7.3.2", "vue-numeric": "^2.3.0", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue-worker": "^1.2.1", "vuedraggable": "^2.16.0", "vuex": "3.0.1", "vxe-table": "2.9.19", "worker-loader": "^3.0.8", "xe-utils": "2.7.8", "xlsx": "^0.11.16"}, "devDependencies": {"autoprefixer": "8.5.0", "babel-core": "6.26.3", "babel-eslint": "^10.1.0", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-loader": "7.1.5", "babel-plugin-dynamic-import-node": "2.0.0", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-runtime": "6.23.0", "babel-plugin-transform-vue-jsx": "3.7.0", "babel-preset-env": "1.7.0", "babel-preset-stage-2": "6.24.1", "chalk": "2.4.1", "compression-webpack-plugin": "2.0.0", "connect": "3.6.6", "copy-webpack-plugin": "4.5.2", "cross-env": "5.2.0", "css-loader": "1.0.0", "dotenv": "^17.2.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.10.0", "eslint-friendly-formatter": "4.0.1", "eslint-loader": "2.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^7.20.0", "file-loader": "1.1.11", "friendly-errors-webpack-plugin": "1.7.0", "hash-sum": "1.0.2", "html-webpack-plugin": "4.0.0-alpha", "husky": "0.14.3", "lint-staged": "7.2.2", "mini-css-extract-plugin": "0.4.1", "node-notifier": "5.2.1", "node-sass": "^4.7.2", "optimize-css-assets-webpack-plugin": "5.0.0", "ora": "3.0.0", "path-to-regexp": "2.4.0", "portfinder": "1.0.13", "postcss-import": "11.1.0", "postcss-loader": "2.1.6", "postcss-url": "7.3.2", "prettier": "^2.8.8", "rimraf": "2.6.2", "sass-loader": "7.0.3", "sass-resources-loader": "^2.0.1", "script-ext-html-webpack-plugin": "2.0.1", "script-loader": "0.7.2", "semver": "5.5.0", "serve-static": "1.13.2", "shelljs": "0.8.2", "svg-sprite-loader": "3.8.0", "svgo": "^1.2.1", "uglifyjs-webpack-plugin": "1.2.7", "url-loader": "1.0.1", "vue-loader": "15.3.0", "vue-style-loader": "4.1.2", "vue-template-compiler": "^2.6.2", "webpack": "4.16.5", "webpack-bundle-analyzer": "2.13.1", "webpack-cli": "3.1.0", "webpack-dev-server": "3.1.14", "webpack-merge": "4.1.4"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}