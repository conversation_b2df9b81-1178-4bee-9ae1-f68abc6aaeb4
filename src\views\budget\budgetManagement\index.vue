<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <split-pane
      ref="horizontalSplitPane"
      :min-percent="30"
      :default-percent.sync="preferences.filters.horizontalPercent"
      split="horizontal"
      @resize="horizontalResize"
    >
      <template slot="paneL">
        <el-form ref="form" :inline="true" class="mini-form" style="padding: 5px 10px 0">
          <!-- ---------- 年份 ---------- -->
          <el-form-item :label="$t('filters.years')">
            <el-select
              v-model="preferences.filters.selectedYear"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>
          <!-- ---------- 預算 ---------- -->
          <el-form-item :label="$t(langKey + 'budget')" prop="selectedGroupId">
            <BudgetSelectTree
              :data="budgetGroupList"
              :budget-id.sync="preferences.filters.selectedGroupId"
              :language="language"
              :status-color="statusColor"
              style="width: 300px"
              @focus="onFocusBudgetGroup"
              @change="onChangeBudgetGroup"
            />
          </el-form-item>

          <!-- ---------- 負責人 ---------- -->
          <el-form-item :label="$t('budget.budgetManagement.label.manager')">
            <el-input :readonly="true" :value="manager" />
          </el-form-item>
          <!-- ---------- 審核 ---------- -->
          <el-form-item :label="$t('budget.budgetManagement.label.approve')">
            <el-input :readonly="true" :value="approve" />
          </el-form-item>
          <!-- ---------- 上年度 ---------- -->
          <el-form-item :label="$t(langKey + 'lastYear')">
            <el-checkbox
              v-model="preferences.filters.showLastYear"
              true-label="1"
              false-label="0"
              @change="onFold"
            />
          </el-form-item>
          <!-- ---------- 審核按鈕 ---------- -->
          <el-form-item v-if="budgetGroup && budgetGroup.budget_stage" class="stage-action">
            <div class="stage-action-box">
              <el-button-group>
                <el-button
                  v-if="cancelStage && leftBtnShow"
                  :style="leftButtonStyle"
                  size="mini"
                  class="pre-btn"
                  @click="handleCancelStage"
                >
                  {{ cancelStage }}
                </el-button>
                <el-button
                  :disabled="true"
                  :style="centerButtonStyle"
                  class="status-button"
                  size="mini"
                >
                  {{ stage }}
                </el-button>
                <el-button
                  v-if="rightBtnShow && canNextApproval"
                  :style="rightButtonStyle"
                  size="mini"
                  class="next-btn"
                  @click="handleNextStage"
                >
                  {{ nextStage }}
                </el-button>
              </el-button-group>
            </div>
          </el-form-item>
        </el-form>
        <split-pane
          ref="verticalSplitPane"
          :min-percent="30"
          :default-percent.sync="preferences.filters.verticalPercent"
          :style="`height: calc(100% - ${formHeight}px)`"
          class="top-split"
          split="vertical"
          @resize="verticalResize"
        >
          <template slot="paneL">
            <!--            左邊      -->

            <div class="budget-management-left">
              <div v-show="false && !fold" class="filter">
                <el-form ref="form2" :inline="true" class="mini-form">
                  <el-form-item class="left-title">
                    <i
                      v-if="!fold"
                      class="edac-icon edac-icon-shouqi action-icon"
                      style="
                        display: inline;
                        font-size: 18px;
                        color: #409eff;
                        vertical-align: middle;
                        line-height: 25px;
                      "
                      @click="onFold(true)"
                    />
                    <i
                      v-if="fold"
                      class="edac-icon edac-icon-zhankai action-icon"
                      style="
                        display: inline;
                        font-size: 18px;
                        color: #409eff;
                        vertical-align: middle;
                        line-height: 25px;
                      "
                      @click="onFold(false)"
                    />
                    <div style="display: inline; color: #606266">
                      {{ $t(langKey + 'lastYear') }}
                    </div>
                  </el-form-item>
                </el-form>
              </div>
              <div v-show="!fold" :style="leftTableHeight" class="left-table">
                <el-table
                  ref="leftDataTable"
                  :data="lastTableData"
                  :row-class-name="isStripe"
                  :show-summary="false"
                  :summary-method="leftGetSummaries"
                  :highlight-current-row="true"
                  height="100%"
                  class="tree-table"
                  style="width: 100%"
                  :row-style="rowStyle2"
                  border
                  @row-click="onLeftRowClick"
                >
                  <el-table-column
                    :label="$t(langKey + 'lastYear')"
                    :resizable="false"
                    :width="leftColumnWidths.icon"
                    align="left"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <div>
                        <span style="display: inline-block; font-size: 16px">{{
                          '&emsp;'.repeat((scope.row.level - 1) * 2)
                        }}</span>
                        <span v-if="scope.row.budget_type === 'C'" style="display: inline-block">
                          <i class="el-icon-caret-bottom" style="line-height: 16px" />
                          <i
                            :class="{
                              activate: scope.row.budget_active,
                            }"
                            class="edac-icon edac-icon-folder"
                          />
                        </span>
                        <span
                          v-else-if="scope.row.budget_type === 'D'"
                          style="display: inline-block"
                        >
                          <i
                            :class="{
                              activate: scope.row.budget_active,
                            }"
                            class="edac-icon edac-icon-file"
                          />
                        </span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :resizable="false"
                    :label="$t(langKey + 'budget_code')"
                    :width="leftColumnWidths.budget_code"
                    header-align="center"
                    align="left"
                    prop="budget_code"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span :title="scope.row.budget_code">{{ scope.row.budget_code }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :resizable="false"
                    :label="$t(langKey + 'name_')"
                    :prop="languageKey('name_')"
                    header-align="center"
                    align="left"
                    min-width="120"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span :title="scope.row[languageKey('name_')]">{{
                        scope.row[languageKey('name_')]
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :resizable="false"
                    :label="$t(langKey + 'budget_IE')"
                    :width="leftColumnWidths.budget_IE"
                    prop="budget_IE"
                    header-align="center"
                    align="center"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="'IB'.includes(scope.row.budget_IE)">I</span>
                      <span v-if="'EB'.includes(scope.row.budget_IE)">E</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :resizable="false"
                    :label="$t(langKey + 'budget_amount')"
                    :width="leftColumnWidths.budget_amount"
                    prop="budget_amount"
                    header-align="center"
                    align="right"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="'IB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.I_budget_amount) }}
                      </span>
                      <span v-if="'EB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.E_budget_amount) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :resizable="false"
                    :label="$t(langKey + 'actual_amount')"
                    :width="leftColumnWidths.actual_amount"
                    prop="actual_amount"
                    header-align="center"
                    align="right"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="'IB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.I_actual_amount) }}
                      </span>
                      <span v-if="'EB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.E_actual_amount) }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-table
                  ref="leftSummaryTable"
                  :data="[lastSummary]"
                  :row-class-name="isStripe"
                  :show-header="false"
                  :span-method="summarySpanMethod"
                  :width="leftColumnWidths.icon"
                  :row-style="rowStyle2"
                  stripe
                  border
                  style="width: 100%"
                >
                  <el-table-column :width="leftColumnWidths.icon" align="right">
                    <template v-if="scope && scope.row" slot-scope="scope">
                      {{ $t('budget.budgetList.label.total') }}
                    </template>
                  </el-table-column>
                  <el-table-column :width="leftColumnWidths.budget_code" />
                  <el-table-column min-width="120" />
                  <el-table-column
                    :label="$t(langKey + 'budget_IE')"
                    :width="leftColumnWidths.budget_IE"
                    prop="budget_IE"
                    header-align="center"
                    align="center"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="'IB'.includes(scope.row.budget_IE)">I</span>
                      <span v-if="'EB'.includes(scope.row.budget_IE)">E</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t(langKey + 'budget_amount')"
                    :width="leftColumnWidths.budget_amount"
                    prop="budget_amount"
                    header-align="center"
                    align="right"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="'IB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.I_budget_amount) }}
                      </span>
                      <span v-if="'EB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.E_budget_amount) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t(langKey + 'actual_amount')"
                    :width="leftColumnWidths.actual_amount"
                    prop="actual_amount"
                    header-align="center"
                    align="right"
                  >
                    <template v-if="scope && scope.row" slot-scope="scope">
                      <span v-if="'IB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.I_actual_amount) }}
                      </span>
                      <span v-if="'EB'.includes(scope.row.budget_IE)">
                        {{ amountFormat(scope.row.E_actual_amount) }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-show="fold">
                <div style="display: inline">
                  {{ $t(langKey + 'lastYear') }}
                </div>
                <i
                  class="edac-icon edac-icon-zhankai action-icon"
                  style="
                    display: inline;
                    font-size: 18px;
                    color: #409eff;
                    vertical-align: middle;
                    line-height: 25px;
                  "
                  @click="onFold(false)"
                />
                <br>
                <br>
                <div class="filter">
                  <el-form :inline="true" class="mini-form">
                    <!-- ---------- 年份 ---------- -->
                    <el-form-item :label="$t('filters.years')" style="padding: 0; margin: 0">
                      <el-select
                        v-model="preferences.filters.selectedYear"
                        style="width: 100%"
                        @change="onChangeYear"
                      >
                        <el-option
                          v-for="item in years"
                          :key="item.fy_id"
                          :label="item.fy_name"
                          :value="item.fy_code"
                        />
                      </el-select>
                    </el-form-item>
                    <!-- ---------- 預算 ---------- -->
                    <el-form-item
                      :label="$t(langKey + 'budget')"
                      prop="selectedGroupId"
                      style="padding: 0; margin: 0"
                    >
                      <BudgetSelectTree
                        :data="budgetGroupList"
                        :budget-id.sync="preferences.filters.selectedGroupId"
                        :language="language"
                        :status-color="statusColor"
                        style="width: 100%"
                        @focus="onFocusBudgetGroup"
                        @change="onChangeBudgetGroup"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </template>
          <template slot="paneR">
            <!--            右邊      -->
            <div :style="rightTableHeight" class="budget-management-right right-table">
              <tree-table
                v-if="showRightTable"
                ref="rightDataTable"
                :data="thisTableData"
                :expand-all="true"
                :first-field="language === 'en' ? 'name_en' : 'name_cn'"
                :first-field-width="rightColumnWidths.icon"
                :first-label="$t(langKey + 'thisYear')"
                border
                class="right-table-1"
                first-field-align="left"
                folder-field="st_type_id"
                :height="rightTableHeight1"
                :row-style="rowStyle"
                :cell-class-name="rowClassName"
                number-field="budget_code"
                @row-click="onRightRowClick"
              >
                <!--        @changeWidth="changeColumnWidth"-->
                <!--        @changeExpanded="onChangeExpanded"-->
                <!--    編號姓名      -->
                <template slot="firstField" slot-scope="{ scope }">
                  <i
                    :class="{
                      activate: scope.row.budget_active === 'Y',
                      'edac-icon-file': scope.row.budget_type === 'D',
                      'edac-icon-folder-f': scope.row.budget_type === 'F',
                      'edac-icon-folder-c': scope.row.budget_type === 'C',
                      'edac-icon-folder-g': scope.row.budget_type === 'G',
                    }"
                    class="edac-icon"
                  />
                  <!--          <span-->
                  <!--            v-if="!scope.row.st_type_id"-->
                  <!--            :class="{-->
                  <!--              selected: scope.row.budget_active === 'Y'-->
                  <!--            }"-->
                  <!--            class="number-field"-->
                  <!--          >{{ scope.row.budget_code }}</span>-->
                  <!--          <span class="key">{{ scope.row[language === 'en' ? 'name_en' : 'name_cn'] }}</span>-->
                </template>

                <!--   預算編號     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'budget_code')"
                  :resizable="false"
                  :width="rightColumnWidths.budget_code"
                  align="left"
                  header-align="center"
                  prop="budget_code"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <div
                      v-if="
                        budgetGroup &&
                          budgetGroup &&
                          scope.row.edit &&
                          ['X', 'R'].includes(budgetGroup.budget_stage)
                      "
                      :title="scope.row.budget_code"
                    >
                      <el-input v-model="scope.row['budget_code' + '_temp']" />
                    </div>
                    <div v-else :title="scope.row.budget_code">
                      {{ scope.row.budget_code }}
                    </div>
                  </template>
                </el-table-column>
                <!--   預算項目     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'name_')"
                  :min-width="rightColumnWidths.name_"
                  :prop="languageKey('name_')"
                  :resizable="false"
                  align="left"
                  header-align="center"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <div
                      v-if="
                        budgetGroup &&
                          scope.row.edit &&
                          ['X', 'R'].includes(budgetGroup.budget_stage)
                      "
                      :title="scope.row[languageKey('name_')]"
                    >
                      <!-- <el-input v-model="scope.row[languageKey('name_') + '_temp']"/> -->
                      <el-form class="name-input" label-width="30px">
                        <el-form-item :label="$t('budget.budgetSet.label.cn')">
                          <el-input v-model="scope.row.name_cn_temp" />
                        </el-form-item>
                        <el-form-item :label="$t('budget.budgetSet.label.en')">
                          <el-input v-model="scope.row.name_en_temp" />
                        </el-form-item>
                      </el-form>
                    </div>
                    <div v-else :title="scope.row[languageKey('name_')]">
                      {{ scope.row[languageKey('name_')] }}
                    </div>
                  </template>
                </el-table-column>
                <!--   分類     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'budget_IE')"
                  :resizable="false"
                  :width="rightColumnWidths.budget_IE"
                  align="center"
                  header-align="center"
                  prop="budget_IE"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <el-select
                      v-if="scope.row.edit && ['X', 'R'].includes(budgetGroup.budget_stage)"
                      v-model="scope.row.budget_IE"
                      class="budget_IE_select"
                    >
                      <el-option
                        v-for="item in budgetIEOptions(scope.row)"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <div v-else>
                      <span>{{ scope.row.budget_IE }}</span>
                    </div>
                  </template>
                </el-table-column>
                <!--   預算金額     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'budget_amount')"
                  :resizable="false"
                  :width="rightColumnWidths.budget_amount"
                  align="right"
                  header-align="center"
                  prop="budget_amount"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <div
                      v-if="
                        scope.row.edit &&
                          scope.row.budget_type === 'D' &&
                          ['X', 'S'].includes(budgetGroup.budget_stage)
                      "
                    >
                      <el-input-number
                        v-model="scope.row[editAmountField + '_temp']"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                      />
                    </div>
                    <div v-else>
                      <span
                        v-if="
                          budgetGroup &&
                            'SA'.includes(budgetGroup.budget_stage) &&
                            scope.row.proposed_amount != scope.row.approved_amount
                        "
                        class="m-row oldAmount"
                      >
                        {{ amountFormat(scope.row.proposed_amount) }}
                      </span>
                      <span>
                        {{ amountFormat(scope.row[budgetAmountField]) }}
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <!--   修訂金額   -->
                <el-table-column
                  v-if="showRevised"
                  :key="Math.random()"
                  :label="$t(langKey + 'revised_amount')"
                  :resizable="false"
                  :width="rightColumnWidths.revised_amount"
                  align="right"
                  header-align="center"
                  prop="approved_amount"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span
                      v-if="
                        budgetGroup &&
                          'TK'.includes(budgetGroup.budget_stage) &&
                          scope.row.proposed_amount != scope.row.approved_amount
                      "
                      class="m-row oldAmount"
                    >
                      {{ amountFormat(scope.row.proposed_amount) }}
                    </span>
                    <div
                      v-if="
                        scope.row.edit &&
                          scope.row.budget_type === 'D' &&
                          ['R', 'T'].includes(budgetGroup.budget_stage)
                      "
                    >
                      <el-input-number
                        v-model="scope.row[editAmountField + '_temp']"
                        :min="0"
                        :controls="false"
                        style="width: 100%"
                      />
                    </div>

                    <span v-else>
                      {{ amountFormat(scope.row[revisedAmountField]) }}
                    </span>
                  </template>
                </el-table-column>
                <!--   實際金額     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'actual_amount')"
                  :resizable="false"
                  :width="rightColumnWidths.actual_amount"
                  align="right"
                  header-align="center"
                  prop="actual_amount"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span>
                      {{ amountFormat(scope.row.actual_amount) }}
                    </span>
                  </template>
                </el-table-column>
                <!--   結餘     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'balance')"
                  :resizable="false"
                  :width="rightColumnWidths.balance"
                  align="right"
                  header-align="center"
                  prop="balance"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span
                      v-if="
                        budgetGroup &&
                          'SATK'.includes(budgetGroup.budget_stage) &&
                          scope.row.proposed_amount != scope.row.approved_amount
                      "
                      class="m-row oldAmount"
                    >
                      {{ amountFormat(scope.row.proposed_amount) }}
                    </span>
                    <span>
                      {{ amountFormat(scope.row[surplusAmountField] - scope.row.actual_amount) }}
                    </span>
                  </template>
                </el-table-column>
                <!--   結餘率     -->
                <el-table-column
                  :key="Math.random()"
                  :label="$t(langKey + 'balancePercentage')"
                  :resizable="false"
                  :width="rightColumnWidths.percentage"
                  align="right"
                  header-align="center"
                  prop="balance"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span>
                      <div v-if="scope.row.edit">
                        <i
                          class="edac-icon action-icon edac-icon-save"
                          @click="handleUpdate(scope)"
                        />
                        <i
                          class="edac-icon action-icon edac-icon-cancel"
                          @click="handleCancel(scope)"
                        />
                      </div>
                      <span v-else>{{ calcBalancePercentage(scope.row) }}</span>
                    </span>
                  </template>
                </el-table-column>
                <!--操作列-->
                <el-table-column
                  v-if="false && showAction"
                  :key="Math.random()"
                  :label="$t('table.action')"
                  :width="rightColumnWidths.action"
                  align="left"
                  header-align="left"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <div class="row-action">
                      <!--                      <i-->
                      <!--                        v-if="'XR'.includes(budgetGroup.budget_stage) && scope.row.budget_id"-->
                      <!--                        :class="{ disabled : !showUp(scope) }"-->
                      <!--                        :title="$t(btnKey + 'up')"-->
                      <!--                        class="edac-icon action-icon edac-icon-up"-->
                      <!--                        @click="handleUp(scope)"/>-->
                      <!--                      <i-->
                      <!--                        v-if="'XR'.includes(budgetGroup.budget_stage) && scope.row.budget_id"-->
                      <!--                        :class="{ disabled : !showDown(scope) }"-->
                      <!--                        :title="$t(btnKey + 'down')"-->
                      <!--                        class="edac-icon action-icon edac-icon-down"-->
                      <!--                        @click="handleDown(scope)"/>-->
                      <!--                      <i-->
                      <!--                        v-if="'XR'.includes(budgetGroup.budget_stage) && scope.row.budget_type === 'C' && scope.row.budget_id"-->
                      <!--                        :title="$t(btnKey + 'addItem')"-->
                      <!--                        class="edac-icon action-icon edac-icon-add-folder-full"-->
                      <!--                        @click="handleAdd(scope, 'C')"/>-->
                      <!--                      <i-->
                      <!--                        v-if="'XR'.includes(budgetGroup.budget_stage) && scope.row.budget_type === 'C' && scope.row.budget_id"-->
                      <!--                        :title="$t(btnKey + 'addDetail')"-->
                      <!--                        class="edac-icon action-icon edac-icon-add-list"-->
                      <!--                        @click="handleAdd(scope, 'D')"/>-->
                      <!--                      <i-->
                      <!--                        v-if="!scope.row.edit && 'XSTR'.includes(budgetGroup.budget_stage)"-->
                      <!--                        :title="$t(btnKey + 'edit')"-->
                      <!--                        class="edac-icon action-icon edac-icon-edit"-->
                      <!--                        @click="handleEdit(scope)"/>-->
                      <i
                        v-if="scope.row.edit"
                        class="edac-icon action-icon edac-icon-save"
                        @click="handleUpdate(scope)"
                      />
                      <i
                        v-if="scope.row.edit"
                        class="edac-icon action-icon edac-icon-cancel"
                        @click="handleCancel(scope)"
                      />
                      <!--                      <i-->
                      <!--                        v-if="!scope.row.edit && showDelete(scope) && scope.row.budget_id"-->
                      <!--                        :title="$t(btnKey + 'delete')"-->
                      <!--                        class="edac-icon action-icon edac-icon-delete"-->
                      <!--                        @click="handleDelete(scope)"/>-->
                    </div>
                  </template>
                </el-table-column>
              </tree-table>
              <el-table
                v-if="showRightTable"
                ref="rightSummaryTable"
                :data="[thisSummary]"
                :row-class-name="isStripe"
                :show-header="false"
                :span-method="rightSummarySpanMethod"
                border
                class="summary-table"
                stripe
                style="width: 100%"
              >
                <el-table-column :width="rightColumnWidths.icon" align="right">
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <div v-if="canEdit" style="float: left; line-height: 23px; height: 23px">
                      <div style="display: inline-block">
                        <i
                          v-if="
                            budgetGroup &&
                              budgetGroup.budget_stage === 'X' &&
                              thisTableData.length === 0
                          "
                          :title="$t(btnKey + 'copyComposition')"
                          class="edac-icon action-icon edac-icon-dept"
                          @click="handleCopyComposition('ALL')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              budgetGroup.budget_stage === 'X' &&
                              thisTableData.length === 0
                          "
                          :title="$t(btnKey + 'copyCompositionAndAmount')"
                          class="edac-icon action-icon edac-icon-data"
                          @click="handleCopyComposition('STRUCTURE')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              budgetGroup.budget_stage === 'X' &&
                              thisTableData.length > 0
                          "
                          :title="$t(btnKey + 'clearComposition')"
                          class="edac-icon action-icon edac-icon-dept_delete1"
                          @click="handleCleanComposition('ALL')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              budgetGroup.budget_stage === 'X' &&
                              thisTableData.length > 0
                          "
                          :title="$t(btnKey + 'clearCompositionAndAmount')"
                          class="edac-icon action-icon edac-icon-data-delete"
                          @click="handleCleanComposition('CONTENT')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              'XR'.includes(budgetGroup.budget_stage) &&
                              userCanUse('C')
                          "
                          :title="$t(btnKey + 'addItem')"
                          class="edac-icon action-icon edac-icon-add-folder-full"
                          @click="handleAdd(null, 'C')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              'XR'.includes(budgetGroup.budget_stage) &&
                              userCanUse('D')
                          "
                          :title="$t(btnKey + 'addDetail')"
                          class="edac-icon action-icon edac-icon-add-list"
                          @click="handleAdd(null, 'D')"
                        />
                      </div>

                      <div
                        v-if="rightScope && rightScope.row && rightScope.row.budget_id"
                        class="row-action"
                        style="display: inline-block; padding: 0 10px"
                      >
                        <i
                          :class="{ disabled: !showUp(rightScope) }"
                          :title="$t(btnKey + 'up')"
                          class="edac-icon action-icon edac-icon-up"
                          @click="handleUp(rightScope)"
                        />
                        <i
                          :class="{ disabled: !showDown(rightScope) }"
                          :title="$t(btnKey + 'down')"
                          class="edac-icon action-icon edac-icon-down"
                          @click="handleDown(rightScope)"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              'XR'.includes(budgetGroup.budget_stage) &&
                              rightScope &&
                              rightScope.row &&
                              rightScope.row.budget_type === 'C' &&
                              userCanUse('C')
                          "
                          :title="$t(btnKey + 'addItemBySelect')"
                          class="edac-icon action-icon edac-icon-add-folder-full"
                          @click="handleAdd(rightScope, 'C')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              'XR'.includes(budgetGroup.budget_stage) &&
                              rightScope &&
                              rightScope.row &&
                              rightScope.row.budget_type === 'C' &&
                              userCanUse('D')
                          "
                          :title="$t(btnKey + 'addDetailBySelect')"
                          class="edac-icon action-icon edac-icon-add-list"
                          @click="handleAdd(rightScope, 'D')"
                        />
                        <i
                          v-if="
                            budgetGroup &&
                              rightScope &&
                              rightScope.row &&
                              !rightScope.row.edit &&
                              'XSTR'.includes(budgetGroup.budget_stage)
                          "
                          :title="$t(btnKey + 'edit')"
                          class="edac-icon action-icon edac-icon-edit"
                          @click="handleEdit(rightScope)"
                        />
                        <!--                <i v-if="rightScope && rightScope.row && rightScope.row.edit" class="edac-icon action-icon edac-icon-save" @click="handleUpdate(rightScope)"/>-->
                        <i
                          v-if="showDelete(rightScope)"
                          :title="$t(btnKey + 'delete')"
                          class="edac-icon action-icon edac-icon-delete"
                          @click="handleDelete(rightScope)"
                        />
                      </div>
                    </div>
                    <span>{{ $t('budget.budgetList.label.total') }}</span>
                  </template>
                </el-table-column>
                <!--  預算編號  -->
                <el-table-column :width="rightColumnWidths.budget_code" />
                <!--  預算項目  -->
                <el-table-column :min-width="rightColumnWidths.name_" />
                <!--   分類   -->
                <el-table-column
                  :label="$t(langKey + 'budget_IE')"
                  :width="rightColumnWidths.budget_IE"
                  align="center"
                  header-align="center"
                  prop="budget_IE"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span> {{ lastGroupData.budget_IE }}</span>
                  </template>
                </el-table-column>
                <!--   預算金額     -->
                <el-table-column
                  :label="$t(langKey + 'budget_amount')"
                  :width="rightColumnWidths.budget_amount"
                  align="right"
                  header-align="center"
                  prop="budget_amount"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <template v-if="['B'].includes(lastGroupData.budget_IE)">
                      <span
                        v-if="
                          budgetGroup &&
                            'SA'.includes(budgetGroup.budget_stage) &&
                            scope.row.I_proposed_amount != scope.row.I_approved_amount
                        "
                        class="m-row oldAmount"
                      >
                        {{ amountFormat(scope.row.I_proposed_amount) }}
                      </span>
                      <span class="m-row">
                        {{ amountFormat(scope.row['I_' + budgetAmountField]) }}
                      </span>
                      <span
                        v-if="
                          budgetGroup &&
                            'SA'.includes(budgetGroup.budget_stage) &&
                            scope.row.E_proposed_amount != scope.row.E_approved_amount
                        "
                        class="m-row oldAmount"
                      >
                        {{ amountFormat(scope.row.E_proposed_amount) }}
                      </span>
                      <span class="m-row">
                        {{ amountFormat(scope.row['E_' + budgetAmountField]) }}
                      </span>
                    </template>
                    <template v-if="['I'].includes(lastGroupData.budget_IE)">
                      {{
                        amountFormat(
                          scope.row['I_' + budgetAmountField] - scope.row['E_' + budgetAmountField]
                        )
                      }}
                    </template>
                    <template v-if="['E'].includes(lastGroupData.budget_IE)">
                      {{
                        amountFormat(
                          scope.row['E_' + budgetAmountField] - scope.row['I_' + budgetAmountField]
                        )
                      }}
                    </template>
                  </template>
                </el-table-column>
                <!--   修訂金額     -->
                <el-table-column
                  v-if="showRevised"
                  :label="$t(langKey + 'revised_amount')"
                  :resizable="false"
                  :width="rightColumnWidths.revised_amount"
                  align="right"
                  header-align="center"
                  prop="approved_amount"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span>
                      <template v-if="['B'].includes(lastGroupData.budget_IE)">
                        <span
                          v-if="
                            budgetGroup &&
                              'SATK'.includes(budgetGroup.budget_stage) &&
                              scope.row.I_proposed_amount != scope.row.I_approved_amount
                          "
                          class="m-row oldAmount"
                        >
                          {{ amountFormat(scope.row.I_proposed_amount) }}
                        </span>
                        <span class="m-row">
                          {{ amountFormat(scope.row['I_' + revisedAmountField]) }}
                        </span>
                        <span
                          v-if="
                            budgetGroup &&
                              'SATK'.includes(budgetGroup.budget_stage) &&
                              scope.row.E_proposed_amount != scope.row.E_approved_amount
                          "
                          class="m-row oldAmount"
                        >
                          {{ amountFormat(scope.row.E_proposed_amount) }}
                        </span>
                        <span class="m-row">
                          {{ amountFormat(scope.row['E_' + revisedAmountField]) }}
                        </span>
                      </template>
                      <template v-if="['E'].includes(lastGroupData.budget_IE)">
                        {{
                          amountFormat(
                            scope.row['E_' + revisedAmountField] -
                              scope.row['I_' + revisedAmountField]
                          )
                        }}
                      </template>
                      <template v-if="['I'].includes(lastGroupData.budget_IE)">
                        {{
                          amountFormat(
                            scope.row['I_' + revisedAmountField] -
                              scope.row['E_' + revisedAmountField]
                          )
                        }}
                      </template>
                    </span>
                  </template>
                </el-table-column>
                <!--   實際金額     -->
                <el-table-column
                  :label="$t(langKey + 'actual_amount')"
                  :width="rightColumnWidths.actual_amount"
                  align="right"
                  header-align="center"
                  prop="actual_amount"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span>
                      <template v-if="['B'].includes(lastGroupData.budget_IE)" class="m-row">
                        <span class="m-row">
                          {{ amountFormat(scope.row['I_actual_amount']) }}
                        </span>
                        <span class="m-row">
                          {{ amountFormat(scope.row['E_actual_amount']) }}
                        </span>
                      </template>
                      <span v-if="['E'].includes(lastGroupData.budget_IE)">
                        {{
                          amountFormat(scope.row['E_actual_amount'] - scope.row['I_actual_amount'])
                        }}
                      </span>
                      <span v-if="['I'].includes(lastGroupData.budget_IE)">
                        {{
                          amountFormat(scope.row['I_actual_amount'] - scope.row['E_actual_amount'])
                        }}
                      </span>
                    </span>
                  </template>
                </el-table-column>
                <!--   結餘     -->
                <el-table-column
                  :label="$t(langKey + 'balance')"
                  :resizable="false"
                  :width="rightColumnWidths.balance"
                  align="right"
                  header-align="center"
                  prop="balance"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span>
                      <template v-if="['B'].includes(lastGroupData.budget_IE)">
                        <span
                          v-if="
                            budgetGroup &&
                              'SATK'.includes(budgetGroup.budget_stage) &&
                              scope.row.I_proposed_amount != scope.row.I_approved_amount
                          "
                          class="m-row oldAmount"
                        >
                          {{
                            amountFormat(scope.row.I_proposed_amount - scope.row.I_actual_amount)
                          }}
                        </span>
                        <span class="m-row">
                          {{
                            amountFormat(
                              scope.row['I_' + surplusAmountField] - scope.row['I_actual_amount']
                            )
                          }}
                        </span>
                        <span
                          v-if="
                            budgetGroup &&
                              'SATK'.includes(budgetGroup.budget_stage) &&
                              scope.row.E_proposed_amount != scope.row.E_approved_amount
                          "
                          class="m-row oldAmount"
                        >
                          {{
                            amountFormat(scope.row.E_proposed_amount - scope.row.E_actual_amount)
                          }}
                        </span>
                        <span class="m-row">
                          {{
                            amountFormat(
                              scope.row['E_' + surplusAmountField] - scope.row['E_actual_amount']
                            )
                          }}
                        </span>
                      </template>
                      <template v-if="['E'].includes(lastGroupData.budget_IE)">
                        <span>
                          {{
                            amountFormat(
                              scope.row['E_' + surplusAmountField] -
                                scope.row['E_actual_amount'] -
                                scope.row['I_' + surplusAmountField] +
                                scope.row['I_actual_amount']
                            )
                          }}
                        </span>
                      </template>
                      <template v-if="['I'].includes(lastGroupData.budget_IE)">
                        <span>
                          {{
                            amountFormat(
                              scope.row['I_' + surplusAmountField] -
                                scope.row['I_actual_amount'] -
                                scope.row['E_' + surplusAmountField] +
                                scope.row['E_actual_amount']
                            )
                          }}
                        </span>
                      </template>
                    </span>
                  </template>
                </el-table-column>
                <!--   結餘率     -->
                <el-table-column
                  :label="$t(langKey + 'balancePercentage')"
                  :resizable="false"
                  :width="rightColumnWidths.percentage"
                  align="right"
                  header-align="center"
                  prop="balance"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span>
                      <template v-if="['B'].includes(lastGroupData.budget_IE)">
                        <span
                          v-if="
                            budgetGroup &&
                              'SATK'.includes(budgetGroup.budget_stage) &&
                              scope.row.I_proposed_amount != scope.row.I_approved_amount
                          "
                          class="m-row oldAmount"
                        >
                          {{
                            calcBalancePercentage1(
                              scope.row.I_proposed_amount,
                              scope.row.I_actual_amount
                            )
                          }}
                        </span>
                        <span class="m-row">
                          {{
                            calcBalancePercentage1(
                              scope.row['I_' + budgetAmountField],
                              scope.row['I_actual_amount']
                            )
                          }}
                        </span>
                        <span
                          v-if="
                            budgetGroup &&
                              'SATK'.includes(budgetGroup.budget_stage) &&
                              scope.row.E_proposed_amount != scope.row.E_approved_amount
                          "
                          class="m-row oldAmount"
                        >
                          {{
                            calcBalancePercentage1(
                              scope.row.E_proposed_amount,
                              scope.row.E_actual_amount
                            )
                          }}
                        </span>
                        <span class="m-row">
                          {{
                            calcBalancePercentage1(
                              scope.row['E_' + budgetAmountField],
                              scope.row['E_actual_amount']
                            )
                          }}
                        </span>
                      </template>
                      <template v-if="['E', 'I'].includes(lastGroupData.budget_IE)">
                        <span>
                          {{
                            calcBalancePercentage1(
                              scope.row[budgetAmountField],
                              scope.row.actual_amount
                            )
                          }}
                        </span>
                      </template>
                    </span>
                  </template>
                </el-table-column>
                <!--操作列-->
                <!--                <el-table-column-->
                <!--                  v-if="showAction"-->
                <!--                  :resizable="false"-->
                <!--                  :width="rightColumnWidths.action"-->
                <!--                  align="right"-->
                <!--                  fixed="right"-->
                <!--                  header-align="center"-->
                <!--                  label="操作"/>-->
              </el-table>
            </div>
          </template>
        </split-pane>
      </template>
      <template slot="paneR">
        <!--        下邊-->

        <div
          v-loading="bottomLoading"
          class="budget-management-bottom"
          style="padding: 5px 10px 0; height: 100%"
        >
          <div class="filter">
            <el-form :inline="true" class="mini-form">
              <el-form-item :label="$t('filters.years')">
                <el-select
                  v-model="preferences.filters.bottomYear"
                  style="width: 100px"
                  @change="handleChangeBottomYear(preferences.filters.bottomYear, false)"
                >
                  <el-option
                    v-for="item in years"
                    :key="item.fy_id"
                    :label="item.fy_name"
                    :value="item.fy_code"
                  />
                </el-select>
              </el-form-item>
              <!-- ---------- 預算 ---------- -->
              <el-form-item :label="$t(langKey + 'budget')" prop="parent_budget_id">
                <BudgetSelectTreeVue
                  :data="bottomTreeData"
                  :budget-id.sync="preferences.filters.bottomBudgetId"
                  :language="language"
                  style="width: 300px"
                  @change="onChangeBottomBudget"
                />
              </el-form-item>
              <el-form-item class="icon-button">
                <i
                  :title="$t('btnTitle.pageSetting')"
                  class="edac-icon action-icon edac-icon-setting1"
                  @click="onShowSetting"
                />
                <!--              <i class="edac-icon edac-icon-save"/>-->
                <!--              <i class="edac-icon edac-icon-excel_add"/>-->
                <!--          <i v-if="hasPermission_Output" class="edac-icon action-icon edac-icon-excel" @click="onExport"/>-->
              </el-form-item>
              <el-form-item class="icon-button">
                <el-button @click="onFullList">
                  {{ t('fullList') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="bottom-table" style="height: calc(100% - 45px)">
            <ETable
              ref="bottomTable"
              :data="bottomTableData"
              :style-columns="styleColumns"
              :lang-key="langKey"
              :show-index="false"
              :show-checkbox="false"
              :default-top="230"
              :show-actions="false"
              :full-column="true"
              :row-style="rowStyle1"
              border
              style="height: 100%"
              @changeWidth="changeColumnWidth"
              @selection-change="onSelectionChange"
            >
              <template slot="columns">
                <el-table-column
                  v-for="item in styleColumns.filter(i => i.ss_key !== '_index')"
                  :key="item.ss_key"
                  :label="$t(langKey + item.ss_key)"
                  :align="item.alignment"
                  :width="item.width"
                  :property="$refs.bottomTable.column_property(item)"
                  :column-key="item.ss_key"
                >
                  <template v-if="scope && scope.row" slot-scope="scope">
                    <span v-if="item.ss_key === 'amount'">
                      {{ amountFormat(scope.row.amount_dr - scope.row.amount_cr) }}
                    </span>
                    <span v-else>{{
                      $refs.bottomTable.customFormatter(
                        item.ss_key,
                        scope.row[$refs.bottomTable.column_property(item)],
                        customDateFormat
                      )
                    }}</span>
                  </template>
                </el-table-column>
              </template>
            </ETable>
          </div>

          <!-- 頁面設置 -->
          <customStyle
            :dialog-visible.sync="showDialog"
            :columns="tableColumns"
            :lang-key="langKey"
            :title="$t('style.defaultTitle')"
            table-type="full-screen-with-first-field"
            @reloadStyleSheets="loadUserStyle"
            @close="onCloseCustomStyleDialog"
          />
        </div>
      </template>
    </split-pane>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getBudgetYears } from '@/api/master/years'
import {
  copyBudgetTree,
  cleanBudgetTree,
  editBudgetSeq,
  editBudget,
  createBudget,
  fetchBudgetLedgers,
  exportBudgetLedgers,
} from '@/api/budget'
import loadPreferences from '@/views/mixins/loadPreferences'
import BudgetSelectTree from './../common/BudgetSelectTreeVue'
import treeTable from '@/components/TreeTable'
import splitPane from 'vue-splitpane'
import pageLeft from './left'
import pageRight from './right'
import pageBottom from './bottom'

import rightMixin from './rightMixin'
import leftMixin from './leftMixin'
import bottomMixin from './bottomMixin'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import mixinPermission from '@/views/mixins/permission'

import { amountFormat, convertTreeToList, toDecimal } from '@/utils'
import { fetchBudgetTree, getBudgetByCode } from '@/api/budget'
import {
  formatBudgetData,
  getFirstItem,
  getTreeBudgetByCode,
  getTreeBudgetById,
} from '@/views/budget/common/utils'
import { listenTo } from '@/utils/resizeListen'
import { exportExcel } from '@/utils/excel'

export default {
  name: 'BudgetManagementIndex',
  components: {
    splitPane,
    BudgetSelectTree,
    treeTable,
    pageLeft,
    pageRight,
    pageBottom,
  },
  mixins: [loadPreferences, mixinPermission, loadCustomStyle, leftMixin, rightMixin, bottomMixin],
  data() {
    return {
      loading: false,
      langKey: 'budget.budgetManagement.label.',
      years: [],
      tableData: [],
      stockList: [],
      preferences: {
        filters: {
          selectedYear: '',
          inquireYear: '',
          showLastYear: '1',

          selectedGroupId: '',
          selectedStage: '',
          selectedManager: '',
          selectedApprove: '',

          // bottom
          bottomYear: '',
          bottomBudgetId: '',

          horizontalPercent: 50,
          verticalPercent: 40,
          horizontalTime: 0,
          verticalTime: 0,
        },
      },
      childPreferences: ['selectedCode'],
      budgetGroupList: [],
      lastTableData: [],
      lastSummary: {},
      thisTableData: [],
      thisSummary: {},
      horizontalPercent: 50,
      verticalPercent: 40,
      showLastYear: '1',
      //

      customDateFormat: 'dd/MM/yy',
      // bottom
      bottomLoading: false,
      bottomRefresh: false, // true 刷新列表,
      bottomBudgetTree: [],
      currentBudgetGroup: {},
      bottomBudgetGroup: {},

      fold: false,
      lastFyCode: '',
      lastGroup: {},

      rightResizeListen: {},
      formListen: {},
      formHeight: 34,
      managerData: [],
      approve_staff_id: 0,
      budget_IE: '',
      lastGroupData: {},
      statusColor: {
        X: '#808080',
        S: '#FF0000',
        A: '#008000',
        O: '#0000FF',
        C: '#000000',
        R: '#808000',
        T: '#800000',
        K: '#004000',
        B: '#000080',
        D: '#000000',
      },
    }
  },
  computed: {
    ...mapGetters([
      'language',
      'styles',
      'grade',
      'user_type',
      'user_id',
      'permissions',
      'grade',
      'school',
    ]),
    canNextApproval() {
      if (this.budgetGroup.budget_stage === 'C') {
        return this.school.edbg_second_approve === 'Y'
      } else {
        return true
      }
    },
    stageOptions() {
      const s = ['X', 'S', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D']
      return s.map(key => {
        return {
          label: this.$t('budget.budgetSet.label.stage_' + key.toLowerCase()),
          value: key,
        }
      })
    },

    // 》》》》》》 右表
    budgetGroup() {
      return this.currentBudgetGroup
    },
    manager() {
      if (!this.budgetGroup) return ''
      return this.language === 'en'
        ? this.budgetGroup.manager_name_en
        : this.budgetGroup.manager_name_cn
    },
    approve() {
      if (!this.budgetGroup) return ''
      return this.language === 'en'
        ? this.budgetGroup.approve_name_en
        : this.budgetGroup.approve_name_cn
    },
    stage() {
      const key = {
        X: 'notSubmitted',
        S: 'submitted',
        A: 'approved',
        O: 'accepted',
        C: 'confirmed',
        R: 'revising',
        T: 'revised',
        K: 'reApproved',
        B: 'reAccepted',
        D: 'reConfirmed',
      }
      const langKey = 'budget.budgetManagement.button.'
      const stage = this.budgetGroup.budget_stage
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    cancelStage() {
      const key = {
        // X: '',
        S: 'withdrawSubmission',
        A: 'withdrawApproval',
        O: 'withdrawAcceptance',
        C: 'withdrawConfirmation',
        R: 'cancelReviseMode',
        T: 'withdrawRevision',
        K: 'withdrawReApproval',
        B: 'withdrawAcceptance',
        D: 'withdrawConfirmation',
      }
      const langKey = 'budget.budgetManagement.button.'
      const stage = this.budgetGroup.budget_stage
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    nextStage() {
      const key = {
        X: 'submitBudget',
        S: 'approveBudget',
        A: 'acceptApproval',
        O: 'confirmAcceptance',
        C: 'reviseMode',
        R: 'summitRevision',
        T: 'reApproveBudget',
        K: 'acceptReApproval',
        B: 'confirmAcceptance',
        D: 'reviseMode',
      }
      const langKey = 'budget.budgetManagement.button.'
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    cancelStageMessage() {
      const key = {
        // X: '',
        S: 'withdrawSubmittedBudget',
        A: 'withdrawApprovedBudget',
        O: 'withdrawAcceptance',
        C: 'withdrawConfirmation',
        R: 'cancelReviseMode',
        T: 'withdrawSubmittedRevision',
        K: 'withdrawReApprovedBudget',
        B: 'withdrawReAcceptance',
        D: 'withdrawReConfirmation',
      }
      const langKey = 'budget.budgetManagement.message.'
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    nextStageMessage() {
      const key = {
        X: 'submitBudgetForApproval',
        S: 'approveBudget',
        A: 'acceptApproval',
        O: 'confirmAcceptBudget',
        C: 'enterReviseBudgetMode',
        R: 'submitRevisionForApproval',
        T: 'reApproveBudget',
        K: 'reAcceptApproval',
        B: 'confirmReAcceptedBudget',
        D: 'enterReviseBudgetMode',
      }
      const langKey = 'budget.budgetManagement.message.'
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return this.$t(langKey + key[stage])
      } else {
        return ''
      }
    },
    surplusAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
          return 'proposed_amount'
        case 'S':
        case 'A':
        case 'O':
          return 'approved_amount'
        case 'R':
          return 'proposed_amount'
        case 'T':
        case 'K':
        case 'B':
          return 'approved_amount'
        case 'C':
        case 'D':
        default:
          return 'budget_amount'
      }
    },
    budgetAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
          return 'proposed_amount'
        case 'S':
        case 'A':
        case 'O':
          return 'approved_amount'
        case 'C':
        case 'R':
        case 'T':
        case 'K':
        case 'B':
        case 'D':
        default:
          return 'budget_amount'
      }
    },
    revisedAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
        case 'S':
        case 'A':
        case 'O':
        case 'C':
          return ''
        case 'R':
          return 'proposed_amount'
        case 'T':
        case 'K':
        case 'B':
          return 'approved_amount'
        case 'D':
        default:
          return 'budget_amount'
      }
    },
    showRevised() {
      if (!this.budgetGroup) return false
      switch (this.budgetGroup.budget_stage) {
        case 'R':
        case 'T':
        case 'K':
        case 'B':
          return true
        case 'D':
        case 'X':
        case 'S':
        case 'A':
        case 'O':
        case 'C':
        default:
          return false
      }
    },
    editAmountField() {
      if (!this.budgetGroup) return ''
      switch (this.budgetGroup.budget_stage) {
        case 'X':
        case 'R':
          return 'proposed_amount'
        case 'S':
        case 'T':
          return 'approved_amount'
        default:
          return ''
      }
    },
    showAction() {
      if (!this.budgetGroup) return false
      return 'XSRT'.includes(this.budgetGroup.budget_stage)
    },
    canEdit() {
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      const isEditMode1 = ['X', 'S', 'R', 'T'].includes(stage)
      const isEditMode2 = ['X', 'R'].includes(stage)
      const isEditMode3 = ['S', 'T'].includes(stage)
      if (this.isSystemAdmin && isEditMode1) return true
      console.log(
        this.isSystemAdmin,
        this.isPrincipal,
        this.isApproveStaff,
        isEditMode1,
        isEditMode2,
        isEditMode3,
        999,
      )

      if (!this.isSystemAdmin && this.isPrincipal && this.isApproveStaff && isEditMode1) return true
      if (!this.isSystemAdmin && this.isPrincipal && isEditMode2) return true
      if (!this.isSystemAdmin && this.isApproveStaff && isEditMode3) return true
      return false
    },
    isSystemAdmin() {
      console.log(
        !!(
          this.user_type === 'S' ||
          (this.permissions['bg.level.admin'] &&
            this.permissions['bg.level.admin'].p_selected === 'Y')
        ),
      )

      return !!(
        this.user_type === 'S' ||
        (this.permissions['bg.level.admin'] &&
          this.permissions['bg.level.admin'].p_selected === 'Y')
      )
    },
    rightTableHeight1() {
      const thisTableData = [this.thisSummary]
      if (this.budget_IE === 'B') {
        const haveRuling = thisTableData.some(
          v =>
            v.I_proposed_amount !== v.I_approved_amount &&
            v.E_proposed_amount !== v.E_approved_amount,
        )
        if (haveRuling) {
          return 'calc(100% - 32px)'
        }
        const haveRuling1 = thisTableData.some(
          v =>
            this.budgetGroup &&
            'SATK'.includes(this.budgetGroup.budget_stage) &&
            (v.I_proposed_amount !== v.I_approved_amount ||
              v.E_proposed_amount !== v.E_approved_amount),
        )
        if (haveRuling1) {
          return 'calc(100% - 18px)'
        }
        return 'calc(100% - 4px)'
      } else {
        const haveRuling = thisTableData.some(
          v =>
            v.I_proposed_amount !== v.I_approved_amount ||
            v.E_proposed_amount !== v.E_approved_amount,
        )
        if (haveRuling) {
          return 'calc(100% - 4px)'
        }
        return '100%'
      }
    },
    leftBtnShow() {
      if (this.isSystemAdmin) return true
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (this.isPrincipal && this.isApproveStaff) {
        return ['S', 'A', 'O', 'R', 'T', 'K'].includes(stage)
      } else if (this.isPrincipal) {
        return ['S', 'O', 'R', 'T', 'B'].includes(stage)
      } else if (this.isApproveStaff) {
        return ['A', 'K'].includes(stage)
      } else {
        return false
      }
    },
    rightBtnShow() {
      if (this.isSystemAdmin) return true
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (this.isPrincipal && this.isApproveStaff) {
        return ['X', 'S', 'A', 'C', 'R', 'T', 'K', 'D'].includes(stage)
      } else if (this.isPrincipal) {
        return ['X', 'A', 'C', 'R', 'D', 'K'].includes(stage)
      } else if (this.isApproveStaff) {
        return ['S', 'T'].includes(stage)
      } else {
        return false
      }
    },
    isApproveStaff() {
      return this.approve_staff_id === this.user_id
    },
    isPrincipal() {
      console.log(this.managerData, this.user_id, 999)
      if (!this.managerData.length) return false

      return this.managerData.some(v => v.staff_id === this.user_id)
    },
    leftButtonStyle() {
      const key = {
        X: '',
        S: '#808080',
        A: '#FF0000',
        O: '#008000',
        C: '#0000FF',
        R: '#000000',
        T: '#D08000',
        K: '#800000',
        B: '#008080',
        D: '#000080',
      }
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return {
          '--bg': `${key[stage]}`,
          backgroundColor: `${key[stage]} !important`,
          borderColor: `${key[stage]} !important`,
          color: '#fff',
        }
      } else {
        return ''
      }
    },
    centerButtonStyle() {
      const key = {
        X: '#808080',
        S: '#FF0000',
        A: '#008000',
        O: '#0000FF',
        C: '#000000',
        R: '#D08000',
        T: '#800000',
        K: '#008080',
        B: '#000080',
        D: '#000000',
      }
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return {
          '--bg': `${key[stage]}`,
          backgroundColor: `${key[stage]} !important`,
          borderColor: `${key[stage]} !important`,
          color: '#fff',
        }
      } else {
        return ''
      }
    },
    rightButtonStyle() {
      const key = {
        X: '#FF0000',
        S: '#008000',
        A: '#0000FF',
        O: '#000000',
        C: '#D08000',
        R: '#800000',
        T: '#008080',
        K: '#000080',
        B: '#000000',
        D: '#D08000',
      }
      const stage = this.budgetGroup ? this.budgetGroup.budget_stage : ''
      if (key[stage]) {
        return {
          '--bg': `${key[stage]}`,
          backgroundColor: `${key[stage]} !important`,
          borderColor: `${key[stage]} !important`,
          color: '#fff',
        }
      } else {
        return ''
      }
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {
    this.$nextTick(() => {
      this.formListen = listenTo(this.$refs.form.$el, ({ width, height, ele }) => {
        // console.log(height, width)
        this.formHeight = height
      })
    })

    // this.leftScrollBarSync()
    // this.$nextTick(() => {
    //   this.leftResizeListen = listenTo(this.$refs.form.$el, ({ width, height, ele }) => {
    //     this.leftInfoHeight = height + 10
    //   })
    // })
    //
    // this.rightScrollBarSync()
    const that = this
    this.$nextTick(() => {
      this.rightResizeListen = listenTo(
        document.querySelector('.right-table .el-table__body-wrapper > table'),
        ({ width, height, ele }) => {
          // this.rightInfoHeight = height + 10
          // console.log(width, height, ele)

          that.syncTable()
          setTimeout(() => {
            // document.querySelector('.budget-management-right .summary-table .el-table__body-wrapper > table').style.width = width +'px'
            // // document.querySelector('.budget-management-right .summary-table .el-table__body-wrapper > table').style.width = width + 'px'
            // const a = document.querySelectorAll('.right-table .el-table__body-wrapper > table > colgroup > col')
            // const b = document.querySelectorAll('.budget-management-right .summary-table .el-table__body-wrapper > table > colgroup > col')
            // a.forEach((ele, i) => {
            //   if (b[i]) {
            //     console.log(b[i].width, ele.width)
            //     b[i].width = ele.width
            //     console.log(b[i].width === ele.width, b[i].width , ele.width)
            //   }
            // })
            that.syncTable()
          }, 400)
        },
      )
    })
  },
  beforeDestroy() {
    // this.leftCancelScrollBarSync()
    // this.rightCancelScrollBarSync()
  },
  methods: {
    syncTable() {
      const width = document.querySelector(
        '.right-table .el-table__body-wrapper > table',
      ).offsetWidth
      document.querySelector(
        '.budget-management-right .summary-table .el-table__body-wrapper > table',
      ).style.width = width + 'px'
      // document.querySelector('.budget-management-right .summary-table .el-table__body-wrapper > table').style.width = width + 'px'
      const a = document.querySelectorAll(
        '.right-table .el-table__body-wrapper > table > colgroup > col',
      )
      const b = document.querySelectorAll(
        '.budget-management-right .summary-table .el-table__body-wrapper > table > colgroup > col',
      )
      a.forEach((ele, i) => {
        if (b[i]) {
          b[i].width = ele.width
        }
      })
    },
    deepCloneBudget(budgetTreeArray, filter = () => true) {
      const newArray = []
      budgetTreeArray.forEach(item => {
        const newItem = Object.assign({}, item)
        if (item.children) {
          newItem.children = this.deepCloneBudget(item.children, filter)
        }
        if (filter(newItem)) {
          newArray.push(newItem)
        }
      })
      return newArray
    },
    scrollEvent() {
      this.dataTableScrollEle.scrollLeft = this.summaryTableScrollEle.scrollLeft
    },
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      // if (!row.date) {
      //   return 'count-row'
      // }
    },
    /**
     * 類型格式轉換
     */
    typeFormat(row, column) {
      console.log(row.type)
      switch (row.type) {
        case 'S':
          return this.$t('stock.label.sales')
        case 'D':
          return this.$t('stock.label.damagedOrLost')
        case 'U':
          return this.$t('stock.label.internalUse')
        case 'W':
          return this.$t('stock.label.writeOff')
        case 'P':
          return this.$t('stock.label.purchase')
        default:
          return ''
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.tableData.length - 1) {
        if (columnIndex < 2 || columnIndex === 3) {
          return { rowspan: 0, colspan: 0 }
        } else if (columnIndex === 2) {
          return { rowspan: 1, colspan: 4 }
        }
      }
      return { rowspan: 1, colspan: 1 }
    },
    // fetchData() {
    //   this.loading = false
    //   fetchYears()
    //     .then(res => {
    //       this.years = res
    //     })
    //     .then(this.loadUserPreference)
    //     .then(() => {
    //       if (this.years.length) {
    //         const month = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
    //         if (month) {
    //           return
    //         }
    //         this.preferences.filters.selectedYear = this.years[0].fy_code
    //       } else {
    //         return Promise.reject(this.$t('message.theYearDoNotExist'))
    //       }
    //     })
    //     .then(() => {
    //       // 類別
    //       const fy_code = this.preferences.filters.selectedYear
    //       const type = 'G'
    //       return fetchBudgetTree({ fy_code, type })
    //     })
    //     .then(res => {
    //       this.budgetGroupList = res
    //       this.currentBudgetGroup = getTreeBudgetById(res, this.preferences.filters.selectedGroupId)
    //       // 上年
    //       const yearIndex = this.years.findIndex(i => i.fy_code === this.preferences.filters.selectedYear)
    //       if (yearIndex > 0) {
    //         const fy_code = this.years[yearIndex - 1].fy_code
    //         const parent_budget_id = this.preferences.filters.selectedGroupId
    //         return fetchBudgetTree({ fy_code, parent_budget_id })
    //       } else {
    //         return { data: [], summary: {}}
    //       }
    //     })
    //     .then(res => {
    //       const { data, summary } = formatBudgetData(res)
    //       this.lastTableData = data
    //       this.lastSummary = summary
    //       // 本年
    //       const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
    //       if (year) {
    //         const fy_code = year.fy_code
    //         const parent_budget_id = this.preferences.filters.selectedGroupId
    //         return fetchBudgetTree({ fy_code, parent_budget_id })
    //       } else {
    //         return []
    //       }
    //     })
    //     .then(res => {
    //       // this.thisTableData = res
    //       const { data, summary } = formatBudgetData(res)
    //       this.thisTableData = data
    //       this.thisSummary = summary
    //     })
    //     .then(() => this.handleChangeBottomYear(this.preferences.filters.bottomYear))
    //     .finally(() => {
    //       this.loading = false
    //     })
    // },
    showError(msgKey) {
      const langKey = 'budget.budgetManagement.message.'
      this.$message.error(this.$t(langKey + msgKey))
    },
    async fetchData() {
      this.loading = true
      try {
        // 所有年份
        this.years = await getBudgetYears()

        // 用戶配置
        await this.loadUserPreference()
        this.onFold(this.preferences.filters.showLastYear)

        // 設置當前年份
        if (this.years.length) {
          const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
          if (!year) {
            this.preferences.filters.selectedYear = this.years[0].fy_code
          }
        } else {
          // this.showError('noYear')
          return
        }
        const yearIndex = this.years.findIndex(
          i => i.fy_code === this.preferences.filters.selectedYear,
        )
        if (yearIndex !== -1) {
          const v = yearIndex > 0 ? '1' : '0'
          this.preferences.filters.showLastYear = v
          this.onFold(v)
        }
        // 獲取預算群組
        const fy_code = this.preferences.filters.selectedYear
        const gType = 'G'
        try {
          const budgetGroupList = await fetchBudgetTree({ fy_code, type: gType })
          this.budgetGroupList = budgetGroupList
          console.log('budgetGroupList', JSON.parse(JSON.stringify(budgetGroupList)))

          // 當前選中的群組
          this.currentBudgetGroup = getTreeBudgetById(
            budgetGroupList,
            this.preferences.filters.selectedGroupId,
          )
          this.bottomBudgetGroup = this.currentBudgetGroup
          // this.preferences.filters.selectedGroupId = ''
          // this.setBottomFirstBudget()
          this.handleChangeBottomYear(this.preferences.filters.bottomYear, false)
          // this.$refs['bottomPage'].fetchData()
        } catch (e) {
          console.log(e)
          // this.showError('loadingPageDataFailed')
          // return
        }

        // 本年
        this.loadThisData()
        // 上年
        this.loadLastData()
      } catch (e) {
        console.log(e)
      }

      this.loading = false
    },
    async loadLastData() {
      // 上年
      try {
        const yearIndex = this.years.findIndex(
          i => i.fy_code === this.preferences.filters.selectedYear,
        )
        if (yearIndex > 0 && this.currentBudgetGroup) {
          // 獲取上年數據
          const fy_code = this.years[yearIndex - 1].fy_code
          this.lastFyCode = fy_code
          // const budget_code = this.currentBudgetGroup.budget_code
          const bg = getTreeBudgetById(
            this.budgetGroupList,
            this.preferences.filters.selectedGroupId,
          )

          let budget_code = ''
          if (!bg || !bg.budget_code) {
            this.lastGroup = {}
            // 無上年數據
            this.lastTableData = []
            this.lastSummary = {}
          } else {
            budget_code = bg.budget_code
          }

          let lastYearHasCode
          let lastGroup
          try {
            lastGroup = await getBudgetByCode({ fy_code, budget_code })
            lastYearHasCode = true
          } catch (e) {
            lastYearHasCode = false
          }
          if (lastYearHasCode) {
            this.lastGroup = lastGroup
            const parent_budget_id = lastGroup.budget_id
            if (parent_budget_id) {
              const res = await fetchBudgetTree({ fy_code, parent_budget_id })
              // 格式化
              const lastData = convertTreeToList(res, 0, false)
              const { data, summary } = formatBudgetData(lastData, true)
              this.lastTableData = data
              this.lastSummary = summary
            } else {
              this.lastGroup = {}
              // 無上年數據
              this.lastTableData = []
              this.lastSummary = {}
            }
          } else {
            this.lastGroup = {}
            // 無上年數據
            this.lastTableData = []
            this.lastSummary = {}
          }
        } else {
          // 無上年數據
          this.lastTableData = []
          this.lastSummary = {}
        }
      } catch (e) {
        this.showError('loadingLastYearDataFailed')
      }
    },
    handleReload(position) {
      if (position === 2) {
        this.loadThisData()
      }
    },
    async loadThisData() {
      try {
        this.rightScope = {}
        // 本年
        const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
        if (year) {
          const fy_code = year.fy_code

          let parent_budget_id = ''
          const oldItem = getTreeBudgetById(
            this.budgetGroupList,
            this.preferences.filters.selectedGroupId,
          )

          let lastYearHasCode
          let lastGroup
          if (oldItem) {
            try {
              lastGroup = await getBudgetByCode({ fy_code, budget_code: oldItem.budget_code })
              this.lastGroupData = lastGroup
              this.managerData = lastGroup.manager_staffs
              this.approve_staff_id = lastGroup.approve_staff_id
              this.budget_IE = lastGroup.budget_IE
              lastYearHasCode = true
            } catch (e) {
              lastYearHasCode = false
            }
          }
          if (lastYearHasCode) {
            parent_budget_id = this.preferences.filters.selectedGroupId
          }

          // const parent_budget_id = this.preferences.filters.selectedGroupId
          if (parent_budget_id) {
            const res = await fetchBudgetTree({ fy_code, parent_budget_id })
            // 本年數據
            // const thisData = convertTreeToList(res, 0, false)
            const { data, summary } = formatBudgetData(res)
            this.thisTableData = data
            console.log('summary', summary)

            this.thisSummary = summary
            return
          }
        }
      } catch (e) {
        this.showError('loadingThisYearDataFailed')
      }
      // 無本年數據
      this.thisTableData = []
      this.thisSummary = {}
      this.$nextTick(() => {
        this.syncTable()
      })
    },
    loadBottomData(budget_id) {
      if (this.bottomLoading) return
      this.bottomLoading = true
      const fy_code = this.fyCode
      // const budget_id = this.preferences.filters.selectedGroupId
      if (!fy_code || !budget_id) return
      fetchBudgetLedgers({ fy_code, budget_id })
        .then(res => {
          this.bottomTableData = res
        })
        .finally(() => {
          this.bottomLoading = false
        })
    },
    async reloadData() {
      // 本年
      await this.loadThisData()
      // 上年
      await this.loadLastData()
    },
    formatData(data) {
      const newData = []
      const sum = {
        this_budget_IE: '',
        this_I_total_proposed_amount: 0,
        this_I_total_approved_amount: 0,
        this_I_actual_amount: 0,
        this_I_balance: 0,
        this_E_total_proposed_amount: 0,
        this_E_total_approved_amount: 0,
        this_E_actual_amount: 0,
        this_E_balance: 0,
        last_budget_IE: '',
        last_I_total_budget_amount: 0,
        last_I_actual_amount: 0,
        last_I_balance: 0,
        last_E_total_budget_amount: 0,
        last_E_actual_amount: 0,
        last_E_balance: 0,
        actual_amount: 0,

        proposed_amount: 0,
        approved_amount: 0,
        budget_amount: 0,
      }
      let thisI = false
      let lastI = false
      let thisE = false
      let lastE = false
      const cols = [
        'this_I_total_proposed_amount',
        'this_I_total_approved_amount',
        'this_I_actual_amount',
        'this_E_total_proposed_amount',
        'this_E_total_approved_amount',
        'this_E_actual_amount',
        'last_I_total_budget_amount',
        'last_I_actual_amount',
        'last_E_total_budget_amount',
        'last_E_actual_amount',

        'proposed_amount',
        'approved_amount',
        'budget_amount',
        'actual_amount',
      ]
      const balanceCols = {
        this_I_balance: ['this_I_total_proposed_amount', 'this_I_total_approved_amount'],
        this_E_balance: ['this_E_total_proposed_amount', 'this_E_total_approved_amount'],
        last_I_balance: ['this_I_total_approved_amount', 'last_I_actual_amount'],
        last_E_balance: ['this_E_total_approved_amount', 'last_E_actual_amount'],
      }

      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        const newItem = Object.assign({}, item)
        newData.push(newItem)
        cols.forEach(key => {
          sum[key] = this.add(sum[key], item[key])
        })
        for (const key in balanceCols) {
          const l = balanceCols[key][0]
          const r = balanceCols[key][1]
          const balance = toDecimal(sum[key] + (newItem[l] - newItem[r]))
          newItem[key] = balance
          sum[key] = balance
        }
        if (!thisI && 'IB'.includes(newItem.this_budget_IE)) {
          thisI = true
        }
        if (!thisE && 'EB'.includes(newItem.this_budget_IE)) {
          thisE = true
        }
        if (!lastI && 'IB'.includes(newItem.last_budget_IE)) {
          lastI = true
        }
        if (!lastE && 'EB'.includes(newItem.last_budget_IE)) {
          lastE = true
        }
      }
      if (thisI && thisE) {
        sum.this_budget_IE = 'B'
      } else if (thisI) {
        sum.this_budget_IE = 'I'
      } else {
        sum.this_budget_IE = 'E'
      }
      if (lastI && lastE) {
        sum.last_budget_IE = 'B'
      } else if (lastI) {
        sum.last_budget_IE = 'I'
      } else {
        sum.last_budget_IE = 'E'
      }
      return {
        data: newData,
        summary: sum,
      }
    },
    add(a, b) {
      const a1 = Number(a)
      const a2 = isNaN(a1) ? 0 : a1
      const b1 = Number(b)
      const b2 = isNaN(b1) ? 0 : b1
      return toDecimal(a2 + b2)
    },
    setFirstBudget(tree) {
      const budget = getFirstItem(tree, item => item.budget_type !== 'F')
      if (budget) {
        this.preferences.filters.selectedGroupId = budget.budget_id
        return true
      } else {
        this.preferences.filters.selectedGroupId = ''
        return false
      }
    },
    setBottomFirstBudget(tree) {
      const budget = getFirstItem(tree, item => item.budget_type !== 'F')
      if (budget) {
        this.preferences.filters.bottomBudgetId = budget.budget_id
        return true
      } else {
        this.preferences.filters.bottomBudgetId = ''
        return false
      }
    },
    languageKey(key) {
      return key + (this.language === 'en' ? 'en' : 'cn')
    },
    formatterByStage(row, column, cellValue, index) {
      const stage = this.stageOptions.find(i => i.value === cellValue)
      return stage ? stage.label : ''
    },
    amountFormat: amountFormat,
    summarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3,
        }
      } else if (columnIndex < 3) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },

    // *******************  聯動 *******************//
    /*
      | update  |           load              |
      |   Y1    |  B1 |  Y2 |  B2 |  T1 |  T2 |
      |   B1    |     |  Y2 |  B2 |  T1 |  T2 |
      |   Y2    |     |     |  B2 |     |  T2 |
      |   B2    |     |     |     |     |  T2 |
      |         |     |     |     |     |     |
    */
    /*          一. Y1
        1. B1: 重設轉賬budget_id
        2. Y2: 同步Y1 fy_code
        3. B2: 同步B2 budget_id
        4. T1：刷新
        5. T2: 刷新

     */

    /*          二. B1
        2. Y2: 同步Y1 fy_code
        3. B2: 同步B2 budget_id
        4. T1：刷新
        5. T2: 刷新

     */

    /* 複製結構 */
    async handleCopyComposition(mode) {
      const fy_code = this.preferences.filters.selectedYear
      const budget_id = this.preferences.filters.selectedGroupId
      try {
        await copyBudgetTree({ fy_code, budget_id, mode })
        this.loadThisData()
        this.handleChangeBottomYear(this.preferences.filters.selectedYear, false)
        this.$message.success(this.$t('copySuccess'))
      } catch (e) {
        // this.$message.error('複製失敗')
      }
    },
    /* 清除結構 */
    async handleCleanComposition(mode) {
      let title = mode === 'ALL' ? 'cleanComposition' : 'cleanCompositionAndAmount'
      title = 'budget.budgetManagement.message.' + title
      this.$confirm(this.$t(title), this.$t('message.tips'), {
        confirmButtonText: this.$t('button.confirm'),
        cancelButtonText: this.$t('button.cancel'),
        type: 'warning',
      })
        .then(async() => {
          const fy_code = this.preferences.filters.selectedYear
          const budget_id = this.preferences.filters.selectedGroupId
          try {
            await cleanBudgetTree({ fy_code, budget_id, mode })
            this.loadThisData()
            this.preferences.filters.bottomBudgetId = ''
            this.bottomTableData = []
            await this.handleChangeBottomYear(this.preferences.filters.selectedYear, false)
            this.$message.success(this.$t('message.clearSuccess'))
          } catch (e) {
            // this.$message.error('清除失敗')
          }
        })
        .catch(() => {
          // this.$message.info('已取消')
        })
    },
    onFold(val) {
      // this.fold = val
      if (val === '1') {
        this.preferences.filters.verticalPercent = 40
      } else {
        this.preferences.filters.verticalPercent = 0
      }
    },

    //  全局
    async onChangeYear(fy_code) {
      const filters = this.preferences.filters

      // const year = this.years.find(i => i.fy_code === val)
      // if (year) {
      // const type = 'G'
      // 獲取當前選中的BudgetGroup
      //   const oldItem = getTreeBudgetById(this.budgetGroupList, this.preferences.filters.selectedGroupId)
      //   fetchBudgetTree({ fy_code: year.fy_code, type })
      //     .then(res => {
      //       // set B1
      //       this.budgetGroupList = res
      //       if (oldItem) {
      //         const thisBudget = getTreeBudgetByCode(res, oldItem.budget_code)
      //         if (thisBudget) {
      //           this.preferences.filters.selectedGroupId = thisBudget.budget_id
      //           this.currentBudgetGroup = Object.assign({}, thisBudget)
      //           return
      //         }
      //       }
      //       const hasBudget = this.setFirstBudget(res)
      //       if (!hasBudget) {
      //         return Promise.reject()
      //       }
      //     })
      //     .then(() => {
      //       // set B2
      //       this.preferences.filters.bottomBudgetId = this.preferences.filters.selectedGroupId
      //       // set Y2 異步 => T2
      //       this.preferences.filters.bottomYear = year.fy_code
      //       this.handleChangeBottomYear(year.fy_code, true)
      //     })
      //     .then(this.reloadData)
      //     .catch(() => {
      //       this.lastTableData = []
      //       this.lastSummary = {}
      //     })
      // }

      const year = this.years.find(i => i.fy_code === fy_code)

      if (year) {
        // 有效年
        // 查詢去年是否有一樣的預算項目

        const type = 'G'
        this.budgetGroupList = await fetchBudgetTree({ fy_code: year.fy_code, type })

        // 獲取當前選中的BudgetGroup
        const oldItem = getTreeBudgetById(this.budgetGroupList, filters.selectedGroupId)
        if (oldItem) {
          try {
            // const type = 'G'
            // const res = await fetchBudgetTree({ fy_code: year.fy_code, type })
            //
            // this.budgetGroupList = res
            if (oldItem) {
              const thisBudget = getTreeBudgetByCode(this.budgetGroupList, oldItem.budget_code)
              if (thisBudget) {
                this.preferences.filters.selectedGroupId = thisBudget.budget_id
                this.currentBudgetGroup = Object.assign({}, thisBudget)
                this.reloadData()
                return
              }
            }
            const hasBudget = this.setFirstBudget(this.budgetGroupList)
            if (!hasBudget) {
              return Promise.reject()
            }

            // 設置下表
            // set B2
            this.preferences.filters.bottomBudgetId = this.preferences.filters.selectedGroupId
            // set Y2 異步 => T2
            this.preferences.filters.bottomYear = year.fy_code
            this.handleChangeBottomYear(year.fy_code, false)
          } catch (e) {
            console.log(e)
          }
        } else {
          this.currentBudgetGroup = {}
          this.preferences.filters.selectedGroupId = ''
        }
        await this.reloadData()
      } else {
        // 無效年
        filters.selectedYearCode = ''
        filters.selectedYear = ''
      }
      const yearIndex = this.years.findIndex(
        i => i.fy_code === this.preferences.filters.selectedYear,
      )
      if (yearIndex !== -1) {
        const v = yearIndex > 0 ? '1' : '0'
        this.preferences.filters.showLastYear = v
        this.onFold(v)
      }
    },

    async onChangeBudgetGroup(val) {
      // 當前選中的群組
      this.currentBudgetGroup = val // getTreeBudgetById(this.budgetGroupLis, val.budget_id)
      this.bottomBudgetGroup = this.currentBudgetGroup
      console.log(val)
      await this.loadThisData()
      await this.loadLastData()
      await this.changeBottomList(this.fyCode, this.preferences.filters.selectedGroupId, false)
    },
    async onFocusBudgetGroup() {
      this.budgetGroupList = await fetchBudgetTree({
        fy_code: this.preferences.filters.selectedYear,
        type: 'G',
      })
    },
    // 左

    // 右

    // 下

    async handleChangeBottomYear(val, changeGroup) {
      const fy_code = val // this.preferences.filters.bottomYear
      let parent_budget_id = this.preferences.filters.selectedGroupId
      if (!val || !parent_budget_id) {
        this.bottomBudgetGroup = {}
        return Promise.reject()
      }
      let oldItem

      if (changeGroup) {
        const budget = getTreeBudgetById(
          this.budgetGroupList,
          this.preferences.filters.selectedGroupId,
        )
        if (budget) {
          this.bottomBudgetGroup = budget
        }
      } else {
        this.bottomBudgetGroup = {}
        oldItem = getTreeBudgetById(this.bottomBudgetTree, this.preferences.filters.bottomBudgetId)
      }
      const budget_code =
        (this.bottomBudgetGroup && this.bottomBudgetGroup.budget_code) || undefined
      let newYearHasCode = false
      if (budget_code) {
        try {
          const res = await getBudgetByCode({ fy_code, budget_code })
          parent_budget_id = res.budget_id
          newYearHasCode = true
        } catch (e) {
          //
        }
      }
      try {
        const res = await fetchBudgetTree({
          fy_code,
          parent_budget_id: newYearHasCode ? parent_budget_id : undefined,
        })
        console.log(res, 88888888)
        this.bottomBudgetTree = res
        if (changeGroup) {
          this.preferences.filters.bottomBudgetId =
            (this.bottomBudgetGroup && this.currentBudgetGroup.budget_id) || ''
        } else {
          if (oldItem && oldItem.budget_code) {
            const thisBudget = getTreeBudgetByCode(res, oldItem.budget_code)
            if (thisBudget) {
              this.preferences.filters.bottomBudgetId = thisBudget.budget_id
              this.$nextTick(() => {
                this.fetchBottomData()
              })
              return
            } else {
              this.preferences.filters.bottomBudgetId = ''
              this.bottomTableData = []
            }
          }
          if (changeGroup) {
            const hasBudget = this.setBottomFirstBudget(res)
            if (!hasBudget) {
              return Promise.reject()
            }
          }
          this.$nextTick(() => {
            this.fetchBottomData()
          })
        }
      } catch (e) {
        //
      }
    },
    horizontalResize() {
      this.horizontalTime = new Date().getTime()
      setTimeout(() => {
        if (new Date().getTime() - this.horizontalTime >= 500) {
          this.preferences.filters.horizontalPercent = this.$refs.horizontalSplitPane.percent
        }
      }, 500)
    },
    verticalResize() {
      this.verticalTime = new Date().getTime()
      setTimeout(() => {
        if (new Date().getTime() - this.verticalTime >= 500) {
          this.preferences.filters.verticalPercent = this.$refs.verticalSplitPane.percent
        }
      }, 500)
    },
    budgetIEOptions(row) {
      let data = []
      console.log('this.budget_IE', this.budget_IE)

      if (this.budget_IE === 'E') {
        data.push({ label: 'E', value: 'E' })
      } else if (this.budget_IE === 'I') {
        data.push({ label: 'I', value: 'I' })
      } else {
        data.push({ label: 'I', value: 'I' })
        data.push({ label: 'E', value: 'E' })
      }
      if (['C', 'G'].includes(row.budget_type)) {
        data.unshift({ label: 'B', value: 'B' })
      }
      console.log('DATA', data)

      if (data.length) {
        if (this.thisTableData) {
          if (this.budget_IE === 'B') return data
          data = data.filter(i => i.value === this.budget_IE)
        }
      }
      return data
    },
    rowClassName({ row, column }) {
      if (
        row.edit &&
        [
          'budget_code',
          this.languageKey('name_'),
          'budget_IE',
          'budget_amount',
          'approved_amount',
        ].includes(column.property)
      ) {
        return ' cell-edit'
      } else {
        return ''
      }
    },
    rowStyle({ row, rowIndex }) {
      if (this.rightScope.row && this.rightScope.row.budget_id === row.budget_id) {
        return 'background: #E2F0FA'
      } else if (row.budget_type === 'C') {
        return 'background: #e2e2e2'
      } else if (rowIndex % 2 === 0) {
        return 'background: #FAFAFA'
      } else {
        return 'background: #fff'
      }
    },
    rowStyle1({ rowIndex }) {
      if (rowIndex % 2 === 0) {
        return 'background: #FAFAFA !important'
      } else {
        return 'background: #fff !important'
      }
    },
    rowStyle2({ row, rowIndex }) {
      if (this.leftScope.row && this.leftScope.row.budget_id === row.budget_id) {
        return 'background: #E2F0FA !important'
      } else if (row.budget_type === 'C') {
        return 'background: #e2e2e2 !important'
      } else if (rowIndex % 2 === 0) {
        return 'background: #FAFAFA !important'
      } else {
        return 'background: #fff !important'
      }
    },
    onFullList() {
      this.loading = true
      exportBudgetLedgers({
        fy_code: this.preferences.filters.bottomYear,
        budget_id: this.preferences.filters.bottomBudgetId,
      })
        .then(res => {
          exportExcel(res)
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    userCanUse(type) {
      if (this.isSystemAdmin) return true
      console.log(this.lastGroupData, 'this.thisTableData')
      const manager_staffs = this.lastGroupData.manager_staffs
      const data = manager_staffs.find(i => i.staff_id === this.user_id)
      if (data) {
        if (data.type === 'E') {
          return true
        }
        if (data.type === 'N' && type === 'C') {
          return false
        }
        if (data.type === 'L') {
          return false
        }
        return true
      }
      return false
    },
  },
}
</script>

<style lang="scss" scoped>
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.app-container {
  height: 100%;
  padding: 0;
}

/deep/ {
  .splitter-pane.splitter-paneL.vertical,
  .splitter-pane-resizer.vertical {
    transition: all 0.1s ease 0s;
  }
  .splitter-pane.splitter-paneR.vertical {
    transition: all 0.1s ease 0s;
  }
}

.top-split {
  /deep/ {
    .splitter-pane {
      border-top: 1px solid #eaeaea;
    }
  }
}
.edac-icon.edac-icon-setting1 {
  color: #b9b6b6;
}
</style>
<!-- 右  -->
<style lang="scss" scoped>
.stage-action {
  /*float: right;*/
  line-height: 25px;

  .status-button {
    cursor: auto;
  }
  .next-btn {
    transition: 0s;

    &:after {
      content: '';
      position: absolute;
      right: -21px;
      top: -1px;
      /* width: 15px; */
      /* height: 15px; */
      /* transform: rotate(225deg); */
      /* transform-origin: top left; */
      /* border: 1px solid #ccc; */
      /* border-top: none; */
      /* border-right: none; */
      /* background: red; */
      /* background-color: #409EFF; */
      /* z-index: -1; */
      width: 0;
      height: 0;
      border-width: 11px;
      border-style: solid;
      border-color: transparent transparent transparent var(--bg);

      right: -1.8em;
      top: -0.1em;
      border-width: 0.9em;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover {
      &:after {
        border-color: transparent transparent transparent var(--bg);
      }
    }
    &:active,
    &.is-active {
      &:after {
        border-color: transparent transparent transparent var(--bg);
      }
    }
  }
  .pre-btn {
    transition: 0s;

    &:after {
      content: '';
      position: absolute;
      left: -21px;
      top: -1px;
      /* width: 15px; */
      /* height: 15px; */
      /* transform: rotate(225deg); */
      /* transform-origin: top left; */
      /* border: 1px solid #ccc; */
      /* border-top: none; */
      /* border-right: none; */
      /* background: red; */
      /* background-color: #409EFF; */
      /* z-index: -1; */
      width: 0;
      height: 0;
      border-width: 11px;
      border-style: solid;
      border-color: transparent var(--bg) transparent transparent;

      left: -1.8em;
      top: -0.1em;
      border-width: 0.9em;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover {
      &:after {
        border-color: transparent var(--bg) transparent transparent;
      }
    }
    &:active,
    &.is-active {
      &:after {
        border-color: transparent var(--bg) transparent transparent;
      }
    }
  }

  /deep/ {
    .el-button + .el-button {
      margin-left: 2px;
      margin: 0;
    }
  }
}
.EDAC {
  &.font-size-10 {
    .stage-action {
      .pre-btn:after {
        left: -22px;
        border-width: 11px;
      }
      .next-btn:after {
        right: -22px;
        border-width: 11px;
      }
    }
  }
  &.font-size-11 {
    .stage-action {
      .pre-btn:after {
        left: -22px;
        border-width: 11px;
      }
      .next-btn:after {
        right: -21px;
        border-width: 11px;
      }
    }
  }
  &.font-size-12 {
    .stage-action {
      .pre-btn:after {
        left: -23px;
        border-width: 12px;
      }
      .next-btn:after {
        right: -23px;
        border-width: 12px;
      }
    }
  }
  &.font-size-13 {
    .stage-action {
      .pre-btn:after {
        left: -24px;
        border-width: 12px;
      }
      .next-btn:after {
        right: -24px;
        border-width: 12px;
      }
    }
  }
}

.budget-management-right {
  padding: 5px;
  height: 100%;

  &.right-table {
    height: calc(100% - 90px);
    /*padding-bottom: 30px;*/
    /deep/ {
      .cell {
        span.m-row {
          display: block;
          line-height: 14px;
        }

        span.oldAmount {
          text-decoration: line-through;
        }
      }

      .data-table.el-table {
        height: 100%;
        width: 100%;

        .cell {
          i {
            line-height: 16px;
            vertical-align: middle;
          }
        }

        .el-table__body-wrapper {
          overflow: auto;
        }
      }

      .summary-table.el-table {
        height: auto;
        overflow-y: scroll;
        &::-webkit-scrollbar {
          display: none;
        }
        .el-table__body-wrapper {
          overflow: hidden;
          overflow-y: scroll;
        }

        /*.el-table__fixed-right {*/
        /*  height: 50px !important;*/
        /*}*/
      }

      i.disabled {
        cursor: not-allowed;
        color: #c8c9cc;
      }

      tr.current-row td {
        background-color: transparent;
      }

      .row-action {
        user-select: none;
      }
    }
  }

  .data-action {
    .edac-icon {
      width: 25px;
      height: 25px;
      line-height: 25px;
      font-size: 20px;
    }
  }
  .tree-table {
    /deep/ {
      .current-row {
        background-color: #3e97dd26;
      }
    }
  }
}
</style>
<!-- 左 -->

<style lang="scss" scoped>
.budget-management-left {
  padding: 5px;
  height: 100%;

  .left-title {
    float: right;
    line-height: 25px;
  }
  .left-table {
    /*height: calc(100% - 80px);*/
    /*padding-bottom: 30px;*/
    /deep/ {
      .cell {
        span {
          display: block;
        }
      }
      .data-table.el-table {
        height: 100%;
        width: 100%;
        .cell {
          i {
            line-height: 16px;
            vertical-align: middle;
          }
        }
        .el-table__body-wrapper {
          overflow: auto;
        }
      }
      .summary-table.el-table {
        height: auto;
        overflow-y: scroll;
        &::-webkit-scrollbar {
          display: none;
        }
        .el-table__body-wrapper {
          overflow: hidden;
          overflow-y: scroll;
        }
      }
    }
  }

  .left-data-table {
    /deep/ {
      .el-table__row {
        > td {
          // background: #FFFFFF;
        }
        &.current-row {
          > td {
            background-color: #3e97dd26;
          }
        }
      }
    }
  }
}
.app-container {
  /deep/ {
    .el-table__row {
      & > td {
        & > div.cell {
          white-space: nowrap;
          text-overflow: ellipsis;
          width: auto;
          overflow: hidden;
          & > div,
          & > span {
            white-space: nowrap;
            text-overflow: ellipsis;
            width: auto;
            overflow: hidden;
          }
        }
      }
    }
  }
}
.budget_IE_select {
  overflow: visible !important;
  /deep/.el-input__inner {
    padding-right: 0 !important;
  }
  /deep/ .el-input__suffix {
    .el-input__icon {
      line-height: 25px !important;
    }
  }
}
/deep/ .cell-edit {
  .cell {
    padding: 0;
  }
}
/deep/ {
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #e2f0fa !important;
  }
}
.name-input {
  ::v-deep {
    .el-form-item__content {
      line-height: 30px !important;
    }
    .el-form-item {
      margin-bottom: 0 !important;
    }
    .el-form-item__label {
      padding-right: 0 !important;
      line-height: 32px !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.stage-action-box {
  display: flex;
  align-items: center;
  .can-next-approval {
    margin-left: 20px;
  }
}
</style>
