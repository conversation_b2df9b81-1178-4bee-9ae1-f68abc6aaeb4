<template>
  <!--部門彈窗-->
  <el-dialog :visible.sync="showDialog" :title="$t('daily.dialog.department')" @open="onOpenDialog">
    <addPage
      v-if="dialogView === 'add'"
      :default-parent="searchFilter.fund_id"
      :edit-parent="addParent"
      class="voucher-dialog-add"
      @onCancel="onCancel"
    />
    <div v-else class="selectPayee">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.dept')">
            <el-select v-model="searchFilter.dept_type_id" style="width: 150px">
              <el-option :label="$t('daily.label.allStaff')" value="" />
              <el-option
                v-for="item in deptTypeList"
                :key="item.dept_type_id"
                :label="language === 'en' ? item.dept_type_en : item.dept_type_cn"
                :value="item.dept_type_id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('daily.label.name')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear(true)">
              {{ $t('button.clear') }}
            </el-button>
            <el-button v-if="hasPermission_Add" size="mini" type="primary" @click="onAdd">
              {{ $t('button.add') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="dialog-table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="400"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="$t('daily.label.no')" property="dept_code" width="150" />
          <el-table-column :label="$t('daily.label.nameCN')" property="dept_name_cn" width="250" />
          <el-table-column :label="$t('daily.label.nameEN')" property="dept_name_en" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import mixinPermission from '@/views/mixins/permission'
import addPage from '@/views/assistance/department/add'

import { fetchDepartmentTypes } from '@/api/assistance/department/departmentType'
import { searchDepartment } from '@/api/assistance/department'

export default {
  name: 'DialogDepartment',
  components: {
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
  },
  data() {
    return {
      p_code: 'ac.assistance.department',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      showDialog: this.dialogVisible,
      dialogView: 'list',
      deptTypeList: [],
      companyList: [],
      tableData: [],
      searchFilter: {
        dept_type_id: '',
        name: '',
      },
      defaultFilter: {
        dept_type_id: '',
        name: '',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    onClear(update) {
      this.searchFilter = Object.assign({}, this.defaultFilter)
      if (update) this.fetchData()
    },
    onOpenDialog() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.onClear()
      fetchDepartmentTypes({ fy_code: this.fy_code })
        .then(res => {
          this.deptTypeList = res
        })
        .then(this.fetchData)
    },
    fetchData() {
      this.tableLoading = true
      searchDepartment({
        fy_code: this.fy_code,
        parent_dept_type_id: this.searchFilter.dept_type_id,
        name: this.searchFilter.name,
      })
        .then(res => {
          this.tableData = res
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    onAdd() {
      // const fund_id = this.searchFilter.fund_id
      // if (fund_id) {
      //   if (this.searchFilter.fund_id_target) {
      //     this.addParent = { fund_id: this.searchFilter.fund_id_target }
      //   }
      this.dialogView = 'add'
      // }
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$emit('selectRow', currentRow)
      this.showDialog = false
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectPayee {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
</style>
