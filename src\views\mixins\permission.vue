<script>
import { mapGetters } from 'vuex'
export default {
  props: {},
  data() {
    return {
      permission_selected: '',
    }
  },
  computed: {
    ...mapGetters(['permissions']),
    /*
      權限內容(逗號分隔):
      Y:使用
      A:新增
      D:刪除
      E:修改
      O:導出
      I:導入
      V:傳票
      L:列表
      C:綜合
      R:收據
      M:標籤
      N:信封
      P:列印
      T:上載
      W:下載
      U:還原
      Z:重排
     */
    hasPermission_Y() {
      return this.hasPermission('Y')
    },
    hasPermission_Add() {
      return this.hasPermission('A')
    },
    hasPermission_Delete() {
      return this.hasPermission('D')
    },
    hasPermission_Edit() {
      return this.hasPermission('E')
    },
    hasPermission_Update() {
      return this.hasPermission('F')
    },
    hasPermission_Output() {
      return this.hasPermission('O')
    },
    hasPermission_Input() {
      return this.hasPermission('I')
    },
    hasPermission_Voucher() {
      return this.hasPermission('V')
    },
    hasPermission_List() {
      return this.hasPermission('L')
    },
    hasPermission_Complex() {
      return this.hasPermission('C')
    },
    hasPermission_Receipt() {
      return this.hasPermission('R')
    },
    hasPermission_M() {
      return this.hasPermission('M')
    },
    hasPermission_N() {
      return this.hasPermission('N')
    },
    hasPermission_Print() {
      return this.hasPermission('P')
    },
    hasPermission_T() {
      return this.hasPermission('T')
    },
    hasPermission_W() {
      return this.hasPermission('W')
    },
    hasPermission_U() {
      return this.hasPermission('U')
    },
    hasPermission_Z() {
      return this.hasPermission('Z')
    },
    hasPermission_Copy() {
      return this.hasPermission('B')
    },
  },
  created() {
    const p_code = this.p_code ? this.p_code : this.$route.meta.p_code
    this.permission_selected =
      this.permissions && this.permissions[p_code] ? this.permissions[p_code].p_selected : ''
    if (!this.p_isComponent && !this.hasPermission_Y) {
      this.$router.push('401')
    }
  },
  methods: {
    hasPermission(p) {
      const p_code = this.p_code ? this.p_code : this.$route.meta.p_code
      console.log(
        p_code,
        this.permission_selected.includes(p) ? this.$t('message.hasPermission') : this.$t('message.noPermission'),
        '【' + this.$t('permission.' + p),
        '】權限',
      )
      return this.permission_selected.includes(p)
    },
    hasPermissionByCode(p_code, p) {
      if (p === undefined) {
        p = 'Y' // 默認使用權限
      }
      const item = this.permissions[p_code]
      if (item) {
        return item.p_selected.includes(p)
      } else {
        return false
      }
    },
  },
}
</script>
