import request from '@/utils/request'

/**
 * 返回新增后的職員id
 * @param {string} username 用戶名
 * @param {string} password 密碼
 * @param {string} st_name_cn 中文名
 * @param {string} st_name_en 英文名
 * @param {string} st_code 職員編號
 * @param {string} st_grade 在預算系統中的職員級別,A=審批,M=負責人,G=成員,S=職員,可以多選,逗號分隔
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {string} role_id 角色id
 * @param {string} other_role_id 角色id
 * @param {integer} parent_st_type_id 父職員類別組id((不傳則為根節點)
 * @param {integer} seq 在組別中的順序
 */
export function createStaff(
  username,
  password,
  st_name_cn,
  st_name_en,
  st_code,
  st_grade,
  active_year,
  role_id,
  other_role_id,
  parent_st_type_id,
  seq,
) {
  return request({
    url: '/staffs/actions/create',
    method: 'post',
    data: {
      username,
      password,
      st_name_cn,
      st_name_en,
      st_code,
      st_grade,
      active_year,
      role_id,
      other_role_id,
      parent_st_type_id,
      seq,
    },
  })
}

/**
 * 修改職員
 * @param {integer} staff_id 職員id
 * @param {string} username 用戶名
 * @param {string} password 密碼
 * @param {string} st_name_cn 中文名
 * @param {string} st_name_en 英文名
 * @param {string} st_code 職員編號
 * @param {string} st_grade 在預算系統中的職員級別,A=審批,M=負責人,G=成員,S=職員,可以多選,逗號分隔
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {string} role_id 角色id
 * @param {string} other_role_id 角色id
 * @param {integer} parent_st_type_id 父職員類別組id((不傳則為根節點)
 * @param {integer} seq 在組別中的順序
 */
export function editStaff(
  staff_id,
  username,
  password,
  st_name_cn,
  st_name_en,
  st_code,
  st_grade,
  active_year,
  role_id,
  other_role_id,
  parent_st_type_id,
  seq,
) {
  return request({
    url: '/staffs/actions/update',
    method: 'post',
    data: {
      staff_id,
      username,
      password,
      st_name_cn,
      st_name_en,
      st_code,
      st_grade,
      active_year,
      role_id,
      other_role_id,
      parent_st_type_id,
      seq,
    },
  })
}

/**
 * 刪除職員
 * @param {integer} staff_id 職員id
 */
export function deleteStaff(staff_id) {
  return request({
    url: '/staffs/actions/delete',
    method: 'post',
    data: {
      staff_id,
    },
  })
}

/**
 * 返回職員樹
 * @param {string} fy_code 選擇的會計週期
 */
export function getStaffsTree(fy_code) {
  return request({
    url: '/staffs/tree',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 返回員層級數
 * @param {string} fy_code 選擇的會計週期
 */
export function staffLevels(fy_code) {
  return request({
    url: '/staffs/levels',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 返回職員樹節點
 * @param {string} fy_code 選擇的會計週期
 * @param {integer} parent_st_type_id 上級職員類別id
 * @param {integer} except_st_type_id 排除職員類別id
 * @param {integer} except_staff_id 排除職員id
 */
export function getStaffsTreeNode(fy_code, parent_st_type_id, except_st_type_id, except_staff_id) {
  return request({
    url: '/staffs/tree/node',
    method: 'get',
    params: {
      fy_code,
      parent_st_type_id,
      except_st_type_id,
      except_staff_id,
    },
  })
}

/**
 * 獲取某職員詳情
 * @param {integer} staff_id 職員id
 */
export function getStaff(staff_id) {
  return request({
    url: '/staffs/actions/inquire',
    method: 'get',
    params: {
      staff_id,
    },
  })
}

/**
 * 返回職員及職員類別excel數據

 */
export function exportStaffs() {
  return request({
    url: '/staffs/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入職員及職員類別excel數據
 * @param {string} data_json 匯入的數據
 */
export function importStaffs(data_json) {
  return request({
    url: '/staffs/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

/**
 * 返回符合條件的職員
 * @param {integer} fy_code 會計週期編號
 * @param {integer} parent_st_type_id 父職員類別id
 * @param {string} name 模糊搜索名字
 * @return {Promise}
 */
export function searchStaffs({ fy_code, parent_st_type_id, name, st_grade }) {
  return request({
    url: '/staffs/actions/search',
    method: 'get',
    params: {
      fy_code,
      parent_st_type_id,
      name,
      st_grade,
    },
  })
}

/**
 * 修改(當前)職員密碼
 * @param {string} channel 指定驗證token通道,可传AC或BG,不傳默認AC
 * @param {string} password 舊密碼
 * @param {string} new_password 新密碼
 * @return {Promise}
 */
export function staffChangePsw({ password, new_password }) {
  return request({
    url: '/staffs/me/actions/change-psw',
    method: 'post',
    data: {
      password,
      new_password,
    },
  })
}

/**
 * 返回職員
 * @param {string} [fy_code] 選擇的會計週期
 * @param {string} [st_grade] A=審批,M=負責人,G=成員,S=職員
 * @return {Promise}
 */
export function fetchStaffs({ fy_code, st_grade }) {
  return request({
    url: '/staffs',
    method: 'get',
    params: {
      fy_code,
      st_grade,
    },
  })
}
