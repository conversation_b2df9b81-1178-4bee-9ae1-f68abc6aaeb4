import Layout from '@/views/layout/Layout'

const budgetRouter = [
  {
    // daily
    path: '/budget/daily',
    component: Layout,
    redirect: 'noredirect',
    name: 'budgetDaily',
    meta: {
      title: 'router.budget.daily',
      p_code: 'bg.daily',
      icon: 'daily',
    },
    children: [
      {
        path: 'budget_list',
        component: () => import('@/views/budget/budgetList/index.vue'),
        name: 'budgetDailyBudgetList',
        needAPrint: 'Y',
        meta: {
          title: 'router.budget.dailyBudgetList',
          p_code: 'bg.daily.budget_list',
        },
      },
      {
        path: 'budget_management',
        component: () => import('@/views/budget/budgetManagement/index.vue'),
        name: 'budgetDailyBudgetManagement',
        meta: {
          title: 'router.budget.dailyBudgetManagement',
          p_code: 'bg.daily.budget_management',
        },
      },
      {
        path: 'enquires',
        component: () => import('@/views/budget/enquires/index.vue'),
        name: 'budgetDailyEnquires',
        meta: {
          title: 'router.budget.dailyEnquires',
          p_code: 'bg.daily.enquires',
        },
      },
    ],
  },
  {
    path: '/budget/system_settings',
    component: Layout,
    redirect: 'noredirect',
    name: 'budgetSystemSettings',
    meta: {
      title: 'router.budget.systemSettings',
      p_code: 'bg.system_settings',
      icon: 'setting',
    },
    children: [
      // {
      //   path: 'fund_setup',
      //   component: () => import('@/views/errorPage/coding'),
      //   name: 'systemSettingsFundSetup',
      //   meta: {
      //     title: 'router.budget.systemSettingsFundSetup',
      //     p_code: 'bg.system_settings.fund_setup'
      //   }
      // },
      {
        path: 'account_input',
        component: () => import('@/views/budget/accountInput/index.vue'),
        name: 'systemSettingsAccountInput',
        meta: {
          title: 'router.budget.systemSettingsAccountInput',
          p_code: 'bg.system_settings.account_input',
        },
      },
      {
        path: 'role_set',
        component: () => import('@/views/assistance/budgetRole/index.vue'),
        name: 'systemSettingsRoleSet',
        meta: {
          title: 'router.budget.systemSettingsRoleSet',
          p_code: 'bg.system_settings.role_set',
        },
      },
      {
        path: 'staff_set',
        component: () => import('@/views/assistance/staff/index.vue'),
        name: 'systemSettingsStaffSet',
        meta: {
          title: 'router.budget.systemSettingsStaffSet',
          p_code: 'bg.system_settings.staff_set',
        },
      },
      {
        path: 'budget_set',
        component: () => import('@/views/budget/budgetSet/index'),
        name: 'systemSettingsBudgetSet',
        meta: {
          title: 'router.budget.systemSettingsBudgetSet',
          p_code: 'bg.system_settings.budget_set',
        },
      },
    ],
  },
  // {
  //   path: '/budget/reports',
  //   component: Layout,
  //   redirect: 'noredirect',
  //   name: 'budgetReports',
  //   meta: {
  //     title: 'router.budget.reports',
  //     p_code: 'bg.reports',
  //     icon: 'report'
  //   },
  //   children: [
  //     {
  //       path: 'budget_list',
  //       component: () => import('@/views/errorPage/coding'),
  //       name: 'reportsBudgetList',
  //       needAPrint: 'Y',
  //       meta: {
  //         title: 'router.budget.reportsBudgetList',
  //         p_code: 'bg.reports.budget_list'
  //       }
  //     },
  //     {
  //       path: 'individual_budget_report',
  //       component: () => import('@/views/errorPage/coding'),
  //       name: 'reportsIndividualBudgetReport',
  //       needAPrint: 'Y',
  //       meta: {
  //         title: 'router.budget.reportsIndividualBudgetReport',
  //         p_code: 'bg.reports.individual_budget_report'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/budget/personal_set',
    component: Layout,
    redirect: 'noredirect',
    name: 'personalSet',
    meta: {
      title: 'router.budget.personalSet',
      p_code: 'bg.personal_set',
      icon: 'user1',
    },
    children: [
      {
        path: 'change_password',
        component: () => import('@/views/setting/userInfo/index.vue'),
        name: 'personalSetChangePassword',
        meta: {
          title: 'router.budget.personalSetChangePassword',
          p_code: 'bg.personal_set.change_password',
        },
      },
      // {
      //   path: 'screen',
      //   component: () => import('@/views/errorPage/coding'),
      //   name: 'personalSetScreen',
      //   meta: {
      //     title: 'router.budget.personalSetScreen',
      //     p_code: 'bg.personal_set.screen'
      //   }
      // },
      {
        path: 'printout',
        component: () => import('@/views/setting/printout/index.vue'),
        name: 'personalSetPrintout',
        meta: {
          title: 'router.budget.personalSetPrintout',
          p_code: 'bg.personal_set.printout',
        },

        redirect: 'printout/printout_redirect', // 重定向
        children: [
          // 定位
          {
            path: 'printout_redirect',
            component: () => import('@/views/setting/printout/redirect.vue'),
            name: 'settingPrintout.printout_redirect',
            meta: {
              title: '',
              p_code: 'bg.personal_set.printout',
              noCache: true,
            },
          },
          // 預算系統 - 日常
          // 預算列表
          {
            path: 'daily_budget_list',
            component: () => import('@/views/setting/printout/page/bgBudgetList.vue'),
            name: 'settingPrintout.daily.pdfbudget',
            meta: {
              title: 'setting.printout.navMenu.bg_budget_list',
              p_code: 'bg.personal_set.printout',
              noCache: true,
            },
          },
        ],
      },
    ],
  },
]

export default budgetRouter
