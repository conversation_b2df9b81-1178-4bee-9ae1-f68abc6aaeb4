import request from '@/utils/request'

/**
 * 修改每日收款
 * @param {integer} receipt_date_json  收據日期json,[{"fy_code":"18","vc_id":"1","vc_receipt_date":"2019-09-04"},...]
 */
export function updateDailyCollections(receipt_date_json) {
  return request({
    url: '/cycle/daily-receipts/actions/update',
    method: 'post',
    data: {
      receipt_date_json,
    },
  })
}

/**
 * 查詢每日收款
 * @param {string} fund_id 賬目類別(大類)id
 * @param {string} begin_date  開始日期
 * @param {string} end_date  結束日期
 */
export function fetchDailyCollections({ fund_id, begin_date, end_date }) {
  return request({
    url: '/cycle/daily-receipts',
    method: 'get',
    params: {
      fund_id,
      begin_date,
      end_date,
    },
  })
}

export default { updateDailyCollections, fetchDailyCollections }
