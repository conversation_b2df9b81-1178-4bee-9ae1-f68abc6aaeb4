<script>
import { amountFormat, toDecimal } from '@/utils'
import dateUtil from '@/utils/date'
import { fetchDepartmentLedgersForPrint } from '@/api/report/department'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'

export default {
  name: 'HandlePDF',
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting

      return new Promise((resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(this.formatPrintData)
          .then(({ schoolInfo, pageInfo, columns, tableData, content }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              printSetting,
              content,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const language = printSetting.language

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const department = this.getSelectedDepartment(
          this.treeData,
          this.preferences.filters.dept_selectGroup,
        )
        const langKey = this.langKey
        const title = this.$t(langKey + 'title', language)
        const departmentName = department
          ? language === 'en'
            ? department.name_en
            : department.name_cn
          : this.$t('filters.all', language)
        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        let dateStr = ''
        if (begin_date && end_date) {
          dateStr =
            dateUtil.format(new Date(begin_date), 'dd/MM/yyyy') +
            ' - ' +
            dateUtil.format(new Date(end_date), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${departmentName} - ${dateStr}`,
          data: [
            {
              label: this.$t('filters.period', language) + ':',
              value: dateStr,
            },
            {
              label: this.$t('filters.dept', language) + ':',
              value: departmentName,
            },
          ],
        }

        // 主數據
        //

        // 表頭
        const columnData = printSetting.columnData
          .filter(i => i.position > 0)
          .sort((a, b) => a.position - b.position)

        // 由於打印的數據格式 跟 頁面格式不一致，需重新獲取
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, '1')
        const lastDateObj = new Date(year, month + 1, '0')

        // let begin_date, end_date
        // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
        //   begin_date = this.dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        //   end_date = this.dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        //   this.onChangeDateRange([begin_date, end_date])
        // } else {
        //   begin_date = this.preferences.filters.date_range[0]
        //   end_date = this.preferences.filters.date_range[1]
        // }
        // if (!begin_date || !end_date) {
        //   // reject()
        //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        // }
        if (!begin_date || !end_date) {
          begin_date = dateUtil.format(beginDateObj, this.dateFormatStr)
          end_date = dateUtil.format(lastDateObj, this.dateFormatStr)
          this.preferences.filters.begin_date = begin_date
          this.preferences.filters.end_date = end_date
        }
        this.onChangeDateRange([begin_date, end_date])
        const parent_dept_type_id = this.preferences.filters.parent_dept_type_id
        const dept_code = this.preferences.filters.dept_selectGroup
          ? ''
          : this.preferences.filters.dept_code
        const BIE = this.preferences.filters.BIE

        // tableData
        fetchDepartmentLedgersForPrint({
          parent_dept_type_id,
          dept_code,
          begin_date,
          end_date,
          BIE,
        })
          .then(res => {
            const { content } = this.generateContent(
              schoolInfo,
              pageInfo,
              columnData,
              begin_date,
              printSetting,
              res,
            )
            resolve({
              schoolInfo,
              pageInfo,
              printSetting,
              content,
            })
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            const content = []
            resolve({
              schoolInfo,
              pageInfo,
              printSetting,
              content,
            })
          })
      })
    },
    getSelectedDepartment(tree, selectGroup) {
      let result
      if (selectGroup === true) {
        tree.forEach(i => {
          if (result !== undefined) {
            return
          }
          if (i.children.length > 0) {
            if (i.dept_type_id === this.preferences.filters.parent_dept_type_id) {
              result = i
            } else {
              result = this.getSelectedDepartment(i.children, selectGroup)
            }
          }
        })
      } else {
        tree.forEach(i => {
          if (result !== undefined) {
            return
          }
          if (i.children !== undefined && i.children.length > 0) {
            result = this.getSelectedDepartment(i.children, selectGroup)
          } else {
            if (i.children === undefined) {
              if (i.code === this.preferences.filters.dept_code) {
                result = i
                return
              }
            }
          }
        })
      }
      return result
    },
    generateDepartmentInfo(groupItem, language, tableColumnsLength) {
      return [
        {
          // Header
          margin: [0, 0, 0, 0],
          colSpan: tableColumnsLength,
          border: [false, false, false, false],
          columns: [
            {
              style: 'tableExample',
              width: '*',
              table: {
                widths: [200, '*'],
                heights: [10, 10],
                headerRows: 1,
                body: [
                  [
                    {
                      text:
                        this.$t(this.langKey + 'vc_dept', language) +
                        ': ' +
                        (groupItem[0].vc_dept === null ? '' : groupItem[0].vc_dept),
                      alignment: 'left',
                      bold: false,
                      border: [false, false, false, false],
                    },
                    {
                      text:
                        this.$t(this.langKey + 'dept_name_', language) +
                        ': ' +
                        (language === 'en'
                          ? groupItem[0].dept_name_en === null
                            ? ''
                            : groupItem[0].dept_name_en
                          : groupItem[0].dept_name_cn === null
                            ? ''
                            : groupItem[0].dept_name_cn),
                      alignment: 'left',
                      bold: false,
                      border: [false, false, false, false],
                    },
                  ],
                ],
              },
              layout: 'noBorders',
            },
          ],
        },
        ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
          text: '',
          border: [false, false, false, false],
        })),
      ]
    },
    generateContentObject(groupIndex, widths, pageHeader, columns, departmentInfo, tableData) {
      return [
        {
          // Content
          headlineLevel: groupIndex > 0 ? 1 : 0,
          pageBreak: groupIndex > 0 ? 'before' : undefined,
          width: '100%',
          style: 'tableExample',
          table: {
            dontBreakRows: true,
            keepWithHeaderRows: 1,
            // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
            widths: widths,
            // heights: tableData.map((e, i) => i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto'),
            headerRows: 3, // columns.length + 1,
            body: [
              pageHeader, // 頁頭
              departmentInfo, // 職員信息
              columns, // 數據表頭
              ...tableData, // 數據
            ],
          },
          layout: {
            vLineWidth: lineWidth,
            hLineWidth: lineWidth,
            hLineColor: lineColor,
            vLineColor: lineColor,
          },
        },
      ]
    },
    generateContent(schoolInfo, pageInfo, columnData, beginDateObj, printSetting, data) {
      const language = printSetting.language

      const margin_right = mm2pt(printSetting.margin_right)
      const title_width = mm2pt(printSetting.title_width)

      const mergeRows = ['vc_no', 'vc_date', 'vc_dept', 'dept_name_']

      const content = []
      data.forEach((groupItem, groupIndex) => {
        if (
          printSetting.blank_ledger !== '1' &&
          (groupItem[0].vc_dept === null || groupItem[0].vc_dept === '')
        ) {
          return
        }

        const borderAll = [true, true, true, true]
        // const borderBottom = [false, false, false, true]
        const borderTop = [false, true, false, false]
        const borderX = [false, true, false, true]
        const borderNone = [false, false, false, false]
        const columns = []

        columnData.forEach(col => {
          columns.push({
            text: this.$t(this.langKey + col.name, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: col.alignment,
            width: col.width,
            // border: [true, true, true, true],
            border: borderAll,
          })
        })

        // 學校信息
        const schoolTable = generateSchoolInfo(
          schoolInfo.name_cn,
          schoolInfo.name_en,
          schoolInfo.logo,
        )
        // 頁面信息
        const pageTable = generatePageInfo(
          pageInfo.title,
          pageInfo.filename,
          pageInfo.data,
          title_width,
          margin_right,
        )

        // 頁頭，包含LOGO，頁面信息
        const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

        // 表格寬度
        const widths = generateWidths(columns)

        const tableData = []
        const sumRow = []
        let prevItem = {}

        // 初始化行
        columnData.forEach((col, colIndex) => {
          if (col.position === 0) {
            // 即不顯示列
            return
          }
          switch (col.name) {
            case 'amount_dr':
            case 'amount_cr':
            case 'amount_net':
            case 'amount_balance': {
              sumRow.push({
                name: col.name,
                text: '0.00',
                value: 0,
                style: 'tableFooter',
                border: borderX,
              })
              break
            }
            default: {
              sumRow.push({ name: col.name, text: '', style: 'tableFooter', border: borderX })
            }
          }
        })

        let sumIndex = 0
        groupItem.forEach((item, rowIndex) => {
          const row = []
          sumIndex = 0
          // const border = [true, true, true, true]
          columnData.forEach((col, colIndex) => {
            if (col.position === 0) {
              // 即不顯示列
              return
            }

            const isNewVoucher = prevItem.vc_no !== item.vc_no
            const border = isNewVoucher ? borderTop : borderNone
            const cell = {
              name: col.name,
              text: '',
              alignment: col.alignment,
              style: 'tableContent',
              border,
            }
            if (!isNewVoucher && mergeRows.includes(col.name)) {
              row.push(cell)
              sumIndex++
              return
            }
            switch (col.name) {
              case 'descr':
              case 'break_down':
              case 'dept_name_': {
                // if (isNewVoucher) {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = language === 'en' ? key + 'en' : key + 'cn'
                }
                const text = item[key]
                cell.text = text
                // }
                // row.push({
                //   text: text,
                //   alignment: col.alignment,
                //   style: 'tableContent',
                //   width: col.width,
                //   border: border
                // })
                break
              }
              case 'vc_no':
              case 'vc_dept':
                // if (isNewVoucher) {
                cell.text = item[col.name]
                // }
                break
              case 'vc_date': {
                if (item.vc_date !== null) {
                  const vc_date = new Date(item.vc_date)

                  cell.text = dateUtil.format(new Date(vc_date), 'dd/MM/yyyy')
                  // row.push({
                  //   text: dateStr,
                  //   alignment: col.alignment,
                  //   style: 'tableContent',
                  //   width: col.width,
                  //   border: border
                  // })
                } else {
                  // row.push({
                  //   text: '',
                  //   alignment: col.alignment,
                  //   style: 'tableContent',
                  //   width: col.width,
                  //   border: border
                  // })
                }
                break
              }
              case 'amount_dr':
              case 'amount_cr': {
                let v = Number(item[col.name])
                isNaN(v) && (v = 0)

                sumRow[sumIndex].alignment = col.alignment
                sumRow[sumIndex].value = toDecimal(Number(sumRow[sumIndex].value) + v)
                sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)

                cell.text = amountFormat(v)
                break
              }
              case 'amount_net': {
                let v = toDecimal(Number(item['amount_dr']) - Number(item['amount_cr']))
                isNaN(v) && (v = 0)

                sumRow[sumIndex].alignment = col.alignment
                sumRow[sumIndex].value = toDecimal(Number(sumRow[sumIndex].value) + v)
                sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)

                cell.text = amountFormat(v)
                break
              }
              case 'amount_balance': {
                let v = Number(item[col.name])
                isNaN(v) && (v = 0)

                sumRow[sumIndex].alignment = col.alignment
                sumRow[sumIndex].value = v
                sumRow[sumIndex].text = amountFormat(sumRow[sumIndex].value)

                cell.text =
                  v >= 0 ? amountFormat(v) + ' Dr' : amountFormat(toDecimal(v * -1)) + ' Cr'
                break
              }
              case 'tx_num':
                cell.text = Number(item.tx_num) + 1
                break
              default: {
                let key = col.name
                if (key[key.length - 1] === '_') {
                  key = language === 'en' ? key + 'en' : key + 'cn'
                }
                const v = item[key]
                cell.text = v === undefined ? '' : v
              }
            }
            row.push(cell)
            sumIndex++
          })
          prevItem = item
          tableData.push(row)
        })
        if (sumRow.length > 0) {
          const sumPosition = sumRow.findIndex(i => i.name === 'descr')
          if (sumPosition > 0) {
            sumRow[0].text = this.$t('print.total', language)
            sumRow[0].colSpan = sumPosition
            sumRow[0].alignment = 'right'
            sumRow[0].bold = true
            sumRow[0].style = 'tableFooter'
          }

          const sumDrPosition = sumRow.findIndex(i => i.name === 'amount_dr')
          const sumCrPosition = sumRow.findIndex(i => i.name === 'amount_cr')
          const sumNetPosition = sumRow.findIndex(i => i.name === 'amount_net')
          const sumBalPosition = sumRow.findIndex(i => i.name === 'amount_balance')

          if (sumDrPosition !== -1) {
            sumRow[sumDrPosition].style = 'tableFooter'
            sumRow[sumDrPosition].alignment = columnData[sumDrPosition].alignment
          }
          if (sumCrPosition !== -1) {
            sumRow[sumCrPosition].style = 'tableFooter'
            sumRow[sumCrPosition].alignment = columnData[sumCrPosition].alignment
          }
          if (sumNetPosition !== -1) {
            sumRow[sumNetPosition].style = 'tableFooter'
            sumRow[sumNetPosition].alignment = columnData[sumNetPosition].alignment
          }
          if (sumBalPosition !== -1) {
            sumRow[sumBalPosition].style = 'tableFooter'
            sumRow[sumBalPosition].text =
              Number(sumRow[sumBalPosition].value) >= 0
                ? amountFormat(sumRow[sumBalPosition].value) + ' Dr'
                : amountFormat(toDecimal(sumRow[sumBalPosition].value * -1)) + ' Cr'
            sumRow[sumBalPosition].alignment = columnData[sumBalPosition].alignment
          }

          tableData.push(sumRow)
        }
        const departmentInfo = this.generateDepartmentInfo(groupItem, language, columns.length)

        content.push(
          this.generateContentObject(
            groupIndex,
            widths,
            pageHeader,
            columns,
            departmentInfo,
            tableData,
          ),
        )

        if (printSetting.sign_style.toString() === '1') {
          // 浮動
          const sign_height = mm2pt(printSetting.sign_height)
          const sign_space = mm2pt(printSetting.sign_space)
          const margin_left = mm2pt(printSetting.margin_left)
          const bottom_sign = printSetting.sign_style.toString() === '2'

          // 簽名設置
          const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
          const signTable = generateSign(
            printSetting.sign_line,
            signColumn,
            printSetting.language,
            sign_height,
            sign_space,
            margin_left,
            margin_right,
            printSetting.font_size_signature,
            bottom_sign,
          )

          content.push(signTable)
        }
      })
      return { content }
    },
    async showPDF({ schoolInfo, pageInfo, printSetting, content }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: content,
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        pageBreakBefore: function(
          currentNode,
          followingNodesOnPage,
          nodesOnNextPage,
          previousNodesOnPage,
        ) {
          return currentNode.headlineLevel === 1
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      // console.log(JSON.stringify(docDefinition))
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      await openPdf(docDefinition)
    },
  },
}
</script>
