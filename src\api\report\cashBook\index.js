import request from '@/utils/request'

/**
 * 返回現金/銀行賬簿報表數據
 * @param {string} ac_code 銀行會計賬目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function fetchCashOrBankLedgers({ ac_code, begin_date, end_date }) {
  return request({
    url: '/reports/accounts/cash-ledgers',
    method: 'get',
    params: {
      ac_code,
      begin_date,
      end_date,
    },
  })
}

export default {
  fetchCashOrBankLedgers,
}
