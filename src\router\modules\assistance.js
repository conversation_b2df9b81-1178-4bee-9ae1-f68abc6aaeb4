import Layout from '@/views/layout/Layout'

const assistanceRouter = {
  path: '/assistance',
  component: Layout,
  redirect: 'noredirect',
  name: 'assistance',
  meta: {
    title: 'router.assistance',
    p_code: 'ac.assistance',
    icon: 'auxiliaryFiles',
  },
  children: [
    {
      path: 'budget',
      component: () => import('@/views/budget/budgetSet/index.vue'),
      name: 'assistanceBudget',
      meta: {
        title: 'router.assistanceBudget',
        p_code: 'ac.assistance.budget',
        noCache: true,
      },
    },
    {
      path: 'description',
      component: () => import('@/views/assistance/description/index.vue'),
      name: 'assistanceDescription',
      meta: {
        title: 'router.assistanceDescription',
        p_code: 'ac.assistance.description',
        noCache: true,
      },
    },
    {
      path: 'payee_payer',
      component: () => import('@/views/assistance/payeePayer/index.vue'),
      name: 'assistancePayeePayer',
      meta: {
        title: 'router.assistancePayeePayer',
        p_code: 'ac.assistance.payee_payer',
        noCache: true,
      },
    },
    {
      path: 'staff',
      component: () => import('@/views/assistance/staff/index.vue'),
      name: 'assistanceStaff',
      meta: {
        title: 'router.assistanceStaff',
        p_code: 'ac.assistance.staff',
        noCache: true,
      },
    },
    {
      path: 'department',
      component: () => import('@/views/assistance/department/index.vue'),
      name: 'assistanceDepartment',
      meta: {
        title: 'router.assistanceDepartment',
        p_code: 'ac.assistance.department',
        noCache: true,
      },
    },
    {
      path: 'contra_code',
      component: () => import('@/views/assistance/contraCode/index.vue'),
      name: 'assistanceContraCode',
      meta: {
        title: 'router.assistanceContraCode',
        p_code: 'ac.assistance.contra_code',
        noCache: true,
      },
    },
    {
      path: 'cheque_book',
      component: () => import('@/views/assistance/chequeBook/index.vue'),
      name: 'assistanceChequeBook',
      meta: {
        title: 'router.assistanceChequeBook',
        p_code: 'ac.assistance.cheque_book',
        noCache: true,
      },
    },
    {
      path: 'budget_role',
      component: () => import('@/views/assistance/budgetRole/index.vue'),
      name: 'assistanceBudgetRole',
      meta: {
        title: 'router.assistanceBudgetRole',
        p_code: 'ac.assistance.budget_role',
        noCache: true,
      },
    },
    {
      path: 'ledger_budget',
      component: () => import('@/views/assistance/ledgerBudget/index.vue'),
      name: 'assistanceLedgerBudget',
      meta: {
        title: 'router.assistanceLedgerBudget',
        p_code: 'ac.assistance.ledger_budget',
        noCache: true,
      },
    },
    {
      path: 'number_of_school',
      component: () => import('@/views/assistance/numberOfSchool/index.vue'),
      name: 'assistanceNumberOfSchool',
      meta: {
        title: 'router.assistanceNumberOfSchool',
        p_code: 'ac.assistance.number_of_school',
        noCache: true,
      },
    },
    {
      path: 'fund-summary-types-relation',
      component: () => import('@/views/assistance/fundSummaryTypesRelation/index.vue'),
      name: 'assistanceFundSummaryTypesRelation',
      meta: {
        title: 'router.assistanceFundSummaryTypesRelation',
        p_code: 'ac.assistance.fund_summary_types_relation',
        noCache: true,
      },
    },
    {
      path: 'edb',
      component: () => import('@/views/assistance/norrayEDBRelation/index.vue'),
      name: 'assistanceNorrayEDBRelation',
      meta: {
        title: 'router.assistanceNorrayEDBRelation',
        p_code: 'ac.assistance.edb',
        noCache: true,
      },
    },
    {
      path: 'edb_secondary_school',
      component: () => import('@/views/report/EDBRelation/index.vue'),
      name: 'edbBySecondarySchool',
      meta: {
        title: 'router.assistanceEDBRelationBySecondarySchool',
        p_code: 'ac.assistance.edb_secondary_school',
        noCache: true,
      },
    },
    {
      path: 'fix-wrong-voucher',
      component: () => import('@/views/assistance/fixWrongVoucher/index.vue'),
      name: 'assistanceFixWrongVoucher',
      meta: {
        title: 'router.assistanceFixWrongVoucher',
        p_code: 'ac.assistance.fix_wrong_voucher',
        noCache: true,
      },
    },
    {
      path: 'entrySummary',
      component: () => import('@/views/assistance/entrySummary/index.vue'),
      name: 'assistanceEntrySummary',
      meta: {
        title: 'router.assistanceEntrySummary',
        p_code: 'ac.assistance.entry_summary',
        noCache: true,
      },
    },
  ],
}
// console.log(store.getters)
// debugger
// console.log(store.getters.user_type)
// debugger
// if (store.getters.user_type === 'S') {
//   assistanceRouter.children.append({
//     path: 'fund-summary-types-relation',
//     component: () => import('@/views/errorPage/coding/index.vue'),
//     name: 'assistanceFundSummaryTypesRelation',
//     meta: {
//       title: 'router.assistanceLedgerBudget',
//       p_code: '*',
//       noCache: true
//     }
//   })
// }

export default assistanceRouter
