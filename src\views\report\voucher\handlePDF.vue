<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
import dateUtil from '@/utils/date'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
import dayjs from 'dayjs'

export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  data() {
    return {
      // ps_code: 'pdftrialbalance'
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo', 'school', 'currentDate']),
  },
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      return new Promise(async(resolve, reject) => {
        this.loadPrintoutSetting()
          .then(ps => {
            printSetting = ps
            return ps
          })
          .then(() => this.formatPrintData(printSetting))
          .then(({ schoolInfo, pageInfo, columns, tableData }) => {
            this.$store.commit('setPrintList', {
              name: pageInfo.filename,
              status: 0,
              startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              finishTime: '',
              url: '',
            })
            this.showPDF({
              schoolInfo,
              pageInfo,
              columns,
              tableData,
              printSetting,
            }).then(() => {
              resolve()
            })
          })
          .catch(err => {
            reject(err)
            console.error('onPrint', err)
          })
      })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const filters = this.currentFilter
        const language = printSetting.language
        const langKey = 'report.voucher.label.'

        const columnData = printSetting.columnData
          .filter(i => i.position)
          .sort((a, b) => a.position - b.position)

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }
        const categoryKey = [
          { value: '', key: 'allCategory' },
          { value: 'P', key: 'paymentVoucher' },
          { value: 'C', key: 'cashVoucher' },
          { value: 'R', key: 'receiptVoucher' },
          { value: 'A', key: 'ap' },
          { value: 'T', key: 'transferVoucher' },
          { value: 'J', key: 'journalVoucher' },
        ]

        const fund = this.funds.find(i => i.fund_id === filters.fund_id)
        const fundName = fund
          ? language === 'en'
            ? fund.fund_name_en
            : fund.fund_name_cn
          : this.$t('report.voucher.content.allFund', language)
        const category = categoryKey.find(i => i.value === filters.vt_category)
        const categoryName = this.$t(
          'report.voucher.content.' + (category ? category.key : categoryKey[0].key),
          language,
        )
        const title = this.$t(langKey + 'title', language)
        const begin_date = filters.begin_date
        const end_date = filters.end_date
        let dateStr = ''
        if (begin_date && end_date) {
          dateStr =
            dateUtil.format(new Date(begin_date), 'dd/MM/yyyy') +
            ' - ' +
            dateUtil.format(new Date(end_date), 'dd/MM/yyyy')
        }
        // 右邊表格
        const pageInfo = {
          title: title,
          filename: `${title} - ${fundName} - ${categoryName} - ${dateStr}`,
          data: [
            {
              label: this.$t('report.voucher.label.category', language) + ':',
              value: fundName,
            },
            {
              label: this.$t('report.voucher.label.voucherType', language) + ':',
              value: categoryName,
            },
            {
              label: this.$t('filters.period', language) + ':',
              value: dateStr,
            },
          ],
        }

        // 表頭
        const columns = []
        columnData.forEach(item => {
          const key = item.name
          columns.push({
            text: this.$t(langKey + key, language),
            style: 'tableHeader',
            rowSpan: 1,
            alignment: item.alignment,
            width: item.width,
            margin: [5, 0, 5, 0],
            border: [false, true, false, true],
          })
        })

        // 主數據
        //

        // tableData
        const tableData = []
        let preRow = {}

        this.tableData.forEach((item, rowIndex) => {
          if (rowIndex !== 0 && item.vc_no) {
            // 分割
            tableData.push(columnData.map(i => ({ text: '', margin: [0, 10, 0, 0] })))
          }

          const row = []
          columnData.forEach((col, colIndex) => {
            const cell = {
              margin: [5, 0, 5, 0],
              text: '',
              alignment: col.alignment,
              style: 'tableContent',
            }
            switch (col.name) {
              case 'amount_dr':
              case 'amount_cr': {
                let v = Number(item[col.name])
                isNaN(v) && (v = 0)
                if (v || item.sumRow) {
                  cell.text = amountFormat(v)
                }
                if (item.sumRow) {
                  preRow[colIndex].border = [false, false, false, true]
                  cell.border = [false, false, false, true]
                }
                break
              }
              case 'ac_name_':
              case 'st_name_':
                cell.text = item[col.name + (language === 'en' ? 'en' : 'cn')]
                break
              case 'vc_date':
                {
                  const v = item[col.name]
                  if (v) {
                    cell.text = dateUtil.format(new Date(v), 'dd/MM/yyyy')
                  }
                }
                break
              default: {
                cell.text = item[col.name]
              }
            }
            row.push(cell)
          })
          preRow = row
          tableData.push(row)
        })

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'
      // 表格寬度
      const widths = generateWidths(columns)

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns.length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              heights: tableData.map((e, i) =>
                i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto',
              ),
              headerRows: 2,
              body: [
                pageHeader, // 頁頭
                columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              defaultBorder: false,
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: Number(printSetting.font_size_content),
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_cn),
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: Number(printSetting.font_size_school_name_en),
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: Number(printSetting.font_size_title),
          },
          tableContent: {
            bold: false,
            fontSize: Number(printSetting.font_size_content),
          },
          signCell: {
            fontSize: Number(printSetting.font_size_signature),
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      console.log(JSON.stringify(docDefinition))
      docDefinition.printListIndex = this.$store.state.printList.printList.length - 1

      await openPdf(docDefinition)
    },
  },
}
</script>
