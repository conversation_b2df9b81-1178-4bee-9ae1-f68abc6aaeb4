<script>
export default {
  props: {
    pageHadChange: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },
  data() {
    return {
      hadChange: false,
    }
  },
  computed: {},
  created() {
    this.hadChange = this.pageHadChange
  },
  updated() {
    this.$nextTick(() => {
      this.hadChange = true
      this.$emit('update:pageHadChange', true)
    })
  },
  methods: {
    updated() {
      this.$nextTick(() => {
        this.hadChange = true
        this.$emit('update:pageHadChange', true)
      })
    },
    loadingCompleted() {
      this.$nextTick(() => {
        this.hadChange = false
        this.$emit('update:pageHadChange', false)
      })
    },
    checkOut() {
      return new Promise((resolve, reject) => {
        if (this.hadChange) {
          this.$confirm(this.$t('message.leaveSave'), this.$t('message.tips'), {
            confirmButtonText: this.$t('button.confirm'),
            cancelButtonText: this.$t('button.cancel'),
            type: 'warning',
          })
            .then(() => {
              resolve()
            })
            .catch(() => {
              reject()
            })
        } else {
          resolve()
        }
      })
    },
  },
}
</script>
