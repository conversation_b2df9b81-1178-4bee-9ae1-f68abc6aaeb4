<template>
  <div class="budget-management-left">
    <div v-show="!fold" class="filter">
      <el-form ref="form" :inline="true" class="mini-form">
        <!-- ---------- 年份 ---------- -->
        <el-form-item :label="$t('filters.years')">
          <el-select v-model="currentYearCode" style="width: 100px" @change="onChangeYear">
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
        </el-form-item>
        <!-- ---------- 預算 ---------- -->
        <el-form-item :label="$t(langKey + 'budget')" prop="parent_budget_id">
          <!--          <el-select-->
          <!--            v-model="parent_budget_id"-->
          <!--            :placeholder="$t('placeholder.select')"-->
          <!--            @change="onChangeBudgetGroup"-->
          <!--          >-->
          <!--            <el-option-->
          <!--              v-for="item in groupList"-->
          <!--              :key="item.budget_id"-->
          <!--              :value="item.budget_id"-->
          <!--              :disabled="item.budget_type === 'F'"-->
          <!--              :label="conversionTreeOption(item)"-->
          <!--              v-html="conversionTreeOption(item, true)"/>-->
          <!--          </el-select>-->
          <BudgetSelectTree
            :data="budgetGroupList"
            :budget-id.sync="parent_budget_id"
            :language="language"
            style="width: 300px"
            @change="onChangeBudgetGroup"
          />
        </el-form-item>
        <el-form-item class="left-title">
          <i
            v-if="!fold"
            class="edac-icon edac-icon-shouqi action-icon"
            style="
              display: inline;
              font-size: 18px;
              color: #409eff;
              vertical-align: middle;
              line-height: 25px;
            "
            @click="onFold"
          />
          <i
            v-if="fold"
            class="edac-icon edac-icon-zhankai action-icon"
            style="
              display: inline;
              font-size: 18px;
              color: #409eff;
              vertical-align: middle;
              line-height: 25px;
            "
            @click="onUnfold"
          />
          <div style="display: inline; color: #606266">
            {{ $t(langKey + 'lastYear') }}
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="!fold" :style="tableHeight" class="left-table">
      <el-table
        ref="dataTable"
        :data="data"
        :row-class-name="isStripe"
        :show-summary="false"
        :summary-method="getSummaries"
        height="100%"
        class="data-table"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column
          :label="$t(langKey + 'lastYear')"
          :resizable="false"
          :width="columnWidths.icon"
          align="left"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <div>
              <span style="display: inline-block; font-size: 16px">{{
                '&nbsp;'.repeat((scope.row.level - 1) * 6)
              }}</span>
              <span v-if="scope.row.budget_type === 'C'" style="display: inline-block">
                <i class="el-icon-caret-bottom" style="line-height: 16px" />
                <i
                  :class="{
                    activate: scope.row.budget_active,
                  }"
                  class="edac-icon edac-icon-folder"
                />
              </span>
              <span v-else-if="scope.row.budget_type === 'D'" style="display: inline-block">
                <i
                  :class="{
                    activate: scope.row.budget_active,
                  }"
                  class="edac-icon edac-icon-file"
                />
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :resizable="false"
          :label="$t(langKey + 'budget_code')"
          :width="columnWidths.budget_code"
          header-align="center"
          align="left"
          prop="budget_code"
        />
        <el-table-column
          :resizable="false"
          :label="$t(langKey + 'name_')"
          :prop="languageKey('name_')"
          header-align="center"
          align="left"
          min-width="120"
        />
        <el-table-column
          :resizable="false"
          :label="$t(langKey + 'budget_IE')"
          :width="columnWidths.budget_IE"
          prop="budget_IE"
          header-align="center"
          align="center"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">I</span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">E</span>
          </template>
        </el-table-column>
        <el-table-column
          :resizable="false"
          :label="$t(langKey + 'budget_amount')"
          :width="columnWidths.budget_amount"
          prop="budget_amount"
          header-align="center"
          align="right"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_budget_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_budget_amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :resizable="false"
          :label="$t(langKey + 'actual_amount')"
          :width="columnWidths.actual_amount"
          prop="actual_amount"
          header-align="center"
          align="right"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_actual_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_actual_amount) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        ref="summaryTable"
        :data="[summary]"
        :row-class-name="isStripe"
        :show-header="false"
        :span-method="summarySpanMethod"
        :width="columnWidths.icon"
        class="summary-table"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column :width="columnWidths.icon" align="right">
          <template v-if="scope && scope.row" slot-scope="scope">
            {{ $t('budget.budgetList.label.total') }}
          </template>
        </el-table-column>
        <el-table-column :width="columnWidths.budget_code" />
        <el-table-column min-width="120" />
        <el-table-column
          :label="$t(langKey + 'budget_IE')"
          :width="columnWidths.budget_IE"
          prop="budget_IE"
          header-align="center"
          align="center"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">I</span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">E</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'budget_amount')"
          :width="columnWidths.budget_amount"
          prop="budget_amount"
          header-align="center"
          align="right"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_budget_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_budget_amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t(langKey + 'actual_amount')"
          :width="columnWidths.actual_amount"
          prop="actual_amount"
          header-align="center"
          align="right"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="'IB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.I_actual_amount) }}
            </span>
            <span v-if="'EB'.includes(scope.row.budget_IE)">
              {{ amountFormat(scope.row.E_actual_amount) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-show="fold">
      <div style="display: inline">
        {{ $t(langKey + 'lastYear') }}
      </div>
      <i
        class="edac-icon edac-icon-zhankai action-icon"
        style="
          display: inline;
          font-size: 18px;
          color: #409eff;
          vertical-align: middle;
          line-height: 25px;
        "
        @click="onUnfold"
      />
      <br>
      <br>
      <div class="filter">
        <el-form :inline="true" class="mini-form">
          <!-- ---------- 年份 ---------- -->
          <el-form-item :label="$t('filters.years')" style="padding: 0; margin: 0">
            <el-select v-model="currentYearCode" style="width: 100%" @change="onChangeYear">
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_code"
              />
            </el-select>
          </el-form-item>
          <!-- ---------- 預算 ---------- -->
          <el-form-item :label="'預算'" prop="parent_budget_id" style="padding: 0; margin: 0">
            <!--          <el-select-->
            <!--            v-model="parent_budget_id"-->
            <!--            :placeholder="$t('placeholder.select')"-->
            <!--            @change="onChangeBudgetGroup"-->
            <!--          >-->
            <!--            <el-option-->
            <!--              v-for="item in groupList"-->
            <!--              :key="item.budget_id"-->
            <!--              :value="item.budget_id"-->
            <!--              :disabled="item.budget_type === 'F'"-->
            <!--              :label="conversionTreeOption(item)"-->
            <!--              v-html="conversionTreeOption(item, true)"/>-->
            <!--          </el-select>-->
            <BudgetSelectTree
              :data="budgetGroupList"
              :budget-id.sync="parent_budget_id"
              :language="language"
              style="width: 100%"
              @change="onChangeBudgetGroup"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { amountFormat, convertTreeToList } from '@/utils'
import BudgetSelectTree from './../common/BudgetSelectTreeVue'
import { cancelScrollBarSync, scrollBarSync } from '@/views/budget/common/utils'
import { listenTo } from '@/utils/resizeListen'

export default {
  name: 'BudgetManagementLeft',
  components: {
    BudgetSelectTree,
  },
  props: {
    years: {
      type: Array,
      default: () => [],
    },
    fyCode: {
      type: String,
      default: '',
    },
    budgetId: {
      type: [String, Number],
      default: '',
    },
    budgetGroupList: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
    summary: {
      type: Object,
      default: () => ({}),
    },
    fold: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      langKey: 'budget.budgetManagement.label.',
      dataTableWrapper: {},
      summaryTableWrapper: {},
      columnWidths: {
        icon: 120,
        budget_code: 120,
        name_: 150,
        budget_IE: 50,
        budget_amount: 100,
        actual_amount: 100,
      },
      resizeListen: {},
      infoHeight: 40,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    currentYearCode: {
      get() {
        return this.fyCode
      },
      set(val) {
        this.$emit('update:fyCode', val)
      },
    },
    parent_budget_id: {
      get() {
        return this.budgetId
      },
      set(val) {
        this.$emit('update:budgetId', val)
      },
    },
    groupList: {
      get() {
        return convertTreeToList(this.budgetGroupList, 1)
      },
    },
    tableHeight() {
      // return `height: calc(100% - ${this.summary.budget_IE === 'B' ? 82 : 60}px);`
      return `height: calc(100% - ${this.infoHeight}px - 20px);`
    },
  },
  created() {},
  mounted() {
    this.dataTableWrapper = this.$refs['dataTable'].$el.querySelector('.el-table__body-wrapper')
    this.summaryTableWrapper =
      this.$refs['summaryTable'].$el.querySelector('.el-table__body-wrapper')
    // scrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    scrollBarSync(this.dataTableWrapper, this.summaryTableWrapper)

    this.$nextTick(() => {
      this.resizeListen = listenTo(this.$refs.dataTable.$el, ({ width, height, ele }) => {
        this.infoHeight = ele.offsetTop
      })
      this.setSummaryTableWidth()
    })
    this.resizeListen = listenTo(
      this.summaryTableWrapper.querySelector('table'),
      ({ width, height, ele }) => {
        this.setSummaryTableWidth()
      },
    )
  },

  beforeDestroy() {
    // cancelScrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    cancelScrollBarSync(this.dataTableWrapper, this.summaryTableWrapper)
  },
  methods: {
    setSummaryTableWidth() {
      // this.$nextTick(() => {
      const table = this.summaryTableWrapper.querySelector('table')
      const maxWidth = Number(table.style.width.replace('px'))
      const cols = this.summaryTableWrapper.querySelectorAll('table colgroup col')
      let n = 0
      cols.forEach((c, i) => {
        if (i < cols.length - 1) n += Number(c.width)
      })
      cols[0].style.setProperty('width', '110px', 'important')
      // cols[0].width = 110 // Number(cols[0].width) - 10
      cols[cols.length - 1].width = maxWidth - n + 10 // Number(cols[cols.length - 1].width) + 10
      // cols[cols.length - 1].style.width = '510px!important'
      cols[cols.length - 1].style.setProperty('width', '510px', 'important')
      // })
    },
    isStripe() {},
    initData() {},
    conversionTreeOption(item, html, level = 1) {
      let text = this.language === 'en' ? item.name_en : item.name_cn
      if (html) {
        text = '&nbsp;'.repeat((item.level - 1 - level + 1) * 4) + text
      }
      return text
    },
    onChangeBudgetGroup(val) {
      this.$emit('changeBudgetGroup', val)
    },
    languageKey(key) {
      return key + (this.language === 'en' ? 'en' : 'cn')
    },
    getSummaries({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('budget.budgetManagement.label.total')
          return
        } else if (index === 1) {
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          // sums[index] = 'N/A'
        }
      })

      return sums
    },

    amountFormat: amountFormat,
    onChangeYear(val) {
      this.$emit('changeYear', val)
    },
    summarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3,
        }
      } else if (columnIndex < 3) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    onFold() {
      this.$emit('onFold')
    },
    onUnfold() {
      this.$emit('onUnfold')
    },
  },
}
</script>

<style lang="scss" scoped>
.budget-management-left {
  padding: 10px;
  height: 100%;

  .left-title {
    float: right;
    line-height: 25px;
  }
  .left-table {
    /*height: calc(100% - 80px);*/
    /*padding-bottom: 30px;*/
    /deep/ {
      .cell {
        span {
          display: block;
        }
      }
      .data-table.el-table {
        height: 100%;
        width: 100%;
        .cell {
          i {
            line-height: 16px;
            vertical-align: middle;
          }
        }
        .el-table__body-wrapper {
          overflow: auto;
        }
      }
      .summary-table.el-table {
        height: auto;
        overflow-y: scroll;
        &::-webkit-scrollbar {
          display: none;
        }
        .el-table__body-wrapper {
          overflow: hidden;
        }
      }
    }
  }
}
</style>
