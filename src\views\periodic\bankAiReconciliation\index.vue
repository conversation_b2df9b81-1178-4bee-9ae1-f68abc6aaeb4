<template>
  <!-- 篩選 -->
  <div ref="page" v-loading="loading" class="app-container">
    <div style="height: 100%">
      <VBreadCrumb class="breadcrumb" />
      <div ref="filters" class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 銀行 -->
          <el-form-item :label="$t('filters.bank')">
            <el-select
              v-model="preferences.filters.selectedAcCode"
              class="bank"
              style="width: 250px"
            >
              <el-option
                v-for="item in bankList"
                :key="item.ac_code"
                :label="language === 'en' ? item.ac_name_en : item.ac_name_cn"
                :value="item.ac_code"
              />
            </el-select>
          </el-form-item>

          <!-- 時期 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              :value-format="dateValueFormat"-->
            <!--              type="daterange"-->
            <!--              style="width: 250px"-->
            <!--              @change="onChangeDateRange"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>

          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <!--              @change="onChangeYear"-->
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <!--              @change="onChangeMonth"-->
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <!-- 支票號碼 -->
          <el-form-item :label="$t('filters.chequeCode')">
            <el-input v-model="preferences.filters.ref" />
          </el-form-item>
          <!-- checkBox -->
          <el-form-item>
            <el-checkbox v-model="preferences.filters.vc_rstatus" @change="reloadData">
              {{ $t('filters.isMatching') }}
            </el-checkbox>
          </el-form-item>
          <!-- btn -->
          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" type="primary" @click="onAutoSelectHedging">
              {{ t('dataHedging') }}
            </el-button>
            <el-button
              :disabled="!lastAutoSelectData.length"
              size="mini"
              type="primary"
              @click="onUndo"
            >
              {{ t('undo') }}{{ lastAutoSelectData.length ? `(${lastAutoSelectData.length})` : '' }}
            </el-button>
            <el-button size="mini" type="primary" @click="onClearAll">
              {{ t('clearAll') }}
            </el-button>
          </el-form-item>
          <!-- checkBox -->
          <el-form-item>
            <el-checkbox v-model="preferences.filters.isMatching" @change="reloadData">
              {{ t('dataHedgingShowBank') }}
            </el-checkbox>
          </el-form-item>
          <!-- btn -->
          <el-form-item>
            <div style="display: flex; align-items: center">
              <el-button size="mini" type="primary" @click="bankMonthlyState = true">
                {{ t('bankMonth') }}({{ finishNum }})
              </el-button>
              <div class="actions-icon" style="height: 26px">
                <i
                  :title="$t('btnTitle.pageSetting')"
                  class="edac-icon action-icon edac-icon-setting1"
                  @click="onSetting"
                />
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div v-if="runningNumber" class="right-loading">
          <img :src="LoadingImg" alt="" class="img-loading">
          <span>
            {{ t('fileScan', { number: runningNumber }) }}
          </span>
        </div>
      </div>
      <div class="bank-reconciliation-table">
        <div style="width: 58%">
          <BTable
            ref="table"
            v-loading="!loading && tableLoading"
            :data="tableData"
            :style-columns="styleColumns"
            :amount-columns="amountColumns"
            :lang-key="langKey"
            :show-index="true"
            :show-actions="false"
            :actions-min-width="5"
            :show-checkbox="false"
            :default-top="230"
            :height="pageHeight - filtersHeight - summaryHeight - (showFooter ? 55 : 20)"
            :filter-class-function="rowClassName"
            :sort-config="{ trigger: 'cell', orders: ['asc', 'desc', null] }"
            border
            @cell-click="cellClick"
            @changeWidth="changeColumnWidth"
            @scroll="onScroll"
          >
            <template slot="columns">
              <vxe-table-column
                v-for="item in filterVcRdateColumns"
                :key="item.ss_key"
                :title="$t(langKey + item.ss_key)"
                :align="item.alignment"
                :class-name="item.ss_key + ' mini-form'"
                :width="item.width"
                :property="$refs.table.column_property(item)"
                :column-key="item.ss_key"
                :params="{ key: item.ss_key }"
                :field="$refs.table.column_property(item)"
                :sort-method="$refs.table.sortAmountMethod(item.ss_key)"
                sortable
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <div v-if="item.ss_key === 'vc_method'">
                    <span>{{ scope.row.vc_method ? t(scope.row.vc_method) : '' }}</span>
                  </div>
                  <!-- <span v-else-if="item.ss_key === 'Unpaid'">
                    <el-checkbox
                      v-model="scope.row.unpaid" @change="updaterDate($event, scope)">
                    </el-checkbox>
                  </span> -->
                  <span v-else>{{
                    $refs.table.customFormatter(
                      item.ss_key,
                      scope.row[$refs.table.column_property(item)]
                    )
                  }}</span>
                </template>
              </vxe-table-column>
              <vxe-table-column
                v-for="item in vcRdateColumns"
                :key="item.ss_key"
                :title="$t(langKey + item.ss_key)"
                :align="item.alignment"
                :class-name="item.ss_key + ' mini-form'"
                :width="item.width"
                :property="$refs.table.column_property(item)"
                :column-key="item.ss_key"
                :params="{ key: item.ss_key }"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <div v-if="item.ss_key === 'vc_rdate'">
                    <div v-if="hasPermission_Edit" class="vc_rdate">
                      <span class="vc-rdate-detail">
                        <HedgingTable
                          ref="hedgingTable"
                          :row="scope.row"
                          :begin_date="preferences.filters.begin_date"
                          :end_date="preferences.filters.end_date"
                          :ac_code="preferences.filters.selectedAcCode"
                          :row-index="scope.rowIndex"
                          :is-matching="preferences.filters.isMatching"
                          :vc_rdate="scope.row.vc_rdate"
                          @updateTable="reloadData(false)"
                          @handleMouseenter="cellClick1"
                          @open="onOpen"
                        />
                      </span>
                    </div>
                    <span v-else style="padding: 0 10px">
                      <span v-if="scope.row.vc_rstatus === 'D'">
                        {{ $refs.table.customFormatter(item.ss_key, scope.row[item.ss_key]) }}
                      </span>
                    </span>
                  </div>
                </template>
              </vxe-table-column>
            </template>
          </BTable>
        </div>
        <div v-loading="imgLoading" class="pdf">
          <div class="wrapper">
            <div
              class="wrapper-box"
              :style="{
                height: `${pageHeight - filtersHeight - summaryHeight - (showFooter ? 55 : 20)}px`,
              }"
              style="background: #4a4a4a"
            >
              <template>
                <!--<div v-if="imgVisible" v-viewer.static="options" class="image-wrapper">-->
                <!--  <img ref="image" :src="currentImageUrl" alt="">-->
                <!--</div>-->
                <panZoom
                  ref="panZoom"
                  :options="{
                    autocenter: true,
                    transformOrigin: { x: 0.5, y: 0.5 },
                    minZoom: 0.05,
                    bounds: true,
                    boundsPadding: 0.2,
                    initialZoom: 0.05,
                  }"
                  selector=".right-content"
                  @init="onZoomInit"
                  @panstart="onPanStart"
                  @panend="onPanEnd"
                >
                  <div
                    :style="{
                      visibility: currentImageWidth > 0 ? '' : 'hidden',
                      width: currentImageWidth + 'px',
                      height: currentImageHeight + 'px',
                    }"
                    :class="{
                      'pan-ing': panIng,
                    }"
                    class="right-content"
                  >
                    <img
                      :src="currentImageUrl"
                      @dragstart="
                        e => {
                          e.preventDefault()
                          return false
                        }
                      "
                    >
                    <template v-if="ocrSplitResults.length > 0">
                      <div
                        v-for="block in ocrSplitResults"
                        :key="block.index"
                        :class="{
                          'ocr-div': true,
                          'current-select': selectBlock.includes(block.index),
                          // 'recommended-div': checkOcrDivStyle(block.index, 'recommend'),
                          //'is-hit': true,
                        }"
                        :style="{
                          top: block.location.top + 'px',
                          left: block.location.left + 'px',
                          width: block.location.width + 'px',
                          height: block.location.height + 'px',
                          fontSize:
                            (block.location.height < 22 ? block.location.height - 2 : 20) + 'px',
                        }"
                      />
                    </template>
                  </div>
                </panZoom>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div v-if="showFooter" ref="summary" class="summary">
        <el-form :inline="true" class="mini-form">
          <el-form-item :label="$t('periodic.bankReconciliation.label.ledgerBalance')">
            <el-input :value="summary.bf" readonly />
          </el-form-item>
          <span>{{ $t('periodic.balanceTransfer.symbol.add') }}</span>
          <el-form-item :label="$t('periodic.bankReconciliation.label.unpresentedPayment')">
            <el-input :value="summary.un_check_cr" readonly />
          </el-form-item>
          <span>{{ $t('periodic.balanceTransfer.symbol.subtract') }}</span>
          <el-form-item :label="$t('periodic.bankReconciliation.label.unpresentedReceipt')">
            <el-input :value="summary.un_check_dr" readonly />
          </el-form-item>
          <span>{{ $t('periodic.balanceTransfer.symbol.equals') }}</span>
          <el-form-item :label="$t('periodic.bankReconciliation.label.statementBalance')">
            <el-input :value="summary.end" readonly />
          </el-form-item>
          <span style="padding-left: 20px">
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              size="mini"
              type="primary"
              @click="onPagePrint"
            >{{ $t('button.print') }}</el-button>
          </span>
        </el-form>
      </div>
      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
    <BankMonthly
      v-if="bankMonthlyState"
      ref="bankMonthly"
      :dialog-visible="bankMonthlyState"
      :ac_code="preferences.filters.selectedAcCode"
      @updateStatus="bankMonthlyState = $event"
      @changeBank="changeBank"
      @delete="reloadData"
      @uploadSuccess="getRunningNumber"
    />
    <el-dialog
      :title="t('selectUnpaidDate')"
      :visible.sync="unpaidDateData.visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="30%"
      custom-class="unpaid-date-dialog"
      @close="closeDialog"
    >
      <div>
        <el-form ref="unpaidDateForm" :model="unpaidDateData" label-width="0">
          <el-form-item
            label=" "
            prop="vc_rdate"
            :rules="[{ required: true, message: t('selectUnpaidDateRequired'), trigger: 'blur' }]"
          >
            <!-- <el-select v-model="unpaidDateData.vc_rdate" :placeholder="$t('placeholder.selectDate')" style="width: calc(100% - 25px)">
              <el-option
                v-for="item in unpaidDateData.dateData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> -->
            <el-date-picker
              v-model="unpaidDateData.vc_rdate"
              :default-value="unpaidDateData.rowData.vc_date"
              :clearable="false"
              :format="dateValueFormat"
              :value-format="dateValueFormat"
              :picker-options="{ disabledDate: disabledDate }"
              type="date"
              style="width: calc(100% - 25px)"
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">{{
          $t('periodic.balanceTransfer.button.cancel')
        }}</el-button>
        <el-button type="primary" @click="confirmDialog">{{
          $t('periodic.balanceTransfer.button.confirm')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { fetchAccounts } from '@/api/master/account'
import {
  editBankLedgers,
  fetchAiBankLedgers,
  saveBankLedgers,
} from '@/api/periodic/bankReconciliation'
import BTable from '@/components/BTable'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'

import ENumeric from '@/components/ENumeric'
import dateUtil from '@/utils/date'
import { listenTo } from '@/utils/resizeListen'
import DateRange from '@/components/DateRange/index'
import panZoom from '@/components/pan-zoom/component'
import HedgingTable from './components/hedgingTable.vue'
import BankMonthly from './components/bankMonthly.vue'
import {
  getDocumentFileLedger,
  getFileActions,
  autoSelectFileLedgers,
  fetchDocumentFilesSummary,
} from '@/api/periodic/bankReconciliation'
import LoadingImg from '../../../assets/AILoading/chatLoading.gif'
import dayjs from 'dayjs'
import { amountFormat } from '@/utils'

export default {
  name: 'BankReconciliationIndex',
  components: {
    DateRange,
    BankMonthly,
    BTable,
    panZoom,
    customStyle,
    HedgingTable,
    ENumeric,
    VBreadCrumb,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission],
  data() {
    return {
      loading: true,
      LoadingImg,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      typeOption: {
        X: this.$t('daily.cheque.typeOption.type_X'),
        N: this.$t('daily.cheque.typeOption.type_N'),
        C: this.$t('daily.cheque.typeOption.type_C'),
      },
      bankList: [],
      chequeBooks: [],
      years: '',
      selectedYearId: '',
      tableData: [],
      summary: {
        bf: 0,
        un_check_cr: 0,
        un_check_dr: 0,
        end: 0,
      },
      data: [],
      dateList: [{ label: 'N/A', value: 'E' }],
      monthList: [],
      langKey: 'bankAiReconciliation.',
      tableColumns: [
        'vc_date',
        'vc_no',
        'descr',
        'vc_payee',
        'vc_method',
        'ref',
        'amount_dr',
        'amount_cr',
        'vc_rdate',
      ],
      amountColumns: ['amount_dr', 'amount_cr'],
      preferences: {
        filters: {
          selectedAcCode: '',
          selectedYearCode: '',
          ref: '',
          selectedMonth: '',
          // date_range: [],
          begin_date: '',
          end_date: '',
          vc_rstatus: false,
        },
      },
      childPreferences: ['selectedMonth'],
      showYearPicker: false,
      periods: [],
      imgLoading: false,
      unpaidDateData: {
        visible: false,
        dateData: [],
        vc_date: '',
        rowData: {},
      },
      panIng: false,
      currentImageWidth: 400,
      currentImageHeight: 400,
      currentImageUrl: '',

      yearLastDate: new Date(),
      yearStartDate: new Date(),
      dateValueFormat: 'yyyy-MM-dd',
      // dataShowFormat: 'yyyy-MM-dd'
      selectBlock: [],

      com_vc_rdate: 'E',

      filtersResizeListen: {},
      filtersHeight: 40,
      summaryResizeListen: {},
      summaryHeight: 40,
      pageResizeListen: {},
      pageHeight: 500,

      list: [],
      showLabel: '',
      showOption: false,
      value1: '',
      btnLoading: false,

      bankMonthlyState: false,

      imgList: [],
      downImgTime: null,
      mouseData: null,
      imageIndex: 0,
      ocrResults: [],
      hoverGroup: '',
      runningNumber: 0,
      finishNum: 0,
      runningNumberOut: null,
      showFooter: false,
      lastAutoSelectData: [],
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'user_id', 'remoteServerInfo']),
    columns() {
      const arr = this.styleColumns.filter(i => i.ss_key !== '_index')
      if (!arr.find(i => i.ss_key === 'vc_rdate')) return arr
      // const Unpaid = {
      //   ss_key: 'Unpaid',
      //   ss_type:'column',
      //   alignment: 'left',
      //   width: 100 ,
      //   export: 0,
      //   seq:10,
      //   ss_id:689,
      //   x:9,
      //   ss_code:'ac.periodic.bank_reconciliation',
      //   user_id: this.user_id,
      //   system: 'AC'
      // }
      // arr.splice(arr.length - 1 , 0 , Unpaid)
      return arr
    },
    filterVcRdateColumns() {
      return this.columns.filter(i => i.ss_key !== 'vc_rdate')
    },
    vcRdateColumns() {
      return this.columns.filter(i => i.ss_key === 'vc_rdate')
    },
    currentImg() {
      return this.imgList.length ? this.imgList[0] : ''
    },
    ocrSplitResults() {
      const ocrResultsCopy = JSON.parse(JSON.stringify(this.ocrResults))
      if (ocrResultsCopy[this.imageIndex] && ocrResultsCopy[this.imageIndex].length > 0) {
        return ocrResultsCopy[this.imageIndex]
      } else {
        return []
      }
    },
  },
  watch: {
    // selectedYearId: function(currentVal) {
    //   this.years.forEach(ele => {
    //     if (currentVal === ele.fy_id) {
    //       this.preferences.filters.selectedYearCode = ele.fy_code
    //     }
    //   })
    //   this.getMonth().then(this.reloadData)
    // },
    // 'preferences.filters.selectedAcCode': function(currentVal) {
    // this.getChequeBooks().then(() => this.reloadData())
    // }
  },
  created() {
    this.fetchData()
    this.saveUserLastPage()
    this.showSecondaryWindow = true
    this.channel = new BroadcastChannel('EDAC_BroadcastChannelBankStatementPreview')
    this.channel.addEventListener('message', this.omChannelMessage)
  },
  mounted() {
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
      this.summaryResizeListen = listenTo(this.$refs.summary, ({ width, height, ele }) => {
        this.summaryHeight = height
      })
    })
  },
  destroyed() {
    this.mouseId = null
    if (this.downImgTime) {
      clearTimeout(this.downImgTime)
      this.downImgTime = null
    }
    if (this.runningNumberOut) {
      clearTimeout(this.runningNumberOut)
      this.runningNumberOut = null
    }
  },
  methods: {
    disabledDate(time) {
      return (
        time.getTime() < dayjs(this.unpaidDateData.rowData.vc_date).valueOf() ||
        time.getTime() > dayjs(this.preferences.filters.end_date).valueOf()
      )
    },
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(() => fetchAccounts({ ac_bank: 'C,S,F,P' }))
        .then(res => {
          this.bankList = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.preferences.filters.selectedAcCode === '') {
            this.preferences.filters.selectedAcCode =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].ac_code : ''
          } else {
            if (this.bankList && this.bankList.length > 0) {
              let bool = false
              this.bankList.forEach(i => {
                if (i.ac_code === this.preferences.filters.selectedAcCode) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
              }
            }
          }
          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    omChannelMessage(ev) {
      const { type } = ev // data
      switch (type) {
        case 'keepAlive':
          !this.showSecondaryWindow && (this.showSecondaryWindow = true)
          break
        case 'close':
          this.showSecondaryWindow && (this.showSecondaryWindow = false)
          break
        case 'ready':
          this.postMsg('init', {
            isFilePreview: this.isFilePreview,
            previewType: this.previewType,
            companyId: this.companyId,
            bankStatementFileId: this.bankStatementFileId,
            bankStatementGroupId: this.bankStatementGroupId,
          })
          this.postAlwaysIndexes()
          break
      }
    },
    postMsg(type, data, channel) {
      const c = channel || this.channel
      if (c) {
        c.postMessage(
          {
            type,
            from: 'page',
            data,
          },
          'page',
        )
      }
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    onZoomInit() {
      console.log('onZoomInit')
      this.waitPanElement()
    },
    onPanStart() {
      this.panIng = true
    },
    onPanEnd() {
      this.panIng = false
    },
    async loadImages() {
      const imageLoadId = new Date().getTime() + Math.random()
      this.imageLoadId = imageLoadId
      for (let i = 0; i < this.imgList.length; i++) {
        if (this.imageLoadId !== imageLoadId) break
        const url = this.imgList[i].tempUrl
        try {
          const img = await this.toDataURL(url)
          if (this.imageLoadId !== imageLoadId) break
          this.imgList[i].url = img.url
          this.imgList[i].width = img.width
          this.imgList[i].height = img.height
          this.imgList[i].status = 'done'
          if (i === this.imageIndex) {
            this.$nextTick(() => {
              this.currentImageUrl = img.url
              console.log('currentImageUrl', this.currentImageUrl)
              this.refreshImg()
            })
          }
        } catch (e) {
          this.imageLinks[i].status = 'error'
        }
      }
    },
    toDataURL(src, outputFormat) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'Anonymous'
        // img.setAttribute('crossOrigin', 'anonymous')
        img.onload = function() {
          const canvas = document.createElement('CANVAS')
          const ctx = canvas.getContext('2d')
          canvas.height = this.naturalHeight
          canvas.width = this.naturalWidth
          ctx.drawImage(this, 0, 0)
          // const dataURL = canvas.toDataURL(outputFormat)
          // resolve({ url: dataURL, width: this.naturalWidth, height: this.naturalHeight })
          canvas.toBlob(blobObj => {
            const imgSrc = window.URL.createObjectURL(blobObj)
            resolve({ url: imgSrc, width: this.naturalWidth, height: this.naturalHeight })
          })
        }
        img.οnerrοr = function(e) {
          console.log(e)
          reject(e)
        }
        img.src = src
        // if (img.complete || img.complete === undefined) {
        //   img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw=='
        //   img.src = src
        // }
      })
    },
    refreshImg() {
      this.currentImageStatus = ''
      this.$nextTick(() => {
        this.imgInit = false
        this.imgVisible = false
        this.currentImageUrl = this.imgList[this.imageIndex].url
        this.currentImageWidth = this.imgList[this.imageIndex].width
        this.currentImageHeight = this.imgList[this.imageIndex].height
        this.$nextTick(() => {
          this.imgVisible = true
          this.onChangeZoomInit()
          this.currentImageStatus = this.imgList[this.imageIndex].status
          this.$nextTick(() => {
            this.onChangeZoomInit()
          })
        })
      })
    },
    onChangeZoomInit() {
      console.log('onChangeZoomInit')
      this.imgInit = true
      this.$nextTick(() => {
        this.waitPanElement()
      })
    },
    updaterDate(e, scope) {
      if (e) {
        this.unpaidDateData.visible = true
        this.unpaidDateData.dateData = this.dateList.filter(i => !['E', 'U', 'F'].includes(i.value))
        this.unpaidDateData.rowData = scope.row
      }
    },
    async waitPanElement() {
      let i = 0
      while (!this.$refs.panZoom && i < 50) {
        i++
        await this.sleep(50)
      }
      this.$refs.panZoom && this.$refs.panZoom.autoZoom(false)
      await this.sleep(50)
      this.$refs.panZoom && this.$refs.panZoom.autoZoom(false)
    },
    sleep(time) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve()
        }, time)
      })
    },
    reloadData(clearMakeData = true) {
      console.log('this.$refs.hedgingTable', this.$refs.hedgingTable)
      if (this.$refs.hedgingTable) {
        this.$refs.hedgingTable.forEach(item => {
          item.close()
        })
      }
      return new Promise((resolve, reject) => {
        const ac_code = this.preferences.filters.selectedAcCode
        const ref = this.preferences.filters.ref
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, 1)
        const lastDateObj = new Date(year, month + 1, 0)
        const vc_rstatus = this.preferences.filters.vc_rstatus ? 'E' : ''
        if (clearMakeData) {
          this.mouseData = null
        }
        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        if (!this.preferences.filters.begin_date) {
          begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
          this.preferences.filters.begin_date = begin_date
        }
        if (!this.preferences.filters.end_date) {
          end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
          this.preferences.filters.end_date = end_date
        }
        // this.onChangeDateRange([begin_date, end_date])
        this.dateList = this.getAllDate(begin_date, end_date)
        this.loading = true
        this.tableLoading = true
        this.$forceUpdate()
        fetchAiBankLedgers({
          ac_code,
          begin_date,
          ref,
          vc_rstatus,
          end_date,
        })
          .then(res => {
            const data = res.data || res
            data.forEach(item => {
              item.old_vc_rdate = item.vc_rdate
              if ('EFU'.includes(item.vc_rstatus)) {
                item.vc_rdate = item.vc_rstatus
              }
              item.old_vc_rstatus = item.vc_rstatus
            })
            this.tableData = this.formatData(data)
            if (!this.preferences.filters.ref && !this.preferences.filters.vc_rstatus) {
              this.summary = {
                bf: amountFormat(res.summary.bf),
                un_check_cr: amountFormat(res.summary.un_check_cr),
                un_check_dr: amountFormat(res.summary.un_check_dr),
                end: amountFormat(res.summary.end),
              }
            }
            if (this.$refs.hedgingTable) {
              this.$refs.hedgingTable.forEach(item => {
                item.makeDateList()
              })
            }
            this.showFooter = !this.preferences.filters.ref && !this.preferences.filters.vc_rstatus
            this.getRunningNumber()
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
            this.tableLoading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      data.forEach(item => {
        const newItem = Object.assign({}, item)
        // switch (newItem.vc_rstatus) {
        //   case 'E':
        //     break
        //   case 'D':
        //     newItem.vc_rdate = newItem.vc_rstatus
        //     break
        // }
        // if (newItem.vc_rstatus !== 'D') {
        //   newItem.vc_rdate = newItem.vc_rstatus
        // }
        newItem.unpaid = newItem.vc_rstatus === 'U'
        newData.push(newItem)
      })
      return newData
    },
    formatDate(date) {
      date = date.replace(/-/g, '/')
      return dateUtil.format(new Date(date), this.styles.dateFormat)
    },
    async getRunningNumber() {
      try {
        const res = await fetchDocumentFilesSummary({
          ac_code: this.preferences.filters.selectedAcCode,
        })
        this.finishNum = res.total
        console.log(this.runningNumber, 'this.runningNumber')
        clearTimeout(this.runningNumberOut)
        this.runningNumberOut = null

        if (res.progressing > 0) {
          this.runningNumberOut = setTimeout(() => {
            this.getRunningNumber()
          }, 5000)
        }
        if (this.runningNumber > res.progressing) {
          this.$refs.bankMonthly.getFileList(false)
        }
        this.runningNumber = res.progressing
      } catch (error) {
        console.log(error)
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    dateByFormat(date, format = 'yyyy-MM-dd') {
      return dateUtil.format(date, format)
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    getAllDate(begin, end) {
      const arr = []
      arr.push({
        label: 'N/A',
        value: 'E',
      })
      const ab = begin.split('-')
      const ae = end.split('-')
      const db = new Date()
      db.setUTCFullYear(ab[0], ab[1] - 1, ab[2])
      const de = new Date()
      de.setUTCFullYear(ae[0], ae[1] - 1, ae[2])
      const unixDb = db.getTime() - 24 * 60 * 60 * 1000
      const unixDe = de.getTime() - 24 * 60 * 60 * 1000
      for (let k = unixDb; k <= unixDe;) {
        k = k + 24 * 60 * 60 * 1000
        const d = new Date(parseInt(k))
        arr.push({
          label: this.dateByFormat(d, this.styles.dateFormat),
          value: this.dateByFormat(d),
        })
      }

      arr.push({
        label: 'Future',
        value: 'F',
      })
      arr.push({
        label: 'Unpaid',
        value: 'U',
      })
      return arr
    },
    onChangeDateRange(val) {
      // this.dateList = this.getAllDate(val[0], val[1])
    },
    onApplyReceipt() {
      this.tableData = this.tableData.map(item => {
        const newItem = Object.assign({}, item)

        if (
          newItem.amount_dr &&
          typeof Number(newItem.amount_dr) === 'number' &&
          (!newItem.vc_rdate || newItem.vc_rdate === 'E')
        ) {
          newItem.vc_rdate = newItem.vc_date
        }
        return newItem
      })
    },
    onAutoSelectHedging() {
      this.$confirm(
        `${this.t('autoSelectHedgingConfirm')}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      ).then(() => {
        this.autoSelectHedging()
      })
    },
    onClearAll() {
      this.$confirm(`${this.t('isClearAll')}` + '?', this.$t('confirm.warningTitle'), {
        confirmButtonText: this.$t('confirm.confirmButtonText'),
        cancelButtonText: this.$t('confirm.cancelButtonText'),
        type: 'warning',
      }).then(() => {
        const that = this
        const paramsArr = this.tableData
          .filter(item => item.vc_rdate !== 'E' && item.vc_rdate)
          .map(item => {
            console.log(item, 'item')

            const params = {
              fy_code: item.fy_code,
              lg_id: item.lg_id,
              vc_rstatus: 'E',
              vc_rdate: '',
            }
            return params
          })
        console.log(paramsArr, 'paramsArr')
        editBankLedgers(paramsArr)
          .then(res => {
            that.$message.success(this.$t('message.success'))
            this.lastAutoSelectData = []
            that.reloadData()
          })
          .catch(err => {
            this.$message.error(err.message)
          })
      })
    },
    onUndo() {
      this.$confirm(
        `${this.t('isUndo', { num: this.lastAutoSelectData.length })}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      ).then(() => {
        const that = this
        const paramsArr = this.lastAutoSelectData.map(item => {
          const params = {
            fy_code: item.fy_code,
            lg_id: item.lg_id,
            vc_rstatus: 'E',
            vc_rdate: '',
          }
          return params
        })
        editBankLedgers(paramsArr)
          .then(res => {
            that.$message.success(this.$t('message.success'))
            that.lastAutoSelectData = []
            that.reloadData()
          })
          .catch(err => {
            this.$message.error(err.message)
          })
      })
    },
    async autoSelectHedging() {
      if (this.loading) return
      this.loading = true
      try {
        const params = {
          ac_code: this.preferences.filters.selectedAcCode,
          begin_date: this.preferences.filters.begin_date,
          end_date: this.preferences.filters.end_date,
        }
        autoSelectFileLedgers(params).then(res => {
          this.lastAutoSelectData = res
          this.reloadData()
          this.$message.success(this.t('pairing', { num: res.length }))
        })
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    onChangeMonth(val) {
      if (val === '') {
        // 全年
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateValueFormat)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateValueFormat)
        // this.preferences.filters.date_range = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      // beginDate
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, this.dateValueFormat)

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, this.dateValueFormat)
      // this.preferences.filters.date_range = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
      this.onChangeDateRange([beginDate, endDate])
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateValueFormat,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateValueFormat,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    cellClick({ row }) {
      if (this.mouseData && row && this.mouseData.lg_id === row.lg_id) return
      this.mouseData = row
      this.imgList = []
      this.currentImageUrl = ''
      this.imageIndex = -1
      if (!row || !row.ai_document_file_ledger_id) {
        return
      }
      if (this.downImgTime) {
        this.mouseId = null
        clearTimeout(this.downImgTime)
        this.downImgTime = null
      }
      console.log(row, 'row')

      this.downImgTime = setTimeout(() => {
        this.getImage(row)
      }, 500)
    },
    cellClick1({ row }) {
      console.log(row, 'row')
      if (this.mouseData && row && this.mouseData.lg_id === row.lg_id && !row.needRefresh) return
      this.imgList = []
      this.currentImageUrl = ''
      this.imageIndex = -1
      if (!row || !row.ai_document_file_ledger_id) {
        return
      }
      if (this.downImgTime) {
        this.mouseId = null
        clearTimeout(this.downImgTime)
        this.downImgTime = null
      }
      console.log(row, 'row')

      this.downImgTime = setTimeout(() => {
        this.getImage(row)
      }, 500)
    },
    rowClassName({ row, $rowIndex }) {
      if (this.mouseData && this.mouseData.lg_id === row.lg_id) {
        return 'vxe-body--row-click'
      }
      if ($rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    async getImage(row) {
      if (this.imgLoading) return
      this.imgLoading = true
      try {
        this.imgList = []
        const res = await getDocumentFileLedger(row.ai_document_file_ledger_id)
        const fileRes = await getFileActions(res.aiDocumentFileLedger.ai_document_file_id)
        const { remoteFilePath } = this.remoteServerInfo
        this.imgList = fileRes.imgs.map(i => {
          return {
            tempUrl: `${remoteFilePath}${i.file_path}`,
          }
        })
        const blockData = fileRes.imgs.map(item => {
          return JSON.parse(item.block_data)
        })
        this.ocrResults = blockData
        this.$nextTick(() => {
          this.loadImages()
        })
        this.imageIndex = res.aiDocumentFileLedger.page_in_group - 1
        this.handleMouseenter(row)
      } catch (e) {
        console.log(e)
      }
      this.imgLoading = false
    },
    onSetComDate(scope) {
      scope.row.vc_rdate = this.com_vc_rdate
    },
    changeBank(ac_code) {
      this.preferences.filters.selectedAcCode = ac_code
      this.reloadData()
    },
    closeDialog() {
      this.unpaidDateData.visible = false
      this.unpaidDateData.vc_rdate = ''
      this.unpaidDateData.rowData = {}
      this.$refs.unpaidDateForm.resetFields()
      this.reloadData()
    },
    async confirmDialog() {
      this.$refs.unpaidDateForm.validate(async valid => {
        if (!valid) return
        try {
          const params = {
            fy_code: this.unpaidDateData.rowData.fy_code,
            lg_id: this.unpaidDateData.rowData.lg_id,
            vc_rstatus: 'U',
            vc_rdate: this.unpaidDateData.vc_rdate === 'E' ? '' : this.unpaidDateData.vc_rdate,
          }
          const res = await editBankLedgers([params])
          this.unpaidDateData.visible = false
          this.$message.success(this.$t('message.success'))
          this.reloadData()
        } catch (error) {
          console.log(error)
        }
      })
    },
    getDateList(vc_date) {
      return this.dateList.filter(i => {
        // if ('EFU'.includes(i.value)) {
        //   return true
        // }
        const iDate = new Date(i.value)
        const vDate = new Date(vc_date)
        return iDate >= vDate
      })
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
    handleMouseenter(row) {
      if (this.hoverGroup !== row.block_indexes) {
        this.hoverGroup = row.block_indexes
        const arr = row.block_indexes ? (row.block_indexes + '').split(',').map(i => Number(i)) : []
        const diff = this.isEqual(this.selectBlock, arr)
        if (!diff) {
          this.selectBlock = arr // row.blockIndexes ? (row.blockIndexes + '').split(',').map(i => Number(i)) : []
          this.postMsg('selectBlock', arr)
          this.postMsg('switchImage', this.imageIndex)
        }
      }
    },
    isEqual(arr1, arr2) {
      return JSON.stringify(arr1) === JSON.stringify(arr2)
    },
    popoverClick() {
      // this.$refs.hedgingTable.length && this.$refs.hedgingTable && this.$refs.hedgingTable[0].open()
    },
    onOpen(rowIndex) {
      this.$refs.hedgingTable.forEach(item => {
        item.close(rowIndex)
      })
    },
    onScroll() {
      this.$refs.hedgingTable.forEach(item => {
        item.close()
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
  }
}
::v-deep {
  .el-form-item.el-form-item--medium {
    margin-right: 10px !important;
  }
  .el-checkbox {
    margin-right: 10px !important;
  }
}
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
  }
  .bank-reconciliation-table {
    /*height: calc(100vh - 270px);*/
    width: 100%;
    display: flex;
    .pdf {
      // height: calc(100vh - 195px);
      width: 42%;
      flex-shrink: 0;
      background: #4a4a4a;
    }
    .b-table {
      flex: 1;
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .vxe-cell {
            max-height: unset;
            height: 27px;
            line-height: 25px;
          }
          .cell,
          .vxe-cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .select {
              min-width: 80px;
            }
            svg {
              width: 19px;
              height: 19px;
              vertical-align: middle;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
        .el-checkbox {
          margin-right: 0 !important;
        }
      }
    }
  }
  .summary {
    margin: 5px 0;
    .mini-form {
      /deep/ {
        .el-form-item.el-form-item--medium {
          margin-right: 0 !important;
        }
        .el-input {
          width: 120px;
          input {
            text-align: right;
          }
        }
      }

      span {
        line-height: 25px !important;
        height: 25px !important;
      }
      .el-button {
        padding: 0px 15px;
        vertical-align: middle;
      }
    }
  }
}
.vc_rdate {
  display: flex;
  // align-items: center;
  .action-icon {
    font-size: 12px;
    height: 25px !important;
    background: #fff;
    display: inline-block;
    border-radius: 0px 4px 4px 0px;
    border: 1px solid #dcdfe6;
    border-left: none;
    width: 25px !important;
    padding: 2px;
  }
}
.wrapper {
  // padding: 4px 20px 20px;
  overflow: hidden;
  text-align: center;
  .wrapper-box {
    //max-height: 70vh;
    min-height: 60vh;
    max-height: 90vh;
    //overflow-y: auto;
    overflow: hidden;
    width: 100%;
    position: relative;
    .example {
      position: absolute;
      top: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .left-icon,
    .right-icon {
      position: absolute;
      top: 45%;
      width: 34px;
      height: 34px;
      display: flex;
      // background-color: #FAFAFA;
      background-color: rgba(250, 250, 250, 0.8);
      justify-content: center;
      cursor: pointer;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
    }
    .left-icon {
      left: 10px;
    }
    .right-icon {
      right: 10px;
    }
    > div {
      width: 100%;
      img {
        // width: 50%;
        //visibility: hidden;
      }
    }

    .image-info-wrapper {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      text-align: left;
      padding: 10px 40px;
      //background: #333333;
      //opacity: 0.8;
      background: rgba(51, 51, 51, 0.8);
      color: #ffffff;
      //height: 68px;
      div {
        line-height: 19px;
        margin-bottom: 10px;
        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
  }
}
.ocr-div {
  position: absolute;
  // opacity: 0.2;

  background: rgba(255, 193, 5, 0.2);
  border: 1px solid #ffc105;
  color: #4c5362;
  //visibility: hidden;
  line-height: 1;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  &.current-select {
    // visibility: unset;
    opacity: 1;
  }
  &:hover {
    opacity: 1;
  }
}

.ocr-div.recommended-div {
  background-color: #d100f5;
}

.ocr-div.selected-div {
  background-color: #48c760;
}

.ocr-div.show-words {
  background-color: #ff980033;
  color: #4c5362;
  opacity: 1;
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  visibility: unset;
}

.ocr-div.recommended-div.show-words {
  background-color: #d100f533;
  color: #4c5362;
}

.ocr-div.selected-div.show-words {
  //background-color: #48C76033;
  background-color: #ff980033;
  color: #4c5362;
}

.ocr-div.is-hit.show-words {
  background-color: #48c76033;
}
.right-loading {
  display: flex;
  align-items: center;
  margin-left: auto;
  white-space: nowrap;
  // flex-wrap: nowrap;
  width: 120px;
  .img-loading {
    width: 20px;
    height: 20px;
  }
}
.vc-rdate-detail {
  width: 100%;
}
.summary {
  margin: 5px 0;
  /deep/ {
    .el-form-item.el-form-item--medium {
      margin-right: 0;
    }
    .el-input {
      width: 120px;
      input {
        text-align: right;
      }
    }

    span {
      line-height: 25px !important;
      height: 25px !important;
    }
    .el-button {
      padding: 0px 15px;
      vertical-align: middle;
    }
  }
}
</style>
<style lang="scss">
.unpaid-date-dialog {
  .el-dialog__body {
    height: 100% !important;
    .el-input {
      input {
        height: 36px !important;
      }
    }
    .el-form-item__error {
      margin-left: 14px;
    }
  }
}
</style>
