<template>
  <div v-loading="loading || uploadLoading" class="upload-wrapper">
    <input
      ref="excel-upload-input"
      :accept="fileSuffix"
      class="excel-upload-input"
      type="file"
      @change="handleClick"
    >
    <div class="drop" @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover">
      <span v-if="currentFile.name" class="filename">
        <i
          :class="{
            'el-icon-document': excelFile,
            'el-icon-coin': !excelFile,
          }"
        />
        {{ currentFile.name }}
      </span>
      <span v-else>
        {{ msg ? msg : $t('message.dropExcel') }}
      </span>
      <el-button
        :loading="loading"
        style="margin-left: 16px"
        size="mini"
        type="primary"
        @click="handleUpload"
      >
        {{ $t('file.browse') }}
      </el-button>
    </div>
    <div v-if="showTip" class="upload-tips el-row--flex">
      <div style="padding-right: 20px">
        {{ fileTypeTips ? fileTypeTips : $t('file.pleaseSelectExcelFile') }}
      </div>
      <div v-if="onTemplate" class="tem-link">
        <p @click="handleTemplate">
          {{ $t('file.downTemplate') }}
        </p>
      </div>
    </div>
    <el-row v-if="showActions" class="actions">
      <el-col class="submit">
        <el-button :disabled="loading || vLoading" type="primary" size="mini" @click="handleSubmit">
          {{ $t('button.submit') }}
        </el-button>
        <el-button :disabled="loading || vLoading" type="danger" size="mini" @click="handleCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import XLSX from 'xlsx'
// import { Loading } from 'element-ui'

export default {
  name: 'ManualControl',
  props: {
    beforeUpload: Function, // eslint-disable-line
    onSuccess: Function, // eslint-disable-line
    onTemplate: Function, // eslint-disable-line
    onSubmit: Function, // eslint-disable-line
    onCancel: Function, // eslint-disable-line
    fileTypeTips: String, // eslint-disable-line
    supportsFileTypeTips: String, // eslint-disable-line
    msg: String, // eslint-disable-line
    showTip: {
      type: Boolean,
      default: true,
    },
    excelFile: {
      type: Boolean,
      default: true,
    },
    uploadField: {
      // 備份上傳formData，參數名
      type: String,
      default: 'backup_file',
    },
    fileSuffix: {
      // 選擇文件時篩選
      type: String,
      default: '.xlsx, .xls, .csv',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    vLoading: {
      type: Boolean,
      default: false,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      excelData: {
        header: null,
        results: null,
      },
      currentFile: {},
      loadingInstance: null,
      uploadLoading: false,
    }
  },
  // watch: {
  //   loading(val) {
  //     if (val) {
  //       this.loadingInstance = Loading.service({
  //         target: '.upload-wrapper'
  //       })
  //     } else {
  //       this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
  //         this.loadingInstance && this.loadingInstance.close()
  //       })
  //     }
  //   }
  // },
  methods: {
    generateData({ header, results }) {
      this.loading = true
      this.excelData.header = header
      this.excelData.results = results
      if (this.onSuccess) {
        this.uploadLoading = true
        this.onSuccess(this.excelData).finally(() => {
          this.uploadLoading = false
        })
      }
      this.loading = false
    },
    generateDataMultiple(data) {
      if (this.onSuccess) {
        this.uploadLoading = true
        this.onSuccess(data).finally(() => {
          this.uploadLoading = false
        })
      }
    },
    handleDrop(e) {
      e.stopPropagation()
      e.preventDefault()
      if (this.loading) return
      const files = e.dataTransfer.files
      if (files.length !== 1) {
        this.$message.error(this.$t('message.supportUploadingOneFile'))
        return
      }
      const rawFile = files[0] // only use files[0]
      console.log('rawFile', rawFile)

      if (!this.isAccordFileType(rawFile)) {
        this.$message.error(this.supportsFileTypeTips || this.$t('message.supportsExcelFile'))
        return false
      }
      this.onChange(rawFile)
      // this.upload(rawFile)
      this.currentFile = rawFile
      this.$refs['excel-upload-input'].value = null
      e.stopPropagation()
      e.preventDefault()
    },
    handleDragover(e) {
      e.stopPropagation()
      e.preventDefault()
      e.dataTransfer.dropEffect = 'copy'
    },
    handleUpload() {
      this.$refs['excel-upload-input'].click()
    },
    handleClick(e) {
      const files = e.target.files
      const rawFile = files[0] // only use files[0]
      if (!rawFile) return
      this.onChange(rawFile)
      this.currentFile = rawFile
      this.$refs['excel-upload-input'].value = null
      // this.upload(rawFile)
    },
    handleTemplate() {
      this.onTemplate()
    },
    upload(rawFile) {
      // this.$refs['excel-upload-input'].value = null // fix can't select the same excel
      const func = this.excelFile ? this.readerDataExcel : this.readerDataBackupFile
      if (!this.beforeUpload) {
        // this.loading = true
        func(rawFile).finally(() => {
          // this.loading = false
        })
        return
      }
      const before = this.beforeUpload(rawFile)
      if (before) {
        this.loading = true
        func(rawFile).finally(() => {
          this.loading = false
        })
      }
    },
    readerDataExcel(rawFile) {
      this.loading = true
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = e => {
          const data = e.target.result
          const fixedData = this.fixData(data)
          const workbook = XLSX.read(btoa(fixedData), { type: 'base64' })
          if (this.multiple) {
            // 所有Sheet
            const data = {}
            workbook.SheetNames.forEach(name => {
              const worksheet = workbook.Sheets[name]
              const header = this.getHeaderRow(worksheet)
              const results = XLSX.utils.sheet_to_json(worksheet)
              data[name] = {
                header,
                results,
              }
            })
            this.generateDataMultiple(data)
          } else {
            // 第一個Sheet
            const firstSheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[firstSheetName]
            const header = this.getHeaderRow(worksheet)
            const results = XLSX.utils.sheet_to_json(worksheet)
            this.generateData({ header, results })
          }
          this.loading = false
          resolve()
        }
        reader.readAsArrayBuffer(rawFile)
      })
    },
    readerDataBackupFile(rawFile) {
      this.loading = true
      const formData = new FormData()
      formData.append(this.uploadField, rawFile)
      this.$refs['excel-upload-input'].value = null // fix can't select the same file
      if (this.onSuccess) {
        const r = this.onSuccess(formData, rawFile)
        if (r instanceof Promise) {
          r.finally(() => {
            this.loading = false
          })
        } else {
          this.loading = false
        }
      } else {
        this.loading = false
      }
    },
    fixData(data) {
      let o = ''
      let l = 0
      const w = 10240
      for (; l < data.byteLength / w; ++l) {
        o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w, l * w + w)))
      }
      o += String.fromCharCode.apply(null, new Uint8Array(data.slice(l * w)))
      return o
    },
    getHeaderRow(sheet) {
      const headers = []
      const range = XLSX.utils.decode_range(sheet['!ref'])
      let C
      const R = range.s.r
      /* start in the first row */
      for (C = range.s.c; C <= range.e.c; ++C) {
        /* walk every column in the range */
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        /* find the cell in the first row */
        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default
        if (cell && cell.t) {
          hdr = XLSX.utils.format_cell(cell)
          headers.push(hdr)
        }
      }
      return headers
    },
    isAccordFileType(file) {
      const text = this.fileSuffix
        .split(',')
        .map(item => item.trim())
        .join('|')
      let fileName = file.name
      fileName = fileName.toLowerCase()
      return new RegExp(`(${text})$`).test(fileName)
    },
    isZip(file) {
      return /\.zip$/.test(file.name)
    },
    handleSubmit() {
      this.onSubmit()
        .then(() => {
          if (this.currentFile && this.currentFile.name) {
            // 提交
            this.upload(this.currentFile)
          } else {
            this.$message.error(this.$t('message.noSelectedFile'))
          }
        })
        .catch(() => {})
    },
    handleCancel() {
      this.onCancel()
    },
    init() {
      this.currentFile = {}
      this.$refs['excel-upload-input'].value = null
    },
    onChange(file) {
      this.$emit('change', file)
    },
  },
}
</script>

<style lang="scss" scoped>
.upload-wrapper {
  margin: 0;
  width: auto;
  user-select: none;
}
.excel-upload-input {
  display: none;
  z-index: -9999;
}
.drop {
  border: 2px dashed #bbb;
  max-width: 600px;
  height: 160px;
  line-height: 160px;
  margin: 0 auto;
  font-size: 24px;
  border-radius: 5px;
  text-align: center;
  color: #bbb;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.upload-tips {
  /*margin-top: 8px;*/
  align-items: center;
  font-weight: bold;
  max-width: 600px;
  /*margin: auto;*/
  text-align: left;

  margin: 10px auto;
}
.tem-link {
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: underline;
  color: rgb(42, 125, 207);
  &:hover {
    color: rgb(20, 216, 86);
  }
}
.actions {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  .submit {
    text-align: right;
    position: relative;
  }
}
span.filename {
  max-width: 500px;
  line-height: 30px;
}
</style>
