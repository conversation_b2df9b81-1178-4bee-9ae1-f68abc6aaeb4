<template>
  <div ref="page" v-loading="loading" class="payment-info">
    <div ref="filters" class="filter">
      <el-form :inline="true" class="form-inline">
        <el-form-item :label="$t('daily.voucher.label.voucherType')">
          <el-select
            v-model="preferences.filters.vt_code"
            :placeholder="$t('placeholder.select')"
            style="width: 250px"
            @change="onChangeVoucherType"
          >
            <el-option :label="allVoucherType.label" :value="allVoucherType.value" />
            <el-option
              v-for="item in voucherTypeList"
              :key="item.voucher_type_id"
              :label="getVoucherTypeLabel(item)"
              :value="item.vt_code"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('daily.voucher.label.year')">
          <el-select
            v-model="preferences.filters.fy_id"
            :placeholder="$t('placeholder.select')"
            @change="onChangeYear"
          >
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('daily.voucher.label.month')">
          <el-select
            v-model="preferences.filters.pd_code"
            :placeholder="$t('placeholder.select')"
            @change="onChangeMonth"
          >
            <el-option
              v-for="item in month_list"
              :key="item.pd_id"
              :label="item.pd_name"
              :value="item.pd_code"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="icon-button">
          <i v-if="hasPermission_Add" class="edac-icon action-icon edac-icon-add1" @click="onAdd" />
        </el-form-item>
        <el-form-item class="icon-button" style="float: right">
          <i
            v-if="hasPermission_Output"
            class="edac-icon action-icon edac-icon-export"
            @click="onExport"
          />
        </el-form-item>
      </el-form>
    </div>
    <!--第一行-->
    <div ref="actions" class="actions">
      <el-input v-model="current_vc_no" readonly />
      <i class="el-icon-arrow-left action-icon" @click="prevVoucher" />
      <!--      <el-input v-model="form.vc_no">-->
      <!--        <i slot="suffix" class="edac-icon edac-icon-search"/>-->
      <!--      </el-input >-->
      <el-select
        v-model="current_vc_id"
        :placeholder="$t('placeholder.select')"
        @change="changeVoucher"
      >
        <el-option v-if="!hanVoucher" :label="current_vc_no" :value="current_vc_id" />
        <el-option
          v-for="item in voucherList"
          :key="item.vc_no"
          :label="item.vc_no"
          :value="item.vc_id"
        />
      </el-select>
      <i class="el-icon-arrow-right action-icon" @click="nextVoucher" />
      <div v-if="isView" class="edit-button" style="display: inline">
        <i
          v-if="canEditVoucher(form)"
          class="edac-icon action-icon edac-icon-return_list"
          @click="onCancel"
        />
        <i
          v-if="
            hasPermission_Edit &&
              canModify &&
              pageHasVoucher &&
              !form.vc_rdate &&
              canEditVoucher(form)
          "
          class="edac-icon action-icon edac-icon-edit"
          @click="onEdit"
        />
        <i
          v-if="
            hasPermission_Delete &&
              canModify &&
              pageHasVoucher &&
              !form.vc_rdate &&
              canEditVoucher(form)
          "
          class="edac-icon action-icon edac-icon-delete"
          @click="onDelete"
        />
        <i
          v-if="pageHasVoucher && canEditVoucher(form)"
          class="edac-icon action-icon edac-icon-copy"
          @click="onCopy"
        />
        <i
          v-if="vt_category != 'C' && pageHasVoucher && canEditVoucher(form)"
          class="edac-icon action-icon edac-icon-copy_p"
          @click="onReverseCopy"
        />
        <el-button type="primary" size="mini" @click="onPrintVoucherByView">
          {{ $t('daily.voucher.button.voucher') }}
        </el-button>
        <el-button
          v-if="showPrintCheque"
          :type="canPrintCheque ? 'primary' : 'info'"
          size="mini"
          @click="onPrintCheque(1)"
        >
          {{ $t('daily.voucher.button.chequePreview') }}
        </el-button>
        <el-button
          v-if="showPrintCheque"
          :type="canPrintCheque ? 'primary' : 'info'"
          size="mini"
          @click="onPrintCheque(0)"
        >
          {{ $t('daily.voucher.button.cheque') }}
        </el-button>
        <el-button
          v-if="canPrintEnvelope"
          :type="form.company_id ? 'primary' : 'info'"
          size="mini"
          @click="onPrintEnvelope"
        >
          {{ $t('daily.voucher.button.envelop') }}
        </el-button>
      </div>
      <div v-else class="view-button" style="display: inline">
        <el-button type="primary" size="mini" @click="onAddRow">
          {{ $t('daily.voucher.button.addRow') }}
        </el-button>
        <el-button type="primary" size="mini" @click="onDeleteRow">
          {{ $t('daily.voucher.button.deleteRow') }}
        </el-button>
        <el-button type="primary" size="mini" @click="onSave">
          {{ $t('daily.voucher.button.save') }}
        </el-button>
        <el-button type="primary" size="mini" @click="onCancel">
          {{ $t('daily.voucher.button.cancel') }}
        </el-button>
        <i class="edac-icon action-icon edac-icon-excel" style="float: right" />
      </div>
    </div>
    <summaryView
      v-if="isView"
      ref="summary"
      :is-view="isView"
      :fy_code="fy_code"
      :data.sync="form"
      :table-data.sync="tableData"
      @checkPayee="checkPayee"
    />
    <v-table
      v-if="fy_code"
      ref="DTable"
      :is-view="isView"
      :fy_code="fy_code"
      :data.sync="tableData"
      :info="form"
      :lg_id="selectLgId"
      :max-height="pageHeight - filtersHeight - actionsHeight - summaryHeight - 10"
      action="view"
      @handleDescCopyUp="handleDescCopyUp"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import summaryView from '@/views/daily/components/summaryView'
import VTable from '@/views/daily/components/VTable'

// API
import { fetchYears, getYear, searchByDate } from '@/api/master/years'
import { fetchVoucherTypes } from '@/api/master/voucherType'
import { deleteVoucher, exportVoucher } from '@/api/daily/payment'
import { fetchVouchers, createVoucher, editVoucher, getVoucher } from '@/api/daily/payment'

// import { deepCloneByJSON } from '@/utils'
// 偏好
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'
import { exportExcel } from '@/utils/excel'
import { fetchDescriptions, getDescriptions } from '@/api/assistance/description'
import { searchCompanies } from '@/api/assistance/payeePayer'
import { listenTo } from '@/utils/resizeListen'

export default {
  name: 'DailyView',
  components: { VBreadCrumb, summaryView, VTable },
  mixins: [loadPreferences, mixinPermission, handlePDF, loadPrintoutSetting],
  props: {
    vt_category: {
      type: String,
      required: true,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    editObject: {
      type: Object,
      default: null,
    },
    parentFyCode: {
      type: String,
      default: '',
    },
    parentFyId: {
      type: String,
      default: '',
    },
    parentPdCode: {
      type: String,
      default: '',
    },
    lg_id: {
      type: Number,
      default: 0,
    },
    time: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      current_vc_no: '',
      current_vc_id: '',
      tableData: [],
      currentYear: null,
      fy_code: '',
      form: {
        vc_id: 0,
        vc_no: '',
        vt_category: this.vt_category,
        vt_code: '',
        pd_code: '',
        vc_date: new Date(),
        vc_summary: '',
        vc_amount: '0.00',
        vc_method: 'TRANSF',
        ref: '',
        vc_chq_date: '',
        vc_payee: '',
        vc_st_code: '',
        vc_extra: 'N',
        vc_receipt: 'Y',
        vc_receipt_date: null,
        vc_status: 1,
        vc_chq_status: 1,
        ledgers: [],
      },
      defaultItem: {
        ac_code: '',
        account_id: '',
        descr: '',
        tx_type: 'I',
        amount_dr: '0.00',
        amount_cr: '0.00',
        vc_payee: '',
        ref: '',
        budget_code: null,
        st_code: null,
        vc_contra: null,
        vc_dept: null,
        vc_quote: '',
        vc_qnum: null,
      },

      // 偏好設置
      preferences: {
        filters: {
          vt_code: '', // vt_code
          pd_code: '', // pd_code
          fy_id: '', // fy_id
          fy_code: '', // fy_code
        },
      },
      childPreferences: ['vt_code', 'fy_id', 'pd_code'],
      // 下拉選項
      years: [],
      month_list: [],
      voucherTypeList: [],
      voucherList: [],

      // payeeIsNew: false,

      // hasPermission_Edit: true
      isInit: false,
      isInit2: false,

      filtersResizeListen: {},
      filtersHeight: 40,
      actionsResizeListen: {},
      actionsHeight: 30,
      summaryResizeListen: {},
      summaryHeight: 0,
      pageResizeListen: {},
      pageHeight: 500,
      selectLgId: null,
      OptionObj: {},
    }
  },
  computed: {
    ...mapGetters(['language', 'user_type']),
    allMonth() {
      return {
        pd_code: '',
        pd_id: '',
        pd_name: this.$t('daily.payment.allMonth'),
      }
    },
    allVoucherType() {
      return {
        value: '',
        label: this.$t('master.voucher_type.all'),
      }
    },
    canModify() {
      const month = this.month_list.find(i => i.pd_code === this.form.pd_code)
      return month && month.pd_status === 'O'
    },
    pageHasVoucher() {
      const hasVoucher = this.voucherList.length > 0
      const vc_id = !!this.current_vc_id
      return hasVoucher || vc_id
    },
    showPrintCheque() {
      return this.vt_category === 'P'
    },
    canPrintCheque() {
      return this.vt_category === 'P' && this.form.vc_method === 'CHEQUE'
    },
    canPrintEnvelope() {
      return 'PCR'.includes(this.vt_category)
    },
    hanVoucher() {
      return this.voucherList.findIndex(i => i.vc_id === this.current_vc_id) !== -1
    },
  },
  watch: {
    // 'form.vc_date'(val) {
    //   // this.handleYear()
    //   console.log('vc_date', val)
    // },
    editObject: {
      deep: true,
      handler() {
        this.initData().then(this.fetchData)
      },
    },
    isView() {
      this.initData().then(this.fetchData)
    },
    lg_id() {
      // this.initData().then(this.fetchData)
    },
  },
  beforeRouteEnter(to, from, next) {
    if (to.params.vt_category) {
      next(vm => {})
    } else {
      next(to.matched[1])
    }
  },
  // beforeRouteLeave(to, from, next) {
  //   next()
  // },
  created() {
    if (this.$route.params.lg_id) {
      this.selectLgId = this.$route.params.lg_id
    } else {
      this.selectLgId = null
    }
    // if (!this.vt_category) {
    //   this.$router.push({ name: 'dailyPaymentList' })
    // }
    // if (this.parentFyCode) {
    //   this.preferences.filters.fy_code = this.parentFyCode
    //   if (this.parentPdCode) {
    //     this.preferences.filters.pd_code = this.parentPdCode
    //   }
    // }
    this.initData().then(this.fetchData)
  },
  mounted() {
    // this.handleYear()
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.filtersResizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
      this.actionsResizeListen = listenTo(this.$refs.actions, ({ width, height, ele }) => {
        this.actionsHeight = height
        if (this.$refs.summary) {
          this.summaryHeight = this.$refs.summary.$el.offsetHeight
        }
      })
    })
  },
  methods: {
    initData() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // edit
          this.loadVoucher(this.parentFyCode, this.editObject.vc_id).finally(() => {
            resolve()
          })
        } else {
          // add
          this.tableData = [Object.assign({}, this.defaultItem)]
          resolve()
        }
      })
    },
    loadVoucher(fy_code, vc_id) {
      return new Promise((resolve, reject) => {
        getVoucher({
          fy_code: fy_code,
          vc_id: vc_id,
        })
          .then(res => {
            this.form = res
            this.tableData = res.ledgers
            this.current_vc_no = res.vc_no
            this.current_vc_id = res.vc_id
            this.checkDesc()
            this.checkPayee()
            // if (this.lg_id) {
            //   this.preferences.filters.vt_code = res.vt_code
            //   this.preferences.filters.pd_code = this.dateToPdCode(res.vc_date)
            //   this.preferences.filters.fy_code = res.fy_code
            //   this.preferences.filters.fy_id = ''
            // }

            resolve()
          })
          .catch(() => {
            this.current_vc_no = ''
            this.current_vc_id = ''
            reject()
          })
          .finally(() => {})
      })
    },
    getVoucherTypeLabel(item) {
      const { vt_code, vt_ac_code } = item
      const name = this.language === 'en' ? item.vt_description_en : item.vt_description_cn
      let label = `[${vt_code}]`
      if (vt_ac_code) {
        label += `[${vt_ac_code}]`
      }
      return label + ' ' + name
    },
    async checkDesc() {
      const data = this.tableData
      const fy_code = this.fy_code
      try {
        const full_desc = this.form.vc_summary + ''
        const res = await fetchDescriptions({ full_desc })
        this.$set(this.form, '_descIsNew', !res.some(i => i.comp_name === full_desc))
      } catch (e) {
        console.error(e)
      }
      const checkList = []
      for (let i = 0; i < data.length; i++) {
        const row = data[i]
        const full_desc = row.descr + ''
        if (full_desc.replace(/ /g, '').length === 0) {
          break
        }
        const itemIndex = checkList.findIndex(i => i.full_desc === full_desc)
        if (itemIndex === -1) {
          checkList.push({ full_desc, indexs: [i] })
        } else {
          checkList[itemIndex].indexs.push(i)
        }
        // const res = await fetchDescriptions({ full_desc })
        // this.$set(data[i], 'desc_is_new', !res || res.length === 0)
      }
      try {
        const res = await getDescriptions(
          checkList.map(i => {
            return { full_desc: i.full_desc, fy_code }
          }),
        )
        console.log(res)
        if (res.length === checkList.length) {
          checkList.forEach((item, index) => {
            const isNew = res[index].length === 0
            item.indexs.forEach(i => {
              this.$set(data[i], 'desc_is_new', isNew)
            })
          })
        }
      } catch (e) {
        console.error(e)
      }
    },
    async checkPayee() {
      const payee = this.form.vc_payee
      if (!payee || payee.replace(/ /g, '').length === 0) {
        return
      }
      try {
        const res = await searchCompanies({ name: payee })
        res.forEach(i => {
          i.value = i.comp_name
        })
        // this.form._payeeIsNew = !res.some(i => i.comp_name === payee)
        this.$set(this.form, '_payeeIsNew', !res.some(i => i.comp_name === payee))
      } catch (e) {
        console.error(e)
      }
    },
    dateToPdCode(date) {
      const d = new Date(date)
      if (!d) return ''
      let y, m
      y = d.getFullYear() % 100
      m = d.getMonth() + 1
      y = y < 10 ? '0' + y : '' + y
      m = m < 10 ? '0' + m : '' + m
      return y + m
    },
    fetchData() {
      this.loading = true
      this.loadUserPreference()
        .then(this.handleYear)
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.isInit) {
            this.preferences.filters.fy_code = this.parentFyCode
            const year = this.years.find(i => i.fy_code === this.preferences.filters.fy_code)
            if (year) {
              this.preferences.filters.fy_id = year.fy_id
              if (this.parentPdCode) {
                this.preferences.filters.pd_code = this.parentPdCode
              }
            }
            this.isInit = true
          } else {
            return this.updateChildPreferenceByName('fy_id')
          }
        })
        .then(() => {})
        .then(() => {
          if (
            (!this.preferences.filters.fy_id || !this.preferences.filters.fy_code) &&
            this.years.length > 0
          ) {
            const year = this.years[0]
            this.preferences.filters.fy_id = year.fy_id
            this.preferences.filters.fy_code = year.fy_code
            this.fy_code = year.fy_code
          }
        })
        .then(() => {
          if (this.preferences.filters.fy_id) {
            return getYear(this.preferences.filters.fy_id)
          } else {
            return null
          }
        })
        .then(res => {
          if (res) {
            this.OptionObj = res
            console.log(this.OptionObj.periods, 'form')
            this.OptionObj.periods.forEach(item => {
              console.log(item.pd_name, 'item')
              if (item.pd_name === this.getYearMonth(this.form.vc_date)) {
                if (item.pd_status === 'O') {
                  this.form.IfShow = true
                } else {
                  this.form.IfShow = false
                }
              }
              this.form.canEdit = !this.form.vc_rdate
              if (['P', 'R', 'C', 'T', 'J'].includes(this.vt_category) && this.form.vc_rdate) {
                this.form.canEdit = true
              }
            })
            console.log(this.OptionObj, 'OptionObj')
            try {
              this.month_list = [this.allMonth, ...res.periods]
            } catch (e) {
              console.error(e)
            }
            !this.preferences.filters.pd_code && (this.preferences.filters.pd_code = '')
          }
        })
        .then(() => fetchVoucherTypes({ vt_category: this.vt_category }))
        .then(res => {
          this.voucherTypeList = res
          this.current_vc_id = this.form.vc_id
        })
        .then(this.updateChildPreference)
        .then(() => {
          if (!this.isInit2) {
            this.preferences.filters.fy_code = this.parentFyCode
            const year = this.years.find(i => i.fy_code === this.preferences.filters.fy_code)
            if (year) {
              this.preferences.filters.fy_id = year.fy_id
              this.preferences.filters.pd_code = this.parentPdCode
            }
            this.isInit2 = true
          }
        })
        .then(this.loadTableData)
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.handleYear()
          this.loading = false
        })
    },
    handleYear() {
      if (!this.form.vc_date) return Promise.reject()
      return new Promise((resolve, reject) => {
        searchByDate(this.form.vc_date)
          .then(res => {
            this.currentYear = res
            this.fy_code = res.fy_code
            if (!this.preferences.filters.fy_code && this.lg_id) {
              this.$set(this.preferences.filters, 'fy_id', res.fy_id)
              this.$set(this.preferences.filters, 'fy_code', res.fy_code)
              this.$set(this.preferences.filters, 'pd_code', res.pd_code)
              this.$set(this.form, 'fy_code', res.fy_code)
            }
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onCancel() {
      // this.$router.back()
      this.gotoList()
    },
    handleDescCopyUp(desc) {
      this.$set(this.form, 'vc_summary', desc)
    },
    onAddRow() {
      this.$refs.DTable.addRow()
    },
    onDeleteRow() {
      this.$refs.DTable.deleteRow()
    },
    onSave() {
      this.$refs.summary
        .validate()
        .then(() => {
          const fy_code = this.parentFyCode
          const vc_id = this.form.vc_id
          const vt_category = this.vt_category
          const vc_no = this.form.vc_no
          const vt_code = this.form.vt_code
          const vc_date = this.form.vc_date
          const vc_summary = this.form.vc_summary
          const vc_method = this.form.vc_method
          const ref = this.form.ref
          const vc_chq_date = this.form.vc_chq_date
          const vc_payee = this.form.vc_payee
          const vc_st_code = this.form.vc_st_code
          const vc_extra = this.form.vc_extra
          const vc_receipt = this.form.vc_receipt
          const ledgers_json = this.tableData
          if (!ledgers_json || ledgers_json.length === 0) {
            this.$message.error(this.$t('daily.voucher.message.inputVoucherInfo'))
            return
          }
          if (this.editObject) {
            editVoucher({
              fy_code,
              vt_category,
              vc_id,
              vc_no,
              vt_code,
              vc_date,
              vc_summary,
              vc_method,
              ref,
              vc_chq_date,
              vc_payee,
              vc_st_code,
              vc_extra,
              vc_receipt,
              ledgers_json,
            }).then(res => {
              this.$message.success(this.$t('message.editSuccess'))
              this.$emit('onCancel', true)
              this.gotoList()
            })
          } else {
            // add
            createVoucher({
              vc_no,
              vt_category,
              vt_code,
              vc_date,
              vc_summary,
              vc_method,
              ref,
              vc_chq_date,
              vc_payee,
              vc_st_code,
              vc_extra,
              vc_receipt,
              ledgers_json,
            }).then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
              this.gotoList()
            })
          }
        })
        .catch(() => {})
    },
    onEdit() {
      this.$router.push({
        name: this.$route.matched[1].name + 'Edit',
        params: {
          action: 'edit',
          isView: false,
          lg_id: this.lg_id,
          editObject: this.form,
          parentFyCode: this.fy_code,
          vt_category: this.vt_category,
        },
      })
    },
    onDelete() {
      const fy_code = this.fy_code
      const vc_id = this.form.vc_id
      const vc_no = this.form.vc_no
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}: ${vc_no}` + '?',
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          return new Promise((resolve, reject) => {
            deleteVoucher({ fy_code, vc_id })
              .then(res => {
                // this.loadTableData()
                this.form.vc_no = ''
                this.form.vc_id = ''
                this.current_vc_id = ''
                this.current_vc_no = ''
                this.loadTableData(true)
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    gotoList() {
      this.$router.push(this.$route.matched[1])
    },
    enquiryGrantFetch() {
      if (!this.voucherTypeList || !this.voucherTypeList.length) return
      let item
      if (this.preferences.filters.vt_code) {
        // 修改右邊津貼查詢
        item = this.voucherTypeList.find(i => i.vt_code === this.preferences.filters.vt_code)
      }
      if (!item) {
        item = this.voucherTypeList[0]
      }
      const ac_code = item.vt_ac_code
      const fund_id = item.fund_id
      if (!ac_code) return

      const fy_id = this.preferences.filters.fy_id
      const fy_code = this.preferences.filters.fy_code
      if (!fy_id || !fy_code) return

      const pd_code = this.preferences.filters.pd_code
      this.$bus.emit('enquiryGrantFetch', fund_id, ac_code, fy_id, fy_code, pd_code)
    },
    onChangeVoucherType(val) {
      // if (val) {
      //   // 修改右邊津貼查詢
      //   const item = this.voucherTypeList.find(i => i.vt_code === val)
      //   if (item) {
      //     this.$bus.emit('enquiryGrantFetch', item.fund_id, item.vt_ac_code)
      //   }
      // }
      this.enquiryGrantFetch()
      this.loadTableData(true)
    },
    loadTableData(isNew) {
      const fy_code = this.preferences.filters.fy_code
      if (!fy_code) {
        return null
      }
      this.loading = true
      const pd_code = this.preferences.filters.pd_code
      const vt_code = this.preferences.filters.vt_code
      return new Promise((resolve, reject) => {
        fetchVouchers({
          fy_code,
          pd_code,
          vt_code,
          vt_category: this.vt_category,
        })
          .then(res => {
            console.log(this.OptionObj, 'OptionObj1')

            this.voucherList = res

            // resolve(res)
            if (res && res.length > 0) {
              this.current_vc_id = this.form.vc_id
              return res[0]
            } else {
              // TODO 刪除傳票號，如果沒有其他傳票，退出到列表
              // this.current_vc_id = ''
              // this.gotoList()
              return Promise.reject()
            }
          })
          .then(item => isNew && this.loadVoucher(fy_code, item.vc_id))

          .catch(() => {})
          .finally(() => {
            this.loading = false
          })
      })
    },
    onChangeYear(val) {
      const item = this.years.find(i => i.fy_id === val)
      if (item) this.preferences.filters.fy_code = item.fy_code

      if (this.preferences.filters.fy_id) {
        getYear(this.preferences.filters.fy_id)
          .then(res => {
            this.month_list = [this.allMonth, ...res.periods]
            this.preferences.filters.pd_code = ''
          })
          .then(() => {
            this.enquiryGrantFetch()
            this.loadTableData(true)
          })
      } else {
        this.enquiryGrantFetch()
        this.loadTableData(true)
      }
    },
    onChangeMonth() {
      this.enquiryGrantFetch()
      this.loadTableData(true)
    },
    onAdd() {
      // this.currentView = 'add'
      // this.editObject = null
      // this.isView = false
      this.$router.push({
        name: this.$route.matched[1].name + 'Add',
        params: {
          action: 'add',
          vt_category: this.vt_category,
        },
      })
    },
    onCopy() {
      this.$router.push({
        name: this.$route.matched[1].name + 'Add',
        params: {
          action: 'copy',
          isView: false,
          editObject: this.form,
          parentFyCode: this.fy_code,
          vt_category: this.vt_category,
        },
      })
    },
    onReverseCopy() {
      const vt_category =
        this.vt_category === 'P' ? 'R' : this.vt_category === 'R' ? 'P' : this.vt_category

      fetchVoucherTypes({ vt_category: vt_category, vt_ac_code: this.form.vt_ac_code })
        .then(res => {
          if (res.length === 0) {
            return Promise.reject(this.$t('message.noReverseVoucherType'))
          } else {
            const copyForm = this.form
            copyForm.vt_category = vt_category
            copyForm.vt_code = res[0].vt_code
            copyForm.fund_id = res[0].fund_id
            copyForm.fund_name_cn = res[0].fund_name_cn
            copyForm.fund_name_en = res[0].fund_name_en

            let routeName = ''
            switch (vt_category) {
              case 'P':
                routeName = 'dailyPayment'
                break
              case 'C':
                routeName = 'dailyPettyCash'
                break
              case 'R':
                routeName = 'dailyReceipt'
                break
              case 'T':
                routeName = 'dailyBankTransfer'
                break
              case 'J':
                routeName = 'dailyJournal'
                break
              default:
                routeName = this.$router.matched[1].name
            }
            this.$router.push({
              name: routeName + 'Add',
              params: {
                action: 'reverse_copy',
                isView: false,
                editObject: copyForm,
                parentFyCode: this.fy_code,
                vt_category: vt_category,
              },
            })
          }
        })
        .catch(err => {
          return Promise.reject(err)
        })
    },
    changeVoucher(vc_id) {
      this.loadVoucher(this.preferences.filters.fy_code, vc_id)
    },
    getCurrentIndex() {
      const list = this.voucherList
      for (let i = 0; i < list.length; i++) {
        if (list[i].vc_id === this.current_vc_id) {
          return i
        }
      }
      return -1
    },
    prevVoucher() {
      const prevIndex = this.getCurrentIndex() - 1
      const minIndex = 0
      if (prevIndex >= minIndex) {
        this.changeVoucher(this.voucherList[prevIndex].vc_id)
        this.handleEnquirySelected()
      }
    },
    nextVoucher() {
      const nextIndex = this.getCurrentIndex() + 1
      const maxIndex = this.voucherList.length
      if (nextIndex < maxIndex) {
        this.changeVoucher(this.voucherList[nextIndex].vc_id)
        this.handleEnquirySelected()
      }
    },
    handleEnquirySelected() {
      this.$bus.emit('enquirySetCurrentRow')
    },
    onPrintEnvelope() {
      if (!this.canPrintEnvelope) return
      const company_id = this.form.company_id
      if (company_id) {
        this.handlerPrintEnvepole([company_id])
      }
    },
    onPrintCheque(preview) {
      if (!this.canPrintCheque) return
      const isPreview = preview === 1
      const voucherType = this.voucherTypeList.find(i => i.vt_code === this.form.vt_code)
      const voucher = this.form
      if (voucherType) {
        const chq_template_code = voucherType.chq_template_code
        this.handlerPrintCheque(voucher, chq_template_code, isPreview)
      }
    },
    async onExport() {
      try {
        const fy_code = this.fy_code
        const vc_no = this.form.vc_no || undefined
        const res = await exportVoucher({ fy_code, vc_no })
        console.log(res)
        const result = await exportExcel(res)
        console.log(result)
      } catch (e) {
        console.error(e)
      }
    },
    canEditVoucher(row) {
      console.log(row)
      console.log(this.user_type)
      console.log(this.user_id)
      if (this.user_type === 'G') {
        if (this.user_id !== row.entry_user_id) {
          return false
        }
      }

      return row.IfShow && row.canEdit
    },
    getYearMonth(date) {
      var dateStr = date
      var reg = /^(\d{4})-(\d{1,2})-(\d{1,2})$/
      dateStr.match(reg)
      return RegExp.$2 + '/' + RegExp.$1
    },
  },
}
</script>

<style lang="scss" scoped>
i {
  font-size: 20px;
  vertical-align: middle;
  line-height: 25px;
}
.payment-info {
  margin: 5px 0;
  height: 100%;
  overflow-y: auto;
  /*/deep/ .el-input--medium .el-input__icon, /deep/ .el-input__prefix{*/
  /deep/ {
    .el-form-item--medium .el-form-item__content,
    .el-form-item--medium .el-form-item__label,
    .el-input--medium .el-input__inner,
    .el-input--medium .el-input__icon {
      line-height: 25px !important;
      height: 25px !important;
    }
    /*.el-form-item__content{*/
    /*  width: calc(100% - 80px);*/
    /*}*/
    .actions {
      .el-input {
        line-height: 25px;
        height: 25px;
        max-width: 150px;
      }
      .edit-button {
        button {
          vertical-align: middle;
          margin-left: 3px;
        }
      }
    }

    .filter,
    .actions {
      min-width: 555px;
    }

    /*[class^='el-input'],*/
    [class^='el-form'],
    .el-input__icon {
      vertical-align: middle;
    }
    .el-form-item__label {
      vertical-align: sub;
      padding: 0 5px 0 0;
    }
    .el-button--mini {
      padding: 5px 10px;
    }
    .el-button + .el-button {
      margin-left: 5px;
    }
    .el-radio + .el-radio {
      margin-left: 0px;
    }
    .el-input--suffix .el-input__inner {
      padding-right: 3px;
    }
    .el-radio__label {
      padding-left: 2px;
    }
    .el-form--inline .el-form-item__content {
      vertical-align: middle;
    }
  }
  .info-form {
    padding: 5px;
  }
  .actions {
    /*background: #CDEFFF;*/
    padding: 2px 0;
  }
  .info {
    background: #f4f4f4;
  }
  /*.edac-icon{*/
  /*  color:#68AFFF;*/
  /*}*/

  .edac-icon {
    &.edac-icon-import,
    &.edac-icon-export {
      float: right;
      background: #409eff;
      color: #ffffff;
      height: 20px;
      width: 20px;
      text-align: center;
      vertical-align: middle;
      line-height: 20px;
      margin-top: 5px;
      margin-right: 2px;
      border-radius: 2px;
    }
  }
}
</style>
