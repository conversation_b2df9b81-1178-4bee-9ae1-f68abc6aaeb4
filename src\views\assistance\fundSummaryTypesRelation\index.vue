<template>
  <div class="app-container">
    <VBreadCrumb :show="true" class="breadcrumb" />

    <el-form
      ref="form"
      v-loading="loading"
      :model="preferences.filters"
      label-position="right"
      label-width="80px"
    >
      <!-- 學年 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.fundSummaryTypesRelation.year')"
        class="year"
        prop="year"
      >
        <el-select v-model="preferences.filters.year">
          <el-option
            v-for="item in years"
            :key="item.fy_id"
            :value="item.fy_code"
            :label="item.fy_name"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onImport">
          {{ $t('button.import') }}
        </el-button>
        <el-button size="mini" type="primary" @click="onExport">
          {{ $t('button.export') }}
        </el-button>
      </el-form-item>
    </el-form>
    <!-- import 對話框 -->
    <!--    <el-dialog-->
    <!--      v-loading="loading"-->
    <!--      :title="$t('file.excelImport')"-->
    <!--      :visible.sync="importDialog"-->
    <!--      class="upload-dialog"-->
    <!--      width="450px"-->
    <!--    >-->
    <!--      <UploadExcel :on-success="handleImport" :on-template="onExport"/>-->
    <!--    </el-dialog>-->
    <import-dialog
      :visible.sync="importDialog"
      :on-success="handleImport"
      :on-template="onExport"
      :multiple="true"
    />
  </div>
</template>

<script>
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import { mapGetters } from 'vuex'
import loadPreferences from '@/views/mixins/loadPreferences'
import UploadExcel from '@/components/UploadExcel/index'
import { fetchYears } from '@/api/master/years'
import {
  exportFundSummaryTypesRelation,
  importFundSummaryTypesRelation,
} from '@/api/assistance/fundSummary'
import { exportExcel, importExcelMultiple } from '@/utils/excel'
import ImportDialog from '@/components/ImportDialog/index'

export default {
  name: 'AssistanceFundSummaryTypesRelation',
  components: {
    VBreadCrumb,
    ImportDialog,
    UploadExcel,
  },
  mixins: [loadPreferences],
  data() {
    return {
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      preferences: {
        filters: {
          year: '',
        },
      },
      years: [],
      importDialog: false,
    }
  },
  computed: {
    ...mapGetters(['system', 'currentYear']),
  },
  created() {
    console.log('initData')
    this.initData()
    this.saveUserLastPage()
  },
  methods: {
    async initData() {
      this.loading = false
      try {
        this.years = await fetchYears()
        await this.loadUserPreference()
        await this.$nextTick()
        if (!this.preferences.filters.year && this.currentYear) {
          this.preferences.filters.year = this.currentYear.fy_code
        }
      } catch (e) {
        console.log(e)
      }
      this.loading = false
    },
    async onImport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return

      this.importDialog = true
      // this.loading = true
      // importExcel(this, importDepartments, results, header)
      //   .then(() => this.fetchTree())
      //   .catch(() => {})
      //   .finally(() => {
      //     this.loading = false
      //     this.importDialog = false
      //   })
    },
    async handleImport(data) {
      console.log(data)
      this.loading = true
      const fy_code = this.preferences.filters.year

      return importExcelMultiple(
        this,
        importFundSummaryTypesRelation,
        {
          cate_data_json: data['Financial Statement Category'],
          rel_data_json: data['Category Relation'],
        },
        { fy_code },
      )
        .then(this.initData)
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.importDialog = false
        })
    },
    async onExport() {
      const valid = await this.$refs['form'].validate()
      if (!valid) return
      try {
        this.loading = true
        const fy_code = this.preferences.filters.year
        const res = await exportFundSummaryTypesRelation({ fy_code })
        await exportExcel(res, '')
        this.$message.success(this.$t('file.exportSuccess'))

        // this.loading = true
        // exportDepartments()
        //   .then(res => exportExcel(res, this.exportFileName))
        //   .then(() => {
        //     this.$message.success(this.$t('file.exportSuccess'))
        //   }).catch(() => {
        //   this.$message.error(this.$t('file.exportError'))
        // }).finally(() => {
        //   this.loading = false
        // })
      } catch (e) {
        this.$message.error(this.$t('file.exportError'))
        console.log(e)
      }
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.year {
  width: 400px;
}
</style>
