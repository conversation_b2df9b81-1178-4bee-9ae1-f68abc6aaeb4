<script>
import { mapGetters } from 'vuex'
import { amountFormat } from '@/utils'
// import dateUtil from '@/utils/date'
import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import { isInitPDF, initTips, openPdf, getUrlBase64, mm2pt } from '@/utils/pdf/index'
import {
  generateSchoolInfo,
  generatePageInfo,
  generateSign,
  // lineStyle,
  lineWidth,
  // pdfStyle,
  lineColor,
  generateGeneralHeader,
  generateWidths,
  margin_bottom_offset,
  font_size_fotter,
} from '@/utils/pdf/generator'
export default {
  name: 'HandlePDF',
  mixins: [loadPrintoutSetting],
  data() {
    return {
      ps_code: 'pdfbudget',
    }
  },
  computed: {
    ...mapGetters(['remoteServerInfo', 'school']),
  },
  methods: {
    onPrint() {
      if (!isInitPDF) {
        return initTips()
      }
      let printSetting
      this.loadPrintoutSetting()
        .then(ps => {
          printSetting = ps
          return ps
        })
        .then(this.formatPrintData)
        .then(({ schoolInfo, pageInfo, columns, tableData }) => {
          this.showPDF({
            schoolInfo,
            pageInfo,
            columns,
            tableData,
            printSetting,
          })
        })
        .catch(err => {
          console.error('onPrint', err)
        })
    },
    formatPrintData(printSetting) {
      return new Promise(async(resolve, reject) => {
        const $t = this.$t.bind(this)
        const rawData = this.tableData
        const langKey = 'setting.printout.budget.budgetList.label.'
        const language = printSetting.language

        const columnData = printSetting.columnData
          .filter(a => a.position)
          .sort((a, b) => a.position - b.position)

        const rsi = this.remoteServerInfo
        const logoURL = `${rsi.protocol}://${rsi.ip}:${rsi.port}/${rsi.remoteProjectName}/${rsi.uri}/${this.school.sch_gray_logo}`

        // 數據
        const schoolInfo = {
          name_cn: this.school.sch_name_cn,
          name_en: this.school.sch_name_en,
          logo: await getUrlBase64(logoURL),
        }

        const title = this.$t('budget.budgetList.label.budgetList', language)
        // let dateStr = ''
        // const date = this.preferences.filters.selectedPeriod
        // if (date && date.length === 2) {
        //   dateStr = dateUtil.format(new Date(date[0]), 'dd/MM/yyyy') + ' - ' + dateUtil.format(new Date(date[1]), 'dd/MM/yyyy')
        // }
        // 右邊表格
        const yearValue = this.years.find(
          item => item.fy_code === this.preferences.filters.selectedYear,
        )
        const pageInfo = {
          title: title,
          filename: `${title}`,
          data: [
            {
              label: this.$t('budget.budgetList.label.year', language),
              value: yearValue ? yearValue.fy_name : '',
            },
          ],
        }

        const borderAll = [true, true, true, true]
        // const borderBottom = [false, false, false, true]
        // const borderTop = [false, true, false, false]
        // const borderX = [false, true, false, true]
        // const borderNone = [false, false, false, false]
        const columns = [[], []]

        const getCol = key => {
          const col = columnData.find(item => item.name === key)
          return col || { width: 50, alignment: 'center' }
        }
        const cols = ['budget_code', 'name', 'manager', 'budget_stage', 'budget_IE', 'amount']
        const colWidths = {}
        for (let i = 0; i < cols.length; i++) {
          colWidths[cols[i]] = getCol(cols[i])
        }

        const showLastYear = this.preferences.filters.lastYear
        // 表頭
        // 1. 編號
        columns[0].push({
          text: $t(langKey + 'budget_code', language),
          style: 'tableHeader',
          rowSpan: 2,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['budget_code'].width,
          border: borderAll,
        })
        columns[1].push({
          text: '',
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          width: colWidths['budget_code'].width,
          border: borderAll,
        })
        // 2. 預算項目
        columns[0].push({
          text: $t(langKey + 'name', language),
          style: 'tableHeader',
          rowSpan: 2,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['name'].width,
          border: borderAll,
        })
        columns[1].push({
          text: '',
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          width: colWidths['name'].width,
          border: borderAll,
        })
        // 3. 預算項目
        columns[0].push({
          text: $t(langKey + 'manager', language),
          style: 'tableHeader',
          rowSpan: 2,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['manager'].width,
          border: borderAll,
        })
        columns[1].push({
          text: '',
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          width: colWidths['manager'].width,
          border: borderAll,
        })
        // 4. 狀態
        columns[0].push({
          text: $t(langKey + 'budget_stage', language),
          style: 'tableHeader',
          rowSpan: 2,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['budget_stage'].width,
          border: borderAll,
        })
        columns[1].push({
          text: '',
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          width: colWidths['budget_stage'].width,
          border: borderAll,
        })
        if (showLastYear) {
          // 5. 上年度
          columns[0].push({
            text: $t(langKey + 'lastYear', language),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 4,
            alignment: 'center',
            width: colWidths['budget_IE'].width + colWidths['amount'].width * 3,
            border: borderAll,
          })
          for (let i = 0; i < 3; i++) {
            columns[0].push({
              text: '',
              rowSpan: 1,
              colSpan: 1,
              width: 100,
              border: borderAll,
            })
          }
          // 5. 分類
          columns[1].push({
            text: $t(langKey + 'budget_IE', language),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: colWidths['budget_IE'].width,
            border: borderAll,
          })
          // 6. 預算金額
          columns[1].push({
            text: $t(langKey + 'budget_amount', language),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: colWidths['amount'].width,
            border: borderAll,
          })
          // 7. 實際金額
          columns[1].push({
            text: $t(langKey + 'actual_amount', language),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: colWidths['amount'].width,
            border: borderAll,
          })
          // 8. 結餘
          columns[1].push({
            text: $t(langKey + 'balance', language),
            style: 'tableHeader',
            rowSpan: 1,
            colSpan: 1,
            alignment: 'center',
            width: colWidths['amount'].width,
            border: borderAll,
          })
        }
        // 9. 本年度
        columns[0].push({
          text: $t(langKey + 'thisYear', language),
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 5,
          alignment: 'center',
          width: colWidths['budget_IE'].width + colWidths['amount'].width * 4,
          border: borderAll,
        })
        for (let i = 0; i < 4; i++) {
          columns[0].push({
            text: '',
            border: borderAll,
          })
        }
        // 9. 分類
        columns[1].push({
          text: $t(langKey + 'budget_IE', language),
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['budget_IE'].width,
          border: borderAll,
        })
        // 10. 建議金額
        columns[1].push({
          text: $t(langKey + 'proposed_amount', language),
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['amount'].width,
          border: borderAll,
        })
        // 11. 確認金額
        columns[1].push({
          text: $t(langKey + 'approved_amount', language),
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['amount'].width,
          border: borderAll,
        })
        // 12. 實際金額
        columns[1].push({
          text: $t(langKey + 'actual_amount', language),
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['amount'].width,
          border: borderAll,
        })
        // 13. 結餘
        columns[1].push({
          text: $t(langKey + 'balance', language),
          style: 'tableHeader',
          rowSpan: 1,
          colSpan: 1,
          alignment: 'center',
          width: colWidths['amount'].width,
          border: borderAll,
        })

        // 主數據
        const tableData = []
        // const prevItem = {}
        // const sumRow = []
        // const sumIndex = 0

        const commonCols = ['budget_code', 'name_', 'manager_staff_name_', 'budget_stage']
        const contentColsI = [
          'last_budget_IE',
          'last_I_total_budget_amount',
          'last_I_actual_amount',
          'last_I_balance',
          'this_budget_IE',
          'this_I_total_proposed_amount',
          'this_I_total_approved_amount',
          'this_I_actual_amount',
          'this_I_balance',
        ]
        const contentColsE = [
          'last_budget_IE',
          'last_E_total_budget_amount',
          'last_E_actual_amount',
          'last_E_balance',
          'this_budget_IE',
          'this_E_total_proposed_amount',
          'this_E_total_approved_amount',
          'this_E_actual_amount',
          'this_E_balance',
        ]
        const newCell = col => {
          let key = col
          const style = 'tableContent'
          switch (col) {
            case 'name_':
              key = 'name'
              break
            case 'manager_staff_name_':
              key = 'manager'
              break
            case 'last_budget_IE':
            case 'this_budget_IE':
              key = 'budget_IE'
              break
            case 'last_I_total_budget_amount':
            case 'last_I_actual_amount':
            case 'last_I_balance':
            case 'this_I_total_proposed_amount':
            case 'this_I_total_approved_amount':
            case 'this_I_actual_amount':
            case 'this_I_balance':
            case 'last_E_total_budget_amount':
            case 'last_E_actual_amount':
            case 'last_E_balance':
            case 'this_E_total_proposed_amount':
            case 'this_E_total_approved_amount':
            case 'this_E_actual_amount':
            case 'this_E_balance':
              key = 'amount'
              break
          }
          const border = [true, true, true, true]
          return {
            name: col,
            text: '',
            alignment: colWidths[key].alignment,
            style: style,
            border,
          }
        }
        // sumIndex = 0
        rawData.forEach((item, rowIndex) => {
          const mergeIE = item.this_budget_IE + item.last_budget_IE
          const twoRow =
            item.this_budget_IE === 'B' ||
            item.last_budget_IE === 'B' ||
            (mergeIE.includes('I') && mergeIE.includes('E'))
          if (twoRow) {
            const rowI = []
            const rowE = []
            // 通用
            commonCols.forEach(col => {
              const cell = newCell(col)
              switch (col) {
                case 'name_':
                case 'manager_staff_name_':
                  cell.text = language === 'en' ? item[col + 'en'] : item[col + 'cn']
                  break
                case 'budget_stage':
                  cell.text = this.$t(
                    'budget.budgetSet.label.stage_' + item['budget_stage'].toLowerCase(),
                    language,
                  )
                  break
                default:
                  cell.text = item[col]
                  break
              }
              // cell.rowSpan = twoRow ? 2 : 1
              cell.rowSpan = 1
              rowI.push(cell)
              rowE.push(newCell(col))
            })

            contentColsI.forEach(col => {
              switch (col) {
                // 上年度
                case 'last_budget_IE':
                case 'last_I_total_budget_amount':
                case 'last_I_actual_amount':
                case 'last_I_balance':
                  if (showLastYear) {
                    const cell = newCell(col)
                    if ('IB'.includes(item.last_budget_IE)) {
                      if (col === 'last_budget_IE') {
                        cell.text = 'I'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowI.push(cell)
                  }
                  break
                // 本年度
                case 'this_budget_IE':
                case 'this_I_total_proposed_amount':
                case 'this_I_total_approved_amount':
                case 'this_I_actual_amount':
                case 'this_I_balance':
                  // if (showLastYear) {
                  {
                    const cell = newCell(col)
                    if ('IB'.includes(item.this_budget_IE)) {
                      if (col === 'this_budget_IE') {
                        cell.text = 'I'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowI.push(cell)
                  }
                  // }
                  break
              }
            })
            contentColsE.forEach(col => {
              switch (col) {
                // 上年度
                case 'last_budget_IE':
                case 'last_E_total_budget_amount':
                case 'last_E_actual_amount':
                case 'last_E_balance':
                  if (showLastYear) {
                    const cell = newCell(col)
                    if ('EB'.includes(item.last_budget_IE)) {
                      if (col === 'last_budget_IE') {
                        cell.text = 'E'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowE.push(cell)
                  }
                  break
                // 本年度
                case 'this_budget_IE':
                case 'this_E_total_proposed_amount':
                case 'this_E_total_approved_amount':
                case 'this_E_actual_amount':
                case 'this_E_balance':
                  // if (showLastYear) {
                  {
                    const cell = newCell(col)
                    if ('EB'.includes(item.this_budget_IE)) {
                      if (col === 'this_budget_IE') {
                        cell.text = 'E'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowE.push(cell)
                  }
                  break
              }
            })
            const r = []
            for (let i = 0; i < rowI.length; i++) {
              const item = rowI[i]
              if (rowE[i] && rowE[i].text) {
                item.text += '\n' + rowE[i].text
              }
              r.push(item)
            }
            tableData.push(r)
          } else {
            const row = []

            // 通用
            commonCols.forEach(col => {
              const cell = newCell(col)
              switch (col) {
                case 'name_':
                case 'manager_staff_name_':
                  cell.text = language === 'en' ? item[col + 'en'] : item[col + 'cn']
                  break
                case 'budget_stage':
                  cell.text = this.$t(
                    'budget.budgetSet.label.stage_' + item['budget_stage'].toLowerCase(),
                    language,
                  )
                  break
                default:
                  cell.text = item[col]
                  break
              }
              cell.rowSpan = 1
              row.push(cell)
            })
            if (showLastYear) {
              // 上年度
              for (let i = 0; i < 4; i++) {
                if (item.last_budget_IE === 'I') {
                  // 上年度 I
                  const col = contentColsI[i]
                  switch (col) {
                    case 'last_budget_IE':
                    case 'last_I_total_proposed_amount':
                    case 'last_I_total_approved_amount':
                    case 'last_I_total_budget_amount':
                    case 'last_I_actual_amount':
                    case 'last_I_balance':
                      if (showLastYear) {
                        const cell = newCell(col)

                        if ('IB'.includes(item.last_budget_IE)) {
                          if (col === 'last_budget_IE') {
                            cell.text = 'I'
                          } else {
                            cell.text = amountFormat(item[col])
                          }
                        }
                        row.push(cell)
                      }
                      break
                  }
                } else {
                  // 上年度 E
                  const col = contentColsE[i]
                  switch (col) {
                    // 上年度
                    case 'last_budget_IE':
                    case 'last_E_total_proposed_amount':
                    case 'last_E_total_approved_amount':
                    case 'last_E_total_budget_amount':
                    case 'last_E_actual_amount':
                    case 'last_E_balance':
                      if (showLastYear) {
                        const cell = newCell(col)
                        if ('EB'.includes(item.last_budget_IE)) {
                          if (col === 'last_budget_IE') {
                            cell.text = 'E'
                          } else {
                            cell.text = amountFormat(item[col])
                          }
                        }
                        row.push(cell)
                      }
                      break
                  }
                }
              }
            }
            // 本年度
            for (let i = 4; i < contentColsI.length; i++) {
              if (item.this_budget_IE === 'I') {
                // 本年度 I
                const col = contentColsI[i]
                switch (col) {
                  case 'this_budget_IE':
                  case 'this_I_total_proposed_amount':
                  case 'this_I_total_approved_amount':
                  case 'this_I_actual_amount':
                  case 'this_I_balance':
                    {
                      const cell = newCell(col)
                      if ('EB'.includes(item.this_budget_IE)) {
                        if (col === 'this_budget_IE') {
                          cell.text = 'E'
                        } else {
                          cell.text = amountFormat(item[col])
                        }
                      }
                      row.push(cell)
                    }
                    break
                }
              } else {
                // 本年度 E
                const col = contentColsE[i]
                switch (col) {
                  // 本年度
                  case 'this_budget_IE':
                  case 'this_E_total_proposed_amount':
                  case 'this_E_total_approved_amount':
                  case 'this_E_total_budget_amount':
                  case 'this_E_actual_amount':
                  case 'this_E_balance':
                    {
                      const cell = newCell(col)
                      if ('EB'.includes(item.this_budget_IE)) {
                        if (col === 'this_budget_IE') {
                          cell.text = 'E'
                        } else {
                          cell.text = amountFormat(item[col])
                        }
                      }
                      row.push(cell)
                    }
                    break
                }
              }
            }

            tableData.push(row)
          }
        })

        // 合計行
        {
          const item = this.summary
          const mergeIE = item.this_budget_IE + item.last_budget_IE
          const twoRow =
            item.this_budget_IE === 'B' ||
            item.last_budget_IE === 'B' ||
            (mergeIE.includes('I') && mergeIE.includes('E'))
          if (twoRow) {
            const rowI = []
            const rowE = []

            // 通用
            commonCols.forEach(col => {
              const cell = newCell(col)
              cell.rowSpan = 2
              rowI.push(cell)
              rowE.push(newCell(col))
            })
            rowI[0].colSpan = 4
            rowI[0].alignment = 'right'
            rowI[0].text = this.$t('budget.budgetList.label.total', language)

            contentColsI.forEach(col => {
              switch (col) {
                // 上年度
                case 'last_budget_IE':
                case 'last_I_total_proposed_amount':
                case 'last_I_total_approved_amount':
                case 'last_I_total_budget_amount':
                case 'last_I_actual_amount':
                case 'last_I_balance':
                  if (showLastYear) {
                    const cell = newCell(col)
                    if ('IB'.includes(item.last_budget_IE)) {
                      if (col === 'last_budget_IE') {
                        cell.text = 'I'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowI.push(cell)
                  }
                  break
                // 本年度
                case 'this_budget_IE':
                case 'this_I_total_proposed_amount':
                case 'this_I_total_approved_amount':
                case 'this_I_actual_amount':
                case 'this_I_balance':
                  // if (showLastYear) {
                  {
                    const cell = newCell(col)
                    if ('IB'.includes(item.this_budget_IE)) {
                      if (col === 'this_budget_IE') {
                        cell.text = 'I'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowI.push(cell)
                  }
                  break
              }
            })
            contentColsE.forEach(col => {
              switch (col) {
                // 上年度
                case 'last_budget_IE':
                case 'last_E_total_proposed_amount':
                case 'last_E_total_approved_amount':
                case 'last_E_total_budget_amount':
                case 'last_E_actual_amount':
                case 'last_E_balance':
                  if (showLastYear) {
                    const cell = newCell(col)
                    if ('EB'.includes(item.last_budget_IE)) {
                      if (col === 'last_budget_IE') {
                        cell.text = 'E'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowE.push(cell)
                  }
                  break
                // 本年度
                case 'this_budget_IE':
                case 'this_E_total_proposed_amount':
                case 'this_E_total_approved_amount':
                case 'this_E_actual_amount':
                case 'this_E_balance':
                  // if (showLastYear) {
                  {
                    const cell = newCell(col)
                    if ('EB'.includes(item.this_budget_IE)) {
                      if (col === 'this_budget_IE') {
                        cell.text = 'E'
                      } else {
                        cell.text = amountFormat(item[col])
                      }
                    }
                    rowE.push(cell)
                  }
                  break
              }
            })
            tableData.push(rowI)
            tableData.push(rowE)
          } else {
            const row = []

            // 通用
            commonCols.forEach(col => {
              const cell = newCell(col)
              cell.rowSpan = 1
              row.push(cell)
            })
            row[0].colSpan = 4
            row[0].alignment = 'right'
            row[0].text = this.$t('budget.budgetList.label.total', language)

            if (showLastYear) {
              // 上年度
              for (let i = 0; i < 4; i++) {
                if (item.last_budget_IE === 'I') {
                  // 上年度 I
                  const col = contentColsI[i]
                  switch (col) {
                    case 'last_budget_IE':
                    case 'last_I_total_proposed_amount':
                    case 'last_I_total_approved_amount':
                    case 'last_I_total_budget_amount':
                    case 'last_I_actual_amount':
                    case 'last_I_balance':
                      if (showLastYear) {
                        const cell = newCell(col)
                        if ('IB'.includes(item.last_budget_IE)) {
                          if (col === 'last_budget_IE') {
                            cell.text = 'I'
                          } else {
                            cell.text = amountFormat(item[col])
                          }
                        }
                        row.push(cell)
                      }
                      break
                  }
                } else {
                  // 上年度 E
                  const col = contentColsE[i]
                  switch (col) {
                    // 上年度
                    case 'last_budget_IE':
                    case 'last_E_total_proposed_amount':
                    case 'last_E_total_approved_amount':
                    case 'last_E_total_budget_amount':
                    case 'last_E_actual_amount':
                    case 'last_E_balance':
                      if (showLastYear) {
                        const cell = newCell(col)
                        if ('EB'.includes(item.last_budget_IE)) {
                          if (col === 'last_budget_IE') {
                            cell.text = 'E'
                          } else {
                            cell.text = amountFormat(item[col])
                          }
                        }
                        row.push(cell)
                      }
                      break
                  }
                }
              }
            }

            // 本年度
            for (let i = 4; i < contentColsI.length; i++) {
              if (item.this_budget_IE === 'I') {
                // 本年度 I
                const col = contentColsI[i]
                switch (col) {
                  case 'this_budget_IE':
                  case 'this_I_total_proposed_amount':
                  case 'this_I_total_approved_amount':
                  case 'this_I_total_budget_amount':
                  case 'this_I_actual_amount':
                  case 'this_I_balance':
                    {
                      const cell = newCell(col)
                      if ('IB'.includes(item.this_budget_IE)) {
                        if (col === 'this_budget_IE') {
                          cell.text = 'I'
                        } else {
                          cell.text = amountFormat(item[col])
                        }
                      }
                      row.push(cell)
                    }
                    break
                }
              } else {
                // 本年度 E
                const col = contentColsE[i]
                switch (col) {
                  // 本年度
                  case 'this_budget_IE':
                  case 'this_E_total_proposed_amount':
                  case 'this_E_total_approved_amount':
                  case 'this_E_total_budget_amount':
                  case 'this_E_actual_amount':
                  case 'this_E_balance':
                    {
                      const cell = newCell(col)
                      if ('EB'.includes(item.this_budget_IE)) {
                        if (col === 'this_budget_IE') {
                          cell.text = 'E'
                        } else {
                          cell.text = amountFormat(item[col])
                        }
                      }
                      row.push(cell)
                    }
                    break
                }
              }
            }

            tableData.push(row)
          }
        }

        resolve({
          schoolInfo,
          pageInfo,
          columns,
          tableData,
        })
      })
    },
    async showPDF({ schoolInfo, pageInfo, columns, tableData, printSetting }) {
      const $t = this.$t.bind(this)

      const margin_left = mm2pt(printSetting.margin_left)
      const margin_top = mm2pt(printSetting.margin_top)
      const margin_right = mm2pt(printSetting.margin_right)
      let margin_bottom = mm2pt(printSetting.margin_bottom) + margin_bottom_offset // 預留頁尾位置

      const page_width = mm2pt(printSetting.page_width)
      const page_height = mm2pt(printSetting.page_height)

      const sign_height = mm2pt(printSetting.sign_height)
      const sign_space = 20 // mm2pt(printSetting.sign_space)

      const title_width = mm2pt(printSetting.title_width)

      const bottom_sign = printSetting.sign_style.toString() === '2'

      const font_size_content = 12
      const font_size_title = 13
      const font_size_school_name_cn = 15
      const font_size_school_name_en = 12
      const font_size_signature = 11

      // 表格寬度
      const widths = generateWidths(columns[1])

      // 學校信息
      const schoolTable = generateSchoolInfo(
        schoolInfo.name_cn,
        schoolInfo.name_en,
        schoolInfo.logo,
      )
      // 頁面信息
      const pageTable = generatePageInfo(
        pageInfo.title,
        pageInfo.filename,
        pageInfo.data,
        title_width,
        margin_right,
      )
      // 頁頭，包含LOGO，頁面信息
      const pageHeader = generateGeneralHeader(schoolTable, pageTable, columns[0].length)

      // 簽名設置
      const signColumn = printSetting.sign_data.slice(0, Number(printSetting.sign_num))
      const signTable = generateSign(
        printSetting.sign_line,
        signColumn,
        printSetting.language,
        sign_height,
        sign_space,
        margin_left,
        margin_right,
        printSetting.font_size_signature,
        bottom_sign,
      )
      if (bottom_sign) {
        // 簽名固定底部時，需預留簽名位置
        margin_bottom += signTable.height // + 20// +50
      }

      const docDefinition = {
        info: {
          title: pageInfo.filename,
          author: 'Norray',
          subject: pageInfo.filename,
        },
        content: [
          {
            // Content
            width: '100%',
            style: 'tableExample',
            table: {
              // unbreakable: true,
              dontBreakRows: true,
              // dontBreakRows: (row) => row.pageNumbers.length > 1,
              // keepWithHeaderRows: 2,
              // widths: ['*', ...[...Array(columns[0].length - 1)].map(() => '*')],
              widths: widths,
              // heights: tableData.map((e, i) => i < 3 ? Number(printSetting.table_header_height) / 2 : 'auto'),
              // headerRows: columns.length + 1,
              headerRows: 3,
              body: [
                pageHeader, // 頁頭
                ...columns, // 數據表頭
                ...tableData, // 數據
              ],
            },
            layout: {
              defaultBorder: false,
              vLineWidth: lineWidth,
              hLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
            },
          },
        ],
        makeFooter: function(printSetting, language) {
          const funcStr = 'currentPage, pageCount, pageSize'
          const funcBody = `
            const data = []
            const printSetting = ${JSON.stringify(printSetting)}
            const bottom_sign = printSetting.sign_style.toString() === '2'
            const signTable = ${JSON.stringify(signTable)}
            const font_size_page_num = ${printSetting.font_size_page_num}
            if (bottom_sign) {
              data.push(signTable)
            }
            const language = '${language}'
            let text = ''
            if (language === 'zh-hk') {
              text = '第 ' + currentPage + ' / ' + pageCount + ' 頁'
            } else {
              text = 'Page ' + currentPage + ' / ' + pageCount
            }
            data.push({
              text,
              alignment: 'center',
              fontSize: font_size_page_num
            })
            return data
          `
          docDefinition.footer = new Function(funcStr, funcBody)
        },
        styles: {
          tableExample: {
            fontSize: font_size_content,
            margin: [0, 0, 0, 0],
          },
          tableHeader: {
            bold: true,
            fontSize: font_size_title,
            height: Number(printSetting.table_header_height),
            color: 'black',
            // fillColor: '#CCCCCC',
            alignment: 'center',
          },
          tableFooter: {
            bold: true,
            fontSize: font_size_title,
            height: Number(printSetting.table_footer_height),
            color: 'black',
            // fillColor: '#CCCCCC'
          },
          schoolNameCN: {
            bold: true,
            fontSize: font_size_school_name_cn,
            color: 'black',
          },
          schoolNameEN: {
            bold: true,
            fontSize: font_size_school_name_en,
            color: 'black',
          },
          titleCell: {
            bold: true,
            fontSize: font_size_title,
          },
          tableContent: {
            bold: false,
            fontSize: font_size_content,
          },
          signCell: {
            fontSize: font_size_signature,
          },
        },
        pageSize: {
          width: page_width,
          height: page_height,
        },
        // pageOrientation: printSetting.page.orientation ? 'landscape' : 'portrait',
        pageMargins: [margin_left, margin_top, margin_right, margin_bottom],
      }
      const language1 = window.sessionStorage.getItem('language')
      docDefinition.makeFooter(printSetting, language1)
      if (printSetting.sign_style.toString() === '1') {
        // 浮動
        docDefinition.content.push(signTable)
      }
      console.log(JSON.stringify(docDefinition))
      await openPdf(docDefinition)
    },
  },
}
</script>
