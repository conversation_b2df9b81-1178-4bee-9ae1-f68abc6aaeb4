<template>
  <VueNumeric
    v-model="currentVal"
    :empty-value="0"
    :precision="precision"
    :min="min"
    :max="max"
    :minus="true"
    v-bind="$attrs"
    class="e-numeric"
    separator=","
  />
</template>
<script>
// import VueNumeric from 'vue-numeric'
import VueNumeric from './vue-numeric'

export default {
  name: 'ENumeric',
  components: {
    VueNumeric,
  },
  props: {
    value: {
      type: [String, Number],
      required: true,
    },
    max: {
      type: Number,
      default: 999999999.99,
    },
    min: {
      type: Number,
      default: -999999999.99,
    },
    precision: {
      type: Number,
      default: 2,
    },
    disableUpdate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentVal: this.value,
    }
  },
  watch: {
    currentVal: {
      immediate: false,
      handler(value) {
        if (this.disableUpdate) return
        this.$emit('input', value)
        this.$emit('change', value)
      },
    },
    value: {
      immediate: false,
      handler(value) {
        this.currentVal = value
      },
    },
  },
}
</script>
<style lang="scss" scoped>
input.e-numeric {
  margin: 0 2px;
  width: calc(100% - 4px);
  padding: 0 5px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  line-height: 25px;
  height: 25px;
  outline: none;
  text-align: right;
  color: #606266;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  &:hover {
    border-color: #c0c4cc;
  }
  &:focus {
    border-color: #409eff;
    outline: 0;
  }
}
</style>
