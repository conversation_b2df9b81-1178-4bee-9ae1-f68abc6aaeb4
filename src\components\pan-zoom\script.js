import panZoom from 'panzoom'
const PanZoomComponent = {
  name: 'panZoom',
  props: {
    options: Object,
    selector: String,
    showLocation: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      $panZoomInstance: null,
      instanceId: null,
      defaultOptions: {
        autocenter: true,
        bounds: true,
        transformOrigin: {
          x: 0.5,
          y: 0.5,
        },
      },
      init: false,
      currentScale: 1,
    }
  },
  created() {
    this.$panZoom = panZoom
    this.instanceId = this.generateRandomId(20)
  },
  mounted() {
    if (this.scene && !this.init) {
      this.init = true
      var _options = Object.assign({}, this.defaultOptions, this.options)
      this.$panZoomInstance = this.$panZoom(this.scene, _options)
      this.$panZoomInstanceId = this.instanceId
      this.attachEvents()
    }
  },
  computed: {
    scene() {
      var el
      var _wrapper = this.$el.querySelector('.vue-pan-zoom-scene')
      if (this.selector) {
        el = _wrapper.querySelector(this.selector)
      } else {
        el = _wrapper.querySelector('svg, object, embed')
        if (!el) {
          el = _wrapper.firstChild
        }
      }
      return el
    },
  },
  methods: {
    generateRandomId(l) {
      l = l || 16
      var chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
      var charsLength = chars.length
      var a = []

      for (var i = 0; i < l; i++) {
        a.push(chars.charAt(Math.floor(Math.random() * charsLength)))
      }
      return a.join('')
    },
    attachEvents() {
      this.$emit('init', this.$panZoomInstance, this.$panZoomInstanceId)

      this.$panZoomInstance.on('panstart', e => {
        this.$emit('panstart', e)
      })

      this.$panZoomInstance.on('panend', e => {
        this.$emit('panend', e)
      })

      this.$panZoomInstance.on('pan', e => {
        // console.log('pan', e)
        this.$emit('pan', e)
      })

      this.$panZoomInstance.on('zoom', e => {
        // console.log('zoom', e)
        this.$emit('zoom', e)
      })

      this.$panZoomInstance.on('transform', e => {
        this.$emit('transform', e)
      })

      this.$panZoomInstance.on('zoomend', e => {
        // console.log('zoomend', e)
        this.currentScale = e.scale
        this.$emit('zoomend', e)
      })
    },
    isPaused() {
      return this.$panZoomInstance.isPaused()
    },
    pause() {
      this.$panZoomInstance.pause()
    },
    resume() {
      this.$panZoomInstance.resume()
    },
    autoZoom(smooth = false, ratio = 0.98) {
      let clientX = 0
      let clientY = 0
      let toScaleValue = 0.5
      clientX = 0
      clientY = 0
      toScaleValue = 0
      const w = this.$refs.zoomScene.offsetWidth
      const h = this.$refs.zoomScene.offsetHeight
      const ww = this.$refs.zoomScene.firstElementChild.offsetWidth
      const hh = this.$refs.zoomScene.firstElementChild.offsetHeight

      const wScale = Math.min(w / ww, 1)
      const hScale = Math.min(h / hh, 1)
      toScaleValue = Math.min(wScale, hScale)
      toScaleValue = toScaleValue * ratio
      if (toScaleValue < this.options.minZoom) {
        toScaleValue = this.options.minZoom
      }
      if (toScaleValue > this.options.maxZoom) {
        toScaleValue = this.options.maxZoom
      }
      clientX = (w - ww * toScaleValue * ratio) / 2
      clientY = (h - hh * toScaleValue * ratio) / 2 / 2
      if (
        !isNaN(clientX) &&
        !isNaN(clientY) &&
        !isNaN(toScaleValue) &&
        isFinite(clientX) &&
        isFinite(clientY) &&
        isFinite(toScaleValue) &&
        toScaleValue > 0
      ) {
        this.$nextTick(() => {
          // const tf = this.$panZoomInstance.getTransform()
          // if (tf && tf.scale !== 0.98) {
          //   toScaleValue = 0.98 / tf.scale * toScaleValue
          // }
          console.log(clientX, clientY, toScaleValue)
          this.$panZoomInstance[smooth ? 'smoothZoomAbs' : 'zoomAbs'](
            clientX,
            clientY,
            toScaleValue,
          )
          // this.$panZoomInstance[smooth ? 'smoothZoomTo' : 'zoomTo'](clientX, clientY, toScaleValue)
          this.$panZoomInstance[smooth ? 'smoothMoveTo' : 'moveTo'](clientX, clientY)
          this.currentScale = toScaleValue

          this.$emit('zoom', {
            getTransform: () => ({
              x: clientX,
              y: clientY,
              scale: toScaleValue,
            }),
          })
        })
      }
    },
  },
}

export default PanZoomComponent
