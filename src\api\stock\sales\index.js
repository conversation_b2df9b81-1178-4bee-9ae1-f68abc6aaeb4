import request from '@/utils/request'

/**
 * 新增銷貨記錄
 * @param {string} fy_code 會計週期編號(如果是期初結餘,則不填)
 * @param {string} type 記錄類型,S=銷售,D=損毀/遺失,U=內部使用,W=註銷
 * @param {string} date 日期
 * @param {string} chq_no 支票編號
 * @param {string} voucher_no 傳票編號
 * @param {integer} stocks_json 貨品數量價格,格式: [{"sk_code":"001","qty":"1","price":"1.00"},...]
 */
export function createSales({ fy_code, type, date, chq_no, voucher_no, stocks_json }) {
  return request({
    url: '/sales-invoices/actions/create',
    method: 'post',
    data: {
      fy_code,
      type,
      date,
      chq_no,
      voucher_no,
      stocks_json,
    },
  })
}

/**
 * 更新銷貨記錄
 * @param {integer} sales_invoice_id 購貨記錄id
 * @param {string} type 記錄類型,S=銷售,D=損毀/遺失,U=內部使用,W=註銷
 * @param {string} date 日期
 * @param {string} chq_no 支票編號
 * @param {string} voucher_no 傳票編號
 * @param {integer} stocks_json 貨品數量價格,格式: [{"sk_code":"001","qty":"1","price":"1.00"},...]
 */
export function updateSales({ sales_invoice_id, type, date, chq_no, voucher_no, stocks_json }) {
  return request({
    url: '/sales-invoices/actions/update',
    method: 'post',
    data: {
      sales_invoice_id,
      type,
      date,
      chq_no,
      voucher_no,
      stocks_json,
    },
  })
}

/**
 * 刪除某購貨記錄詳情
 * @param {integer} sales_invoice_id 購貨記錄id
 */
export function deleteSales(sales_invoice_id) {
  return request({
    url: '/sales-invoices/actions/delete',
    method: 'post',
    data: {
      sales_invoice_id,
    },
  })
}

/**
 * 獲取購貨記錄列表
 * @param {string} fy_code 會計週期編號(如果是期初結餘,則不填)
 */
export function fetchSales({ fy_code }) {
  return request({
    url: '/sales-invoices',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 獲取某購貨記錄詳情
 * @param {integer} sales_invoice_id 賬目類別id
 */
export function getSales(sales_invoice_id) {
  return request({
    url: '/sales-invoices/actions/inquire',
    method: 'get',
    params: {
      sales_invoice_id,
    },
  })
}

export default { createSales, updateSales, deleteSales, fetchSales, getSales }
