import request from '@/utils/request'

/**
 * 返回新增后的職員類別id
 * @param {string} st_type_code 職員類別編號
 * @param {string} st_type_cn 職員類別中文
 * @param {string} st_type_en 職員類別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_st_type_id 父職員類別id
 * @param {integer} seq 所處層級里的位置
 */
export function createStaffType(
  st_type_code,
  st_type_cn,
  st_type_en,
  active_year,
  parent_st_type_id,
  seq,
) {
  return request({
    url: '/staff-types/actions/create',
    method: 'post',
    data: {
      st_type_code,
      st_type_cn,
      st_type_en,
      active_year,
      parent_st_type_id,
      seq,
    },
  })
}

/**
 * 修改職員類別
 * @param {integer} st_type_id 職員類別id
 * @param {string} st_type_code 職員類別編號
 * @param {string} st_type_cn 職員類別中文
 * @param {string} st_type_en 職員類別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_st_type_id 父職員類別id
 * @param {integer} seq 所處層級里的位置
 */
export function editStaffType(
  st_type_id,
  st_type_code,
  st_type_cn,
  st_type_en,
  active_year,
  parent_st_type_id,
  seq,
) {
  return request({
    url: '/staff-types/actions/update',
    method: 'post',
    data: {
      st_type_id,
      st_type_code,
      st_type_cn,
      st_type_en,
      active_year,
      parent_st_type_id,
      seq,
    },
  })
}

/**
 * 刪除職員類別
 * @param {integer} st_type_id 職員類別id
 */
export function deleteStaffType(st_type_id) {
  return request({
    url: '/staff-types/actions/delete',
    method: 'post',
    data: {
      st_type_id,
    },
  })
}

/**
 * 返回職員類別列表
 * @param {integer} st_type_id 職員類別id(返回結果將會除此id)
 * @param {string} fy_code 選擇的會計週期
 */
export function fetchStaffTypes({ st_type_id, fy_code }) {
  return request({
    url: '/staff-types',
    method: 'get',
    params: {
      st_type_id,
      fy_code,
    },
  })
}

/**
 * 獲取某職員類別詳情
 * @param {integer} st_type_id 職員類別id
 */
export function getStaffType(st_type_id) {
  return request({
    url: '/staff-types/actions/inquire',
    method: 'get',
    params: {
      st_type_id,
    },
  })
}

export default { createStaffType, editStaffType, deleteStaffType, fetchStaffTypes, getStaffType }
