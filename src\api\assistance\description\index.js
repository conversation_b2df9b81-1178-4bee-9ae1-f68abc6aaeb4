import request from '@/utils/request'

/**
 * 返回新增后的傳票明細id
 * @param {string} multi_ac_code 歸屬會計科目編號,可傳多個(逗號分隔)
 * @param {string} desc 傳票明細描述
 * @param {string} BIE 會計科目的BIE歸屬,可多選,例如 B,I,E
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @return {Promise}
 */
export function createDescription({ multi_ac_code, desc, BIE, active_year }) {
  return request({
    url: '/descriptions/actions/create',
    method: 'post',
    data: {
      multi_ac_code,
      desc,
      BIE,
      active_year,
    },
  })
}

/**
 * 修改傳票明細
 * @param {integer} desc_id 傳票明細id
 * @param {string} ac_code 歸屬會計科目編號
 * @param {string} desc 傳票明細描述
 * @param {string} BIE 會計科目的BIE歸屬,可多選,例如 B,I,<PERSON>
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @return {Promise}
 */
export function editDescription({ desc_id, ac_code, desc, BIE, active_year }) {
  return request({
    url: '/descriptions/actions/update',
    method: 'post',
    data: {
      desc_id,
      ac_code,
      desc,
      BIE,
      active_year,
    },
  })
}

/**
 * 刪除傳票明細
 * @param {integer} desc_id 傳票明細id
 * @return {Promise}
 */
export function deleteDescription(desc_id) {
  return request({
    url: '/descriptions/actions/delete',
    method: 'post',
    data: {
      desc_id,
    },
  })
}

/**
 * 返回所有傳票明細
 * @param {string} fy_code 活躍的會計週期編號
 * @param {string} desc 模糊搜索
 * @param {string} full_desc 模糊搜索
 * @param {string} ac_code 歸屬會計科目編號
 * @param {integer} fund_id 賬目類別id
 * @return {Promise}
 */
export function fetchDescriptions({ fy_code, desc, full_desc, ac_code, fund_id, BIE }) {
  return request({
    url: '/descriptions',
    method: 'get',
    params: {
      fy_code,
      desc,
      full_desc,
      ac_code,
      fund_id,
      BIE,
    },
  })
}

/**
 * 獲取某傳票明細詳情
 * @param {integer} desc_id 傳票明細id
 * @return {Promise}
 */
export function getDescription(desc_id) {
  return request({
    url: '/descriptions/actions/inquire',
    method: 'get',
    params: {
      desc_id,
    },
  })
}

/**
 * 返回所有傳票明細
 * @param {string} full_desc_arr 過賬的數據(數組) { fy_cod, full_desc }
 * @return {Promise}
 */
export function getDescriptions(full_desc_arr) {
  return request({
    url: '/descriptions/actions/inquire-full-desc',
    method: 'post',
    data: {
      full_desc_arr: full_desc_arr,
    },
  })
}

/**
 * 返回傳票明細別excel數據
 * @return {Promise}
 */
export function exportDescriptions() {
  return request({
    url: '/descriptions/actions/export',
    responseType: 'blob',
    method: 'get',
  })
}

/**
 * 匯入傳票明細excel數據
 * @param {string} data_json 匯入的數據
 * @return {Promise}
 */
export function importDescriptions(data_json) {
  return request({
    url: '/descriptions/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

export default {
  createDescription,
  editDescription,
  deleteDescription,
  fetchDescriptions,
  getDescription,
  exportDescriptions,
  importDescriptions,
}
