import request from '@/utils/request'

/**
 * 返回職員報表數據
 * @param {string} parent_st_type_id 職員組id
 * @param {string} st_code 職員編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} BIE  可傳:B,I,E,IE
 * @return {Promise}
 */
export function fetchStaffLedgers({ parent_st_type_id, st_code, begin_date, end_date, BIE }) {
  return request({
    url: '/reports/staffs/ledgers',
    method: 'get',
    params: {
      parent_st_type_id,
      st_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 返回職員報表數據 - 打印用
 * @param {string} parent_st_type_id 職員組id
 * @param {string} st_code 職員編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} BIE  可傳:B,I,E,IE
 * @return {Promise}
 */
export function fetchStaffLedgersForPrint({
  parent_st_type_id,
  st_code,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/staffs/ledgers-print',
    method: 'get',
    params: {
      parent_st_type_id,
      st_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

export default {
  fetchStaffLedgers,
  fetchStaffLedgersForPrint,
}
