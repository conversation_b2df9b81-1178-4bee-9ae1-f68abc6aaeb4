<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <div>
      <VBreadCrumb class="breadcrumb" />
      <div class="filter">
        <el-form :inline="true" label-width="60px" class="mini-form">
          <!-- 職員 -->
          <el-form-item :label="$t('filters.staff')">
            <SelectTree
              v-model="preferences.filters.st_code"
              :options="treeData"
              :props="{
                label: language === 'en' ? 'name_en' : 'name_cn',
                value: 'code',
                code: 'code',
                group_id: 'st_type_id',
                value_id: 'st_id',
                children: 'children',
              }"
              :group-id.sync="preferences.filters.parent_st_type_id"
              :select-group.sync="preferences.filters.st_selectGroup"
              :show-all-option="false"
              style="width: 300px"
              @change="onSelectStaff"
            />
          </el-form-item>

          <el-form-item :label="$t('filters.group')">
            <el-select v-model="preferences.filters.BIE" class="bank" style="width: 80px">
              <el-option key="all" :label="$t('filters.all')" value="" />
              <el-option v-for="item in groupList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>

          <!-- 時期 -->
          <el-form-item :label="$t('filters.period')">
            <!--            <el-date-picker-->
            <!--              v-model="preferences.filters.date_range"-->
            <!--              :clearable="false"-->
            <!--              :unlink-panels="false"-->
            <!--              :format="styles.dateFormat"-->
            <!--              :range-separator="$t('filters.to')"-->
            <!--              :start-placeholder="$t('placeholder.beginDate')"-->
            <!--              :end-placeholder="$t('placeholder.endDate')"-->
            <!--              type="daterange"-->
            <!--              value-format="yyyy-MM-dd"-->
            <!--              style="width: 250px"-->
            <!--              @change="onChangeDateRange"-->
            <!--            />-->
            <date-range
              :start-date.sync="preferences.filters.begin_date"
              :end-date.sync="preferences.filters.end_date"
            />
          </el-form-item>

          <!-- 年份 -->
          <el-form-item>
            <i class="el-icon-date" @click="showYearPicker = !showYearPicker" />
            <el-select
              v-if="showYearPicker"
              ref="year"
              v-model="selectedYearId"
              class="year"
              style="width: 100px"
              @change="onChangeYear"
            >
              <el-option
                v-for="item in years"
                :key="item.fy_id"
                :label="item.fy_name"
                :value="item.fy_id"
              />
            </el-select>
            <el-select
              v-if="showYearPicker"
              v-model="preferences.filters.selectedMonth"
              class="year"
              style="width: 100px"
            >
              <el-option
                :label="$t('filters.wholeYear')"
                value=""
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
              <el-option
                v-for="item in monthList"
                :key="item.pd_id"
                :label="item.pd_name"
                :value="item.pd_code"
                @click.native="onChangeMonth(preferences.filters.selectedMonth)"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click="reloadData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button
              v-if="hasPermission_Print"
              :loading="btnLoading"
              type="primary"
              size="mini"
              class="action-button"
              @click="onPagePrint"
            >
              {{ $t('button.print') }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="actions-icon">
          <i
            :title="$t('btnTitle.pageSetting')"
            class="edac-icon action-icon edac-icon-setting1"
            @click="onSetting"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelPage')"
            class="edac-icon action-icon edac-icon-excel"
            @click="onExport('PAGE')"
          />
          <i
            v-if="hasPermission_Output"
            :title="$t('btnTitle.exportExcelAll')"
            class="edac-icon action-icon edac-icon-excel_add"
            @click="onExport('ALL')"
          />
        </div>
      </div>
      <div class="cash-book-table">
        <ETable
          ref="table"
          v-loading="!loading && tableLoading"
          :data="tableData"
          :style-columns="styleColumns"
          :amount-columns="amountColumns"
          :lang-key="langKey"
          :show-index="true"
          :show-actions="true"
          :actions-min-width="5"
          :show-checkbox="false"
          :default-top="230"
          :show-summary="true"
          :summary-method="summaryMethod"
          :highlight-current-row="true"
          :sum-text="$t(langKey + 'total')"
          :sortable="true"
          action-label=" "
          border
          @changeWidth="changeColumnWidth"
        />
      </div>

      <!-- 頁面設置 -->
      <customStyle
        :dialog-visible.sync="showDialog"
        :columns="tableColumns"
        :lang-key="langKey"
        :title="$t('style.defaultTitle')"
        table-type="full-screen-without-first-field"
        @reloadStyleSheets="loadUserStyle"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { getStaffsTree } from '@/api/assistance/staff'
import { fetchStaffLedgers } from '@/api/report/staff'
import ETable from '@/components/ETable'
import SelectTree from '@/components/SelectTree'
import customStyle from '@/views/customStyle/index.vue'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'

import loadPrintoutSetting from '@/views/mixins/loadPrintoutSetting'
import handlePDF from './handlePDF'

import { amountFormat, toDecimal } from '@/utils'

import ENumeric from '@/components/ENumeric'
import dateUtil from '@/utils/date'
import { exportExcel } from '@/utils/excel'
import { staffLedgersExport } from '@/api/report/excel'
import DateRange from '@/components/DateRange/index'
export default {
  name: 'ReportStaffIndex',
  components: {
    DateRange,
    ETable,
    customStyle,
    ENumeric,
    VBreadCrumb,
    SelectTree,
  },
  mixins: [loadPreferences, loadCustomStyle, mixinPermission, loadPrintoutSetting, handlePDF],
  data() {
    return {
      loading: true,
      tableLoading: false,
      isAllMonth: true,
      isMonth: false,
      treeData: [],
      years: [],
      selectedYearId: '',
      tableData: [],
      data: [],
      monthList: [],
      langKey: 'report.staff.label.',
      tableColumns: [
        'ac_code',
        'vc_date',
        'vc_no',
        'tx_num',
        'descr',
        'vc_payee',
        'st_code',
        'st_name_',
        'amount_dr',
        'amount_cr',
        'amount_net',
        'amount_balance',
        'ref',
      ],
      amountColumns: ['amount_dr', 'amount_cr', 'amount_net', 'amount_balance'],
      preferences: {
        filters: {
          st_code: '',
          parent_st_type_id: '',
          st_selectGroup: false,
          selectedYearCode: '',
          selectedMonth: '',
          begin_date: '',
          end_date: '',
          // date_range: [],
          BIE: '',
        },
      },
      groupList: ['IE', 'I', 'E', 'B'],
      childPreferences: ['selectedMonth'],
      showYearPicker: false,
      periods: [],
      ps_code: 'pdfstaffledger',

      yearLastDate: new Date(),
      yearStartDate: new Date(),
      btnLoading: false,
      // dateFormatStr: 'yyyy-MM-dd'
    }
  },
  computed: {
    ...mapGetters(['language', 'styles', 'school', 'remoteServerInfo', 'user_id']),
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    allStaff() {
      return {
        active_year: '',
        children: null,
        code: '',
        level: 1,
        name_cn: this.$t('assistance.staff.label.parent_default'),
        name_en: this.$t('assistance.staff.label.parent_default'),
        parent_st_type_id: null,
        seq: 0,
        st_grade: null,
        staff_id: null,
        username: null,
      }
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  methods: {
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      if (row.rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    onSetting() {
      this.showDialog = true
    },
    fetchData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadTree)
        .then(this.loadUserPreference)
        .then(() => {
          if (this.preferences.filters.selectedYearCode !== '') {
            let bool = false
            this.years.forEach(ele => {
              if (this.preferences.filters.selectedYearCode === ele.fy_code) {
                this.selectedYearId = ele.fy_id
                bool = true
                return
              }
            })
            if (!bool) {
              this.preferences.filters.selectedYearCode = this.years[0].fy_code
              this.selectedYearId = this.years[0].fy_id
            }
          } else {
            this.preferences.filters.selectedYearCode =
              this.years && this.years.length > 0 ? this.years[0].fy_code : ''
          }
          if (this.preferences.filters.selectedAcCode === '') {
            this.preferences.filters.selectedAcCode =
              this.bankList && this.bankList.length > 0 ? this.bankList[0].ac_code : ''
          } else {
            if (this.bankList && this.bankList.length > 0) {
              let bool = false
              this.bankList.forEach(i => {
                if (i.ac_code === this.preferences.filters.selectedAcCode) {
                  bool = true
                  return
                }
              })
              if (!bool) {
                this.preferences.filters.selectedAcCode = this.bankList[0].ac_code
              }
            }
          }
          return Promise.resolve()
        })
        .then(() => this.onChangeYear(this.selectedYearId))
        .then(this.updateChildPreference)
        .then(() => {
          if (
            this.monthList &&
            this.monthList.length > 0 &&
            this.preferences.filters.selectedMonth !== ''
          ) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (!month) {
              this.preferences.filters.selectedMonth = ''
            }
          }
        })
        .then(this.reloadData)
    },
    getMonth() {
      const item = this.years.find(i => i.fy_id === this.selectedYearId)
      if (!item) return
      this.preferences.filters.selectedYearCode = item.fy_code
      return new Promise((resolve, reject) => {
        this.monthList = []
        if (this.selectedYearId !== '') {
          getYear(this.selectedYearId).then(res => {
            this.monthList = res.periods
            this.preferences.filters.selectedMonth = ''
            if (!this.loading) this.onChangeMonth('')
            resolve()
          })
        } else {
          reject()
        }
      })
    },
    reloadData() {
      return new Promise((resolve, reject) => {
        const defaultDateObj = new Date()
        const year = defaultDateObj.getFullYear()
        const month = defaultDateObj.getMonth()
        const beginDateObj = new Date(year, month, '1')
        const lastDateObj = new Date(year, month + 1, '0')
        // let begin_date, end_date
        // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
        //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        // } else {
        //   begin_date = this.preferences.filters.date_range[0]
        //   end_date = this.preferences.filters.date_range[1]
        // }
        let begin_date = this.preferences.filters.begin_date
        let end_date = this.preferences.filters.end_date
        if (!begin_date || !end_date) {
          // reject()
          begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
          end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
          // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
          this.preferences.filters.begin_date = begin_date
          this.preferences.filters.end_date = end_date
        }
        this.loading = true
        const parent_st_type_id = this.preferences.filters.parent_st_type_id
        const st_code = this.preferences.filters.st_selectGroup
          ? ''
          : this.preferences.filters.st_code
        const BIE = this.preferences.filters.BIE
        fetchStaffLedgers({
          parent_st_type_id,
          st_code,
          begin_date,
          end_date,
          BIE,
        })
          .then(res => {
            this.tableData = this.formatData(res)
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    formatData(data) {
      const newData = []
      data.forEach(item => {
        const newItem = Object.assign({}, item)
        newItem.amount_net = toDecimal(Number(newItem.amount_dr) - Number(newItem.amount_cr))
        newData.push(newItem)
      })
      return newData
    },
    dateFormat(date) {
      let s = ''
      const mouth = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
      const day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate()
      s += date.getFullYear() + '-' // 获取年份。
      s += mouth + '-' // 获取月份。
      s += day // 获取日。
      return s // 返回日期。
    },
    onChangeDateRange(val) {},
    onChangeMonth(val) {
      if (val === '') {
        const sDate = dateUtil.format(new Date(this.yearStartDate), this.dateFormatStr)
        const eDate = dateUtil.format(new Date(this.yearLastDate), this.dateFormatStr)
        // this.preferences.filters.date_range = [sDate, eDate]
        this.preferences.filters.begin_date = sDate
        this.preferences.filters.end_date = eDate
        return
      }
      // beginDate
      const beginStr = `20${val.substring(0, 2)}-${val.substring(2, 4)}-01`
      const d = new Date(beginStr)
      const beginDate = dateUtil.format(d, 'yyyy-MM-dd')

      // endDate
      d.setMonth(d.getMonth() + 1)
      d.setDate(d.getDate() - 1)
      const endDate = dateUtil.format(d, 'yyyy-MM-dd')
      // this.preferences.filters.date_range = [beginDate, endDate]
      this.preferences.filters.begin_date = beginDate
      this.preferences.filters.end_date = endDate
    },
    summaryMethod({ columns, data }) {
      const sums = []
      columns.forEach(async(column, index) => {
        switch (column.property) {
          case 'vc_date': {
            sums[index] = this.$t(this.langKey + 'total')
            break
          }
          case 'amount_cr': {
            let sum_cr = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[column.property])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum_cr = toDecimal(sum_cr + n)
                }
              }
            })
            sums[index] = amountFormat(sum_cr)
            break
          }
          case 'amount_dr': {
            let sum_dr = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[column.property])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum_dr = toDecimal(sum_dr + n)
                }
              }
            })
            sums[index] = amountFormat(sum_dr)
            break
          }
          case 'amount_net': {
            let sum_net = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item[column.property])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum_net = toDecimal(sum_net + n)
                }
              }
            })
            sums[index] = amountFormat(sum_net)
            break
          }
          case 'amount_balance': {
            let sum = 0
            data.forEach(item => {
              if (item.ac_code) {
                const n = Number(item['amount_net'])
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum = toDecimal(sum + n)
                }
              }
            })
            sums[index] = amountFormat(sum)
            break
          }
          default:
            break
        }
      })
      return sums
    },
    onSelectStaff({ value, selectGroup, group_id, nature_code }) {
      this.preferences.filters.st_code = value
      this.preferences.filters.st_selectGroup = selectGroup
      this.preferences.filters.parent_st_type_id = group_id
      // this.fetchData()
    },
    // 載入會計科目樹
    loadTree() {
      return new Promise((resolve, reject) => {
        getStaffsTree()
          .then(res => {
            const top = Object.assign({}, this.allStaff)
            top.children = res
            this.treeData = [top]
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    onChangeYear(id) {
      const year = this.years.find(i => i.fy_id === id)
      if (!year) return
      return new Promise((resolve, reject) => {
        searchTheDate({ fy_code: year.fy_code })
          .then(res => {
            this.yearLastDate = dateUtil.format(
              new Date(res.the_last_day + ' 00:00'),
              this.dateFormatStr,
            )
            this.yearStartDate = dateUtil.format(
              new Date(res.the_first_day + ' 00:00'),
              this.dateFormatStr,
            )
          })
          .then(this.getMonth)
          .finally(() => {
            resolve()
          })
      })
    },
    /**
     * Button Export
     */
    onExport(export_type) {
      if (this.loading) {
        return
      }
      const user_id = this.user_id

      const defaultDateObj = new Date()
      const year = defaultDateObj.getFullYear()
      const month = defaultDateObj.getMonth()
      const beginDateObj = new Date(year, month, '1')
      const lastDateObj = new Date(year, month + 1, '0')

      // let begin_date, end_date
      // if (!this.preferences.filters.date_range || !this.preferences.filters.date_range.length) {
      //   begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
      //   end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
      //   this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
      // } else {
      //   begin_date = this.preferences.filters.date_range[0]
      //   end_date = this.preferences.filters.date_range[1]
      // }
      let begin_date = this.preferences.filters.begin_date
      let end_date = this.preferences.filters.end_date
      if (!begin_date || !end_date) {
        // reject()
        begin_date = dateUtil.format(beginDateObj, 'yyyy-MM-dd')
        end_date = dateUtil.format(lastDateObj, 'yyyy-MM-dd')
        // this.$set(this.preferences.filters, 'date_range', [begin_date, end_date])
        this.preferences.filters.begin_date = begin_date
        this.preferences.filters.end_date = end_date
      }
      this.loading = true
      const parent_st_type_id = this.preferences.filters.parent_st_type_id
      const st_code = this.preferences.filters.st_selectGroup
        ? ''
        : this.preferences.filters.st_code
      const BIE = this.preferences.filters.BIE

      if (!user_id || !begin_date || !end_date) {
        // this.$message.error('')
        return
      }
      this.loading = true
      staffLedgersExport({
        user_id,
        export_type,
        parent_st_type_id,
        st_code,
        begin_date,
        end_date,
        BIE,
      })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onPagePrint() {
      this.btnLoading = true
      this.onPrint().finally(() => {
        this.btnLoading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$actionIconColor: #68afff;
$settingColor: #b9b6b6;
$disableColor: #b9b6b6;

.el-table th {
  background: #ffffff !important;
  font-size: large;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}

.el-icon-date {
  cursor: pointer;
}
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    /*width: 670px;*/
    margin: 5px 0;
    display: flex;
    /*justify-content: space-between;*/
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    .cheque {
      width: 150px;
    }
    .year {
      width: 150px;
    }
    /deep/ {
      .el-input--medium .el-input__icon {
        /*line-height: 30px;*/
      }

      .el-form-item__label {
        width: auto !important;
      }
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
        .pd-select {
          padding: 0;
        }
      }
    }
  }
  .cash-book-table {
    height: calc(100vh - 200px);

    .el-table {
      height: 100%;
      /*height: calc(100vh - 220px);*/
      /deep/ {
        .el-table__body-wrapper {
          /*height: calc(100vh - 300px);*/
        }
        td.vc_rdate {
          margin: 2px 0;
          .cell {
            height: 27px;
            line-height: 25px;
            padding: 0;
            text-overflow: unset;
            .el-select {
              width: calc(100% - 10px);
              min-width: 80px;
            }
            & > span {
              padding: 0 5px;
            }
          }
        }
      }
    }
  }
}
</style>
