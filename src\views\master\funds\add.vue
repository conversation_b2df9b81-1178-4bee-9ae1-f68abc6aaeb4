<template>
  <div class="fundInfo">
    <el-form ref="form" :model="form" label-position="right" label-width="150px">
      <!-- 賬戶類別編號 -->
      <el-form-item
        :rules="codeRules"
        :label="$t('master.fund.label.fund_code')"
        :class="{ 'code-rules-error': haveExist }"
        prop="fund_code"
      >
        <el-input v-model="form.fund_code" />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.fund.label.fund_name_cn')"
        prop="fund_name_cn"
      >
        <el-input v-model="form.fund_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.fund.label.fund_name_en')"
        prop="fund_name_en"
      >
        <el-input v-model="form.fund_name_en" clearable />
      </el-form-item>
      <!-- 中文簡稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.fund.label.fund_abbr_cn')"
        prop="fund_abbr_cn"
      >
        <el-input v-model="form.fund_abbr_cn" clearable />
      </el-form-item>
      <!-- 英文簡稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.fund.label.fund_abbr_en')"
        prop="fund_abbr_en"
      >
        <el-input v-model="form.fund_abbr_en" clearable />
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('master.fund.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_code"
            :value="item.fy_code"
          >
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editFund ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createFund, editFund, getFund } from '@/api/master/funds'
import { fetchRoles } from '@/api/master/role'
import { fetchYears } from '@/api/master/years'
const virtualPassword = 'this.virtual.password'
export default {
  name: 'MasterFundAdd',
  props: {
    editFund: {
      type: Object,
      default: null,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      system: 'AC',
      years: [],
      active_year_arr: [],
      form: {
        fund_id: '', // 賬目類別id
        fund_code: '', // 賬目類別編號
        fund_name_cn: '', // 中文全稱
        fund_name_en: '', // 英文全稱
        fund_abbr_cn: '', // 中文簡稱
        fund_abbr_en: '', // 英文簡稱
        active_year: '',
      },
      defaultForm: {
        fund_id: '',
        fund_code: '',
        fund_name_cn: '',
        fund_name_en: '',
        fund_abbr_cn: '',
        fund_abbr_en: '',
        active_year: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    codeRules() {
      return [
        {
          required: true,
          validator: this.checkCode,
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    editFund() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    checkRequired(rule, value, callback) {},
    initData() {
      this.disPwd = false
      if (this.editFund) {
        // 編輯
        this.form = Object.assign({}, this.editFund) // init活躍年份
        getFund(this.editFund.fund_id).then(res => {
          this.active_year_arr = res.active_year ? res.active_year.split(',') : []
        })
        if (this.form.username && this.form.username.length > 0) {
          this.disPwd = true
          this.form.password = this.form.confirmPassword = virtualPassword
        }
      } else {
        // 新增
        this.active_year_arr = []
        this.form = Object.assign({}, this.defaultForm)
        if (this.currentYear && this.currentYear.fy_code) {
          this.active_year_arr = [this.currentYear.fy_code]
        }
      }
      this.form.role_id = this.form.role_id ? parseInt(this.form.role_id) : ''

      fetchRoles(this.system)
        .then(res => {
          this.roles = res
        })
        .catch(() => {})
      fetchYears().then(res => {
        this.years = res
      })
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const user_id = this.form.fund_id
        const user_code = this.form.fund_code ? this.form.fund_code : null
        const name_cn = this.form.fund_name_cn
        const name_en = this.form.fund_name_en
        const abbr_cn = this.form.fund_abbr_cn
        const abbr_en = this.form.fund_abbr_en
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const fund_type = 'F'
        // const password = this.form.password === virtualPassword ? '' : this.$md5(this.form.password)
        if (this.editFund) {
          // 編輯
          editFund(user_id, user_code, name_cn, name_en, abbr_cn, abbr_en, fund_type, active_year)
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createFund(user_code, name_cn, name_en, abbr_cn, abbr_en, fund_type, active_year)
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        console.log('checkCode', !value)
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      let check = this.tableData.some(i => i.fund_code === value)
      if (this.editFund) {
        check = this.tableData.some(
          i => i.fund_code === value && i.fund_id !== this.editFund.fund_id,
        )
      }
      if (check) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
