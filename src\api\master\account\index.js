import request from '@/utils/request'

/**
 *返回新增后的會計科目id
 * @param {string} ac_code 會計科目編號
 * @param {string} ac_name_cn 會計科目中文
 * @param {string} ac_name_en 會計科目英文
 * @param {string} ac_abbr_cn 會計科目中文簡稱
 * @param {string} ac_abbr_en 會計科目英文簡稱
 * @param {string} nature_code 賬目性質編號,津貼=G,收入/支出=I/E,收入=I,支出=E,借方結餘=Dr,貸方結餘=Cr
 * @param {string} ac_B 是否為資產類，負債類，儲備金類,可填N或Y
 * @param {string} ac_I 是否為收入類,可填N或Y
 * @param {string} ac_E 是否為支出類,可填N或Y
 * @param {string} ac_bank 非銀行=X,儲蓄=S,往來=C,定期=F,現金=P,A/P=A
 * @param ac_group
 * @param active_year
 * @param income_bg_year
 * @param expense_bg_year
 * @param {integer} parent_fund_id 父賬目類別id
 * @param {integer} seq 所處層級里的位置
 * @param ac_cf
 */
export function createAccount(
  ac_code,
  ac_name_cn,
  ac_name_en,
  ac_abbr_cn,
  ac_abbr_en,
  nature_code,
  ac_B,
  ac_I,
  ac_E,
  ac_bank,
  ac_group,
  active_year,
  income_bg_year,
  expense_bg_year,
  parent_fund_id,
  seq,
  ac_cf,
) {
  return request({
    url: '/accounts/actions/create',
    method: 'post',
    data: {
      ac_code,
      ac_name_cn,
      ac_name_en,
      ac_abbr_cn,
      ac_abbr_en,
      nature_code,
      ac_B,
      ac_I,
      ac_E,
      ac_bank,
      ac_group,
      active_year,
      income_bg_year,
      expense_bg_year,
      parent_fund_id,
      seq,
      ac_cf,
    },
  })
}

/**
 *修改會計科目id
 * @param {string} account_id 會計科目id
 * @param {string} ac_code 會計科目編號
 * @param {string} ac_name_cn 會計科目中文
 * @param {string} ac_name_en 會計科目英文
 * @param {string} ac_abbr_cn 會計科目中文簡稱
 * @param {string} ac_abbr_en 會計科目英文簡稱
 * @param {string} nature_code 賬目性質編號,津貼=G,收入/支出=I/E,收入=I,支出=E,借方結餘=Dr,貸方結餘=Cr
 * @param {string} ac_B 是否為資產類，負債類，儲備金類,可填N或Y
 * @param {string} ac_I 是否為收入類,可填N或Y
 * @param {string} ac_E 是否為支出類,可填N或Y
 * @param {string} ac_bank 非銀行=X,儲蓄=S,往來=C,定期=F,現金=P,A/P=A
 * @param {integer} parent_fund_id 父賬目類別id
 * @param {integer} seq 所處層級里的位置
 */
export function updateAccount({
  account_id,
  ac_code,
  ac_name_cn,
  ac_name_en,
  ac_abbr_cn,
  ac_abbr_en,
  nature_code,
  ac_B,
  ac_I,
  ac_E,
  ac_bank,
  ac_group,
  active_year,
  income_bg_year,
  expense_bg_year,
  parent_fund_id,
  seq,
  ac_cf,
}) {
  return request({
    url: '/accounts/actions/update',
    method: 'post',
    data: {
      account_id,
      ac_code,
      ac_name_cn,
      ac_name_en,
      ac_abbr_cn,
      ac_abbr_en,
      nature_code,
      ac_B,
      ac_I,
      ac_E,
      ac_bank,
      ac_group,
      active_year,
      income_bg_year,
      expense_bg_year,
      parent_fund_id,
      seq,
      ac_cf,
    },
  })
}

/**
 *刪除會計科目
 *@param {string} account_id 會計科目id
 */
export function deleteAccount(account_id) {
  return request({
    url: '/accounts/actions/delete',
    method: 'post',
    data: {
      account_id,
    },
  })
}

/**
 *查詢會計科目樹
 *@param {string} parent_fund_id 父賬目類別id
 */
export function getAccountTree(parent_fund_id, fy_code) {
  return request({
    url: '/accounts/tree',
    method: 'get',
    params: {
      parent_fund_id,
      fy_code,
    },
  })
}

/**
 *查詢會計科目樹節點
 *@param {integer} parent_fund_id 上級賬目類別id
 */
export function getAccountTreeNode(fy_code, parent_fund_id, except_fund_id, except_account_id) {
  return request({
    url: '/accounts/tree/node',
    method: 'get',
    params: {
      fy_code,
      parent_fund_id,
      except_fund_id,
      except_account_id,
    },
  })
}

/**
 *查詢會計科目詳情
 *@param {integer} account_id 會計科目id
 */
export function getAccount(account_id) {
  return request({
    url: '/accounts/actions/inquire',
    method: 'get',
    params: {
      account_id,
    },
  })
}

/**
 *返回會計科目列表
 *@param {string} ac_B 傳N/Y
 */
export function fetchAccounts({ ac_B, ac_bank, parent_fund_id }) {
  return request({
    url: '/accounts',
    method: 'get',
    params: {
      ac_B,
      ac_bank,
      parent_fund_id,
    },
  })
}

/**
 *匯出賬目類別及會計賬目excel數據
 *@param {integer} parent_fund_id 父職員類別id
 */
export function exportAccount(parent_fund_id) {
  return request({
    url: '/accounts/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      parent_fund_id,
    },
  })
}

/**
 *匯入賬目類別及會計科目數據
 *@param {string} data_json 匯入的數據
 */
export function importAccount(data_json) {
  return request({
    url: '/accounts/actions/import',
    method: 'post',
    data: {
      data_json: JSON.stringify(data_json),
    },
  })
}

/**
 * 返回符合條件的會計科目
 * @param {integer} fy_code 會計週期編號
 * @param {integer} parent_fund_id 父職員類別id
 * @param {string} name 模糊搜索名字
 * @param {string} BIE 可傳B,I,E,不傳返回全部
 * @return {Promise}
 */
export function searchAccounts({ fy_code, parent_fund_id, name, BIE }) {
  return request({
    url: '/accounts/actions/search',
    method: 'get',
    params: {
      fy_code,
      parent_fund_id,
      name,
      BIE,
    },
  })
}

/**
 * 通過會計科目編號獲取某會計科目詳情
 * @param {string} ac_code 會計科目ac_code
 * @param {string} fy_code 會計科目活躍年度
 * @return {Promise}
 */
export function searchAccountByCode({ ac_code, fy_code }) {
  return request({
    url: '/accounts/actions/search-by-code',
    method: 'get',
    params: {
      ac_code,
      fy_code,
    },
  })
}
