<template>
  <div class="multi-split-pane">
    <div v-if="paneStateList[0]" :style="`height: ${pane1height}px`" class="split-pane-pane">
      <slot name="pane-1" />
    </div>
    <div v-if="paneStateList[0]" class="split-pane-drag" @mousedown="handleMousedown" />
    <div v-if="paneStateList[1]" :style="`height: ${pane2height}px`" class="split-pane-pane">
      <slot name="pane-2" />
    </div>
    <div v-if="paneStateList[1]" class="split-pane-drag" @mousedown="handleMousedown" />
    <div v-if="paneStateList[2]" :style="`height: ${pane3height}px`" class="split-pane-pane">
      <slot name="pane-3" />
    </div>
    <div v-if="paneStateList[2]" class="split-pane-drag" @mousedown="handleMousedown" />
    <div v-if="paneStateList[3]" :style="`height: ${pane4height}px`" class="split-pane-pane">
      <slot name="pane-4" />
    </div>
    <div v-if="paneStateList[3]" class="split-pane-drag" @mousedown="handleMousedown" />
    <div v-if="paneStateList[4]" :style="`height: ${pane5height}px`" class="split-pane-pane">
      <slot name="pane-5" />
    </div>
    <div v-if="paneStateList[4]" class="split-pane-drag" @mousedown="handleMousedown" />
    <div v-if="paneStateList[5]" class="split-pane-pane" style="flex: 1">
      <slot name="pane-last" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultiSplitPane',
  props: {
    min: {
      type: Number,
      default: 100,
    },
    max: {
      type: Number,
      default: 1000,
    },
    num: {
      type: Number,
      default: 5,
    },
    paneStateList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      ll: [
        'aaaaaaaaaaaaaa',
        'bbbbbbbbbb',
        'ccccccccccccc',
        'ddddddddddddd',
        'eeeeeeeee',
        'ffffffffff',
      ],
      canMove: false,
      currentElement: null,
      startHeight: 0,
      startY: 0,
      nextStartHeight: 0,

      pane1height: 150,
      pane2height: 150,
      pane3height: 150,
      pane4height: 150,
      pane5height: 150,
      pane6height: 150,
    }
  },
  methods: {
    handleMousedown(event) {
      document.addEventListener('mousemove', this.handleMousemove)
      document.addEventListener('mouseup', this.handleMouseup)
      this.canMove = true
      this.currentElement = event.target.previousElementSibling
      let startHeight = this.currentElement.style.height
      if (!startHeight) {
        startHeight = parseInt(event.target.previousElementSibling.offsetHeight)
      } else {
        startHeight = parseInt(startHeight.replace('px', ''))
      }
      this.startHeight = startHeight
      this.startY = event.pageY

      const nextPane = this.currentElement.nextElementSibling.nextElementSibling
      if (nextPane && nextPane.className === 'split-pane-pane') {
        let npHeight = nextPane.style.height
        if (!npHeight) {
          npHeight = parseInt(nextPane.offsetHeight)
        } else {
          npHeight = parseInt(npHeight.replace('px', ''))
        }
        this.nextStartHeight = npHeight
      }
    },
    handleMousemove(event) {
      if (!this.canMove) return
      const nY = event.pageY
      if (this.currentElement && this.startY) {
        const newHeight = nY - this.startY + this.startHeight
        if (newHeight < this.min) return
        if (newHeight > this.max) return
        this.currentElement.style.height = newHeight + 'px'
        //
        const nextPane = this.currentElement.nextElementSibling.nextElementSibling
        if (this.nextStartHeight && nextPane && nextPane.className === 'split-pane-pane') {
          const nHeight = this.nextStartHeight - (newHeight - this.startHeight)
          console.log(newHeight - this.startHeight, this.nextStartHeight, nHeight)
          if (nHeight < this.nextStartHeight && nHeight > this.min) {
            nextPane.style.height = nHeight + 'px'
          }
        }
      }
    },
    handleMouseup() {
      this.canMove = false
      this.currentElement = null
      this.startHeight = 0
      this.startY = 0
    },
  },
}
</script>

<style scoped>
.multi-split-pane {
  position: relative;
  width: 100%;
  height: 100%;
  align-items: stretch;
  display: flex;
  justify-content: flex-start;
  align-items: end;
  flex-direction: column;
}

.split-pane-drag {
  width: 100%;
  cursor: row-resize;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  height: 11px !important;
  margin-top: -5px;
  border-top: 5px solid hsla(0, 0%, 100%, 0);
  border-bottom: 5px solid hsla(0, 0%, 100%, 0);
  box-sizing: border-box;
  background: #000;
  opacity: 0.2;
  z-index: 1;
  background-clip: padding-box;
  margin-left: -2px;
  min-height: 11px;
}

.split-pane-pane {
  width: 100%;
  overflow-y: scroll;
  min-height: 100px;
  /*border: 1px solid;*/
}
</style>

<style>
html,
body,
#app {
  height: 100%;
  margin: 0;
  overflow: hidden;
}
</style>
