import Layout from '@/views/layout/Layout'

const enquiryRouter = {
  path: '/enquiry',
  component: Layout,
  redirect: 'noredirect',
  name: 'Enquiry',
  hidden: true,
  meta: {
    title: 'router.enquiry',
    p_code: 'ac.enquiry',
    icon: 'set',
  },
  children: [
    {
      path: 'grant',
      component: () => import('@/views/enquiry/grant/index.vue'),
      name: 'enquiryGrant',
      meta: {
        title: 'router.enquiryGrant',
        p_code: 'ac.enquiry.grant',
        noCache: true,
      },
    },
    {
      path: 'budget',
      component: () => import('@/views/enquiry/grant/index.vue'),
      name: 'enquiryBudget',
      meta: {
        title: 'router.enquiryBudget',
        p_code: 'ac.enquiry.budget',
        noCache: true,
      },
    },
    {
      path: 'staff',
      component: () => import('@/views/enquiry/grant/index.vue'),
      name: 'enquiryStaff',
      meta: {
        title: 'router.enquiryStaff',
        p_code: 'ac.enquiry.staff',
        noCache: true,
      },
    },
    {
      path: 'department',
      component: () => import('@/views/enquiry/grant/index.vue'),
      name: 'enquiryDepartment',
      meta: {
        title: 'router.enquiryDepartment',
        p_code: 'ac.enquiry.department',
        noCache: true,
      },
    },
    {
      path: 'contra',
      component: () => import('@/views/enquiry/grant/index.vue'),
      name: 'enquiryContra',
      meta: {
        title: 'router.enquiryContra',
        p_code: 'ac.enquiry.contra',
        noCache: true,
      },
    },
    {
      path: 'general',
      component: () => import('@/views/enquiry/grant/index.vue'),
      name: 'enquiryGeneral',
      meta: {
        title: 'router.enquiryGeneral',
        p_code: 'ac.enquiry.general',
        noCache: true,
      },
    },
  ],
}

export default enquiryRouter
