<template>
  <div class="setting-basic">
    <right>
      <el-main slot="content">
        <el-form ref="form" :model="form" class="form" :label-width="isEnglish ? '180px' : '120px'">
          <!--          <el-form-item label="主高度">-->
          <!--            <el-input-number v-model="form.main_menu_height" :min="50" :max="100" :controls="true"/>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="副高度">-->
          <!--            <el-input-number v-model="form.second_menu_height" :min="30" :max="50" :controls="true"/>-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="選項高度">-->
          <!--            <el-input-number v-model="form.options_height" :min="10" :max="30" :controls="true"/>-->
          <!--          </el-form-item>-->
          <el-form-item :label="$t('setting.basic.font')">
            <el-select
              v-model="form.menu_font_family"
              :placeholder="$t('setting.basic.select_font_placeholder')"
            >
              <el-option
                v-for="item in fontFamilyList"
                :key="item.value"
                :label="item.title"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('setting.basic.main_menu_font_size')">
            <el-input-number
              v-model="form.main_menu_font_size"
              :min="10"
              :max="18"
              :controls="true"
            />
          </el-form-item>
          <el-form-item :label="$t('setting.basic.second_menu_font_size')">
            <el-input-number
              v-model="form.second_menu_font_size"
              :min="10"
              :max="18"
              :controls="true"
            />
          </el-form-item>
          <el-form-item :label="$t('setting.basic.main_menu_background_color')">
            <el-color-picker v-model="form.main_menu_background_color" />
          </el-form-item>
          <el-form-item :label="$t('setting.basic.second_menu_hover_color')">
            <el-color-picker v-model="form.second_menu_background_color" />
          </el-form-item>
          <!--          <el-form-item label="活躍顏色1">-->
          <!--            <el-color-picker v-model="form.main_menu_hover_color"/>-->
          <!--          </el-form-item>-->
          <el-form-item :label="$t('setting.basic.second_menu_hover_color')">
            <el-color-picker v-model="form.second_menu_hover_color" />
          </el-form-item>
          <el-row class="actions">
            <el-button size="mini" type="info" @click="onFetch(0)">
              {{ $t('button.loadDefault') }}
            </el-button>
            <el-button size="mini" type="info" @click="onSave(0)">
              {{ $t('button.saveDefault') }}
            </el-button>
            <el-button size="mini" type="danger" @click="onFetch(1)">
              {{ $t('button.reset') }}
            </el-button>
            <el-button size="mini" type="primary" @click="onSave(1)">
              {{ $t('button.update') }}
            </el-button>
          </el-row>
        </el-form>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'

export default {
  name: 'SettingScreenMenu',
  components: {
    right,
  },
  mixins: [common],
  data() {
    return {
      form: {
        main_menu_height: 40,
        second_menu_height: 30,
        options_height: 20,
        menu_font_family: 'Microsoft YaHei',
        main_menu_font_size: 12,
        second_menu_font_size: 12,
        main_menu_background_color: '#3e97dd',
        second_menu_background_color: '#ffffff',
        main_menu_hover_color: '#3e97dd',
        second_menu_hover_color: '#2e9eff',
      },

      ss_code: 'screen_basic_menu',
      ss_type: 'menu',
    }
  },
  computed: {},
  created() {},
  methods: {},
}
</script>
