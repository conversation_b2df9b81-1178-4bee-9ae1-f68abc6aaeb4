<template>
  <div
    :class="{
      'el-divider--horizontal': direction === 'horizontal',
      'el-divider--vertical': direction === 'vertical',
    }"
    :style="{
      width: width,
    }"
    class="el-divider"
  />
</template>

<script>
export default {
  name: 'Divider',
  props: {
    direction: {
      type: [<PERSON><PERSON><PERSON>, String],
      default: 'horizontal',
    },
    width: {
      type: String,
      default: '100%',
    },
  },
}
</script>

<style scoped>
.el-divider {
  background-color: #dcdfe6;
  position: relative;
}
.el-divider--horizontal {
  display: block;
  height: 1px;
  width: 100%;
  margin: 24px 0;
}
.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
</style>
