<script>
export default {
  name: 'EnquiryComponentProxy',
  created() {
    /* 組件通信 */
    // 全局刷新
    this.$bus.on('reloadAllEnquiryPage', this.handleBusReloadPage)
    // 刷新某類
    this.$bus.on('enquirySetCurrentRow', this.handleSetCurrentRow)
    switch (this.$options.name) {
      case 'EnquiryGrant':
        this.$bus.on('enquiryGrantFetch', this.fetchGrant)
        break
      case 'EnquiryBudget':
        this.$bus.on('enquiryBudgetFetch', this.fetchBudget)
        break
      case 'EnquiryStaff':
        this.$bus.on('enquiryStaffFetch', this.fetchStaff)
        break
      case 'EnquiryDepartment':
        this.$bus.on('enquiryDeptFetch', this.fetchDept)
        break
      case 'EnquiryContra':
        this.$bus.on('enquiryContraFetch', this.fetchContra)
        break
    }
    // 取消特定刷新
    this.$bus.on('enquiryCurrentPage', this.handleCurrentPageClickRow)
  },
  beforeDestroy() {
    // 全局刷新
    this.$bus.off('reloadAllEnquiryPage', this.handleBusReloadPage)
    this.$bus.off('enquirySetCurrentRow', this.handleSetCurrentRow)
    // 刷新某類
    switch (this.$options.name) {
      case 'EnquiryGrant':
        this.$bus.off('enquiryGrantFetch', this.fetchGrant)
        break
      case 'EnquiryBudget':
        this.$bus.off('enquiryBudgetFetch', this.fetchBudget)
        break
      case 'EnquiryStaff':
        this.$bus.off('enquiryStaffFetch', this.fetchStaff)
        break
      case 'EnquiryDepartment':
        this.$bus.off('enquiryDeptFetch', this.fetchDept)
        break
      case 'EnquiryContra':
        this.$bus.off('enquiryContraFetch', this.fetchContra)
        break
    }
    // 取消特定刷新
    this.$bus.off('enquiryCurrentPage', this.handleCurrentPageClickRow)
  },
  methods: {
    handleBusReloadPage() {
      console.log('reload', this.$options.name)
      this.fetchData()
    },
    handleSetCurrentRow(lg_id) {
      console.log('SetCurrentRow', lg_id, this.$options.name)
      let row
      if (lg_id) {
        row = this.tableData.find(item => item.lg_id === lg_id)
      }
      if (this.$refs.table && this.$refs.table.setCurrentRow) {
        this.$refs.table.setCurrentRow(row)
        this.$refs.table2 && this.$refs.table2.setCurrentRow()
      } else {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow(row)
          this.$refs.table2 && this.$refs.table2.setCurrentRow()
        })
      }
    },
    fetchStaff(staff_code) {
      console.log('enquiry fetchStaff', this.$options.name)
      this.preferences.filters.st_type_id = ''
      this.preferences.filters.st_code = staff_code
      this.handleBusReloadPage()
    },
    fetchDept(dept_code) {
      console.log('enquiry fetchDept', this.$options.name)
      this.preferences.filters.dept_type_id = ''
      this.preferences.filters.dept_code = dept_code
      this.handleBusReloadPage()
    },
    fetchContra(contra_code, contra_id) {
      console.log('enquiry fetchContra', this.$options.name)
      this.preferences.filters.contra_id = contra_id
      this.preferences.filters.contra_code = contra_code
      this.handleBusReloadPage()
    },
    findAccountByCode(tree, ac_code) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].code === ac_code) {
          return tree[i]
        }
        if (tree[i].children) {
          const result = this.findAccountByCode(tree[i].children, ac_code)
          if (result) return result
        }
      }
      return null
    },
    fetchGrant(fund_id, ac_code, fy_id, fy_code, pd_code) {
      console.log('enquiry fetchGrant', this.$options.name)
      // this.preferences.filters.fund_id = fund_id
      this.loadAccountTree(fund_id)
        .then(() => {
          this.preferences.filters.ac_code = ac_code
          this.preferences.filters.fund = fund_id
          this.preferences.filters.ac_fund_id = ''
          this.preferences.filters.ac_selectGroup = false
          this.getNatureCode(this.accountOptions)
        })
        .then(() => {
          const account = this.findAccountByCode(
            this.accountOptions,
            this.preferences.filters.ac_code,
          )
          if (account) {
            this.preferences.filters.nature_code = account.nature_code
          }

          if (!fy_id && !fy_code) return
          if (!fy_id) {
            const year = this.year_list.find(i => i.fy_code === fy_code)
            fy_id = year.fy_id
          }
          if (!fy_code) {
            const year = this.year_list.find(i => i.fy_id === fy_id)
            fy_code = year.fy_code
          }

          this.preferences.filters.pd_code = pd_code
          this.preferences.filters.type = pd_code ? 'M' : 'Y'
          if (this.preferences.filters.fy_id === fy_id && this.month_list.length) {
            // 年份沒有修改
            return Promise.resolve()
          } else {
            this.preferences.filters.fy_id = fy_id
            this.preferences.filters.fy_code = fy_code
            return this.loadPeriods(fy_id)
          }
        })
        .then(() => {
          this.handleBusReloadPage()
        })
    },
    fetchBudget(fy_id, fy_code, pd_code) {
      let reloadTree = false
      if (!fy_id && !fy_code) return
      if (!fy_id) {
        const year = this.year_list.find(i => i.fy_code === fy_code)
        this.preferences.filters.fy_id = year.fy_id
      }
      if (!fy_code) {
        const year = this.year_list.find(i => i.fy_id === this.preferences.filters.fy_id)
        this.preferences.filters.fy_code = year.fy_code
      }

      this.preferences.filters.pd_code = pd_code
      this.preferences.filters.type = pd_code ? 'M' : 'Y'
      if (this.preferences.filters.fy_id === fy_id && this.month_list.length) {
        // 年份沒有修改
        return
      } else {
        this.preferences.filters.fy_id = fy_id
        this.preferences.filters.fy_code = fy_code
        this.loadPeriods(fy_id)
        reloadTree = true
      }
      if (reloadTree) {
        this.loadTree().then(this.fetchData)
      } else {
        this.fetchData()
      }
    },
    handleThisPageClickRow() {
      this.$bus.emit('enquiryCurrentPage', this.$options.name)
    },
    handleCurrentPageClickRow(name) {
      if (name !== this.$options.name) {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow()
          this.$refs.table2 && this.$refs.table2.setCurrentRow()
        })
      }
    },
  },
}
</script>

<style scoped></style>
