<script>
import { mapGetters, mapActions } from 'vuex'
import {
  editStaffStyleSheets,
  editUserStyleSheets,
  getStaffStyleSheets,
  getUserStyleSheets,
} from '@/api/settings/user/style'
export default {
  props: {},
  data() {
    return {
      defaultScale: 50,
      paneLoading: true,
      styleData: [],
      styleColumns: [],
      showDialog: false,
      autoLoadStyle: true,
    }
  },
  computed: {
    ...mapGetters({ thisSystem: 'system', user_id: 'user_id' }),

    styleAttr() {
      return {
        saveScale: this.saveScale,
        onShowSetting: this.onShowSetting,
        defaultScale: this.defaultScale,
      }
    },
    firstFieldAlign() {
      const first = this.styleData.find(i => i.ss_type === 'table' && i.ss_key === 'first_field')
      return first && first.alignment
    },
    firstFieldWidth() {
      const first = this.styleData.find(i => i.ss_type === 'table' && i.ss_key === 'first_field')
      return first && first.width
    },
  },
  created() {
    if (!this.manualLoadStyle) {
      this.loadUserStyle()
    }
  },
  updated() {},
  methods: {
    ...mapActions(['fetchGlobalStyle']),
    loadUserStyle(loadGlobal = false) {
      this.paneLoading = true
      const system = this.thisSystem
      let user_id = this.user_id
      let staff_id = this.user_id
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code

      let api = getStaffStyleSheets
      if (system === 'AC') {
        api = getUserStyleSheets
        staff_id = undefined
      } else {
        user_id = undefined
      }
      if (loadGlobal) {
        this.fetchGlobalStyle()
      }
      return new Promise((resolve, reject) => {
        api({ user_id, staff_id, system, ss_code })
          .then(data => {
            // 左欄寬度設置
            const scale = data.find(item => {
              return item.ss_type === 'screen' && item.ss_key === 'scale'
            })
            if (scale) {
              this.defaultScale = scale.value ? Number(scale.value) : 50
            }
            this.styleData = data
            this.styleColumns = []
            this.$nextTick(() => {
              const cols = data
                .filter(i => {
                  return i.ss_type === 'column' && i.x
                })
                .sort((a, b) => {
                  return a.x - b.x
                })
              if (cols.length) {
                this.styleColumns = cols
              } else {
                this.styleColumns = this.tableColumns.map((v, i) => {
                  return {
                    ss_id: 0,
                    user_id: null,
                    staff_id: null,
                    system: '',
                    ss_code: ss_code,
                    ss_type: 'column',
                    seq: i,
                    ss_key: v,
                    value: null,
                    alignment: 'left',
                    width: 0,
                    x: i,
                    y: null,
                    export: 1,
                  }
                })
              }
            })
            this.$emit('ready')
            resolve()
          })
          .catch(() => {
            reject()
          })
          .finally(() => {
            this.paneLoading = false
          })
      })
    },
    changeColumnWidth(key, newWidth) {
      const column = this.styleData.find(item => {
        return item.ss_key === key // && item.ss_type === 'column'
      })
      if (column) {
        column.width = newWidth
        this.saveUserStyleSheets()
      }
    },
    saveScale(width) {
      const scale = this.styleData.find(item => {
        return item.ss_type === 'screen' && item.ss_key === 'scale'
      })
      if (scale) {
        scale.value = width
        this.saveUserStyleSheets()
      }
    },
    saveUserStyleSheets() {
      let user_id = this.user_id
      let staff_id = this.user_id
      const system = this.thisSystem
      const ss_code = this.ss_code ? this.ss_code : this.$route.meta.p_code

      const ss_setting_json = this.styleData

      let api = editStaffStyleSheets
      if (system === 'AC') {
        api = editUserStyleSheets
        staff_id = undefined
      } else {
        user_id = undefined
      }
      api({ user_id, staff_id, system, ss_code, ss_setting_json })
        .then(res => {})
        .catch(() => {})
    },
    /**
     * 頁面設置按鈕點擊
     */
    onShowSetting() {
      this.showDialog = true
    },
  },
}
</script>
