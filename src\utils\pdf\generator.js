// import { mm2pt, px2pt } from '@/utils/pdf/index'
import i18n from '@/lang/index.js'
// const pdfStyle = {
//   tableExample: {
//     fontSize: 10,
//     margin: [0, 5, 0, 15]
//   },
//   tableHeader: {
//     bold: true,
//     fontSize: 11,
//     color: 'black',
//     fillColor: '#CCCCCC',
//     alignment: 'center'
//   },
//   schoolNameCN: {
//     bold: true,
//     fontSize: 16,
//     color: 'black'
//   },
//   schoolNameEN: {
//     bold: true,
//     fontSize: 13,
//     color: 'black'
//   },
//   titleCell: {
//     bold: true,
//     fontSize: 11
//   },
//   tableContent: {
//     bold: false,
//     fontSize: 9
//   }
// }

// import { px2pt } from '@/utils/pdf/index'
// import { amountFormat } from '@/utils/index'
import dateUtil from '@/utils/date'

export function lineWidth(i, node) {
  return 0.2
}
export function lineStyle(i, node) {
  return { dash: { length: 3, space: 1 } }
}
export function lineColor(i, node) {
  return 'black'
}
export const paddingLeft = function(i, node) {
  return 0
}
export const paddingRight = function(i, node) {
  return 0
}
export const paddingTop = function(i, node) {
  return 0
}
export const paddingBottom = function(i, node) {
  return 0
}
export function notPadding(i, node) {
  return 0
}
export function footer(language) {
  return function(currentPage, pageCount) {
    return [
      {
        text:
          language === 'en'
            ? `Page ${currentPage} / ${pageCount}`
            : `第 ${currentPage} / ${pageCount} 頁`,
        alignment: 'center',
      },
    ]
  }
}
export const pdfStyle = {
  tableExample: {
    fontSize: 10,
    margin: [0, 5, 0, 15],
  },
  tableHeader: {
    bold: true,
    fontSize: 11,
    color: 'black',
    fillColor: '#CCCCCC',
    alignment: 'center',
  },
  schoolNameCN: {
    bold: true,
    fontSize: 16,
    color: 'black',
  },
  schoolNameEN: {
    bold: true,
    fontSize: 13,
    color: 'black',
  },
  titleCell: {
    bold: true,
    fontSize: 11,
  },
  tableContent: {
    bold: false,
    fontSize: 9,
  },
}

export const font_size_fotter = 12
export const margin_bottom_offset = 25

export function dataFormat(dateStr) {
  if (dateStr) {
    if (dateStr === '0000-00-00') {
      return ''
    }
    try {
      const str = dateStr.replace(/-/g, '/') // 兼容safari
      const date = new Date(str)
      return dateUtil.format(date, 'dd/MM/yyyy')
    } catch (e) {
      console.error(e)
    }
  } else {
    return ''
  }
}

/**
 * 生成簽名表格
 * @param signType
 * @param dataList
 * @param language
 * @param height
 * @param space
 * @param margin_left
 * @param margin_right
 * @param font_size
 * @param bottom_sign
 * @return {{columns: Array}}
 */
export function generateSign(
  signType,
  dataList,
  language,
  height,
  space,
  margin_left,
  margin_right,
  font_size,
  bottom_sign,
) {
  let signList = []
  height = Number(height)
  const lineMaxHeight = 20
  let y = height
  const dateStr = language === 'en' ? 'Date' : i18n.t('enquiry.timeType.day')
  const x_space = Number(space) / 2

  const layout = {
    hLineWidth: lineWidth,
    vLineWidth: lineWidth,
    hLineStyle: lineStyle,
    vLineStyle: lineStyle,
    paddingLeft: notPadding,
    paddingRight: notPadding,
    paddingTop: notPadding,
    paddingBottom: notPadding,
    defaultBorder: false,
  }

  switch (signType.toString()) {
    case '0': // 橫線1
      signList = dataList.map((item, index) => {
        return {
          margin: bottom_sign
            ? [
              index === 0 ? margin_left : x_space,
              0,
              index === dataList.length ? margin_right : x_space,
              0,
            ]
            : [index === 0 ? 0 : x_space, 10, index === dataList.length ? 0 : x_space, 0],
          width: '*',
          table: {
            dontBreakRows: true,
            keepWithHeaderRows: 3,
            headerRows: 3,
            widths: ['*'],
            heights: [lineMaxHeight, height, lineMaxHeight],
            body: [
              [
                {
                  text: language === 'en' ? item.step_en : item.step_cn,
                  noWrap: true,
                  bold: true,
                  style: 'signCell',
                },
              ],
              [{ text: '', border: [false, false, false, true] }],
              [
                {
                  text: language === 'en' ? item.post_en : item.post_cn,
                  noWrap: true,
                  alignment: 'center',
                  style: 'signCell',
                },
              ],
            ],
          },
          layout,
          // layout: {
          //   hLineWidth: lineWidth,
          //   vLineWidth: lineWidth,
          //   hLineStyle: lineStyle,
          //   vLineStyle: lineStyle,
          //   paddingLeft: notPadding,
          //   paddingRight: notPadding,
          //   paddingTop: notPadding,
          //   paddingBottom: notPadding,
          //   defaultBorder: false
          // }
        }
      })
      y = height + lineMaxHeight * 2
      break
    case '1': {
      // 橫線2
      signList = dataList.map((item, index) => {
        return {
          margin: [
            bottom_sign ? (index === 0 ? margin_left : x_space) : 0,
            bottom_sign ? 0 : 10,
            bottom_sign ? (index === dataList.length - 1 ? margin_right : x_space) : 0,
            0,
          ],
          width: '*',
          table: {
            dontBreakRows: true,
            keepWithHeaderRows: 1,
            headerRows: 1,
            widths: ['auto', '*'],
            heights: [height],
            body: [
              [
                {
                  maxHeight: lineMaxHeight,
                  style: 'signCell',
                  margin: [0, height - lineMaxHeight, 10, 0],
                  noWrap: true,
                  text:
                    language === 'en'
                      ? `${item.step_en}(${item.post_en}):`
                      : `${item.step_cn}(${item.post_cn}):`,
                },
                { text: '', border: [false, false, false, true] },
              ],
            ],
          },
          layout: {
            hLineWidth: lineWidth,
            vLineWidth: lineWidth,
            hLineStyle: lineStyle,
            vLineStyle: lineStyle,
            defaultBorder: false,
          },
        }
      })
      y = height // + lineMaxHeight
      break
    }
    case '2': // 橫線3
      {
        // let l_max_width = 0
        // let r_max_width = 0
        // dataList.forEach((item, index) => {
        //   const v = language === 'en'
        //     ? `${item.step_en}(${item.post_en}):`
        //     : `${item.step_cn}(${item.post_cn}):`
        //   const w = mm2pt(v.length * 2) + 10
        //   if (index % 2 === 0) {
        //     w > l_max_width && (l_max_width = w)
        //   } else {
        //     w > r_max_width && (r_max_width = w)
        //   }
        // })
        const cells = dataList.map((item, index) => {
          return [
            {
              style: 'signCell',
              noWrap: true,
              // padding: [0, 0, 0, 0],
              margin: [0, height - lineMaxHeight, 0, 0],
              text:
                language === 'en'
                  ? `${item.step_en}(${item.post_en}):`
                  : `${item.step_cn}(${item.post_cn}):`,
            },
            { text: '', border: [false, false, false, true] },
          ]
        })
        // const reduceDimension = (arr) => {
        //   const newArr = []
        //   let j = -1
        //   for (let i = 0; i < arr.length; i++) {
        //     const x = i % 2
        //     if (x === 0) {
        //       j++
        //       newArr.push([])
        //     }
        //     newArr[j][x] = arr[i]
        //     if (x === 0 && arr.length - 1 === i) {
        //       newArr[j][1] = ''
        //     }
        //   }
        //   return newArr
        // }
        const reduceDimension = arr => {
          const newArr = [[], []] // 左右兩個列表
          for (let i = 0; i < arr.length; i++) {
            const x = i % 2
            // if (x === 0) {
            //   newArr.push([])
            // }

            newArr[x].push(arr[i])
            // 填充空格
            // if (x === 0 && arr.length - 1 === i) {
            //   newArr[x].push('')
            // }
          }
          return newArr
        }
        let max_line = 0
        const body = reduceDimension(cells).map(row => {
          if (row.length > max_line) {
            max_line = row.length
          }
          return {
            margin: [0, 0, x_space * 2, 0],
            table: {
              dontBreakRows: true,
              widths: ['auto', '*'],
              heights: row.map(() => height),
              body: row,
            },
            layout,
            // layout: {
            //   hLineWidth: lineWidth,
            //   vLineWidth: lineWidth,
            //   hLineStyle: lineStyle,
            //   vLineStyle: lineStyle,
            //   defaultBorder: false
            // }
          }
        })
        y = height * max_line
        signList = [
          {
            // margin: [0, 0, 0, 0],
            margin: bottom_sign ? [margin_left, 0, margin_right, 0] : [0, 10, 0, 0],
            width: '*',
            table: {
              widths: ['50%', '50%'],
              heights: [...Array(body.length)].map(() => height),
              body: [body],
            },
            layout,
            // layout: {
            //   hLineWidth: lineWidth,
            //   vLineWidth: lineWidth,
            //   hLineStyle: lineStyle,
            //   vLineStyle: lineStyle,
            //   defaultBorder: false
            // }
          },
        ]
      }
      break
    case '3': // 橫線4
      signList = dataList.map((item, index) => {
        return {
          margin: [
            bottom_sign ? (index === 0 ? margin_left : x_space) : 0,
            bottom_sign ? 0 : 10,
            bottom_sign
              ? index === dataList.length - 1
                ? margin_right
                : x_space
              : index === dataList.length - 1
                ? 0
                : x_space * 2,
            0,
          ],
          width: '*',
          // height: '*',
          table: {
            dontBreakRows: true,
            headerRows: 3,
            keepWithHeaderRows: 3,
            widths: ['*'],
            heights: [height, lineMaxHeight, lineMaxHeight],
            body: [
              // [{ text: '', border: [false, false, false, true] }],
              [
                {
                  style: 'signCell',
                  text: language === 'en' ? item.step_en : item.step_cn,
                  alignment: 'left',
                  noWrap: true,
                  bold: true,
                  border: [false, false, false, true],
                  maxHeight: lineMaxHeight,
                  // marginBottom: lineMaxHeight
                },
              ],
              [
                {
                  style: 'signCell',
                  text: language === 'en' ? item.post_en : item.post_cn,
                  alignment: 'left',
                  noWrap: true,
                  maxHeight: lineMaxHeight,
                  // marginBottom: lineMaxHeight
                },
              ],
              [
                {
                  style: 'signCell',
                  text: dateStr + ':',
                  alignment: 'left',
                  noWrap: true,
                  maxHeight: lineMaxHeight,
                  // marginBottom: lineMaxHeight
                },
              ],
            ],
          },
          layout,
          // layout: {
          //   hLineWidth: lineWidth,
          //   vLineWidth: lineWidth,
          //   hLineStyle: lineStyle,
          //   vLineStyle: lineStyle,
          //   defaultBorder: false
          // }
        }
      })
      y = height + lineMaxHeight * 2
      break
    case '4': // 方格
      {
        const cells = dataList.map(item => {
          return {
            style: 'signCell',
            noWrap: true,
            text:
              language === 'en'
                ? `${item.step_en}(${item.post_en}):`
                : `${item.step_cn}(${item.post_cn}):`,
          }
        })
        const widths = dataList.map(() => '*')

        signList = [
          {
            margin: bottom_sign ? [margin_left, 0, margin_right, 0] : [0, 10, 0, 0],
            width: '*',
            table: {
              dontBreakRows: true,
              keepWithHeaderRows: 1,
              headerRows: 1,
              widths: widths,
              heights: [height],
              body: [cells],
            },
            layout: {
              // hLineWidth: lineWidth,
              // vLineWidth: lineWidth,
              // hLineStyle: lineStyle,
              // vLineStyle: lineStyle,
              // defaultBorder: false
            },
          },
        ]
        y = height
      }

      break
  }
  // if (isBottom) {
  // return { columns: signList, absolutePosition: { x: 0, y: Number(pageHeader) - y }}
  // return { columns: signList, height: '*'}
  // } else {
  return { columns: signList, height: y }
  // }
}

/**
 * 生成學校信息（LOGO）
 * @param name_cn
 * @param name_en
 * @param logo
 * @return {{layout: string, width: string, style: string, table: {heights: number[], widths: *[], headerRows: number, body: *[][]}}}
 */
export function generateSchoolInfo(name_cn, name_en, logo, width) {
  let w
  if (width) {
    const n = Number(width)
    if (isNaN(n)) {
      w = width
    } else {
      w = n
    }
  } else {
    w = '*'
  }
  return {
    width: w,
    style: 'tableExample',
    table: {
      widths: [50, '*'],
      heights: [25, 25],
      headerRows: 2,
      // dontBreakRows: true,
      // keepWithHeaderRows: 1,
      body: [
        [
          {
            image: logo,
            width: 50,
            height: 50,
            rowSpan: 2,
            margin: [0, 0, 10, 0],
          },
          { text: name_cn, bold: true, style: 'schoolNameCN' },
        ],
        [
          '',
          {
            // margin: [10, 0, 0, 0],
            text: name_en,
            bold: true,
            style: 'schoolNameEN',
          },
        ],
      ],
    },
    layout: 'noBorders',
  }
}

/**
 * 生成頁面信息（右上角表格）
 * @param title
 * @param filename
 * @param data
 * @param marginRight
 * @return {{layout: {vLineStyle: (function(*, *): undefined), vLineWidth: (function(*, *): number), hLineStyle: layout.hLineStyle, vLineColor: (function(*, *): string), hLineColor: (function(*, *): string), defaultBorder: boolean, hLineWidth: (function(*, *): number)}, width: *, style: string, table: {heights: number[], widths: number[], headerRows: number, body: *[]}}}
 */
export function generatePageInfo(title, filename, data, width, marginRight) {
  let w
  if (width) {
    const n = Number(width)
    if (isNaN(n)) {
      w = width
    } else {
      w = n + Number(marginRight) + 10
    }
  } else {
    w = '*'
  }
  const rightTableData = data.map(item => {
    return [
      { text: item.label, alignment: 'left' },
      { text: item.value, alignment: 'center', border: [false, false, false, true] },
    ]
  })
  return {
    width: w,
    style: 'tableExample',
    margin: [0, 5, 0, 5],
    table: {
      widths: ['auto', '*'],
      heights: [10, 10, 10, 10],
      headerRows: 0,
      body: [
        [
          {
            text: title,
            style: 'titleCell',
            colSpan: 2,
            alignment: 'center',
            fillColor: '#CCCCCC',
            border: [true, true, true, true],
          },
          '',
        ],
        ...rightTableData,
      ],
    },
    layout: {
      hLineWidth: lineWidth,
      vLineWidth: lineWidth,
      hLineColor: lineColor,
      vLineColor: lineColor,
      hLineStyle: function(i, node) {
        if (i < 2) {
          return undefined
        }
        return { dash: { length: 3, space: 1 } }
      },
      vLineStyle: function(i, node) {
        return undefined
      },
      defaultBorder: false,
    },
  }
}

/**
 * 生成通用表頭
 * @param schoolTable 學校信息
 * @param pageTable 頁面信息
 * @param tableColumnsLength 數據表列數
 * @return {*[]}
 */
export function generateGeneralHeader(schoolTable, pageTable, tableColumnsLength) {
  return [
    {
      // Header
      margin: [0, 0, 0, 10],
      colSpan: tableColumnsLength,
      border: [false, false, false, false],
      columns: [
        // Left Header
        schoolTable, // 學校信息
        // right Header
        pageTable, // 頁面信息
      ],
    },
    ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
      text: '',
      border: [false, false, false, false],
    })),
  ]
}

export function generateWidths(columns) {
  const widths = columns.map(item => item.width)
  const widthTotal = widths.reduce((a, b) => a + b)
  const p = 100 / widthTotal
  // return [...widths.map(w => w * p + '%'), '*']
  return widths.map(w => w * p + '%')
}

export function generateVoucherAccountCell(acCode, type, acName, width, font_size) {
  return {
    width: width,
    // style: 'tableExample',
    table: {
      widths: ['auto', '*'],
      headerRows: 1,
      // dontBreakRows: true,
      // keepWithHeaderRows: 1,
      body: [
        [
          // {
          //   text: acCode,
          //   rowSpan: 1,
          //   bold: true
          // },
          // { text: `[${type}]`, bold: false }
          {
            text: [
              { text: acCode, bold: true },
              { text: ` [${type}] `, bold: false, fontSize: font_size - 1 },
            ],
            colSpan: 2,
          },
          '',
        ],
        [
          {
            text: acName,
            bold: false,
            margin: [0, -5, 0, 0],
            colSpan: 2,
          },
          '',
        ],
      ],
    },
    layout: 'noBorders',
  }
}
export function generateVoucherDescCell(item, language, $t, width) {
  const body = [
    [
      {
        text: item.descr || ' ',
        border: [false, false, false, false],
      },
    ],
  ]
  let hasOther = false
  if (item.ref) {
    hasOther = true
    body.push([
      {
        text: `${$t('daily.voucher.printout.ref', language)}: ${item.ref}`,
      },
    ])
  }
  if (item.budget_code) {
    hasOther = true
    console.log(item, 'item')

    const name = item[language === 'en' ? 'budget_name_en' : 'budget_name_cn']
    body.push([
      {
        text: `${$t('daily.voucher.printout.budget', language)}: [${item.budget_code}] ${name}`,
      },
    ])
  }
  if (item.st_code) {
    hasOther = true
    const name = language === 'en' ? item.st_name_en : item.st_name_cn
    body.push([
      {
        text: `${$t('daily.voucher.printout.staff', language)}: [${item.st_code}] ${name}`,
      },
    ])
  }
  if (item.vc_dept) {
    hasOther = true
    const name = language === 'en' ? item.dept_name_en : item.dept_name_cn
    body.push([
      {
        text: `${$t('daily.voucher.printout.dept2', language)}: [${item.vc_dept}] ${name}`,
      },
    ])
  }
  if (hasOther) {
    body[0][0].border = [false, false, false, true]
  }

  return {
    width: width,
    // style: 'tableExample',
    table: {
      widths: ['*'],
      headerRows: body.length,
      dontBreakRows: true,
      keepWithHeaderRows: body.length,
      body: body,
    },
    layout: {
      hLineWidth: lineWidth,
      vLineWidth: lineWidth,
      hLineColor: lineColor,
      vLineColor: lineColor,
      hLineStyle: function(i, node) {
        return { dash: { length: 3, space: 1 } }
      },
      vLineStyle: function(i, node) {
        return undefined
      },
      defaultBorder: false,
    },
  }
}

export function generateVoucherInfo(info, language, $t, tableColumnsLength) {
  let chq_no = ''
  const vc_date = dataFormat(info.vc_date)
  const vc_chq_date = dataFormat(info.vc_chq_date)
  let chq_date = ''
  switch (info.vc_method) {
    case 'CASH':
      chq_no = $t('daily.voucher.voucherMethod.cash', language)
      chq_date = vc_date
      break
    case 'CHEQUE':
      chq_no = info.ref
      chq_date = vc_chq_date
      break
    case 'TRANSF':
      chq_no = $t('daily.voucher.voucherMethod.transf', language)
      chq_date = vc_date
      break
    case 'AUTO':
      chq_no = $t('daily.voucher.voucherMethod.auto', language)
      chq_date = vc_date
      break
    case 'OTHER':
      chq_no = info.ref
      chq_date = vc_date
      break
  }

  let data = []
  const margin = [0, 0, 0, 10]

  switch (info.vt_category) {
    case 'P':
    case 'C':
    case 'R':
      if (info.vt_category === 'R' && info.vc_method !== 'CHEQUE') {
        data = [
          {
            width: '*',
            // style: 'tableExample',
            border: [false, false, false, false],
            margin,
            colSpan: 4,
            table: {
              widths: ['11%', '*', '10%', '15%'],
              headerRows: 3,
              dontBreakRows: true,
              keepWithHeaderRows: 3,
              body: [
                [
                  {
                    text: $t('daily.voucher.printout.payee_payment', language) + ':',
                    noWrap: true,
                    colSpan: 1,
                  },
                  {
                    text: info.vc_payee,
                    border: [false, false, false, true],
                    colSpan: 3,
                  },
                  '',
                  '',
                ],
                [
                  {
                    text: $t('daily.voucher.printout.bank', language) + ':',
                    noWrap: true,
                    colSpan: 1,
                  },
                  {
                    text: language === 'en' ? info.vt_description_en : info.vt_description_cn,
                    border: [false, false, false, true],
                    colSpan: 3,
                  },
                  '',
                  '',
                ],
                [
                  {
                    text: $t('daily.voucher.printout.summary', language) + ':',
                    noWrap: true,
                    colSpan: 1,
                  },
                  {
                    text: info.vc_summary,
                    border: [false, false, false, true],
                  },
                  {
                    text: $t('daily.voucher.printout.paidBy', language) + ':',
                    noWrap: true,
                    alignment: 'right',
                  },
                  {
                    text: chq_no,
                    border: [false, false, false, true],
                  },
                ],
              ],
            },
            layout: {
              hLineWidth: lineWidth,
              vLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
              hLineStyle: function(i, node) {
                return { dash: { length: 3, space: 1 } }
              },
              vLineStyle: function(i, node) {
                return undefined
              },
              defaultBorder: false,
            },
          },
          ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
            text: '',
            border: [false, false, false, false],
          })),
        ]
      } else {
        data = [
          {
            width: '*',
            // style: 'tableExample',
            border: [false, false, false, false],
            margin,
            colSpan: 4,
            table: {
              widths: ['11%', '*', '10%', '15%'],
              headerRows: 3,
              dontBreakRows: true,
              keepWithHeaderRows: 3,
              body: [
                [
                  {
                    text:
                      $t(
                        'daily.voucher.printout.' +
                          (info.vt_category === 'R' ? 'payee_payment' : 'payee_receipt'),
                        language,
                      ) + ':',
                    noWrap: true,
                    colSpan: 1,
                  },
                  {
                    text: info.vc_payee,
                    border: [false, false, false, true],
                    colSpan: 3,
                  },
                  '',
                  '',
                ],
                [
                  {
                    text: $t('daily.voucher.printout.bank', language) + ':',
                    noWrap: true,
                    colSpan: 1,
                  },
                  {
                    text: language === 'en' ? info.vt_description_en : info.vt_description_cn,
                    border: [false, false, false, true],
                  },
                  {
                    text: $t('daily.voucher.printout.chq_no', language) + ':',
                    noWrap: true,
                    alignment: 'right',
                  },
                  {
                    text: chq_no,
                    border: [false, false, false, true],
                  },
                ],
                [
                  {
                    text: $t('daily.voucher.printout.summary', language) + ':',
                    colSpan: 1,
                    noWrap: true,
                  },
                  {
                    text: info.vc_summary,
                    border: [false, false, false, true],
                  },
                  {
                    text: $t('daily.voucher.printout.chq_date', language) + ':',
                    alignment: 'right',
                    noWrap: true,
                  },
                  {
                    text: chq_date,
                    border: [false, false, false, true],
                  },
                ],
              ],
            },
            layout: {
              hLineWidth: lineWidth,
              vLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
              hLineStyle: function(i, node) {
                return { dash: { length: 3, space: 1 } }
              },
              vLineStyle: function(i, node) {
                return undefined
              },
              defaultBorder: false,
            },
          },
          ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
            text: '',
            border: [false, false, false, false],
          })),
        ]
      }
      break
    case 'T':
    case 'J':
      {
        const category = language === 'en' ? info.vt_description_en : info.vt_description_cn
        // let category = $t('daily.voucher.label.transferVoucher', language)
        // if (info.vt_category === 'J') {
        //   category = $t('daily.voucher.label.journalVoucher', language)
        // }
        data = [
          {
            width: '*',
            // style: 'tableExample',
            border: [false, false, false, false],
            margin,
            colSpan: 5,
            table: {
              widths: ['auto', '*'],
              headerRows: 2,
              dontBreakRows: true,
              keepWithHeaderRows: 2,
              body: [
                [
                  {
                    text: $t('daily.voucher.printout.voucherType', language) + ':',
                  },
                  {
                    text: category,
                    border: [false, false, false, true],
                  },
                ],
                [
                  {
                    text: $t('daily.voucher.printout.summary', language) + ':',
                  },
                  {
                    text: info.vc_summary,
                    border: [false, false, false, true],
                  },
                ],
              ],
            },
            layout: {
              hLineWidth: lineWidth,
              vLineWidth: lineWidth,
              hLineColor: lineColor,
              vLineColor: lineColor,
              hLineStyle: function(i, node) {
                return { dash: { length: 3, space: 1 } }
              },
              vLineStyle: function(i, node) {
                return undefined
              },
              defaultBorder: false,
            },
          },
          ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
            text: '',
            border: [false, false, false, false],
          })),
        ]
      }
      break
  }
  return data
}

export function generateConsolidateDesc($t, language, tableColumnsLength) {
  return [
    [
      {
        margin: [0, 15, 0, 5],
        width: '*',
        colSpan: tableColumnsLength,
        text: $t('setting.printout.daily.consolidateVoucher.tips1', language),
      },
      ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
        text: '',
        border: [false, false, false, false],
      })),
    ],
    [
      {
        margin: [0, 5, 0, 5],
        width: '*',
        colSpan: tableColumnsLength,
        text: $t('setting.printout.daily.consolidateVoucher.tips2', language),
      },
      ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
        text: '',
        border: [false, false, false, false],
      })),
    ],
  ]
}

export function generateConsolidateVoucherInfo(
  bank,
  accountType,
  date,
  $t,
  language,
  tableColumnsLength,
) {
  return [
    {
      width: '*',
      // style: 'tableExample',
      border: [false, false, false, false],
      margin: [0, 10, 0, 10],
      colSpan: tableColumnsLength,
      table: {
        widths: ['11%', '*', '10%', '10%'],
        headerRows: 2,
        dontBreakRows: true,
        keepWithHeaderRows: 2,
        body: [
          [
            {
              text: $t('setting.printout.daily.consolidateVoucher.bank', language) + ':',
            },
            {
              text: bank,
              border: [false, false, false, true],
              colSpan: 3,
            },
            '',
            '',
          ],
          [
            {
              text: $t('setting.printout.daily.consolidateVoucher.account_type', language) + ':',
            },
            {
              text: accountType,
              border: [false, false, false, true],
            },
            {
              text: $t('setting.printout.daily.consolidateVoucher.date', language) + ':',
            },
            {
              text: date,
              border: [false, false, false, true],
            },
          ],
        ],
      },
      layout: {
        hLineWidth: lineWidth,
        vLineWidth: lineWidth,
        hLineColor: lineColor,
        vLineColor: lineColor,
        hLineStyle: function(i, node) {
          return { dash: { length: 3, space: 1 } }
        },
        vLineStyle: function(i, node) {
          return undefined
        },
        defaultBorder: false,
      },
    },
    ...[...Array(Number(tableColumnsLength) - 1)].map(() => ({
      text: '',
      border: [false, false, false, false],
    })),
  ]
}

export function mergeTotalRow(row, hiddenIndexList) {
  const a = row.map((v, i) => {
    return hiddenIndexList.includes(i)
  })
  let start = 0
  let p = false
  const g = []
  a.forEach((v, i) => {
    if (!v) {
      if (!p) start = i
      if (i === a.length - 1) {
        g.push([start, i])
      }
      p = true
    } else {
      if (!p) return
      p = false
      g.push([start, i - 1])
    }
  })
  g.forEach(c => {
    const s = c[0]
    const e = c[1]
    const d = e - s
    row[s].colSpan = 1 + d
  })
}
