<script>
import { listenTo } from '@/utils/resizeListen'
import { toDecimal } from '@/utils'
import { cancelScrollBarSync, scrollBarSync } from '@/views/budget/common/utils'
import {
  createBudget,
  deleteBudget,
  editBudget,
  editBudgetSeq,
  editBudgetStage,
} from '@/api/budget'
import {
  formatBudgetData,
  getFirstItem,
  getTreeBudgetByCode,
  getTreeBudgetById,
} from '@/views/budget/common/utils'

export default {
  props: {},
  data() {
    return {
      rightResizeListen: {},
      rightInfoHeight: 70,
      rightDataTableWrapper: {},
      rightSummaryTableWrapper: {},
      showRightTable: true,
      rightColumnWidths: {
        icon: 75,
        budget_code: 130,
        name_: 150,
        budget_IE: 60,
        budget_amount: 100,
        approved_amount: 100,
        revised_amount: 100,
        actual_amount: 100,
        balance: 100,
        percentage: 80,
        action: 150,
      },
      btnKey: 'budget.budgetManagement.button.',
      rightScope: {},
    }
  },
  computed: {
    rightTableHeight() {
      // return `height: calc(100% - ${this.summary.budget_IE === 'B' ? 113 : 90}px);`
      return `height: calc(100% - ${this.rightInfoHeight}px - 30px);`
    },
    fyCode() {
      return this.preferences.filters.selectedYear
    },
  },
  created() {},
  beforeDestroy() {
    // cancelScrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    this.rightCancelScrollBarSync()
  },
  mounted() {
    this.rightScrollBarSync()
    // this.$nextTick(() => {
    //   this.rightResizeListen = listenTo(this.$refs.form.$el, ({ width, height, ele }) => {
    //     this.rightInfoHeight = height + 10
    //   })
    // })
    this.rightInfoHeight = 0
  },
  updated() {},
  methods: {
    rightScrollBarSync() {
      this.$nextTick(() => {
        try {
          this.rightDataTableWrapper =
            this.$refs['rightDataTable'].$el.querySelector('.el-table__body-wrapper')
          this.rightSummaryTableWrapper =
            this.$refs['rightSummaryTable'].$el.querySelector('.el-table__body-wrapper')
          scrollBarSync(this.rightDataTableWrapper, this.rightSummaryTableWrapper)
        } catch (e) {
          // error
        }
      })
    },
    rightCancelScrollBarSync() {
      this.$nextTick(() => {
        cancelScrollBarSync(this.rightDataTableWrapper, this.rightSummaryTableWrapper)
      })
    },
    calcBalancePercentage(row) {
      const b = Number(row[this.budgetAmountField])
      const a = Number(row.actual_amount)
      // console.log(b, '-', a)
      if (a === 0) {
        return '100%'
      }
      if (Number.isNaN(a) || Number.isNaN(b)) {
        return 'N/A'
      }
      return toDecimal(((b - a) / b) * 100).toFixed(2) + '%'
    },
    calcBalancePercentage1(AData, BData) {
      const b = Number(AData)
      const a = Number(BData)
      // console.log(b, '-', a)
      if (a === 0) {
        return '100%'
      }
      if (Number.isNaN(a) || Number.isNaN(b)) {
        return 'N/A'
      }
      return toDecimal(((b - a) / b) * 100).toFixed(2) + '%'
    },

    handleChangeStage(stage, message, type) {
      if (!this.budgetGroup) return false

      const fy_code = this.fyCode
      const budget_id = this.budgetGroup && this.budgetGroup.budget_id
      const budget_stage = stage || ''
      if (!fy_code || !budget_id || !budget_stage) {
        this.$message.error(this.$t('message.pleaseReload'))
        return
      }
      this.$alert(message, this.$t('budget.budgetManagement.message.tips'), {
        type,
        showCancelButton: true,
        callback: (action, instance) => {
          if (action === 'confirm') {
            editBudgetStage({ fy_code, budget_id, budget_stage }).then(() => {
              const bg = this.budgetGroup
              bg.budget_stage = budget_stage
              this.currentBudgetGroup = bg
              // this.$emit('update:budgetGroup', bg)
              this.$message.success(this.$t('message.success'))
              // this.$emit('reloadThis')
              this.loadThisData()
              this.$nextTick(() => {
                // this.reshowTable()
              })
            })
          }
        },
      })
    },
    handleCancelStage() {
      const haveRevisedNum = this.thisTableData.some(v => v.revised_num > 1)
      const key = {
        X: '',
        S: 'X',
        A: 'S',
        O: 'A',
        C: 'O',
        R: haveRevisedNum ? 'D' : 'C',
        T: 'R',
        K: 'T',
        B: 'K',
        D: 'B',
      }
      console.log(this.thisTableData)
      const oldBudgetStage = this.budgetGroup && this.budgetGroup.budget_stage
      const budget_stage = key[oldBudgetStage] || ''
      this.handleChangeStage(budget_stage, this.cancelStageMessage, 'warning')
    },
    handleNextStage() {
      const key = {
        X: 'S',
        S: 'A',
        A: 'O',
        O: 'C',
        C: 'R',
        R: 'T',
        T: 'K',
        K: 'B',
        B: 'D',
        D: 'R',
      }
      const oldBudgetStage = this.budgetGroup && this.budgetGroup.budget_stage
      const budget_stage = key[oldBudgetStage] || ''

      this.handleChangeStage(budget_stage, this.nextStageMessage, 'success')
    },
    rightSummarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3,
        }
      } else if (columnIndex < 3) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    /* 複製結構 */
    handleCopyComposition(mode) {
      this.$emit('handleCopyComposition', mode)
    },
    /* 清除結構 */
    handleCleanComposition(mode) {
      this.$emit('handleCleanComposition', mode)
    },
    checkHaveEdit() {
      const haveEdit = this.thisTableData.some(i => i.edit)
      if (haveEdit) {
        this.$message.warning(this.$t('budget.budgetManagement.message.addTip'))
        return true
      } else {
        return false
      }
    },
    async handleAdd(scope, budget_type) {
      if (this.checkHaveEdit()) return
      const group = getTreeBudgetById(
        this.budgetGroupList,
        this.preferences.filters.selectedGroupId,
      )
      const data = {
        fy_code: this.preferences.filters.selectedYear,
        budget_type,
        budget_code: '',
        budget_IE:
          budget_type === 'D' ? (group.budget_IE === 'B' ? 'E' : group.budget_IE) : group.budget_IE,
        name_cn: '',
        name_en: '',
        budget_stage: group.budget_stage,
        proposed_amount: 0,
        approved_amount: 0,
        last_proposed_amount: 0,
        last_approved_amount: 0,
        budget_active: 'Y',
        seq: 99,
        edit: true,
        temp_id: Date.now(),
        temp_new: true,
      }
      const list = this.deepCloneBudget(this.thisTableData)
      if (scope && scope.row) {
        // 有上級
        data.parent_budget_id = scope.row.budget_id
        const budget = getTreeBudgetById(list, scope.row.budget_id)
        if (budget.children && Array.isArray(budget.children)) {
          budget.children.push(data)
        } else {
          budget.children = [data]
        }
      } else {
        data.parent_budget_id = group.budget_id
        // 無上級
        list.push(data)
      }
      try {
        this.thisTableData = list

        // await createBudget(data)
        // this.$message.success(this.$t('message.success'))
        // this.loadThisData()
      } catch (e) {
        //
      }
    },
    showUp(scope) {
      if (!this.budgetGroup || !scope || !scope.row) return false
      // const i = scope.$index
      const index = scope.row._index
      if ('XR'.includes(this.budgetGroup.budget_stage)) {
        return index !== 0

        // const p = this.data[i - 1]
        //
        // const type = scope.row.budget_type
        // const level = scope.row.level
        // if (type === 'D') {
        //   if (p.budget_type === 'D' && p.level === level) {
        //     return true
        //   }
        // } else if (type === 'C') {
        //   return p.level !== level - 1
        // }
      }
      return false
    },
    showDown(scope) {
      if (!this.budgetGroup || !scope || !scope.row) return false
      // const i = scope.$index
      const index = scope.row._index
      const item = scope.row
      if ('XR'.includes(this.budgetGroup.budget_stage)) {
        if (item._level === 1) {
          for (let j = 0; j < this.thisTableData.length; j++) {
            const jItem = this.thisTableData[j]
            if (jItem.budget_id === item.budget_id) {
              if (j < this.thisTableData.length - 1) {
                return true
              }
            }
          }
        } else {
          if (item.parent && item.parent.children) {
            if (item.parent.children.length - 1 > index) {
              return true
            }
          }
        }
        // if (i === (this.data.length - 1)) {
        //   return false
        // }
        // const n = this.data[i + 1]
        //
        // const type = scope.row.budget_type
        // const level = scope.row.level
        // if (type === 'D') {
        //   if (n.budget_type === 'D' && n.level === level) {
        //     return true
        //   }
        // } else if (type === 'C') {
        //   let canDown = false
        //   for (let j = (i + 1); j < this.data.length; j++) {
        //     if (j.level === level) {
        //       canDown = true
        //       break
        //     } else if (j.level > level) {
        //       break
        //     }
        //   }
        //   return canDown
        // }
      }
      return false
    },
    async handleDelete(scope) {
      if (!scope || !scope.row) return false
      if (scope.row.budget_type === 'C') {
        if (scope.row.children && scope.row.children.length > 0) {
          this.$message.success(this.$t('budget.budgetManagement.message.hasChildren'))
        }
      }
      const fy_code = this.fyCode
      try {
        await this.$confirm(
          `${this.$t('confirm.deleteConfirm')} [ ${scope.row.budget_code} ${
            scope.row[this.language === 'en' ? 'name_en' : 'name_cn']
          } ] ?`,
          this.$t('confirm.warningTitle'),
          {
            confirmButtonText: this.$t('confirm.confirmButtonText'),
            cancelButtonText: this.$t('confirm.cancelButtonText'),
            type: 'warning',
          },
        )
        const budget_id = scope.row.budget_id
        await deleteBudget({ fy_code, budget_id })
        this.preferences.filters.bottomBudgetId = ''
        this.bottomTableData = []
        await this.handleChangeBottomYear(this.preferences.filters.selectedYear, false)
        // this.$emit('reloadThis')
        await this.loadThisData()
        this.$message.success(this.$t('message.success'))
      } catch (e) {
        console.error(e)
      }
    },
    showDelete(scope) {
      if (!this.budgetGroup || !scope || !scope.row) return false
      if (
        scope.row.revised_num > 0 ||
        (scope.row.actual_amount && Number(scope.row.actual_amount) > 0)
      ) {
        return false
      }
      if ('XR'.includes(this.budgetGroup.budget_stage)) {
        const type = scope.row.budget_type
        if (type === 'D') {
          return true
        } else if (type === 'C') {
          return !scope.row.children || scope.row.children.length === 0
        }
      }
      return false
    },
    onRightRowClick(row, column, event) {
      console.log(
        this.preferences.filters.selectedGroupId,
        'this.preferences.filters.selectedGroupId',
      )
      if (row.edit) return
      if (this.rightScope.row && this.rightScope.row.budget_id === row.budget_id) {
        this.clearLeftSelect()
        this.changeBottomList(
          this.fyCode,
          this.preferences.filters.selectedGroupId,
          this.currentBudgetGroup,
        )
        return
      }
      this.clearLeftSelect()
      this.rightScope = { row }

      this.changeBottomList(this.fyCode, row.budget_id, this.currentBudgetGroup)
    },
    handleUp(scope) {
      const budget_id = scope.row.budget_id
      const seq = scope.row.seq - 1
      this.handleChangeSeq(budget_id, seq)
    },
    handleDown(scope) {
      const budget_id = scope.row.budget_id
      const seq = scope.row.seq + 2
      this.handleChangeSeq(budget_id, seq)
    },
    /* 調整位置 */
    async handleChangeSeq(budget_id, seq) {
      const fy_code = this.preferences.filters.selectedYear
      try {
        await editBudgetSeq({ fy_code, budget_id, seq })
        this.loadThisData()
      } catch (e) {
        //
      }
    },
    /* 編輯 */
    handleEdit(scope) {
      if (this.checkHaveEdit()) return
      this.changeEditState(scope, true)
    },
    handleCancel(scope) {
      this.changeEditState(scope, false)
    },
    changeEditState(scope, state) {
      if (scope.row.budget_id) {
        const list = this.deepCloneBudget(this.thisTableData)
        // const index = list.findIndex(i => i.budget_id === scope.row.budget_id)
        const budget = getTreeBudgetById(list, scope.row.budget_id)
        if (state) {
          budget.budget_code_temp = budget.budget_code
          budget.name_cn_temp = budget.name_cn
          budget.name_en_temp = budget.name_en
          budget.proposed_amount_temp = budget.proposed_amount
          budget.approved_amount_temp = budget.approved_amount
        }
        budget.edit = state
        // this.$set(list[index], 'edit', true)
        this.thisTableData = list
      } else {
        const list = this.deepCloneBudget(
          this.thisTableData,
          i => !i.temp_id || i.temp_id !== scope.row.temp_id,
        )
        this.thisTableData = list
      }
    },
    async handleUpdate(scope) {
      // const data = {
      //   fy_code: this.fyCode,
      //   budget_id: scope.row.budget_id,
      //   budget_code: scope.row.budget_code,
      //   budget_IE: scope.row.budget_IE,
      //   name_cn: scope.row.name_cn,
      //   name_en: scope.row.name_en,
      //   budget_active: scope.row.budget_active,
      //   seq: scope.row.seq
      // }
      // budget code必須填寫
      // name cn必須填寫
      // name en必須填寫
      const list = this.deepCloneBudget(this.thisTableData)
      // const index = list.findIndex(i => i.budget_id === scope.row.budget_id)
      const budget = getTreeBudgetById(list, scope.row.budget_id)
      if (budget) {
        const data = Object.assign({}, budget)

        data.fy_code = this.preferences.filters.selectedYear
        budget.name_en =
          budget.name_cn =
          data.name_en =
          data.name_cn =
            data['name_' + (this.language === 'en' ? 'en' : 'cn') + '_temp']

        data.budget_code = budget.budget_code = budget.budget_code_temp
        data.proposed_amount = budget.proposed_amount = budget.proposed_amount_temp
        data.approved_amount = budget.approved_amount = budget.approved_amount_temp

        if (!data.budget_code) {
          this.$message.error(this.$t('budget.budgetManagement.message.inputBudgetCode'))
          return
        }
        if (!data.name_cn_temp || !data.name_en_temp) {
          this.$message.error(this.$t('budget.budgetManagement.message.inputBudgetName'))
          return
        }
        console.log(data)
        try {
          data.name_cn = data.name_cn_temp
          data.name_en = data.name_en_temp
          if (data.budget_id) {
            await editBudget(data)
          } else {
            await createBudget(data)
          }
          this.$message.success(this.$t('message.success'))
          // this.$emit('reloadThis')
          // this.$set(scope.row, 'edit', false)
          // this.$set(list[index], 'edit', false)
          budget.edit = false
          this.thisTableData = list
          this.loadThisData()
          this.handleChangeBottomYear(this.preferences.filters.selectedYear, false)
        } catch (e) {
          console.error(e)
        }
      }
    },
    clearRightSelect() {
      this.rightScope = {}
      this.$refs.rightDataTable.setCurrentRow()
    },
  },
}
</script>
