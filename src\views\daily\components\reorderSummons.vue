<template>
  <div ref="page" class="reorder-summons-page">
    <BTable
      ref="table"
      :data="tableData"
      :style-columns="styleColumns"
      :lang-key="langKey"
      :show-actions="false"
      :resizable="false"
      :height="pageHeight - 30"
      class="table"
      border
    >
      <template slot="columns">
        <vxe-table-column
          v-for="item in styleColumns"
          :key="item.ss_key"
          :title="$t(langKey + item.title)"
          :width="item.width"
          :field="item.ss_key"
          :column-key="item.ss_key"
          align="center"
        >
          <template v-if="scope && scope.row" slot-scope="scope">
            <span v-if="item.ss_key === 'vc_amount'">
              {{ formatter(scope.row.vc_amount) }}
            </span>
            <span v-else-if="item.ss_key === 'newSummonsNumber'">
              <big-select v-model="scope.row.vc_init_no" :list="noList" class="select" />
            </span>
            <span v-else>
              {{ scope.row[item.ss_key] }}
            </span>
          </template>
        </vxe-table-column>
      </template>
    </BTable>
    <div style="margin-top: 5px">
      <el-button type="primary" size="mini" @click="onUpdate">
        {{ $t('daily.voucher.button.update') }}
      </el-button>
      <el-button type="primary" size="mini" @click="onReset">
        {{ $t('daily.voucher.button.reset') }}
      </el-button>
      <el-button type="primary" size="mini" @click="onCancel">
        {{ $t('daily.voucher.button.cancel') }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { listenTo } from '@/utils/resizeListen'
import BTable from '@/components/BTable'
import BigSelect from '@/components/BigSelect/index'

import { amountFormat } from '@/utils'
import { updateVcNo } from '@/api/vouchers/actions/index.js'
export default {
  name: 'ReorderSummons',
  components: {
    BTable,
    BigSelect,
  },
  beforeRouteEnter(to, from, next) {
    if (!to.params.selectData) {
      next(to.matched[1])
    } else {
      next()
    }
  },
  data() {
    return {
      langKey: 'daily.voucher.label.',
      tableData: [],
      noList: [],
      pageHeight: 500,
      fy_code: '',
    }
  },
  computed: {
    styleColumns() {
      return [
        {
          alignment: 'center',
          title: 'originalSummonsNumber',
          ss_key: 'vc_no',
          ss_type: 'column',
          width: 180,
        },
        {
          alignment: 'center',
          title: 'summonsDate',
          ss_key: 'vc_date',
          ss_type: 'column',
          width: 100,
        },
        {
          alignment: 'center',
          ss_key: 'ref',
          title: 'checkNumber',
          ss_type: 'column',
          width: 80,
        },
        {
          alignment: 'center',
          title: 'payee',
          ss_key: 'vc_payee',
          ss_type: 'column',
          width: 100,
        },
        {
          alignment: 'center',
          title: 'moneny',
          ss_key: 'vc_amount',
          ss_type: 'column',
          width: 80,
        },
        {
          alignment: 'center',
          ss_key: 'newSummonsNumber',
          title: 'newSummonsNumber',
          ss_type: 'column',
          minWidth: 150,
        },
      ]
    },
  },
  mounted() {
    if (this.$route.params.selectData) {
      this.noList = this.$route.params.selectData.map(item => {
        return {
          label: item.vc_no,
          value: item.vc_no,
        }
      })
      this.tableData = this.$route.params.selectData.map(item => {
        return {
          ...item,
          vc_init_no: item.vc_no,
        }
      })
    }
    if (this.$route.params.fy_code) {
      this.fy_code = this.$route.params.fy_code
    }
    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        console.log('width', height)
        this.pageHeight = height
      })
    })
  },
  methods: {
    formatter(data) {
      return amountFormat(data)
    },
    onCancel() {
      this.$router.push(this.$route.matched[1])
    },
    onReset() {
      this.tableData = this.tableData.map(item => {
        return {
          ...item,
          vc_init_no: item.vc_no,
        }
      })
    },
    async onUpdate() {
      const update_json = this.tableData.map(item => {
        return {
          vc_id: item.vc_id,
          vc_no: item.vc_init_no,
        }
      })
      updateVcNo({
        fy_code: this.fy_code,
        update_json: JSON.stringify(update_json),
      })
        .then(response => {
          this.$router.push(this.$route.matched[1])
        })
        .catch(() => {})
    },
  },
}
</script>

<style scoped lang="scss">
.reorder-summons-page {
  height: 100%;
}
.select {
  width: calc(100% - 30px);
  min-width: 80px;
}
.table {
  .b-table {
    height: 100%;

    /*height: calc(100vh - 220px);*/
    /deep/ {
      td.vc_rdate {
        margin: 2px 0;

        .vxe-cell {
          max-height: unset;
          height: 27px;
          line-height: 25px;
        }

        .cell,
        .vxe-cell {
          height: 27px;
          line-height: 25px;
          padding: 0;
          text-overflow: unset;

          .select {
            width: calc(100% - 30px);
            min-width: 80px;
          }

          svg {
            width: 19px;
            height: 19px;
            vertical-align: middle;
          }

          & > span {
            padding: 0 5px;
          }
        }
      }
    }
  }
}
::v-deep .el-input__inner {
  height: 22px !important;
  line-height: 22px !important;
}
::v-deep .vxe-body--column {
  height: 31px !important;
}
</style>
