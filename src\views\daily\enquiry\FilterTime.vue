<template>
  <div v-bind="$attrs" class="filter-time">
    <el-select v-model="vTimeType" class="time-type" @change="onTimeType">
      <el-option
        v-for="item in timeTypeList"
        :key="item.label"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-select v-model="vYearID" :disabled="vTimeType === 'A'" class="year">
      <el-option label="--" value="" />
      <el-option
        v-for="item in year_list"
        :key="item.fy_id"
        :label="item.fy_name"
        :value="item.fy_id"
      />
    </el-select>
    <el-select v-model="vMonthCode" :disabled="vTimeType === 'A'" class="month" @change="change">
      <el-option label="--/----" value="" />
      <el-option
        v-for="item in month_list"
        :key="item.pd_id"
        :label="item.pd_name"
        :value="item.pd_code"
      />
    </el-select>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// API
import { fetchYears, getYear } from '@/api/master/years'

export default {
  name: 'FilterTime',
  props: {
    timeType: {
      type: String,
      default: 'A',
    },
    yearID: {
      type: [String, Number],
      default: '',
    },
    yearCode: {
      type: [String, Number],
      default: '',
    },
    monthID: {
      type: [String, Number],
      default: '',
    },
    monthCode: {
      type: [String, Number],
      default: '',
    },
    // paneStateList: {
    //   type：Array,
    //   required: true
    // }
  },
  data: function() {
    return {
      vTimeType: this.timeType,
      vYearID: this.yearID,
      vYearCode: this.yearCode,
      vMonthID: this.monthID,
      vMonthCode: this.monthCode,

      year_list: [],
      month_list: [],
      loading: true,
    }
  },
  computed: {
    ...mapGetters(['currentYear']),
    timeTypeList() {
      return [
        {
          label: this.$t('enquiry.timeType.all'),
          value: 'A',
        },
        {
          label: this.$t('enquiry.timeType.year'),
          value: 'Y',
        },
        {
          label: this.$t('enquiry.timeType.to'),
          value: 'T',
        },
        {
          label: this.$t('enquiry.timeType.month'),
          value: 'M',
        },
      ]
    },
  },
  watch: {
    timeType: {
      immediate: true,
      handler(value) {
        this.vTimeType = value
      },
    },
    vTimeType: {
      immediate: true,
      handler(value) {
        // this.$emit('update:timeType', value)
      },
    },
    yearID: {
      immediate: true,
      handler(value) {
        this.vYearID = value
      },
    },
    vYearID: {
      immediate: true,
      handler(value) {
        // this.$emit('update:yearID', value)
        this.$nextTick(() => {
          this.changeYear(value)
        })
      },
    },
    yearCode: {
      immediate: true,
      handler(value) {
        this.vYearCode = value
      },
    },
    vYearCode: {
      immediate: true,
      handler(value) {
        // this.$emit('update:yearCode', value)
      },
    },
    monthID: {
      immediate: true,
      handler(value) {
        this.vMonthID = value
      },
    },
    vMonthID: {
      immediate: true,
      handler(value) {
        // this.$emit('update:monthID', value)
      },
    },
    monthCode: {
      immediate: true,
      handler(value) {
        this.vMonthCode = value
      },
    },
    vMonthCode: {
      immediate: true,
      handler(value) {
        // this.$emit('update:monthCode', value)
      },
    },
  },
  created() {},
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.year_list = res
        })
        .finally(() => {
          // this.$emit('init')
          this.loading = false
        })
    },
    change() {
      if (this.vMonthCode) {
        if ('AY'.includes(this.vTimeType)) {
          this.vTimeType = 'M'
        }
      } else if ('MT'.includes(this.vTimeType)) {
        if (this.vYearID) {
          this.vTimeType = 'Y'
        } else {
          this.vTimeType = 'A'
        }
      }

      this.$emit('change', {
        type: this.vTimeType,
        fy_id: this.vYearID,
        fy_code: this.vYearCode,
        pd_id: this.vMonthID,
        pd_code: this.vMonthCode,
      })
    },
    changeYear(fy_id, noEmit) {
      // this.vYearID = item.fy_id
      if (this.loading) return
      if (!fy_id) {
        this.vTimeType = 'A' // 所有
        this.month_list = []
        this.vMonthID = ''
        this.vMonthCode = ''
        this.vYearCode = ''
        this.$nextTick(() => {
          if (!noEmit) this.change()
        })

        return
      }
      // this.vTimeType = 'Y' // 全年
      const item = this.year_list.find(i => i.fy_id === fy_id)
      if (!item) {
        this.$message.error(this.$t('confirm.systemError'))
        return
      }

      this.vYearCode = item.fy_code
      return new Promise(resolve => {
        getYear(fy_id)
          .then(res => {
            const periods = res.periods
            this.month_list = periods
            const month = periods.find(item => item.pd_code === this.vMonthCode)
            if (!month) {
              this.vMonthID = ''
              this.vMonthCode = ''
            }
            if (!noEmit) this.change()
            resolve()
          })
          .catch(() => {})
      })
    },
    setYear() {
      if (!this.vYearCode && this.year_list.length) {
        const year = this.year_list.find(i => i.fy_id === this.currentYear.fy_id)
        if (year) {
          this.vYearCode = year.fy_code
          this.vYearID = year.fy_id
        } else {
          this.vYearCode = this.year_list[0].fy_code
          this.vYearID = this.year_list[0].fy_id
        }
      }
    },
    async onTimeType(val) {
      const that = this
      switch (val) {
        case 'A':
          this.vYearCode = ''
          this.vYearID = ''
          this.vMonthID = ''
          this.vMonthCode = ''
          that.change()
          break
        case 'Y':
          this.setYear()
          this.vMonthID = ''
          this.vMonthCode = ''
          that.change()
          break
        case 'T':
        case 'M':
          that.setYear()
          that.changeYear(this.vYearID, true).then(() => {
            if (this.month_list.length) {
              const item = this.month_list[0]
              this.vMonthCode = item.pd_code
              this.vMonthID = item.pd_id
              this.change()
            }
          })
          break
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.filter-time {
  display: inline-block;
  .time-type {
    width: 100px;
  }
  /deep/ {
    .el-select {
      margin-bottom: 2px;
    }
  }
  .year {
    width: 100px;
  }
  .month {
    width: 100px;
  }
  /deep/ {
    .el-input__inner,
    .el-input__icon {
      height: 25px !important;
      line-height: 25px !important;
    }
  }
}
</style>
