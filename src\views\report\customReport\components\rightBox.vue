<template>
  <div v-loading="loading">
    <div ref="filter" class="filter">
      <el-form :inline="true" label-width="60px" class="mini-form">
        <el-form-item v-if="!isAddOrEdit" :label="$t('customReport.reportName')">
          <el-select
            v-model="preferences.filters.custom_report_id"
            style="width: 300px"
            @change="onChangeCustomReport"
          >
            <el-option
              v-for="item in customReports"
              :key="item.custom_report_id"
              :label="item.report_name"
              :value="item.custom_report_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isAddOrEdit" :label="$t('customReport.reportName')">
          <el-input v-model="reportData.report_name" :placeholder="$t('customReport.placeholder.input')" disabled style="width: 300px;" />
        </el-form-item>
      </el-form>
    </div>
    <template v-if="section_data.length > 0">
      <div v-for="(item, index) in section_data" :key="index">
        <template v-if="['ACCOUNT', 'BANK'].includes(item.section_type)">
          <div v-if="item.section_title" class="section-title">
            {{ item.section_title }}:
          </div>
          <ETable
            :data="commonTabelData(item.row)"
            :show-actions="false"
            :row-style="rowStyle"
            height="auto"
            show-summary
            :summary-method="item.row_show_total === 1 ? () => commonSummaryMethod(item.col) : null"
            :style="{ width: item.section_type === 'ACCOUNT' ? '100%': 261 + (item.col.length - 1) * 80 + 'px' }"
            border
            style="width: 100%"
            class="account-situation-table"
          >
            <template slot="columns">
              <el-table-column
                v-for="(colItem, colIndex) in makeColumnData(item.col)"
                :key="colItem.title"
                :label="$t('customReport.tableKey.' + colItem.title)"
                :width="colItem.width"
                :field="colItem.title"
                :column-key="colItem.title"
                :align="colIndex === 0 ? 'left' : 'right'"
              >
                <template v-if="scope && scope.row" slot-scope="scope">
                  <span v-if="colItem.ss_key === 'title'" :style="{ marginLeft: `${(scope.row.level - 1) * 10}px` }">
                    {{ scope.row.title }}
                  </span>
                  <span v-else>
                    <span>{{ scope.row.type === 'TEXT' ? '' : '0.00' }}</span>
                  </span>
                </template>
              </el-table-column>
            </template>
          </ETable>
        </template>
        <template v-else-if="item.section_type === 'SIGNATURE'">
          <div class="signature-box">
            <div v-for="signatureItem in makeTableData(item.row)" :key="signatureItem.title" class="signature-item">
              <div class="signature-title">
                <span>{{ signatureItem.title }}:</span>
                <span class="line" />
              </div>
              <div v-if="signatureItem.show_date === 1" class="signature-date">
                <span>{{ $t('customReport.date') }}: </span>
                <span class="line" />
              </div>
            </div>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import { fetchCustomReportDetail, fetchCustomReports } from '../../../../api/report/customReports'
import ETable from '@/components/ETable'
export default {
  name: 'RightBox',
  components: {
    ETable,
  },
  props: {
    customReportId: {
      type: [String, Number],
      default: '',
    },
    isAddOrEdit: {
      type: Boolean,
      default: false,
    },
    addReportData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      preferences: {
        filters: {
          custom_report_id: '',
        },
      },
      customReports: [],
      reportData: {},
      section_data: [],
      loading: false,
    }
  },
  watch: {
    customReportId: {
      handler(v) {
        if (v) {
          this.preferences.filters.custom_report_id = v
          this.getReportData()
        } else {
          this.reportData = {}
          this.section_data = []
        }
      },
      deep: true,
      immediate: true,
    },
    addReportData: {
      handler(v) {
        if (v) {
          console.log(v, 'v')
          this.reportData = v
          this.section_data = v.section_data
        } else {
          this.reportData = {}
          this.section_data = []
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.getCustomReports()
  },
  methods: {
    async getCustomReports() {
      try {
        const res = await fetchCustomReports()
        if (res) {
          this.customReports = res
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getReportData() {
      try {
        if (!this.preferences.filters.custom_report_id) return
        this.loading = true
        const res = await fetchCustomReportDetail(this.preferences.filters.custom_report_id)
        if (res) {
          this.reportData = res
          this.section_data = res.section_data
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    onChangeCustomReport() {
      this.getReportData()
    },
    commonTabelData(data) {
      let tableData = []
      if (data && data.length > 0) {
        tableData = this.makeTableData(data)
        console.log(tableData)
      }
      return tableData
    },
    makeColumnData(cols) {
      if (!cols) return []
      const columns = cols.map((item, index) => {
        let otherConfig = {
          width: 80,
          ss_key: 'amount',
        }
        if (index === 0) {
          otherConfig = {
            width: 260,
            ss_key: 'title',
          }
        }
        console.log({
          title: item,
          ...otherConfig,
        }, 9999)
        return {
          title: item,
          ...otherConfig,
        }
      })
      return columns
    },
    makeTableData(data, level = 1) {
      console.log(data, 'data')
      const tableData = []
      data.forEach(item => {
        tableData.push({
          ...item,
          level: item.level || level,
        })
        if (item.sub_row) {
          tableData.push(...this.makeTableData(item.sub_row, level + 1))
        }
      })
      return tableData
    },
    rowStyle({ row, rowIndex }) {
      if (row.level) {
        return {
          backgroundColor: `rgba(62, 151, 221, ${(row.level - 1) * 10}%)`,
        }
      }
      return {}
    },
    commonSummaryMethod(cols) {
      if (!cols) return []
      const footerData = cols.map((item, index) => {
        if (index === 0) {
          return this.$t('customReport.total')
        }
        return '0.00'
      })
      const sums = [...footerData]
      return sums
    },
    clearData() {
      this.preferences.filters.custom_report_id = ''
      this.getCustomReports()
      this.reportData = {}
      this.section_data = []
    },
  },

}
</script>

<style scoped lang="scss">
.filter {
  /*width: 670px;*/
  margin: 0 0 8px;
  display: flex;
  /*justify-content: space-between;*/
  /*align-items: center;*/
  /*justify-content: space-around*/
  span {
    line-height: 30px;
    height: 30px;
    color: gray;
    padding: 0 5px;
  }
  input {
    line-height: 30px;
    height: 30px;
  }
  .year {
    width: 150px;
  }
  /deep/ {
    .el-input--medium .el-input__icon {
      /*line-height: 30px;*/
    }

    .el-form-item__label {
      width: auto !important;
    }
  }
}
.account-situation-table {
  margin-bottom: 10px;
  /deep/ {
    .el-table__body-wrapper {
      height: auto !important;
    }
    .el-table__footer-wrapper {
      tr {
        td {
          color: #fff;
          background-color: #3E97DD!important;
        }
        td:first-child {
          text-align: right;
        }
      }
    }
  }
}
.signature-box {
  display: flex;
  flex-wrap: wrap;
  color: #606266;
  gap: 5px 40px;
  .signature-item {
    display: flex;
    flex-direction: column;
    .signature-title, .signature-date {
      // margin-top: 10px;
      color: #606266;
      align-self: flex-end;
    }
    .signature-date {
      margin-top: 10px;
    }
    .line {
      display: inline-block;
      width: 110px;
      height: 1px;
      background-color: #DCDFE6;
      transform: translateY(5px);
    }
  }
}
.section-title {
  margin-bottom: 5px;
  color: #606266;
}
</style>
