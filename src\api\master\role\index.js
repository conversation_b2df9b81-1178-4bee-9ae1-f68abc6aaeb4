import request from '@/utils/request'

/**
 * 新增角色
 * @param system
 * @param role_code
 * @param role_name_cn
 * @param role_name_en
 * @param p_selected_json
 */
export function createRole(system, role_code, role_name_cn, role_name_en, p_selected_json) {
  return request({
    url: '/roles/actions/create',
    method: 'post',
    data: {
      system,
      role_code,
      role_name_cn,
      role_name_en,
      p_selected_json,
    },
  })
}

/**
 * 修改角色
 * @param role_id
 * @param role_code
 * @param role_name_cn
 * @param role_name_en
 * @param p_selected_json
 */
export function editRole(role_id, role_code, role_name_cn, role_name_en, p_selected_json) {
  return request({
    url: '/roles/actions/update',
    method: 'post',
    data: {
      role_id,
      role_code,
      role_name_cn,
      role_name_en,
      p_selected_json,
    },
  })
}

/**
 * 刪除角色
 * @param role_id
 */
export function deleteRole(role_id) {
  return request({
    url: '/roles/actions/delete',
    method: 'post',
    data: {
      role_id,
    },
  })
}

/**
 * 查詢所有角色
 */
export function fetchRoles(system) {
  return request({
    url: '/roles',
    method: 'get',
    params: {
      system,
    },
  })
}

/**
 * 獲取某角色詳情
 * @param role_id
 */
export function getRole(role_id) {
  return request({
    url: '/roles/actions/inquire',
    method: 'get',
    params: {
      role_id,
    },
  })
}

export default { createRole, editRole, deleteRole, fetchRoles, getRole }
