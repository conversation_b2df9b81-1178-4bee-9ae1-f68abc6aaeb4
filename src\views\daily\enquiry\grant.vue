<template>
  <div ref="page" v-loading="loading" class="enquiry-grant">
    <div ref="filters">
      <el-row style="margin-bottom: 5px">
        <el-col :span="19" class="filter">
          <el-row>
            <el-col :span="6">
              <div class="enquiry-title">
                <i class="edac-icon edac-icon-allowance view-icon" />
                <span class="view-title">{{ $t('router.enquiryGrant') }}</span>
              </div>
            </el-col>
            <el-col :span="18">
              <el-form :inline="true" class="mini-form mini-width">
                <el-form-item>
                  <el-select
                    v-model="preferences.filters.type"
                    class="time-type"
                    style="width: 110px"
                    @change="onChangeTimeType"
                  >
                    <el-option :label="$t('enquiry.timeType.all')" value="A" />
                    <el-option :label="$t('enquiry.timeType.year')" value="Y" />
                    <el-option :label="$t('enquiry.timeType.to')" value="T" />
                    <el-option :label="$t('enquiry.timeType.month')" value="M" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="'YTM'.includes(preferences.filters.type)">
                  <el-select
                    v-model="preferences.filters.fy_id"
                    :disabled="preferences.filters.type === 'A'"
                    style="width: 110px"
                    class="year"
                    @change="onChangeYear"
                  >
                    <el-option
                      v-for="item in year_list"
                      :key="item.fy_id"
                      :label="item.fy_name"
                      :value="item.fy_id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="'TM'.includes(preferences.filters.type)">
                  <el-select
                    v-model="preferences.filters.pd_code"
                    :disabled="preferences.filters.type === 'A'"
                    class="month"
                    style="width: 100px"
                    @change="fetchData"
                  >
                    <el-option
                      v-for="item in month_list"
                      :key="item.pd_id"
                      :label="item.pd_name"
                      :value="item.pd_code"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="5">
          <div class="actions-icon">
            <i
              class="edac-icon action-icon edac-icon-setting1"
              :title="$t('btnTitle.pageSetting')"
              @click="onSetting"
            />
            <i
              v-if="hasPermission_Output"
              :title="$t('btnTitle.exportExcelPage')"
              class="edac-icon action-icon edac-icon-excel"
              @click="onExport('PAGE')"
            />
            <i
              v-if="hasPermission_Output"
              :title="$t('btnTitle.exportExcelAll')"
              class="edac-icon action-icon edac-icon-excel_add"
              @click="onExport('ALL')"
            />
          </div>
        </el-col>
      </el-row>
      <el-row class="account-filter">
        <!-- 賬目類別 -->
        <el-select
          v-model="preferences.filters.fund"
          class="fund"
          style="width: 130px"
          @change="changeFund"
        >
          <el-option
            v-for="item in funds"
            :key="item.fund_id"
            :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
            :value="item.fund_id"
          />
        </el-select>
        <!-- 會計科目 -->
        <SelectTree
          v-model="preferences.filters.ac_code"
          :options="accountOptions"
          :props="{
            label: language === 'en' ? 'name_en' : 'name_cn',
            value: 'code',
            code: 'code',
            group_id: 'fund_id',
            value_id: 'account_id',
            children: 'children',
          }"
          :group-id.sync="preferences.filters.ac_fund_id"
          :select-group.sync="preferences.filters.ac_selectGroup"
          :show-all-option="false"
          style="width: 300px"
          @change="onSelectAccount"
        />
        <!--  -->
        <i v-if="preferences.filters.nature_code" :class="natureClass" @click="changeNature" />
      </el-row>
    </div>
    <el-row v-if="showTable" class="table-row">
      <ETable
        ref="table2"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :data="lastYearData"
        :show-actions="true"
        :actions-min-width="5"
        :auto-height="false"
        :show-summary="false"
        :sortable="true"
        :span-method="spanMethod"
        :summary-method="summaryMethod"
        :highlight-current-row="true"
        :sum-text="$t('enquiry.contra.label.sumText')"
        :index="indexMethod"
        :filter-class-function="stripeClass"
        :full-column-width="20"
        empty-text=" "
        height="auto"
        class="header-table"
        action-label=" "
        border
        @changeWidth="onChangeWidth"
        @row-click="onRowClick"
      >
        <template slot="columns">
          <el-table-column
            v-for="(item, index) in columnList"
            :key="index + item.ss_key"
            :label="columnLabelFormatter(item)"
            :align="item.alignment"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :formatter="$refs.table.formatter"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span v-if="item.ss_key === 'descr'">{{ $t(scope.row.descr) }}</span>
              <span v-else>{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)],
                  customDateFormat
                )
              }}</span>
            </template>
          </el-table-column>
        </template>
      </ETable>
      <b-table
        ref="table"
        :show-header="false"
        :style-columns="styleColumns"
        :amount-columns="amountColumns"
        :lang-key="langKey"
        :data="tableData"
        :show-actions="true"
        :actions-min-width="5"
        :auto-height="false"
        :show-summary="true"
        :sortable="true"
        :span-method="spanMethod"
        :summary-method="summaryMethod"
        :highlight-current-row="true"
        :column-config="{ useKey: true }"
        :sum-text="$t('enquiry.contra.label.sumText')"
        :index="indexMethod"
        :filter-class-function="stripeClass"
        :height="tableHeight"
        :footer-method="footerMethod"
        :full-column="true"
        :full-column-width="55"
        show-footer
        show-footer-overflow
        class="content-table"
        action-label=" "
        border
        @changeWidth="changeColumnWidth"
        @cell-click="onRowClick"
      >
        <template slot="columns">
          <vxe-table-column
            v-for="(item, index) in columnList"
            :key="index + item.ss_key"
            :title="columnLabelFormatter(item)"
            :align="item.alignment"
            :width="item.width"
            :property="$refs.table.column_property(item)"
            :field="$refs.table.column_property(item)"
            :column-key="item.ss_key"
            :formatter="$refs.table.formatter"
            :params="{ key: item.ss_key }"
          >
            <template v-if="scope && scope.row" slot-scope="scope">
              <span>{{
                $refs.table.customFormatter(
                  item.ss_key,
                  scope.row[$refs.table.column_property(item)],
                  customDateFormat
                )
              }}</span>
            </template>
          </vxe-table-column>
        </template>
      </b-table>
    </el-row>

    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      :ss_code="ss_code"
      :has-theme="true"
      table-type="full-screen-without-first-field"
      @reloadStyleSheets="loadUserStyle(true)"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import ETable from '@/components/ETable'
import BTable from '@/components/BTable/index'
import SelectTree from '@/components/SelectTree'
import customStyle from '@/views/customStyle/index.vue'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'
import loadPreferences from '@/views/mixins/loadPreferences'
import mixinPermission from '@/views/mixins/permission'
import enquiryComponentProxy from './enquiryComponentProxy'

// API
import { enquiryGrant } from '@/api/enquiry/grant'
import { fetchFunds } from '@/api/master/funds' // 撥款
import { getAccountTree } from '@/api/master/account' // 撥款
import { fetchYears, getYear, searchTheDate } from '@/api/master/years'
import { enquiryGrantExport } from '@/api/report/excel'

import dateUtil from '@/utils/date'
import { amountFormat, toDecimal } from '@/utils'
import { exportExcel } from '@/utils/excel'
import { cancelScrollBarSync, scrollBarSync } from '@/views/budget/common/utils'
import { listenTo } from '@/utils/resizeListen'
import {
  NATURE_CODE_G,
  NATURE_CODE_IE,
  NATURE_CODE_I,
  NATURE_CODE_E,
  NATURE_CODE_DR,
  NATURE_CODE_CR,
} from '@/constants/account'

const natureIconMap = {
  [NATURE_CODE_DR]: 'edac-icon edac-icon-Dr',
  [NATURE_CODE_CR]: 'edac-icon edac-icon-Cr',
  [NATURE_CODE_I]: 'edac-icon edac-icon-I',
  [NATURE_CODE_E]: 'edac-icon edac-icon-E',
  [NATURE_CODE_G]: 'edac-icon edac-icon-G',
  [NATURE_CODE_IE]: 'edac-icon edac-icon-IE',
}

export default {
  name: 'EnquiryGrant',
  components: {
    BTable,
    ETable,
    customStyle,
    SelectTree,
  },
  mixins: [loadCustomStyle, mixinPermission, loadPreferences, enquiryComponentProxy],
  data() {
    return {
      ss_code: 'ac.enquiry.grant',
      pf_code: 'ac.enquiry.grant',
      p_code: 'ac.enquiry.grant',
      p_isComponent: true,

      loading: false,
      isMounted: false,
      tableColumns: [
        'vc_date', // 日期
        'vc_no', // 傳票編號
        'ac_code', // 會計賬號
        'ac_name_', // 賬目名稱
        'descr', // 內容描述
        'tx_type', // IE
        'amount_dr', // 借方金額
        'amount_cr', // 貸方金額
        'net_amount', // 淨金額
        'bal', // 結餘
        'vc_payee', // 收付款
        'ref', // 參考號
        'budget_code', // 預算名稱
        'vc_dept', // 部門編號
        'dept_name_', // 部門名稱
        'st_code', // 職員編號
        'staff_name_', // 職員名稱
        'vc_contra', // 對沖編號
      ],
      amountColumns: [
        'amount_dr',
        'amount_cr',
        'net_amount', // 淨金額
        'bal', // 結餘
      ],
      langKey: 'enquiry.grant.label.',

      showDialog: false,
      lastDay_year: new Date(),
      firstDay_year: new Date(),
      lastDay_month: new Date(),
      firstDay_month: new Date(),
      tableData: [],
      footerData: [],

      preferences: {
        filters: {
          type: 'A',
          fy_id: '',
          fy_code: '',
          pd_code: '',
          pd_id: '',
          ac_code: '',
          ac_fund_id: '',
          ac_selectGroup: false,
          nature: '',
          nature_code: 'Cr',
          fund: '',
          childrenFund: false,
        },
      },
      childPreferences: ['pd_code', 'pd_id'],
      customDateFormat: 'dd/MM/yy',
      accountOptions: [], // 會計科目，動態
      funds: [], // 會計科目，動態
      year_list: [], // 會計週期（年份）
      month_list: [], // 月份,
      lastYearData: [],

      headerWrapper: {},
      contentWrapper: {},

      // 窗口適應
      resizeListen: {},
      filtersHeight: 30,
      pageResizeListen: {},
      pageHeight: 500,
      headerResizeListen: {},
      headerHeight: 60,
      showTable: true,
      // 是否選中
      // isSelected: false
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    allAccount() {
      return {
        name_cn: this.$t('master.account.all_account'),
        name_en: this.$t('master.account.all_account'),
        abbr_cn: this.$t('master.account.all_account'),
        abbr_en: this.$t('master.account.all_account'),
        fund_id: '',
        account_id: '',
        code: '',
      }
    },
    natureClass() {
      const natureCode = this.preferences.filters.nature_code
      const nature = this.preferences.filters.nature
      const list = [
        NATURE_CODE_DR,
        NATURE_CODE_CR,
        NATURE_CODE_I,
        NATURE_CODE_E,
        NATURE_CODE_G,
        NATURE_CODE_IE,
      ]
      const selected = nature && list.includes(natureCode) ? ' selected' : ''
      const icon = natureIconMap[natureCode] || ''
      return icon + selected
    },
    // filtersHeight() {
    //   if (!this.isMounted) {
    //     return 0
    //   }
    //   return this.$refs.filters.clientHeight
    // },
    columnList() {
      const cl = this.styleColumns.filter(i => i.ss_key !== '_index' && this.columnsVisible(i))
      console.log(
        cl.map(i => i.ss_key),
        'cl',
      )
      return cl
      // return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    tableHeight() {
      const h = this.pageHeight - this.filtersHeight - this.headerHeight - 25
      return h < 100 ? 100 : h
    },
  },
  watch: {
    language: {
      handler() {
        this.footerData = []
        this.$nextTick(() => {
          this.footerData = [this.summaryMethod({ columns: this.columnList, data: this.tableData })]
          this.$refs.table.updateFooter()
        })
      },
      deep: true,
    },
    // 'preferences.filters': {
    //   deep: true,
    //   immediate: false,
    //   handler(val) {
    //     console.log(val)
    //     this.fetchData()
    //   }
    // }
  },
  created() {},
  mounted() {
    this.headerWrapper = this.$refs['table2'].$el.querySelector('.el-table__body-wrapper')
    this.contentWrapper = this.$refs['table'].$el.querySelector('.vxe-table--body-wrapper')
    // scrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    scrollBarSync(this.contentWrapper, this.headerWrapper)

    this.$nextTick(() => {
      this.pageResizeListen = listenTo(this.$refs.page, ({ width, height, ele }) => {
        this.pageHeight = height
      })
      this.resizeListen = listenTo(this.$refs.filters, ({ width, height, ele }) => {
        this.filtersHeight = height
      })
      this.headerResizeListen = listenTo(this.$refs.table2.$el, ({ width, height, ele }) => {
        this.headerHeight = height
      })
    })
    this.isMounted = true
    this.initData()
  },
  beforeDestroy() {
    // cancelScrollBarSync(this.summaryTableWrapper, this.dataTableWrapper)
    cancelScrollBarSync(this.contentWrapper, this.headerWrapper)
  },
  methods: {
    onSetting() {
      this.showDialog = true
    },
    indexMethod(index) {
      const i = this.preferences.filters.type === 'M' ? index - 1 : index
      return i < 1 ? '' : i
    },
    stripeClass({ row, rowIndex }) {
      if (!row.lg_id) {
        return 'count-row'
      }
      /**
       * Table斑馬紋
       */
      if (rowIndex % 2 === 0) {
        return 'table-stripe'
      }
    },
    normalizer(node) {
      return {
        id: !node.account_id ? node.fund_id : node.code,
        label:
          (node.fund_id ? '' : node.code ? `[${node.code}]` : '') +
          (this.language === 'en' ? node.name_en : node.name_cn),
        children: node.children,
      }
    },
    changeFilterTime(data) {
      // this.preferences.filters.fy_id = data.fy_id
      // this.preferences.filters.fy_code = data.fy_code
      // this.preferences.filters.pd_code = data.pd_code
      // this.preferences.filters.pd_id = data.pd_id
      // this.preferences.filters.type = data.type
      Object.assign(this.preferences.filters, data)
      if (this.loading) return
      this.$nextTick(() => {
        this.fetchData()
      })
    },
    fetchData() {
      return new Promise((resolve, reject) => {
        this.loading = true
        const type = this.preferences.filters.type
        let fund_id, ac_code
        if (this.preferences.filters.ac_selectGroup) {
          fund_id = this.preferences.filters.ac_fund_id
        } else {
          fund_id = this.preferences.filters.fund
          if (this.preferences.filters.ac_code) {
            ac_code = this.preferences.filters.ac_code
          }
        }
        const fy_code = this.preferences.filters.fy_code || undefined
        const pd_code = ('MT'.includes(type) && this.preferences.filters.pd_code) || undefined
        // if (!fy_code) return
        searchTheDate({ fy_code })
          .then(res => {
            // 當年
            this.lastDay_year = new Date(res.the_last_day)
            this.firstDay_year = new Date(res.the_first_day)
          })
          .then(() => {
            if ('MT'.includes(this.preferences.filters.type)) {
              return searchTheDate({ fy_code, pd_code })
            } else {
              return Promise.resolve()
            }
          })
          .then(res => {
            if (res) {
              // 當月
              this.lastDay_month = new Date(res.the_last_day)
              this.firstDay_month = new Date(res.the_first_day)
            }
          })
          .then(() =>
            enquiryGrant({
              type,
              fy_code,
              pd_code,
              ac_code,
              fund_id,
            }),
          )
          .then(res => {
            this.tableData = this.formatData(res)
            this.footerData = [
              this.summaryMethod({ columns: this.columnList, data: this.tableData }),
            ]
            resolve()
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    initData() {
      this.loading = true
      fetchYears()
        .then(res => {
          this.year_list = res
        })
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.funds = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.year_list.length) {
            const year = this.year_list.find(i => i.fy_code === this.preferences.filters.fy_code)
            if (year) {
              return
            }
            this.preferences.filters.fy_id = this.year_list[0].fy_id
            this.preferences.filters.fy_code = this.year_list[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => {
          if (this.funds.length) {
            if (!this.preferences.filters.fund && this.funds.length > 0) {
              this.preferences.filters.fund = this.funds[0].fund_id
            }
            return this.loadAccountTree(this.preferences.filters.fund)
          } else {
            return Promise.reject()
          }
        })
        .then(() => {
          const filters = this.preferences.filters
          if ('TM'.includes(filters.type) && filters.fy_id) {
            return this.loadPeriods(filters.fy_id)
          } else {
            return Promise.resolve()
          }
        })
        .then(this.updateChildPreference)
        .then(this.fetchData)
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 修改帳目類別，重新獲取會計科目
    changeFund(val) {
      return new Promise(resolve => {
        this.preferences.filters.ac_code = ''
        this.preferences.filters.childrenFund = false
        this.loadAccountTree(val)
          .then(this.fetchData)
          .then(() => {
            resolve()
          })
      })
    },
    inCode(item, code) {
      if (item.code === code) {
        return true
      }
      if (item.children && item.children.length) {
        for (let i = 0; i < item.children.length; i++) {
          if (this.inCode(item.children[i], code)) {
            return true
          }
        }
      }
      return false
    },
    // 載入會計科目樹
    loadAccountTree(fund_id) {
      return new Promise((resolve, reject) => {
        getAccountTree(fund_id)
          .then(res => {
            const top = Object.assign({}, this.allAccount)
            top.children = res
            this.accountOptions = [top]
            const ac_code = this.preferences.filters.ac_code
            if (ac_code) {
              let inCode = false
              for (let i = 0; i < res.length; i++) {
                if (this.inCode(res[i], ac_code)) {
                  inCode = true
                }
              }
              if (!inCode) {
                this.preferences.filters.ac_code = ''
                this.preferences.filters.ac_fund_id = ''
                this.preferences.filters.ac_selectGroup = false
              }
            }
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    onSelectAccount({ value, selectGroup, group_id, nature_code }) {
      this.preferences.filters.ac_code = value
      this.preferences.filters.ac_selectGroup = selectGroup
      this.preferences.filters.ac_fund_id = group_id
      this.preferences.filters.nature_code = nature_code
      this.fetchData()
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // if (rowIndex === 0) {
      //   switch (columnIndex) {
      //     case 0:
      //       return { rowspan: 1, colspan: 3 }
      //     case 1:
      //     case 2:
      //       return { rowspan: 0, colspan: 0 }
      //     default:
      //       break
      //   }
      // }
    },
    summaryMethod({ columns, data }) {
      const sums = []
      columns.forEach(async(column, index) => {
        switch (column.ss_key) {
          case 'descr':
            sums.push(this.$t('enquiry.grant.label.sumText'))
            break
          case 'amount_cr':
          case 'amount_dr':
          case 'net_amount': {
            let sum = 0
            data.forEach(item => {
              if (item.lg_id) {
                const n = toDecimal(Number(item[column.ss_key]))
                if (!isNaN(n) && Math.abs(n) !== 0) {
                  sum = toDecimal(sum + n)
                }
              }
            })
            sums.push(amountFormat(sum))
            break
          }
          case 'bal': {
            const sum = (data.length > 0 && data[data.length - 1].bal) || 0
            sums.push(amountFormat(sum))
            break
          }
          case 'vc_date': {
            sums.push(
              dateUtil.format(
                'MT'.includes(this.preferences.filters.type)
                  ? this.lastDay_month
                  : this.lastDay_year,
                'dd/MM/yy',
              ),
            )
            break
          }
          default:
            sums.push('')
            break
        }
      })

      return sums
    },
    formatNotAmount(nature_code, amount_dr, amount_cr) {
      let netAmount = 0
      switch (nature_code) {
        case NATURE_CODE_DR:
        case NATURE_CODE_E:
          netAmount = toDecimal(Number(amount_dr) - Number(amount_cr))
          break
        case NATURE_CODE_CR:
        case NATURE_CODE_I:
          netAmount = toDecimal(Number(amount_cr) - Number(amount_dr))
          break
        case NATURE_CODE_G:
        case NATURE_CODE_IE:
          netAmount = toDecimal(Number(amount_cr))
          break
        default:
          netAmount = toDecimal(Number(amount_cr) - Number(amount_dr))
        // this.$message.error('系統錯誤，請聯繫管理員')
        // return []
      }
      return netAmount
    },
    formatData({ result: data, totalBal, beforeNet, beforeDr, prevNet, prevDr, prevCr }) {
      const nature_code = this.preferences.filters.nature_code
      const nature = this.preferences.filters.nature
      let newData = []

      const isMonth = this.preferences.filters.type === 'M'
      let isFirstMonth = false
      const pd_code = this.preferences.filters.pd_code
      if (pd_code) {
        const month = this.month_list.findIndex(i => i.pd_code === pd_code)
        isFirstMonth = month === 0
      }

      // if (nature) {
      if (nature_code !== 'Dr' && nature_code !== 'E') {
        totalBal = -toDecimal(totalBal) // 總金額
        beforeNet = -toDecimal(beforeNet) // 往年結餘
        // prevNet = -toDecimal(prevNet) // 上期結餘
      }
      // }
      // 上期結餘
      if (isMonth && !isFirstMonth) {
        // data.splice(0, 0, {
        //   vc_date: this.firstDay_month,
        //   vc_no: '',
        //   ac_code: '',
        //   descr: '上期總額',
        //   tx_type: '',
        //   amount_dr: Number(prevDr),
        //   amount_cr: Number(prevCr),
        //   bal: prevNet, // 結餘
        //   net_amount: null, // 淨金額
        //
        //   vc_payee: '',
        //   // 收付款簡稱
        //   // 參考號
        //   budget_code: '',
        //   // 預算名稱
        //   st_code: '',
        //   // 職員名稱
        //   vc_contra: '',
        //   vc_dept: ''
        // })
        this.lastYearData = [
          {
            vc_date: this.firstDay_month,
            vc_no: '',
            ac_code: '',
            descr: this.$t('enquiry.grant.label.lastPeriod'),
            tx_type: '',
            amount_dr: toDecimal(Number(prevDr)),
            amount_cr: toDecimal(Number(prevCr)),
            bal: toDecimal(prevNet), // 結餘
            net_amount: null, // 淨金額

            vc_payee: '',
            // 收付款簡稱
            // 參考號
            budget_code: '',
            // 預算名稱
            st_code: '',
            // 職員名稱
            vc_contra: '',
            vc_dept: '',
          },
        ]
      }

      // const period

      const lastPeriod = toDecimal(Number(beforeNet) + Number(prevNet))
      let bal = isMonth ? lastPeriod : toDecimal(totalBal)
      // let bal = isMonth ? toDecimal(prevNet) : toDecimal(totalBal)
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        const newItem = Object.assign({}, item)
        // 淨金額
        let netAmount = 0
        const amount_cr = toDecimal(item.amount_cr) // 貸方金額
        const amount_dr = toDecimal(item.amount_dr) // 借方金額
        let amount_nature = 0 // "性質"金額
        switch (nature_code) {
          case NATURE_CODE_DR:
          case NATURE_CODE_E:
            netAmount = toDecimal(Number(amount_dr) - Number(amount_cr))
            if (nature && nature_code === NATURE_CODE_E) {
              newItem.amount_dr = newItem.amount_dr - newItem.amount_cr
            }
            break
          case NATURE_CODE_CR:
          case NATURE_CODE_I:
            netAmount = toDecimal(Number(amount_cr) - Number(amount_dr))
            if (nature && nature_code === NATURE_CODE_I) {
              newItem.amount_cr = newItem.amount_cr - newItem.amount_dr
            }
            break
          case NATURE_CODE_G:
          case NATURE_CODE_IE:
          default:
            netAmount = toDecimal(Number(amount_cr) - Number(amount_dr))
        }

        if (item.lg_id) {
          // 結餘
          bal = toDecimal(bal + netAmount)
        }
        newItem.bal = toDecimal(bal)
        newItem.net_amount = toDecimal(netAmount)
        if (nature) {
          switch (nature_code) {
            case NATURE_CODE_DR:
            case NATURE_CODE_E:
              amount_nature = toDecimal(Number(amount_dr) - Number(amount_cr))
              break
            case NATURE_CODE_CR:
            case NATURE_CODE_I:
              amount_nature = toDecimal(Number(amount_cr) - Number(amount_dr))
              break
            case NATURE_CODE_G:
            case NATURE_CODE_IE:
              amount_nature = toDecimal(Number(amount_cr))
              break
          }
          newItem.amount_nature = amount_nature
        }

        newData.push(newItem)
      }
      // one
      // newData.splice(0, 0, {
      //   vc_date: this.firstDay_year,
      //   vc_no: '',
      //   ac_code: '',
      //   descr: '上年結餘',
      //   tx_type: '',
      //   amount_dr: null,
      //   amount_cr: null,
      //   bal: totalBal,
      //   net_amount: null,
      //   // 淨金額
      //   // 結餘
      //   vc_payee: '',
      //   // 收付款簡稱
      //   // 參考號
      //   budget_code: '',
      //   // 預算名稱
      //   st_code: '',
      //   // 職員名稱
      //   vc_contra: '',
      //   vc_dept: ''
      // })
      this.lastYearData = [
        {
          vc_date: this.firstDay_year,
          vc_no: '',
          ac_code: '',
          descr: 'enquiry.grant.label.LastYearBF',
          tx_type: '',
          amount_dr: null,
          amount_cr: null,
          bal: toDecimal(beforeNet),
          net_amount: null,
          // 淨金額
          // 結餘
          vc_payee: '',
          // 收付款簡稱
          // 參考號
          budget_code: '',
          // 預算名稱
          st_code: '',
          // 職員名稱
          vc_contra: '',
          vc_dept: '',
        },
      ]
      if (isMonth) {
        this.lastYearData.push({
          vc_date: this.firstDay_month,
          vc_no: '',
          ac_code: '',
          descr: 'enquiry.grant.label.lastPeriod',
          tx_type: '',
          amount_dr: prevDr,
          amount_cr: prevCr,
          bal: toDecimal(lastPeriod),
          net_amount: this.formatNotAmount(
            nature_code,
            toDecimal(Number(prevDr)),
            toDecimal(Number(prevCr)),
          ),
          // 淨金額
          // 結餘
          vc_payee: '',
          // 收付款簡稱
          // 參考號
          budget_code: '',
          // 預算名稱
          st_code: '',
          // 職員名稱
          vc_contra: '',
          vc_dept: '',
        })
      }
      console.log(this.preferences.filters.nature_code, this.preferences.filters.nature, 'newData')

      if (this.preferences.filters.nature_code === 'Dr' && this.preferences.filters.nature) {
        newData = newData.map(i => {
          i.amount_dr = !toDecimal(i.amount_dr)
            ? toDecimal(Number(i.amount_cr) * -1)
            : toDecimal(i.amount_dr)
          return i
        })
      }
      return newData
    },
    changeNature() {
      const nature = !this.preferences.filters.nature
      const nature_code = this.preferences.filters.nature_code
      this.preferences.filters.nature = nature
      // this.isSelected = !this.isSelected
      switch (nature_code) {
        case NATURE_CODE_CR:
          break
        case NATURE_CODE_DR:
          break
        case NATURE_CODE_G:
          break
        case NATURE_CODE_IE:
          break
        case NATURE_CODE_I:
          break
        case NATURE_CODE_E:
          break
      }
      this.fetchData()
    },
    onRowClick({ row }) {
      if (!row) {
        this.$refs.table.setCurrentRow()
        return
      } else {
        this.$refs.table2.setCurrentRow()
      }
      if (!row.lg_id) {
        this.$refs.table.setCurrentRow()
        return
      }
      let routerName = ''
      switch (row.vt_category) {
        case 'P':
          routerName = 'dailyPaymentView'
          break
        case 'C':
          routerName = 'dailyPettyCashView'
          break
        case 'R':
          routerName = 'dailyReceiptView'
          break
        case 'T':
          routerName = 'dailyBankTransferView'
          break
        case 'J':
          routerName = 'dailyJournalView'
          break
        default:
          this.$message.error(this.$t('message.pleaseContactTheAdministrator'))
          return
      }
      const ac_bankData = this.getAccountBank(this.accountOptions)

      // const ac_bank = this.accountOptions.find(i => i.ac_code === this.preferences.filters.ac_code)
      this.$router.replace({
        name: routerName,
        force: true,
        params: {
          action: 'view',
          isView: true,
          parentFyCode: row.fy_code,
          parentFyId: row.fy_id,
          parentPdCode: row.pd_code,
          editObject: row,
          vt_category: row.vt_category,
          ac_code: row.ac_code,
          ac_bank: ac_bankData.ac_bank || '',
          lg_id: row.lg_id,
          time: new Date().getTime(),
        },
        query: {
          t: new Date().getTime(),
        },
      })
      // 取消其他頁面選中
      this.handleThisPageClickRow()
    },
    formatter(row, column, cellValue, index) {
      if (this.preferences.filters.nature && column.columnKey === 'amount_dr') {
        return amountFormat(row.amount_nature)
      }
      if (this.amountColumns.includes(column.columnKey)) {
        if (cellValue == null) return ''
        return amountFormat(cellValue)
      }
      if (column.columnKey === 'vc_date') {
        if (cellValue) {
          return dateUtil.format(new Date(cellValue), 'dd/MM/yy')
        }
      }
      return cellValue
    },
    columnLabelFormatter(item) {
      const columns = ['amount_dr', 'amount_cr', 'net_amount', 'bal']
      let label = this.langKey + item.ss_key
      if (
        this.preferences.filters.nature &&
        this.preferences.filters.nature_code &&
        columns.includes(item.ss_key)
      ) {
        label += '_' + this.preferences.filters.nature_code.toLowerCase().replace('/', '') // 這裡將I/E轉換為IE
      }
      return this.$t(label)
    },
    columnsVisible(item) {
      const columns = ['amount_dr', 'amount_cr'] //, 'amount_nature'
      const ss_key = item.ss_key
      if (this.preferences.filters.nature && columns.includes(ss_key)) {
        const natureCode = this.preferences.filters.nature_code
        switch (natureCode) {
          case NATURE_CODE_DR:
          case NATURE_CODE_E:
            return item.ss_key === 'amount_dr'
          case NATURE_CODE_CR:
          case NATURE_CODE_I:
            return item.ss_key === 'amount_cr'
          case NATURE_CODE_IE:
          case NATURE_CODE_G:
            return true
          //   break
        }
        if (item.ss_key === 'amount_cr' || item.ss_key === 'amount_dr') {
          return false
        } else {
          return true
        }
      }
      // else {
      //   if (item.ss_key === 'amount_nature') {
      //     return false
      //   }
      // }
      return true
    },
    async onChangeTimeType(val) {
      const filters = this.preferences.filters
      switch (val) {
        case 'A':
          filters.fy_id = ''
          filters.fy_code = ''
          filters.pd_code = ''
          filters.pd_id = ''
          break
        case 'Y':
        case 'T':
        case 'M':
          {
            if (!this.year_list && this.year_list.length === 0) {
              filters.fy_id = ''
              filters.fy_code = ''
              filters.pd_code = ''
              filters.pd_id = ''
              break
            }
            const year = this.year_list.find(
              i => i.fy_id === (filters.fy_id ? filters.fy_id : this.currentYear.fy_id),
            )
            if (year) {
              filters.fy_id = year.fy_id
              filters.fy_code = year.fy_code
            } else {
              filters.fy_id = this.year_list[0].fy_id
              filters.fy_code = this.year_list[0].fy_code
            }
            if (val !== 'Y') {
              await this.loadPeriods(filters.fy_id)
            } else {
              filters.pd_code = ''
              filters.pd_id = ''
            }
          }
          break
      }
      this.fetchData()
    },
    loadPeriods(fy_id) {
      return new Promise((resolve, reject) => {
        const filters = this.preferences.filters
        getYear(filters.fy_id)
          .then(res => {
            const periods = res.periods
            this.month_list = periods

            if (periods.length > 0) {
              let month = periods.find(item => item.pd_code === filters.pd_code)
              if (!month) {
                month = periods[0]
              }
              filters.pd_code = month.pd_code
              filters.pd_id = month.pd_id
            } else {
              filters.pd_code = ''
              filters.pd_id = ''
              filters.type = 'Y'
            }
            resolve(periods)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onChangeYear(fy_id) {
      const year = this.year_list.find(i => i.fy_id === fy_id)
      if (year) {
        this.preferences.filters.fy_code = year.fy_code
        this.loadPeriods(fy_id).then(this.fetchData)
      } else {
        this.preferences.filters.fy_id = ''
        this.preferences.filters.fy_code = ''
      }
    },
    onExport(export_type) {
      const user_id = this.user_id
      const type = this.preferences.filters.type
      let fund_id, ac_code
      if (this.preferences.filters.ac_selectGroup) {
        fund_id = this.preferences.filters.ac_fund_id
      } else {
        fund_id = this.preferences.filters.fund
        if (this.preferences.filters.ac_code) {
          ac_code = this.preferences.filters.ac_code
        }
      }
      const fy_code = this.preferences.filters.fy_code || undefined
      const pd_code = ('MT'.includes(type) && this.preferences.filters.pd_code) || undefined
      this.loading = true
      enquiryGrantExport({ user_id, export_type, type, fy_code, pd_code, ac_code, fund_id })
        .then(res => exportExcel(res))
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onChangeWidth(key, newWidth) {
      this.changeColumnWidth(key, newWidth)
    },
    footerMethod() {
      return this.footerData
    },
    // 遞歸取得nature_code
    getNatureCode(array) {
      array.forEach(item => {
        if (item.children) {
          this.getNatureCode(item.children)
        } else {
          if (item.ac_cf === this.preferences.filters.ac_code) {
            this.preferences.filters.nature_code = item.nature_code
            console.log('item', item.nature_code)
          }
        }
      })
    },
    getAccountBank(accountOptions, data = {}) {
      accountOptions.forEach(i => {
        console.log(
          i.code === this.preferences.filters.ac_code,
          i.code,
          this.preferences.filters.ac_code,
          'i',
        )

        if (i.code === this.preferences.filters.ac_code) {
          console.log(i.code, this.preferences.filters.ac_code, 'i')
          data = i
        }
        if (i.children) {
          data = this.getAccountBank(i.children, data)
        }
      })
      return data
    },
  },
}
</script>

<style lang="scss" scoped>
$actionIconColor: #68afff;
$disableColor: #b9b6b6;
$settingColor: #b9b6b6;

.enquiry-grant {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: scroll;
  position: relative;
  min-height: 100px;

  .filter {
  }
  .account-filter {
    margin-bottom: 5px;
    margin-top: 2px;
    /deep/ {
      .el-input__inner,
      .el-input__icon {
        height: 25px !important;
        line-height: 25px !important;
      }
    }
    i.edac-icon {
      color: $disableColor;
      vertical-align: middle;
      font-size: 18px;
      cursor: pointer;
      &.selected {
        color: $actionIconColor;
      }
    }
    .tree-select {
      display: inline-block;
      position: relative;
      width: 300px;
      vertical-align: middle;
      line-height: 25px;
      height: 25px;
      /deep/ {
        .vue-treeselect__control {
          height: 23px;
          .vue-treeselect__value-container {
            height: 23px;
            .vue-treeselect__single-value {
              line-height: 23px;
              height: 23px;
            }
          }
        }
      }
    }
    .el-select {
      margin-bottom: 2px;
    }
  }

  .view-icon {
    vertical-align: middle;
  }
  .edac-icon {
    color: $actionIconColor;
    font-size: 20px;
  }
  .edac-icon-setting1 {
    color: $settingColor;
  }
  .actions-icon {
    float: right;
    padding: 0 10px;
    .edac-icon {
      line-height: 25px;
      vertical-align: middle;
    }
  }
  .enquiry-title {
    line-height: 25px;
    display: inline-flex;
    .view-title {
      vertical-align: middle;
      padding: 0 5px;
      color: #606266;
    }
  }
  .table-row {
    min-height: 140px;
    flex: 1;
    /*height: calc(100% - 50px);*/

    /deep/ {
      .content-table {
        /*height: 100%;*/

        .el-table__body-wrapper {
          /*height: auto;*/
          /*height: calc(100% - 85px - 60px);*/
          overflow-y: scroll;
        }
        .el-table__empty-block {
          min-height: 30px;
        }
        /*.el-table__body tr.current-row>td{*/
        /*  background-color: #6abdff;*/
        /*}*/
        .vxe-table--body-wrapper .vxe-table--body {
          padding: 0 2px;
        }
      }
      .header-table {
        overflow-y: scroll !important;
        scrollbar-color: transparent transparent;
        scrollbar-track-color: transparent;
        -ms-scrollbar-track-color: transparent;
        &::-webkit-scrollbar {
          color: transparent;
          background: transparent;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        .el-table__body-wrapper {
          height: auto !important;
          min-height: auto !important;

          overflow: hidden;
        }
      }
    }
  }
}
</style>
