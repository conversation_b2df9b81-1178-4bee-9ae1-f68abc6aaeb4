import request from '@/utils/request'

/**
 * 通過excel修復錯誤的傳票號碼的模板匯出
 * @return {Promise}
 */
export function exportFixWrongVoucher() {
  return request({
    url: '/fix-wrong-voucher-no/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {},
  })
}

/**
 * 通過excel修復錯誤的傳票號碼
 * @param {string} fy_code 會計週期code
 * @param {string} data_json 匯入的數據
 * @return {Promise}
 */
export function importFixWrongVoucher(data, fy_code) {
  return request({
    url: '/fix-wrong-voucher-no/actions/import',
    method: 'post',
    data: {
      fy_code,
      data_json: JSON.stringify(data),
    },
  })
}
