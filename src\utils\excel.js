export function downloadObjectUrl(url, filename) {
  const link = document.createElement('a')
  link.href = url
  link.target = '_blank'
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 導出數據到Excel
 * @param data
 * @param filename
 * @returns {Promise<boolean>}
 */
export async function exportExcel({ url, filename }) {
  try {
    // // 轉換數組
    // const { exportData, exportHeader } = await dataToArray(data)
    // // 導出文件
    // await dataToExcel({
    //   header: exportHeader,
    //   data: exportData,
    //   filename: filename
    // })
    downloadObjectUrl(url, filename)
    return true
  } catch (e) {
    return false
  }
}
// // 導出
// function dataToArray(data) {
//   return new Promise(resolve => {
//     const exportData = []
//     const exportHeader = []
//     const l = data.length
//     for (let i = 0; i < l; i++) {
//       if (i === 0) {
//         for (const key in data[0]) {
//           exportHeader.push(data[0][key])
//         }
//       } else {
//         const arr = []
//         for (const key in data[i]) {
//           arr.push(data[i][key])
//         }
//         exportData.push(arr)
//       }
//     }
//     resolve({ exportData, exportHeader })
//   })
// }
// // 導出
// function dataToExcel({ header, data, filename }) {
//   return new Promise(resolve => {
//     import('@/vendor/Export2Excel').then(excel => {
//       excel.export_json_to_excel({
//         header: header, // 表头 必填
//         data: data, // 具体数据 必填
//         filename: filename, // 非必填
//         autoWidth: true, // 非必填
//         bookType: 'xlsx' // 非必填
//       })
//       resolve()
//     })
//   })
// }

/**
 * UploadExcel 導入的數據轉為可導入的JSON
 * @param results
 * @param header
 * @returns {string}
 */
export function importResultsFormat(results, header) {
  const data = []
  data.push(header)
  results.forEach(i => {
    const arr = []
    header.forEach((h, hIndex) => {
      // 單元格值爲undefined或null 設為空字符串，否則取原值
      const v = i[h]
      arr.push(v == null ? '' : v)
    })
    data.push(arr)
  })
  const excelData = {}
  data.forEach((item, index) => {
    excelData[index + 1] = item
  })
  return excelData
}

export function importExcel(vue, apiFunc, results, header, args) {
  return new Promise((resolve, reject) => {
    const excelData = importResultsFormat(results, header)
    // apiFunc(excelData, ...args)
    const arg = [excelData]
    if (args) {
      for (const key in args) {
        arg.push(args[key])
      }
    }
    apiFunc
      .apply(this, Array.prototype.slice.call(arg, 0))
      .then(res => {
        vue.$message({
          message: vue.$t('message.success'),
          type: 'success',
        })
        resolve(res)
      })
      .catch(errors => {
        const newData = []
        const h = vue.$createElement
        for (const item in errors) {
          for (const i in item) {
            newData.push(h('p', null, errors[item][i]))
          }
        }
        vue.$confirm(vue.$t('message.tips'), {
          title: vue.$t('message.tips'),
          customClass: 'import-error-dialog',
          message: h('div', { class: 'import-error-dialog-content' }, newData),
          confirmButtonText: vue.$t('button.confirm'),
          type: 'warning',
          showCancelButton: false,
        })
        reject()
      })
  })
}

export function importExcelMultiple(vue, apiFunc, dataObj, args = {}) {
  return new Promise((resolve, reject) => {
    const arg = []
    const excelData = {}
    try {
      const keys = Object.keys(dataObj)
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        excelData[key] = importResultsFormat(dataObj[key].results, dataObj[key].header)
      }
      arg.push(excelData)
    } catch (e) {
      vue.$message({
        message: vue.$t('message.importExcelError'),
        type: 'error',
      })
      reject(vue.$t('message.importExcelError'))
      return
    }
    // apiFunc(excelData, ...args)
    if (args) {
      for (const key in args) {
        arg.push(args[key])
      }
    }
    apiFunc
      .apply(this, Array.prototype.slice.call(arg, 0))
      .then(res => {
        if (args.successTips === undefined || args.successTips === true) {
          vue.$message({
            message: vue.$t('message.success'),
            type: 'success',
          })
        }
        resolve(res)
      })
      .catch(errors => {
        if (!Array.isArray(errors)) {
          reject(errors)
          return
        }
        const newData = []
        const h = vue.$createElement
        for (const item in errors) {
          for (const i in item) {
            newData.push(h('p', null, errors[item][i]))
          }
        }
        debugger
        vue.$confirm(args.filename || vue.$t('message.tips'), {
          title: args.filename || vue.$t('message.tips'),
          customClass: 'import-error-dialog',
          message: h('div', { class: 'import-error-dialog-content' }, newData),
          confirmButtonText: vue.$t('button.confirm'),
          type: 'warning',
          showCancelButton: false,
        })
        reject(newData)
        // reject(errors)
      })
  })
}

export default { exportExcel, importResultsFormat, importExcel, importExcelMultiple }
