<script>
export default {
  props: {},
  data() {
    return {
      pageHadChange: false,
    }
  },
  computed: {},
  created() {},
  updated() {},
  methods: {
    checkOut() {
      return new Promise((resolve, reject) => {
        if (this.pageHadChange) {
          this.$confirm(this.$t('message.leaveSave'), this.$t('message.tips'), {
            confirmButtonText: this.$t('button.confirm'),
            cancelButtonText: this.$t('button.cancel'),
            type: 'warning',
            dangerouslyUseHTMLString: true,
          })
            .then(() => {
              resolve()
            })
            .catch(() => {
              reject()
            })
        } else {
          resolve()
        }
      })
    },
  },
}
</script>
