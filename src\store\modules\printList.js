import dayjs from 'dayjs'
const printList = {
  state: {
    printList: [],
  },
  mutations: {
    setPrintList(state, data) {
      state.printList.push(data)
    },
    printListItemFinish(state, { index, url }) {
      state.printList[index].url = url
      state.printList[index].status = 1
      state.printList[index].finishTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    },
    printListItemError(state, { index }) {
      state.printList[index].status = 2
    },
  },
}
export default printList
