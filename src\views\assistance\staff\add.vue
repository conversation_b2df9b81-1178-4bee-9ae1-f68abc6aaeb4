<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 職員編號 -->
      <el-form-item
        :rules="codeRules"
        :class="{ 'code-rules-error': haveExist }"
        :label="$t('assistance.staff.label.st_code')"
        prop="st_code"
      >
        <el-input v-model="form.st_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.staff.label.st_name_cn')"
        prop="st_name_cn"
      >
        <el-input v-model="form.st_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.staff.label.st_name_en')"
        prop="st_name_en"
      >
        <el-input v-model="form.st_name_en" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :label="$t('assistance.staff.label.parent_st_type_id')"
        prop="parent_st_type_id"
      >
        <el-select
          v-model="form.parent_st_type_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in staffTypes"
            :key="item.value"
            :value="item.st_type_id"
            :label="conversionParentStaffType(item)"
            v-html="conversionParentStaffType(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置 -->
      <el-form-item :label="$t('assistance.staff.label.seq')" prop="seq">
        <el-select v-model="form.seq" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in sepOptions"
            :key="item.st_type_id"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.st_type_id ? item.st_type_id : item.staff_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 登入名稱 -->
      <el-form-item :label="$t('assistance.staff.label.username')" prop="username">
        <el-input v-model="form.username" clearable />
      </el-form-item>
      <!-- 密碼 -->
      <el-form-item
        :rules="[
          {
            required: form.username && form.username.length,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('assistance.staff.label.password')"
        prop="password"
      >
        <el-input v-model="form.password" :disabled="disPwd" type="password" clearable>
          <el-button
            v-if="disPwd"
            slot="append"
            type="mini"
            icon="el-icon-close"
            @click="clearPassword"
          />
        </el-input>
      </el-form-item>

      <!-- 預算級別 -->
      <el-form-item :label="$t('assistance.staff.label.st_grade')">
        <el-checkbox-group v-model="st_grade_arr" @click="forceUpdate">
          <el-checkbox v-for="item in gradeOptions" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- 預算角色 -->
      <el-form-item v-if="hasBG" :label="$t('assistance.staff.label.budgetRole')" prop="role_id">
        <el-select v-model="form.role_id" :placeholder="$t('placeholder.select')" clearable>
          <el-option
            v-for="item in roles"
            :key="item.role_id"
            :label="item.role_name_cn"
            :value="item.role_id"
          />
        </el-select>
      </el-form-item>
      <!-- 採購角色 -->
      <el-form-item
        v-if="hasPC"
        :label="$t('assistance.staff.label.pc_role_id')"
        prop="other_role_id"
      >
        <el-select v-model="form.other_role_id" :placeholder="$t('placeholder.select')" clearable>
          <el-option
            v-for="item in pcRoles"
            :key="item.role_id"
            :label="item.role_name_cn"
            :value="item.role_id"
          />
        </el-select>
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.staff.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_code"
            :value="item.fy_code"
          >
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createStaff, editStaff, getStaff, getStaffsTreeNode } from '@/api/assistance/staff'
import { fetchStaffTypes } from '@/api/assistance/staff/staffType'
import { fetchYears } from '@/api/master/years'
import { fetchRoles } from '@/api/master/role'

const virtualPassword = 'this.virtual.password'
export default {
  name: 'AssistanceStaffAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      system: 'BG',
      form: {
        staff_id: '',
        username: '',
        password: '',
        st_name_cn: '',
        st_name_en: '',
        st_code: '',
        st_grade: '',
        active_year: '',
        role_id: '',
        parent_st_type_id: '',
        seq: '',
      },
      defaultForm: {
        staff_id: '',
        username: '',
        password: '',
        st_name_cn: '',
        st_name_en: '',
        st_code: '',
        st_grade: '',
        active_year: '',
        role_id: '',
        parent_st_type_id: '',
        seq: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      sepOptions: [
        {
          st_type_id: '',
          staff_id: '',
          seq: '',
          name_cn: this.$t('assistance.staff_type.label.seq_default'),
          name_en: this.$t('assistance.staff_type.label.seq_default'),
        },
      ],
      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      staffTypes: [],
      seqStartLevel: 1,
      roles: [],
      pcRoles: [],
      st_grade_arr: [],
      active_year_arr: [],
      loading: true,
      disPwd: false,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'school']),
    gradeOptions() {
      return [
        {
          label: this.$t('assistance.staff.grade.A'),
          value: 'A',
        },
        {
          label: this.$t('assistance.staff.grade.M'),
          value: 'M',
        },
        {
          label: this.$t('assistance.staff.grade.G'),
          value: 'G',
        },
        {
          label: this.$t('assistance.staff.grade.S'),
          value: 'S',
        },
      ]
    },
    defaultSepOptions() {
      return [
        {
          st_type_id: '',
          staff_id: '',
          seq: '',
          name_cn: this.$t('assistance.staff_type.label.seq_default'),
          name_en: this.$t('assistance.staff_type.label.seq_default'),
        },
      ]
    },
    defaultStaffTypes() {
      return [
        {
          st_type_id: '',
          st_type_cn: this.$t('assistance.staff.label.parent_default'),
          st_type_en: this.$t('assistance.staff.label.parent_default'),
        },
      ]
    },
    hasBG() {
      return this.school.systems.includes('BG')
    },
    hasPC() {
      return this.school.systems.includes('PC')
    },
    codeRules() {
      return [{ required: true, validator: this.checkCode, trigger: 'blur' }]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log('codeArr', codeArr)
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.code === i.code) {
          return
        }
        if (i.children) {
          this.getCodeArr(i.children)
        }
        if (i.staff_id) {
          codeArr.push(i.code)
        }
      })
      return codeArr
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentStaffType(staffType, html, startLevel = 1) {
      let text = this.language === 'en' ? staffType.st_type_en : staffType.st_type_cn
      if (html) {
        text = '&nbsp;'.repeat((staffType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    changeParentType() {
      // if (!this.form.parent_st_type_id) {
      //   this.form.seq = ''
      //   return
      // }

      getStaffsTreeNode(
        '',
        this.form.parent_st_type_id,
        this.form.st_type_id,
        this.form.staff_id,
      ).then(res => {
        this.sepOptions = [...res, ...this.defaultSepOptions]
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].st_type_id ? res[i].st_type_id : res[i].staff_id
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    checkRequired(rule, value, callback) {},
    clearPassword() {
      this.form.password = ''
      this.disPwd = false
    },
    setParent() {
      if (this.editParent && this.editParent.st_type_id) {
        this.form.parent_st_type_id = this.editParent.st_type_id
      } else if (this.editObject && this.editObject.parent && this.editObject.parent.st_type_id) {
        this.form.parent_st_type_id = this.editObject.parent.st_type_id
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getStaff(this.editObject.staff_id)
            .then(res => {
              const defaultForm = Object.assign({}, this.defaultForm)
              this.form = Object.assign(defaultForm, res)
              this.setParent()
              this.form.parent_st_type_id =
                res.staffTypeRelation && res.staffTypeRelation.parent_st_type_id
                  ? res.staffTypeRelation.parent_st_type_id
                  : ''
              this.form.seq_i = res.staffTypeRelation ? res.staffTypeRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : []
              this.st_grade_arr = res.st_grade ? res.st_grade.split(',') : []
              if (this.form.username && this.form.username.length > 0) {
                this.form.password = virtualPassword
                this.disPwd = true
              }

              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => fetchStaffTypes({ staff_id: this.form.staff_id }))
        .then(res => {
          this.staffTypes = [...this.defaultStaffTypes, ...res]
          this.changeParentType(this.form.parent_st_type_id)
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject && res.length && this.fyCode) {
            const year = res.find(i => i.fy_code === this.fyCode)
            if (year) this.active_year_arr.push(year.fy_code)
          }
        })
        .then(() => {
          return fetchRoles(this.system)
        })
        .then(res => {
          this.roles = res
          return fetchRoles('PC')
        })
        .then(res => {
          this.pcRoles = res
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.st_type_id ? 'st_type_id' : 'staff_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const staff_id = this.form.staff_id
        const username = this.form.username
        // const password = this.form.password
        const password =
          this.form.password === virtualPassword ? undefined : this.$md5(this.form.password)
        const st_name_cn = this.form.st_name_cn
        const st_name_en = this.form.st_name_en
        const st_code = this.form.st_code
        const st_grade = this.st_grade_arr.filter(i => i).join(',')
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const role_id = this.form.role_id
        const other_role_id = this.form.other_role_id
        const parent_st_type_id = this.form.parent_st_type_id
        const seq = this.newSeq()

        if (this.editObject) {
          // 編輯
          editStaff(
            staff_id,
            username,
            password,
            st_name_cn,
            st_name_en,
            st_code,
            st_grade,
            active_year,
            role_id,
            other_role_id,
            parent_st_type_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createStaff(
            username,
            password,
            st_name_cn,
            st_name_en,
            st_code,
            st_grade,
            active_year,
            role_id,
            other_role_id,
            parent_st_type_id,
            seq,
          )
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
