<template>
  <div class="userInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.role.label.role_code')"
        prop="role_code"
      >
        <el-input v-model="form.role_code" />
      </el-form-item>
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.role.label.role_name_cn')"
        prop="role_name_cn"
      >
        <el-input v-model="form.role_name_cn" />
      </el-form-item>
      <el-form-item
        :rules="[
          {
            required: true,
            trigger: 'blur',
            message: ' ',
          },
        ]"
        :label="$t('master.role.label.role_name_en')"
        prop="role_name_en"
      >
        <el-input v-model="form.role_name_en" />
      </el-form-item>
      <!-- 權限 -->
      <div
        v-for="(item, g_index) in permission_groups"
        v-show="!loading"
        :key="item.p_group_name_en"
        class="permission_groups"
      >
        <!-- 一級權限 -->
        <div class="group_name">
          <el-button
            :type="item.allSelected ? 'green' : ''"
            :class="permission_Y"
            size="mini"
            @click="selectPermissionGroup(g_index)"
          >
            {{ language === 'en' ? item.p_group_name_en : item.p_group_name_cn }}
          </el-button>
        </div>
        <!-- 二級權限 -->
        <ul v-for="(p, p_index) in item.permissions" :key="p.p_code" class="permissions">
          <el-row :gutter="0">
            <el-col :class="firstWidth" :span="firstSpan">
              <el-button
                :type="p.p_selected && p.p_selected.includes('Y') ? 'orange' : ''"
                :class="permission_Y"
                size="mini"
                @click="triggerPermission(g_index, p_index, 'Y', $event)"
              >
                {{ language === 'en' ? p.p_name_en : p.p_name_cn }}
              </el-button>
            </el-col>
            <el-col :span="23 - firstSpan">
              <template v-for="i in formatAction(p.p_content)">
                <el-button
                  v-if="i !== 'Y'"
                  :key="i"
                  :type="p.p_selected && p.p_selected.includes(i) ? 'primary' : ''"
                  :class="permission_O"
                  size="mini"
                  @click="triggerPermission(g_index, p_index, i, $event)"
                >
                  {{ $t('permission.' + i) }}
                </el-button>
              </template>
            </el-col>
          </el-row>
        </ul>
      </div>
      <el-form-item label-width="0px" class="actions">
        <el-button size="mini" type="primary" @click="onSave">
          {{ editRole ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { createRole, editRole, getRole } from '@/api/master/role'
import { fetchPermissions } from '@/api/permissions'
import { mapGetters, mapActions } from 'vuex'
import { deepCloneByJSON, uniqueArr3 } from '@/utils'

export default {
  name: 'MasterRoleAdd',
  props: {
    editRole: {
      type: Object,
      default: null,
    },
    system: {
      type: String,
      default: 'AC',
    },
  },
  data() {
    return {
      form: {
        role_id: '',
        system: '',
        role_code: '',
        role_name_cn: '',
        role_name_en: '',
        permission_groups: [],
      },
      defaultForm: {
        role_id: '',
        system: '',
        role_code: '',
        role_name_cn: '',
        role_name_en: '',
        permission_groups: [],
      },
      permission_groups: [],
      loading: true,
    }
  },
  computed: {
    ...mapGetters(['allPermissions', 'language']),
    permission_Y() {
      return 'permission_Y firstWidth ' + (this.language === 'en' ? 'en' : 'cn')
    },
    permission_O() {
      return 'permission_O ' + (this.language === 'en' ? 'en' : 'cn')
    },
    firstWidth() {
      return 'firstWidth ' + (this.language === 'en' ? 'en' : 'cn')
    },
    firstSpan() {
      return this.language === 'en' ? 8 : 5
    },
  },
  watch: {
    editRole() {
      this.loading = true
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  beforeDestroy() {
    return false
  },
  methods: {
    ...mapActions(['setAllPermissions']),
    checkRequired(rule, value, callback) {},
    valiadatePass(rule, value, callback) {
      if (this.form.password !== this.form.confirmPassword) {
        // return callback(new Error('两次输入密码不一致!'))
        return callback(new Error(' '))
      } else {
        callback()
      }
    },
    getAllPermissions() {
      return new Promise((resolve, reject) => {
        if (!this.allPermissions[this.system].length) {
          fetchPermissions(this.system)
            .then(res => {
              return this.setAllPermissions({ data: res, system: this.system })
            })
            .then(permissions => {
              resolve(deepCloneByJSON(permissions))
            })
            .catch(err => {
              reject(err)
            })
        } else {
          resolve(deepCloneByJSON(this.allPermissions[this.system]))
        }
      })
    },
    initData() {
      if (this.editRole) {
        // 編輯
      } else {
        // 新增
        this.form = Object.assign({}, this.defaultForm)
        this.form.system = this.system
        this.permission_groups = []
      }
      // // 獲取系統所有的權限
      // this.getAllPermissions().then(p => {
      //   this.permission_groups = p
      //   if (this.editRole) {
      //     // 獲取當前角色的權限
      //     this.initPermission().then().finally(() => {
      //       this.permission_groups.forEach(item => {
      //         item.allSelected = this.groupIsAllSelected(item)
      //       })
      //       this.loading = false
      //
      //       this.$nextTick(() => {
      //         this.loadingCompleted()
      //       })
      //     })
      //   } else {
      //     this.loading = false
      //     this.$nextTick(() => {
      //       this.loadingCompleted()
      //     })
      //   }
      // }).catch(() => {
      //   this.loading = false
      //
      //   this.$nextTick(() => {
      //     this.loadingCompleted()
      //   })
      // })
      this.getAllPermissions()
        .then(p => {
          this.permission_groups = p
          if (this.editRole) {
            return this.initPermission()
          } else {
            return Promise.resolve()
          }
        })
        .then(() => {
          this.permission_groups.forEach(item => {
            item.allSelected = this.groupIsAllSelected(item)
          })
          // for (let i = 0; i < this.permission_groups.length; i++) {
          //   this.permission_groups[i].allSelected = this.groupIsAllSelected(this.permission_groups[i])
          // }
          this.loading = false
          return Promise.resolve()
        })

      // fetchPermissions(this.system).then(res => {
      //   this.permission_groups = res
      //
      // })
    },
    groupIsAllSelected(group) {
      let allSelected = true
      for (let i = 0; i < group.permissions.length; i++) {
        if (!this.arrStrEqual(group.permissions[i].p_content, group.permissions[i].p_selected)) {
          allSelected = false
          break
        }
      }
      return allSelected
    },
    initPermission() {
      return new Promise((resolve, reject) => {
        getRole(this.editRole.role_id)
          .then(res => {
            this.form = Object.assign({}, res)
            res.permission_groups.forEach(item => {
              for (let i = 0; i < this.permission_groups.length; i++) {
                const g_group = this.permission_groups[i]
                if (g_group.p_group_name_en === item.p_group_name_en) {
                  for (let j = 0; j < item.permissions.length; j++) {
                    for (let k = 0; k < g_group.permissions.length; k++) {
                      if (item.permissions[j].p_code === g_group.permissions[k].p_code) {
                        g_group.permissions[k].p_selected = item.permissions[j].p_selected
                        // console.log(item.permissions[j].p_code)
                        break
                      }
                    }
                  }
                  break
                }
                // 是否全選
                g_group.allSelected = this.groupIsAllSelected(g_group)
              }
            })
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    formatPermissionToJson(permission_groups) {
      const permissions = {}

      permission_groups.forEach(group => {
        group.permissions.forEach(item => {
          item.p_selected && (permissions[item.p_code] = item.p_selected)
        })
      })
      return JSON.stringify(permissions)
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const role_id = this.form.role_id
        const system = this.form.system
        const role_code = this.form.role_code
        const role_name_cn = this.form.role_name_cn
        const role_name_en = this.form.role_name_en
        const p_selected_json = this.formatPermissionToJson(this.permission_groups)
        if (this.editRole) {
          // 編輯
          editRole(role_id, role_code, role_name_cn, role_name_en, p_selected_json)
            .then(res => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        } else {
          // 新增
          createRole(system, role_code, role_name_cn, role_name_en, p_selected_json)
            .then(res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    formatAction(p_content) {
      return p_content.split(',')
    },
    arrStrEqual(str1, str2) {
      const a = str1 ? str1.split(',') : []
      const b = str2 ? str2.split(',') : []
      const aSet = new Set(a)
      const bSet = new Set(b)
      const result = Array.from(new Set(a.concat(b).filter(v => !aSet.has(v) || !bSet.has(v))))
      return result.length === 0
    },
    triggerPermission(item_index, p_index, i, event) {
      if (!this.permission_groups[item_index].permissions[p_index].p_selected) {
        this.permission_groups[item_index].permissions[p_index].p_selected = ''
      }
      let selected = this.formatAction(
        this.permission_groups[item_index].permissions[p_index].p_selected,
      )
      const content = this.formatAction(
        this.permission_groups[item_index].permissions[p_index].p_content,
      )
      const index = selected.indexOf(i)
      if (index > -1) {
        if (i === 'Y') {
          selected = []
        } else {
          selected.splice(index, 1)
        }
        if (event) {
          // 點擊后取消focus
          event.currentTarget.blur()
        }
      } else {
        if (i === 'Y') {
          selected.push(...content)
        } else if (selected.indexOf('Y') === -1) {
          selected.push('Y')
        }
        selected.push(i)
      }
      selected.sort((a, b) => {
        return content.indexOf(a) - content.indexOf(b)
      })
      selected = uniqueArr3(selected)

      this.permission_groups[item_index].permissions[p_index].p_selected = selected
        .filter(p => p)
        .join(',')
      // 是否全選
      const allSelected = this.groupIsAllSelected(this.permission_groups[item_index])
      this.permission_groups[item_index].allSelected = allSelected
      this.$forceUpdate()
    },
    selectPermissionGroup(g_index) {
      // 是否全選
      const allSelected = this.groupIsAllSelected(this.permission_groups[g_index])
      this.permission_groups[g_index].allSelected = !allSelected

      this.permission_groups[g_index].permissions.forEach(item => {
        item.p_selected = allSelected ? '' : item.p_content
      })
      this.permission_groups[g_index].selectAll = !allSelected
      this.$forceUpdate()
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.permission_groups {
  padding: 10px 15px;
  border: 1px solid #eaeaea;
  margin: 5px 0 0 25px;
  border-radius: 5px;
  .group_name {
    margin: 0px 5px 10px;
    color: #606266;
    font-size: 14px !important;
    font-weight: 500 !important;
    button {
      font-size: 14px !important;
      font-weight: 500 !important;
    }
  }

  .permissions {
    margin: 5px;
    line-height: 20px;
  }
  .firstWidth {
    margin: 0 5px 0 0;
    &.en {
      width: 130px;
    }
    &.cn {
      width: 90px;
    }
  }
  .permission_Y {
    padding: 7px;
  }
  .permission_O {
    margin: 1px;
    padding: 7px !important;
    &.en {
      width: 80px;
    }
    &.cn {
      width: 40px;
    }
  }
}
.actions {
  padding: 10px 15px;
  margin: 5px 0 0 25px;
  border-radius: 5px;
  button {
    margin: 5px;
  }
}
</style>
