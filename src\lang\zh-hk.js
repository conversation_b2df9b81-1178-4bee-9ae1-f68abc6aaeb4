/* eslint-disable no-dupe-keys */
export default {
  // 系統名稱
  system: {
    AC: '賬目系統',
    BG: '預算系統',
    PC: '採購系統',
  },
  // 路由
  router: {
    home: '首頁',
    enquiry: '查詢',
    enquiryGrant: '津貼查詢',
    enquiryBudget: '預算查詢',
    enquiryStaff: '職員查詢',
    enquiryDepartment: '部門查詢',
    enquiryContra: '對沖查詢',
    enquiryGeneral: '綜合查詢',

    daily: '日常',
    voucherAdd: '新增傳票',
    voucherEdit: '修改傳票',
    voucherView: '查看傳票',
    voucherReorderSummons: '重排傳票',

    dailyPayment: '付款傳票',
    dailyPettyCash: '零用現金',
    dailyReceipt: '收款傳票',
    dailyBankTransfer: '銀行轉賬',
    dailyJournal: '一般傳票',
    dailyCheque: '支票',
    dailyAddress: '地址',
    dailyPost: '收費過賬',

    periodic: '週期',
    periodicBankReconciliation: '銀行手動對賬',
    periodicBankAiReconciliation: '銀行 AI 對賬',
    periodicDailyCollection: '每日收款',
    periodicAutopayList: '自動轉賬列表',
    periodicBalanceTransfer: '結餘轉賬',
    periodicObCheque: '期初支票',
    periodicEDBInput: 'EDB年度調整',
    periodicDataBackup: '數據備份',
    restoreDemoData: '還原Demo數據',

    stock: '庫存',
    stockPurchase: '購貨輸入',
    stockSales: '銷售輸入',
    stockMonthlySales: '每月銷售',
    stockBalance: '結存記錄',
    stockIO: '進出記錄',
    stockProfitReport: '利潤報表',
    stockItemSetup: '貨品項目',

    report: '報表',
    reportTrialBalance: '試算表',
    reportLedger: '會計賬目',
    reportCashBook: '現金/銀行賬薄',
    reportBudget: '預算',
    reportStaff: '職員',
    reportDepartment: '部門',
    reportVoucher: '傳票',
    reportExcelReport: 'Excel 報告',
    reportCustomizedReports: '自定義報表',

    master: '主檔案',
    masterFund: '賬目類別',
    masterAccount: '會計科目',
    masterVoucherType: '傳票類別',
    masterUser: '使用者',
    masterRoleSet: '角色設定',
    masterACPeriod: '會計週期',

    assistance: '輔助檔案',
    assistanceBudget: '預算',
    assistanceDescription: '傳票明細',
    assistancePayeePayer: '收付款公司',
    assistanceStaff: '同事檔案',
    assistanceDepartment: '部門',
    assistanceContraCode: '對沖編號',
    assistanceChequeBook: '支票簿',
    assistanceBudgetRole: '預算角色',
    assistanceLedgerBudget: '賬目預算',
    assistanceNumberOfSchool: '學生人數',
    assistanceFundSummaryTypesRelation: '財務報表設定',
    assistanceNorrayEDBRelation: 'EDB設定',
    assistanceEDBRelationBySecondarySchool: '中小學EDB設定',
    assistanceEntrySummary: 'Entry數量',
    assistanceFixWrongVoucher: '數據管理',

    setting: '設定',
    settingUserInfo: '用者資料',
    settingScreen: '畫面',
    settingScreenBasicSetting: '基本設定',
    settingScreenBasic: '字型',
    settingScreenTableHeader: '表頭',
    settingScreenMenu: '菜單',
    settingScreenContent: '內容',

    settingPrintout: '表單列印',
    settingChecking: '數據檢查',
    settingDataExport: '資料導出',
    settingPageSet: '紙設定',
    settingDataImport: '資料導入',
    settingBackup: '數據備份',

    budget: {
      daily: '日常',
      dailyBudgetList: '預算列表',
      dailyBudgetManagement: '預算管理',
      dailyEnquires: '查詢',
      systemSettings: '系統設定',
      systemSettingsFundSetup: '撥款設定',
      systemSettingsAccountInput: '會計賬號輸入',
      systemSettingsRoleSet: '角色設定',
      systemSettingsStaffSet: '同事設定',
      systemSettingsBudgetSet: '預算設定',
      reports: '報表',
      reportsBudgetList: '預算列表',
      reportsIndividualBudgetReport: '個別預算報告',
      personalSet: '個人設定',
      personalSetChangePassword: '更改密碼',
      personalSetScreen: '畫面',
      personalSetPrintout: '表單',
    },
    // 採購
    purchase: {
      daily: '日常',
      dailyProcurement: '採購申請',
      dailyApproval: '採購審批',

      master: '主檔案',
      period: '會計週期',
      staff: '同事檔案',
      masterProcurementLevel: '採購等級',
      role: '角色設定',
      systemSetting: '系統設定',
    },
  },
  navbar: {
    help: '幫助',
    inputDate: '輸入日期：',
    today: '今天',
    yesterday: '昨天',
    oneWeekAgo: '一周前',
    edit: '修改',
    add: '新增',
    switchToEnglish: '切換至英文',
    switchToChinese: '切換至中文',
    zhHK: '繁體中文',
    english: 'English',
    switchToBudgetSystem: '預算系統',
    switchToPurchaseSystem: '採購系統',
  },
  footer: {
    version: '版本',
    versionNumber: '1.0',
    developer: '軟件開發',
    company: '@歐美專業電腦有限公司',
    phone: '電話',
    phoneNumber: '（852）2394-4114',
    fax: '傳真',
    faxNumber: '（852）2789-4910',
    mail: '電郵',
    email: '<EMAIL>',
  },
  // 错误日志
  errorLog: {
    title: '錯誤日誌',
    message: '訊息',
    msg: '訊息',
    url: '網址',
    info: '資訊',
    stack: '堆疊',
    errorIn: '錯誤在',
  },
  // Github 角落
  githubCorner: {
    viewSource: '在 Github 上查看源碼',
  },
  // 頭部搜索
  headerSearch: {
    search: '搜索',
  },
  // 尺寸選擇
  sizeSelect: {
    default: '預設',
    medium: '中等',
    small: '小',
    mini: '迷你',
  },
  // 通用
  common: {
    yes: 'Y',
    no: 'N',
    notApplicable: '不適用',
    colon: '：',
  },
  // 語言選擇
  langSelect: {
    chinese: '中文',
    english: 'English',
    spanish: 'Español',
  },
  // 錯誤頁面
  errorPage: {
    back: '返回',
    oops: 'Oops!',
    orYouCanGo: '或者你可以去:',
    backToHome: '回首頁',
    logout: '登出',
    randomLook: '隨便看看',
    randomLookTitle: '隨便看',
    copyright: '版權所有',
    checkUrl: '請檢查您輸入的網址是否正確，請點擊以下按鈕返回主頁或者發送錯誤報告',
    returnHome: '返回首頁',
  },
  // 全屏組件
  screenfull: {
    browserNotSupported: '您的瀏覽器不支持全屏功能',
  },
  // 上傳組件
  upload: {
    dragText: '將文件拖到此處，或',
    clickUpload: '點擊上傳',
  },
  // 報表
  report: {
    report: '報表',
    year: '學年',
    month: '月份',
    financialStatement: '財務報表',
    edb: '教育局',
    swd: '社會福利署',
    edbSecondarySchool: '教育局中學',
  },
  // 主題
  themeDemo: {
    primary: '主要',
    success: '成功',
    info: '信息',
    warning: '警告',
    danger: '危險',
    search: '搜索',
    upload: '上傳',
  },
  // 語言切換
  langSwitch: {
    success: '切換語言成功',
  },
  // 登錄頁面
  login: {
    tips: '請聯繫系統管理員！',
    forgetPassword: '忘記密碼',
    username: '賬號',
    password: '密碼',
    login: '登錄',
    logOut: '登出',
    systemAdmin: '系統管理員',
  },
  date: {
    dateTips: '請選擇日期',
    days: ['日', '一', '二', '三', '四', '五', '六'],
    justNow: '剛剛',
    minutesAgo: '分鐘前',
    hoursAgo: '小時前',
    daysAgo: '天前',
    month: '月',
    day: '日',
    hour: '時',
    minute: '分',
    pickerOptions: {
      today: '今天',
      lastWeek: '最近一週',
      lastMonth: '最近一個月',
      lastThreeMonths: '最近三個月',
    },
  },
  confirm: {
    deleteConfirm: '確認刪除',
    warningTitle: '警告',
    confirmButtonText: '確定',
    cancelButtonText: '取消',
    cancelConfirm: '確認取消',
    tipsTitle: '提示',
    cancelOperation: '已取消操作',
    systemError: '系統錯誤，請刷新頁面',
  },
  button: {
    save: '儲存',
    saveFile: '存儲並返回',
    keep: '儲存',
    cancel: '取消',
    add: '新增',
    edit: '修改',
    copy: '複製',
    delete: '刪除',
    clear: '清除',
    confirm: '確認',
    loadDefault: '獲取預設',
    saveDefault: '儲為預設',
    reset: '重設',
    update: '更新',
    previewCheque: '預覽支票',
    printCheque: '列印支票',
    fetch: '查詢',
    print: '列印',
    chequePrint: '支票列印',
    chequePreview: '支票預覽',
    select: '選擇',
    submit: '提交',
    back: '返回',
    next: '下一步',
    prev: '上一步',
    upload: '上傳',
    browse: '瀏覽',
    import: '匯入',
    export: '匯出',
    backup: '備份',
    restore: '還原',
    loadData: '獲取數據',
    generateVoucher: '生成傳票',
    generateGroupVoucher: '生成傳票（1份）',
    uploadFile: '上載文件',
    checkConfiguration: '查看配置',
    goTop: '回到頂部',
    abandon: '放棄',
  },
  enquiry: {
    timeType: {
      all: '所有',
      year: '全年',
      to: '年度截至',
      month: '當月',
      free: '自由輸入',
      day: '日期',
    },
    grant: {
      label: {
        vc_date: '日期',
        vc_no: '傳票編號',
        ac_code: '會計賬號',
        ac_name_: '賬目類別',
        descr: '內容描述',
        tx_type: 'IE',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        net_amount: '淨金額', // 淨金額
        bal: '結餘', // 結餘

        // dr
        amount_dr_dr: '借方金額',
        amount_cr_dr: '貸方金額', // x
        net_amount_dr: '借方淨額',
        bal_dr: '借方結餘',
        // cr
        amount_dr_cr: '貸方金額', // x
        amount_cr_cr: '貸方金額',
        net_amount_cr: '貸方淨額',
        bal_cr: '貸方結餘',
        // e
        amount_dr_e: '支出',
        amount_cr_e: '收入', // x
        net_amount_e: '支出淨額',
        bal_e: '累計支出',
        // i
        amount_dr_i: '收入', // x
        amount_cr_i: '收入',
        net_amount_i: '收入淨額',
        bal_i: '累計收入',
        // g
        amount_dr_g: '支出',
        amount_cr_g: '收入',
        net_amount_g: '收支淨額',
        bal_g: '津貼結餘',
        // ie
        amount_dr_ie: '支出',
        amount_cr_ie: '收入',
        net_amount_ie: '收支淨額',
        bal_ie: '累計收支',

        vc_payee: '收付款人',
        // 收付款簡稱
        // 參考號
        budget_code: '預算編號',
        // 預算名稱
        st_code: '職員編號',
        staff_name_: '職員名稱',
        // 職員名稱
        vc_contra: '對沖編號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',

        ref: '參考號',
        vt_category: '類型',
        vt_code: '類別',

        sumText: '本期總額',
        preTotal: '上期總額',

        LastYearBF: '上年結餘',
        lastPeriod: '上期總額',
      },
    },
    staff: {
      label: {
        vc_no: '傳票編號',
        ac_code: '會計賬號',
        ac_name_: '賬目類別',
        descr: '內容描述',
        tx_type: 'IE',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        vc_payee: '收付款人',
        ref: '參考號',
        budget_code: '預算編號',
        st_code: '職員編號',
        staff_name_: '職員名稱',
        vc_contra: '對沖編號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',
        vc_date: '日期',
        vt_category: '類型',
        vt_code: '類別',
        net_amount: '淨金額', // 淨金額
        bal: '結餘', // 結餘

        sumText: '本期總額',
        budgetAmount: '預算金額',
        budgetBalance: '預算餘額',
      },
    },
    department: {
      label: {
        vc_date: '日期',
        vc_no: '傳票編號',
        ac_code: '會計賬號',
        ac_name_: '賬目類別',
        descr: '內容描述',
        tx_type: 'IE',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        net_amount: '淨金額', // 淨金額
        bal: '結餘', // 結餘,
        vc_payee: '收付款人',
        ref: '參考號',
        budget_code: '預算編號',
        st_code: '職員編號',
        staff_name_: '職員名稱',
        vc_contra: '對沖編號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',
        vt_category: '類型',
        vt_code: '類別',

        sumText: '本期總額',
      },
    },
    contra: {
      label: {
        vc_date: '日期',
        vc_no: '傳票編號',
        ac_code: '會計賬號',
        ac_name_: '賬目類別',
        descr: '內容描述',
        tx_type: 'IE',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        net_amount: '淨金額', // 淨金額
        bal: '結餘', // 結餘
        vc_payee: '收付款人',
        ref: '參考號',
        budget_code: '預算編號',
        st_code: '職員編號',
        staff_name_: '職員名稱',
        vc_contra: '對沖編號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',

        pd_code: '月份',
        vt_category: '類型',
        vt_code: '類別',

        sumText: '本期總額',
      },
    },
    general: {
      label: {
        allBudget: '所有預算',

        date: '日期',
        startDate: '開始日期',
        endDate: '結束日期',
        fund: '賬目',

        allAccountType: '所有會計科目',
        amount: '金額',

        type: '類別',
        payee: '收付款',
        ref: '參考號',
        contra: '對沖號',
        quote: '報價',
        desc: '內容',
        vc_code: '傳票號',
        dept: '部門',
        budget: '預算',
        allDept: '所有部門',

        staff: '職員',
        allStaff: '所有職員',

        vc_no: '傳票編號',
        ac_code: '會計賬號',
        ac_name_: '賬目類別',
        descr: '內容描述',
        tx_type: 'IE',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        net_amount: '淨金額', // 淨金額
        vc_payee: '收付款人',
        budget_code: '預算編號',
        st_code: '職員編號',
        staff_name_: '職員名稱',
        vc_contra: '對沖編號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',
        vc_date: '日期',
        vt_category: '類型',
        vt_code: '類別',
      },
      amountType: {
        bet: '範圍',
        val: '數值',
        gt: '大於',
        lt: '小於',
        A: '±Dr/±Cr',
        B: '+Dr/+Cr',
        C: '+Dr/-Cr',
        D: '-Dr/+Cr',
        E: '-Dr/-Cr',
        F: '±Dr',
        G: '+Dr',
        H: '-Dr',
        I: '±Cr',
        J: '+Cr',
        K: '-Cr',
      },
      vc_type: {
        all: '所有',
        i: '收入類',
        e: '支出類',
      },
      timeType: {
        a: '所有',
        y: '全年',
        t: '年度截至',
        m: '當月',
        f: '自由輸入',
        d: '日期',
      },
      quoteType: {
        N: '無',
        Q: '報價',
        T: '投標',
      },
      button: {
        fetch: '查詢',
        reset: '重設',
      },
    },
    budget: {
      label: {
        vc_date: '日期',
        vc_no: '傳票編號',
        ac_code: '會計賬號',
        ac_name_: '賬目名稱',
        descr: '內容描述',
        tx_type: 'IE',
        amount_dr: '支出',
        amount_cr: '收入',
        net_amount: '淨支出',
        bal: '累計淨支出',
        vc_payee: '收付款人',
        ref: '參考號',
        budget_code: '預算編號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',
        st_code: '職員編號',
        staff_name_: '職員名稱',
        vc_contra: '對沖編號',

        sumText: '本期總額',
      },
    },
  },
  daily: {
    label: {
      name: '名稱',
      date: '日期',
      type: '類別',
      number: '編號',
      amt_type: '形式',
      desc: '內容描述',
      staff: '職員',
      payee: '收款人',
      payee_receipt: '收款人',
      payee_payment: '付款人',
      payee_c: '收款人',
      vc_date: '傳票日期',
      all: '所有',
      allAccountType: '所有科目類別',
      allFundType: '賬目類別',
      allType: '所有類型',
      allDepartment: '所有部門',
      accountType: '科目類別',
      crAmtSum: '貸方總額',
      drAmtSum: '借方總額',
      amtSum: '總額',
      normal: '正常輸入',
      correctOne: '審計修正1',
      correctTwo: '審計修正2',
      correctThree: '審計修正3',

      staffType: '同事組別',
      allStaff: '所有職員',
      companyGroups: '公司組別',
      dept: '部門',

      no: '編號',
      staffNameCN: '中文姓名',
      staffNameEN: '英文姓名',
      nameCN: '中文名稱',
      nameEN: '英文名稱',
      comp_name: '名稱',
      comp_abbr: '簡稱',
      allPayee: '所有收付款人',
      account: '會計賬目',
      descr: '描述',
      amount: '金額',

      group: '組別',
      budgetItem: '預算項目',
      category: '分類',
      ac_code: '公司編號',
    },
    dialog: {
      voucherNO: '傳票編號',
      account: '會計科目',
      staff: '同事檔案',
      contra: '對沖編號',
      department: '部門',
      description: '傳票明細',
      payee: '收款人',
      budget: '預算',
      multipleVoucherImport: '傳票匯入',
    },
    payment: {
      allMonth: '所有月份',
      label: {
        vc_no: '傳票號碼',
        vc_date: '日期',
        vc_summary: '概述',
        vc_amount: '傳票金額',
        vc_method: '形式',
        ref: '支票號碼',
        vc_payee: '收付款人',
        st_name_: '職員名稱',
      },
    },
    cheque: {
      typeOption: {
        type_X: '沒有支票號碼',
        type_N: '支票簿未登記',
        type_C: '支票簿',
      },
      label: {
        chq_no: '支票號碼',
        vc_chq_date: '支票日期',
        vc_payee: '支票抬頭',
        vc_amount: '支票金額',
        vc_no: '傳票號碼',
      },
      button: {
        chequeVoid: '作廢',
        chequeRestore: '恢復',
      },
      message: {
        selectChequeBook: '請選擇支票簿',
        selectCheque: '請選擇支票',
      },
    },
    address: {
      label: {
        allPayee: '所有收付款人',
        payeeGroup: '分類',
        optional: '待選擇',
        selected: '已選擇',
        search: '搜索',
      },
      button: {
        fetch: '查詢',
        printLabel: '打印標籤',
        printEnvelope: '打印信封',
      },
    },
    voucher: {
      voucherMethod: {
        cash: '現金',
        // cheque: '支票', // 不顯示
        transf: '自動轉賬',
        // other: '其他', // 不顯示
        auto: 'AUTOPY',
      },
      label: {
        voucherType: '類別',
        year: '年份',
        month: '月份',
        voucherCount: '傳票項數：',
        voucherTotal: '傳票總額：',
        // column
        index: '#',
        account: '會計賬目',
        desc: '內容描述',
        amount: '金額',
        amount_dr: '借方金額\n(HK$)',
        amount_cr: '貸方金額\n(HK$)',
        voucherNo: '編號',
        voucherDate: '日期',

        paymentVoucher: '付款傳票',
        receiptVoucher: '收款傳票',
        cashVoucher: '零用現金傳票',
        transferVoucher: '銀行轉賬傳票',
        journalVoucher: '一般傳票',

        detailStyle: '明細描述顯示狀態',
        staff: '職員',
        ref: '參考編號',
        budget: '預算',
        quote: '報價',
        dept: '部門',
        contra: '對沖編號',
        originalSummonsNumber: '原傳票號',
        summonsDate: '原傳票日期',
        checkNumber: '支票編號',
        payee: '收付款人',
        moneny: '金額',
        newSummonsNumber: '新傳票號',
      },
      button: {
        addRow: '加行',
        deleteRow: '刪行',
        save: '儲存',
        cancel: '放棄',
        voucher: '傳票',
        chequePreview: '支票預覽',
        cheque: '支票',
        envelop: '信封',
        reset: '重設',
        update: '更新',
      },
      message: {
        inputVoucherNo: '請填寫傳票號碼',
        inputVoucherInfo: '請填寫傳票信息',
        inputAccountingSubject: '請選擇會計賬目',
        invalidAcCode: '會計科目無效',
        invalidStaff: '職員無效',
        invalidDept: '部門無效',
        invalidContra: '對沖無效',
        theAmountCannotBeZero: '金額不能為零',

        dcNotEqual: '借貸金額不相等',

        selectFund: '請選擇賬目類別',
        selectVoucherDate: '請選擇傳票日期',

        voucherNotHasPayee: '選擇的傳票不包含收付款公司/人',

        importRowErr: '行{row}：缺少必填項[{field}]',
        dataLoadingFailed: '數據加載失敗，請刷新重試！',
        descCannotEmpty: '描述的內容不能為空',
      },
      printout: {
        ref: '參考號',
        budget: '預算',
        staff: '職員',
        dept: '部門',
        dept2: '部門',
        payee_receipt: '收款人',
        payee_payment: '付款人',
        voucherType: '類別',
        bank: '銀行',
        chq_no: '支票號碼',
        chq_date: '支票日期',
        summary: '撮要',
        paidBy: '付款方式',
      },
    },
    post: {
      label: {
        vt_category: '類別',
        vc_date: '日期',
        ac_code: '會計賬號',
        ac_name: '賬目名稱',
        Descr: '明細',
        amount: '金額($)',
        Amount_Dr: '收入($)',
        Amount_Cr: '支出($)',
        vt_description: '傳票類別',
        date: '截止日期',
        voucher_date: '傳票日期',
        load_data: '獲取收費數據',
        detail: '詳情',
        vc_vtcode: '傳票類別',
        Ref: '支票號碼',
        other: '其他',
        method_of_payment: {
          CASH: '現金',
          CHECK: '支票',
          AUTOPAY: '自動轉賬',
          FPS: 'FPS',
          BANKIN: '銀行入賬',
          OTHER: '其他',
        },
        vc_summary: '內容描述',
        generateVoucher: '生成傳票',
        multipleEdit: '批量修改傳票類別',
        tra_date: '銷售日期',
      },
    },
  },
  periodic: {
    bankReconciliation: {
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        descr: '內容描述',
        vc_payee: '收付款者',
        vc_method: '形式',
        ref: '支票號碼',
        amount_dr: '收入($)',
        amount_cr: '支出($)',
        vc_rdate: '對沖日期',

        ledgerBalance: '銀行賬結餘',
        unpresentedPayment: '未兌現付款',
        unpresentedReceipt: '未兌現收款',
        statementBalance: '月結單結餘',
        CASH: '現金',
        TRANSF: '轉賬',
        CHEQUE: '支票',
        AUTO: '自動',
        OTHER: '其它',
        clearAll: '一鍵清除',
      },
      status: {
        unpaid: 'Unpaid',
        future: 'Future',
        clearAll: '一鍵清除',
        isClearAll: '確認清除所有未兌現收款和未兌現付款嗎？',
      },
      button: {
        applyReceipt: '兌現收款',
      },
    },
    dailyCollection: {
      filters: {
        fund: '賬目',
        all: '所有',
        period: '時段',
        to: '-',
        date: '日期',
        begin_date: '開始日期',
        end_date: '結束日期',
      },
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        ref: '支票號碼',
        ac_code: '會計賬號',
        ac_name_: '賬目名稱',
        ac_name_cn: '賬目名稱(中)',
        ac_name_en: '賬目名稱(英)',
        ac_abbr_cn: '賬目簡稱(中)',
        ac_abbr_en: '賬目簡稱(英)',
        descr: '內容描述',
        lg_amount: '明細',
        vc_amount: '總金額',
        vc_receipt: '收據編號',
        vc_receipt_date: '收據日期',
        vc_method: '形式',
      },
      button: {
        updateReceipt: '更新收據',
      },
    },
    autopayList: {
      typeOption: {
        type_P: '付款',
        type_R: '收款',
      },
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        ac_code: '會計賬號',
        ac_name_: '賬目名稱',
        ac_name_cn: '賬目名稱(中)',
        ac_name_en: '賬目名稱(英)',
        ac_abbr_cn: '賬目簡稱(中)',
        ac_abbr_en: '賬目簡稱(英)',
        descr: '內容描述',
        lg_amount: '明細',
        vc_amount: '總金額',
        bank_ac_code: '銀行賬號',
        vc_receipt: '收據編號',
        fund_name_: '撥款',
        vc_payee: '收付款人',
        vc_method: '形式',
        all: '所有',
      },
    },
    balanceTransfer: {
      label: {
        ac_code: '編號',
        ac_name_: '賬目名稱',
        ac_abbr_: '賬目簡稱',
        ac_B: '轉賬',
        bf_amount: '上年結餘',
        ob_amount: '預算期初',
        adj_amount: '期初調整',

        lastData: '上年度賬目結餘',
        thisData: '本年度期初結餘',

        totalDr: '借方總值',
        totalCr: '貸方總值',
      },
      amountType: {
        dr: '借',
        cr: '貸',
      },
      symbol: {
        plus: '(+)',
        minus: '(-)',
        add: '+',
        subtract: '-',
        colon: '：',
        equals: '=',
        multiply: 'X',
      },
      button: {
        ob: '期初',
        cs: '期末',
        autoTransfer: '自動轉賬',
        update: '更新',
        cancel: '取 消',
        confirm: '确 定',
      },
      message: {
        autoTransfer: '是否確認覆蓋當前期初調整？',
      },
    },
    obCheque: {
      label: {
        amount: '支票金額',
        ref: '支票號碼',
        vc_date: '日期',
        vc_payee: '收款人',
        vc_rdate: '兌現日期',

        cheque_date: '支票日期',
        currentCheque: '此支票',
        Unpaid: 'Unpaid',
      },
    },
  },
  master: {
    user: {
      name_cn: '中文姓名',
      name_en: '英文姓名',
      username: '登入名',
      action: '操作',
      label: {
        name_cn: '中文姓名',
        name_en: '英文姓名',
        username: '登入名稱',
        password: '登入密碼',
        confirm: '確認密碼',
        type: '類別',
        type_admin: '管理員',
        type_general: '一般用者',
        role: '角色',
        group: '組別',
        add: '新增',
        edit: '修改',
      },
      value: {
        typeAdmin: 'A',
        typeGeneral: 'G',
      },
    },
    role: {
      role_name: '角色名',
      role_name_cn: '中文角色名',
      role_name_en: '英文角色名',
      system: '屬於系統',
      action: '操作',
      label: {
        role_name: '角色名',
        role_name_: '角色名',
        // role_id: '角色id',
        system: '屬於系統',
        role_code: '角色編號',
        role_name_cn: '中文角色名',
        role_name_en: '英文角色名',
        // show: '用戶是否可見',
        // created_at: '創建時間',
        // updated_at: '更新時間'
      },
    },
    year: {
      all_year: '所有會計週期',
      fy_code: '編號',
      fy_name: '學年',
      action: '操作',
      month: '月份',
      status: '狀態',
      yymm: 'YYMM',
      label: {
        fy_code: '編號',
        fy_name: '學年',
        fy_status: '狀態',
        fy_enquiry: '查詢',
      },
      option: {
        open: '開啟',
        close: '關閉',
      },
    },
    fund: {
      all_fund: '所有撥款',
      all_fund2: '所有帳目類別',
      fund_id: '賬目類別id',
      name_cn: '中文名稱',
      name_en: '英文名稱',
      username: '登入名',
      action: '操作',
      label: {
        _index: '#',
        fund_id: '賬目類別id',
        fund_code: '賬目類別編號',
        fund_name: '名稱',
        fund_name_: '名稱',
        fund_name_cn: '名稱(中)',
        fund_name_en: '名稱(英)',
        fund_abbr: '簡稱',
        fund_abbr_: '簡稱',
        fund_abbr_cn: '簡稱(中)',
        fund_abbr_en: '簡稱(英)',
        active_year: '活躍年度',
        parent_default: '所有賬目',
        seq_default: '--- 置於最後 ---',
      },
    },
    account: {
      all_account: '所有會計科目',
      label: {
        _index: '#',
        first_field: '編號/名稱',
        ac_bie: 'BIE',
        ac_code: '賬目編號',
        ac_name_cn: '名稱(中)',
        ac_name_en: '名稱(英)',
        ac_abbr_cn: '簡稱(中)',
        ac_abbr_en: '簡稱(英)',
        abbr_cn: '簡稱(中)',
        abbr_en: '簡稱(英)',
        code: '賬目編號',
        parent_fund_id: '上級',
        parent_default: '所有賬目',
        seq: '位置 (前置)',
        seq_default: '--- 置於最後 ---',
        nature_code: '賬戶性質',
        code_default: '津貼',
        ac_category: '賬戶類別',
        ac_bank: '銀行',
        bank_default: '非銀行',
        ac_cf: '年結賬號',
        cf_default: '** 請選擇賬目 **',
        ac_group: '組別',
        active_year: '活躍年度',
        income_bg_year: '收入預算',
        expense_bg_year: '支出預算',
      },
      category: {
        B: 'B',
      },
      natureOptions: {
        option_G: '津貼',
        option_IE: '收入/支出',
        option_I: '收入',
        option_E: '支出',
        option_Dr: '借方結餘',
        option_Cr: '貸方結餘',
      },
      bankOptions: {
        option_X: '非銀行',
        option_S: '儲蓄',
        option_C: '往來',
        option_F: '定期',
        option_P: '現金',
        option_A: 'A/P',
      },
    },
    voucher_type: {
      all: '所有類別',
      label: {
        voucher_type_id: '傳票類別id',
        fund_id: '撥款', // 賬目類別(大)id
        fund_name_: '撥款', // 賬目類別(大)id
        vt_category: '類別', // 傳票類別類型
        vt_code: '編號', // 傳票類別編號
        vt_description_cn: '描述(中)', // 傳票類別描述中文
        vt_description_en: '描述(英)', // 傳票類別描述英文
        vt_description_: '描述',
        vt_format: '格式', // 傳票類別格式
        vt_ac_code: '銀行賬號', // 銀行賬號(會計科目編號)
        chq_template_code: '支票款式', // 支票款式編號
        vt_group: '組別', // 傳票類別組別
        active_year: '活躍年度', // 活躍的會計週期編號
        chq_name_: '支票款式', // 活躍的會計週期編號
      },
      rules: {
        isExist: '編號已存在',
      },
    },
  },
  assistance: {
    staff: {
      label: {
        first_field: '編號/名稱',
        staff_id: '職員id',
        username: '登入名稱',
        password: '密碼',
        st_name_cn: '名稱(中)',
        st_name_en: '名稱(英)',
        st_code: '職員編號',
        st_grade: '預算級別',
        grade: '級別',
        active_year: '活躍年度',
        role_id: '角色',
        budgetRole: '預算角色',
        pc_role_id: '採購角色',
        parent_st_type_id: '上級',
        seq: '位置 (前置)',
        parent_default: '所有職員',
        seq_default: '--- 置於最後 ---',
      },
      grade: {
        A: '審批',
        M: '負責人',
        G: '成員',
        S: '職員',
      },
    },
    staff_type: {
      label: {
        st_type_code: '組別編號',
        st_type_cn: '名稱(中)',
        st_type_en: '名稱(英)',
        parent_st_type_id: '上級',
        seq: '位置 (前置)',
        active_year: '活躍年度',
        parent_default: '所有職員',
        seq_default: '--- 置於最後 ---',
      },
    },
    department: {
      label: {
        _index: '#',
        first_field: '編號/名稱',
        department_id: '部門id',
        dept_name_cn: '名稱(中)',
        dept_name_en: '名稱(英)',
        dept_code: '部門編號',
        active_year: '活躍年度',
        parent_dept_type_id: '上級',
        seq: '位置 (前置)',
        parent_default: '所有部門',
        seq_default: '--- 置於最後 ---',
      },
    },
    dept_type: {
      label: {
        dept_type_code: '部門編號',
        dept_type_cn: '名稱(中)',
        dept_type_en: '名稱(英)',
        active_year: '活躍年度',
        parent_dept_type_id: '上級',
        seq: '位置 (前置)',
        parent_default: '所有部門',
        seq_default: '--- 置於最後 ---',
      },
    },
    contra: {
      label: {
        _index: '#',
        contra_code: '編號',
        contra_name_cn: '中文名稱',
        contra_name_en: '英文名稱',
      },
    },
    chequeBook: {
      allVoucherType: '所有類別',
      label: {
        _index: '#',
        fund_id: '撥款',
        fund_name_: '撥款',
        vt_code: '分類',
        chqbk_code: '編號',
        chqbk_from: '從',
        chqbk_to: '至',
        active_year: '活躍年度',
        bank_code: '銀行',
      },
    },
    description: {
      label: {
        multi_ac_code: '編號',
        // desc_id: '傳票明細id',
        fund_id: '帳目類別',
        ac_code: '編號', // 歸屬會計科目編號
        desc: '內容描述', // 描述
        BIE: 'BIE', // 會計科目的BIE歸屬,可多選,例如 B,I,E
        active_year: '活躍年度', // 活躍的會計週期編號
        selectAccount: '選擇會計賬目',
      },
    },
    ledgerBudget: {
      allFund: '所有賬目類別',
      label: {
        range: '範圍',
        active_year: '活躍年度',
        code: '編號',
        name: '賬目名稱',
        income: '收入預算',
        expense: '支出預算',
      },
    },
    fundSummaryTypesRelation: {
      year: '學年',
      fixWrongVoucher: '需修復傳票編號的學年',
      sch_type: '格式',
      sch_type_pri: '小學',
      sch_type_sec: '中學',
    },
    payeePayer: {
      label: {
        first_field: '編號/名稱',
        comp_code: '公司編號',
        comp_name: '公司名',
        comp_abbr: '簡稱',
        parent_company_group_id: '上級',
        seq: '位置 (前置)',
        comp_group: '類別',
        comp_supplier: '供應商',
        comp_add: '地址',
        comp_tel: '電話',
        comp_fax: '傳真',
        comp_attention: '聯絡人',
        comp_email: '電郵',
        comp_remark: '備註',
        comp_ap: 'A/P',
        comp_credit: 'credit',
        active_year: '活躍年份',
        seq_default: '--- 置於最後 ---',
        parent_default: '所有收付款人',
      },
    },
    payeePayerGroup: {
      label: {
        cg_code: '組別編號',
        cg_name_cn: '名稱(中)',
        cg_name_en: '名稱(英)',
        parent_company_group_id: '上級',
        seq: '位置 (前置)',
        active_year: '活躍年度',
        seq_default: '--- 置於最後 ---',
        parent_default: '所有收付款人',
      },
      groupOptions: {
        B: '收付款',
        I: '收款',
        E: '付款',
      },
    },
    numberOfSchool: {
      month: '月份',
      num_of_half_day: '半日',
      num_of_infant: '幼兒',
      num_of_whole_day: '全日',
      scale_of_whole_day: '分攤比率',
    },
  },
  stock: {
    years: '年份',
    months: '月份',
    style: '樣式',
    mode: '模式',
    stockItem: '存貨項目',
    purchase: {
      label: {
        pi_no: '發票編號',
        date: '日期',
        pi_payee: '供應商編號',
        comp_name: '供應商',
        chq_no: '支票編號',
        voucher_no: '傳票編號',
        count: '數量',
        price: '單價',
        amount: '金額',
        items: '貨品明細',
        openingBalance: '期初結餘',
        sk_code: '編號',
        total: '總金額',
      },
    },
    sales: {
      label: {
        date: '日期',
        type: '類型',
        chq_no: '支票編號',
        voucher_no: '傳票編號',
        count: '數量',
        price: '單價',
        amount: '金額',
        items: '貨品明細',
        openingBalance: '期初結餘',
        sk_code: '編號',
        total: '總金額',
      },
    },
    itemSetup: {
      label: {
        first_field: '編號/名稱',
        sk_code: '貨品編號',
        sk_name_cn: '名稱（中）',
        sk_name_en: '名稱（英）',
        name_cn: '貨品中文名',
        name_en: '貨品英文名',
        parent_stock_group_id: '上級',
        seq: '位置 (前置)',
        active_year: '活躍年度',
        seq_default: '--- 置於最後 ---',
        parent_default: '所有存貨項目',
        purchase_price: '購入單價',
        sales_price: '銷貨單價',
        date: '日期',
      },
      button: {
        goodsImport: '貨品匯入',
        priceImport: '貨品價格匯入',
        goodsExport: '貨品匯出',
        priceExport: '貨品價格匯出',
      },
    },
    itemSetupGroup: {
      label: {
        sg_code: '組別編號',
        sg_name_cn: '名稱（中）',
        sg_name_en: '名稱（英）',
        parent_stock_group_id: '上級',
        seq: '位置 (前置)',
        active_year: '活躍年度',
        seq_default: '--- 置於最後 ---',
        parent_default: '所有存貨項目',
      },
    },
    monthlySales: {
      allMonth: '全年',
      label: {
        sk_code: '編號',
        desc: '描述',
        quantity: '數量',
        averagePrice: '平均價',
        amount: '金額',
        total: '總計',
        monthTotal: '月結總計',
        yearTotal: '年結總計',
      },
    },
    salesProfit: {
      label: {
        sales: '銷貨',
        less: '扣減：',
        openingStock: '期初結餘',
        purchase: '購貨',
        writeOff: '損毀',
        self: '自用',
        DM: '報銷',
        closingStock: '期末結餘',
        cost: '銷貨成本',
        salesProfit: '銷貨利潤',
        DMSWO: '損毀/自用/報銷',
        netProfit: '淨利潤',
        percentageProfit: '利潤百分比',
        title: '銷售利潤報表',
      },
    },
    stockBalance: {
      label: {
        breakdown: '詳細',
        breakdownAndSummary: '詳細+摘要',
        summary: '摘要',

        opening: '期初',
        purchase: '購貨',
        sale: '銷售',
        balance: '結餘',
        adjustment: '調整',
        finalBalance: '最後結餘',

        quantity: '數量',
        unitPrice: '單價',
        amount: '金額',
        sk_code: '編號',
        name: '名稱',
        out_sale_avg: '售價',
        out_sale_amount: '總售價',
        out_amount: '總成本',
        adj_d_qty: '損壞',
        adj_u_qty: '自用',
        adj_w_qty: '報銷',
        adj_amount: '調整成本',

        total: '總計',
      },
    },
    stockIo: {
      label: {
        date: '日期',
        inv_chq_no: '發票/支票號',
        voucher_no: '傳票號碼',
        type: '類別',
        in: '入貨',
        out: '出貨',
        balance: '結餘',
        quantity: '數量',
        unitPrice: '單價',
        amount: '金額',
        total: '總計',
      },
    },
    profitReport: {},
    label: {
      month: '本月',
      year: '全年',
      to: '截止至',

      sales: '銷售',
      damagedOrLost: '損毀/遺失',
      internalUse: '內部使用',
      writeOff: '註銷',
      purchase: '購貨',
    },
  },
  report: {
    trialBalance: {
      pdf: {
        index: '#',
        code: '編號',
        name: '賬目名稱',
        bf_dr: '借方(HK$) ',
        bf_cr: '貸方(HK$) ',
        dr: '借方(HK$) ',
        cr: '貸方(HK$) ',
        end_dr: '借方(HK$) ',
        end_cr: '貸方(HK$) ',
        sum: '總計：',
        bf: '期初結餘',
        mid: '本期',
        end: '期末結餘',
      },
      option1: '#1',
      option2: '#2',
      option3: '#3',
      label: {
        fund: '撥款',
        wholeYear: '全年',
        year: '年份',
        period: '時期',
        mode: '模式',
        skipZero: '跳過零值',
        auditAdjust: '審計修正',
        dateTo: '截至 {date}',
        bf: '期初',
        mid: '期內',
        end: '期末',

        index: '#',
        code: '編號',
        name: '賬目名稱',
        bf_dr: '借方金額',
        bf_cr: '貸方金額',
        dr: '借方金額',
        cr: '貸方金額',
        end_dr: '借方金額',
        end_cr: '貸方金額',
        sum: '總計：',
      },
      content: {
        individualCode: '個別-編號',
        // individualStructure: '個別-結構',
        TreeStructure: '樹狀結構',
        all: '所有',
        none: '無',
      },
    },
    message: {
      yearSelect: '請選擇年份',
      fundSelect: '請選擇撥款',
    },
    ledger: {
      label: {
        title: '總賬賬目列表',
        ac_code: '會計賬號',
        vc_date: '日期',
        vc_no: '傳票編號',
        descr: '內容描述',
        vc_payee: '收付款人',
        st_code: '職員編號',
        st_name_: '職員名稱',
        budget_code: '預算編號',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        net_amount: '淨金額',
        amount_balance: '結餘',
        dept_code: '部門編號',
        dept_name_: '部門名稱',
        ref: '參考號',
        chq_no: '支票號碼',
        ac_name_: '賬目名稱',

        expenditure: '支出',
        income: '收入',

        range: '範圍',
        style: '樣式',
        Show: '顯示',

        order: '排序',
      },
      content: {
        drCrBalance: '借方/(貸方)結餘',
        drBalance: '借方結餘',
        crBalance: '貸方結餘',

        netDr: '借方淨值',
        netCr: '貸方淨值',
        netExpenditure: '淨支出',
        netIncome: '淨收入',
        NetIncomeAndExpenditure: '淨收支',
        grantBalance: '津貼結餘',
        balance: '結餘',

        drSum: '借方累計',
        crSum: '貸方累計',
        cumulativeExpenditure: '累計支出',
        cumulativeIncome: '累計收入',
        accumulatedRevenueAndExpenditure: '累計收支',

        normalEntry: '正常賬目',
        entryAndAdjustment: '賬目+審計修正',
        entryAndAdjustmentAndYearEnd: '賬目+審計修正+年結轉賬',

        byDate: '按日期',
        byVcNo: '按傳票編號',
        byAcCode: '按會計賬號',
      },
    },
    cashBook: {
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        ref: '支票號碼',
        vc_payee: '收付款者',
        descr: '內容描述',
        vc_method: '形式',
        break_down: '明細',
        ac_name_: '賬目名稱',
        amount_dr: '收入',
        amount_cr: '支出',
        amount_balance: '結餘',
        title: '零用現金收支報表',
      },
    },
    cashBookBank: {
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        ref: '支票號碼',
        vc_payee: '收付款者',
        descr: '描述',
        vc_method: '形式',
        break_down: '明細',
        ac_name_: '賬目名稱',
        amount_dr: '收入',
        amount_cr: '支出',
        amount_balance: '結餘',
        title: '銀行賬簿收支報表',
      },
    },
    budget: {
      message: {
        selectBudget: '請選擇預算項目',
      },
    },
    staff: {
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        // ref: '支票號碼',
        mark: '標記',
        vc_payee: '收付款者',
        descr: '內容描述',
        vc_method: '形式',
        break_down: '明細',
        ac_name_: '賬目名稱',
        // amount_dr: '收入',
        // amount_cr: '支出',
        // amount_balance: '結餘',
        tx_num: '行',
        ac_code: '會計賬號',
        ref: '參考號',
        st_code: '職員編號',
        st_name_: '職員名稱',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        amount_net: '凈金額',
        amount_balance: '結餘',
        total: '總計',
        title: '職員賬目報表',
      },
    },
    department: {
      label: {
        vc_date: '日期',
        vc_no: '傳票號碼',
        // ref: '支票號碼',
        mark: '標記',
        vc_payee: '收付款者',
        descr: '內容描述',
        vc_method: '形式',
        break_down: '明細',
        ac_name_: '賬目名稱',
        // amount_dr: '收入',
        // amount_cr: '支出',
        // amount_balance: '結餘',
        tx_num: '行',
        ac_code: '會計賬號',
        ref: '參考號',
        vc_dept: '部門編號',
        dept_name_: '部門名稱',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        amount_net: '凈金額',
        amount_balance: '結餘',
        total: '總計',
        title: '部門賬目報表',
      },
    },
    voucher: {
      label: {
        title: '傳票列表',
        category: '分類',
        voucherType: '傳票類別',
        total: '總計：',
        tx_num: '#',
        vc_no: '傳票編號',
        vc_date: '日期',
        ac_code: '會計賬號',
        ac_name_: '賬目名稱',
        descr: '內容描述',
        vc_payee: '收付款人',
        ref: '參考號',
        st_code: '職員編號',
        st_name_: '職員名稱',
        budget_code: '預算編號',
        amount_dr: '借方金額',
        amount_cr: '貸方金額',
        tx_type: 'IE',
      },
      content: {
        allFund: '所有賬目',
        allCategory: '所有分類',
        paymentVoucher: '付款傳票',
        cashVoucher: '現金傳票',
        receiptVoucher: '收款傳票',
        ap: '應付帳',
        transferVoucher: '銀行轉賬',
        journalVoucher: '一般轉賬',

        allVoucher: '所有傳票',
      },
    },
    excelReport: {
      report: '報表',
      year: '年度',
      month: '月份',
      financialStatement: '財務報表',
      edb: '財務報表(EDB)',
      edbSecondarySchool: '財務報表(中小學EDB)',
      swd: 'SWD報表',
    },
  },
  setting: {
    basic: {
      name: '基本設定',
      font: '字型',
      font_color: '字體顏色',
      background_color: '底色',
      tableHeader: '表頭',
      menu: '菜單',
      content: '內容',

      font_family: '字體',
      font_size: '字體大小',

      content_background_color: '主題色',
      content_line_height: '行高',
      date_format: '日期格式',

      fontFamilyList: {
        sim_sun: '宋體',
        sim_hei: '黑體',
        microsoft_yahei: '微軟雅黑',
        kai_ti: '楷體',
      },

      select_font_placeholder: '請選擇字體',
      main_menu_font_size: '主大小',
      second_menu_font_size: '副大小',
      main_menu_background_color: '菜單顏色1',
      second_menu_background_color: '菜單顏色2',
      second_menu_hover_color: '活躍顏色1',
    },
    settingPageSet: {
      ps_code: '編號',
      ps_name_cn: '中文',
      ps_name_en: '英文',
      width: '寬度',
      height: '高度',
      orientation: '方向',
      transverse: '橫向',
      vertical: '豎向',
    },
    user_info: {
      username: '登入名稱',
      password: '原有密碼',
      new_password: '更新密碼',
      confirm_password: '重入密碼',
    },
    printout: {
      label: {
        unitTips: '單位: mm',
        unitTipsPercentage: '單位: %',

        style: '樣式',
        styleName: '樣式名稱',
        pageStyle: '紙樣式',
        pageSize: '紙大小',
        // pageWidth: '紙闊',
        // pageHeight: '紙長',
        pageOrientation: '方向',
        language: '語言',

        margin: '邊界',
        // marginTop: '上邊界',
        // marginBottom: '下邊界',
        // marginLeft: '左邊界',
        // marginRight: '右邊界',
        tableHeaderHeight: '表頭高度',
        titleWidth: '標題闊度',
        descHeight: '備註高度',

        signStyle: '簽名樣式',
        signNum: '簽名項數',
        signLine: '簽名線格式',
        stage_cn: '階段(中)',
        stage_en: '階段(英)',
        position_cn: '職位(中)',
        position_en: '職位(英)',
        signHeight: '簽名高度',
        signSpace: '簽名間隔',

        fontSize: '字體大小',
        fontSizeSchCN: '中校名',
        fontSizeSchEN: '英校名',
        fontSizeTitle: '標題',
        fontSizeContent: '內容',
        fontSizeSignature: '簽名',
        fontSizePageNum: '頁碼',

        tableFooterHeight: '表腳高度',
        groupBreak: '群組分頁',
        break: '分頁',
        dividingLine: '分隔線',
        fundAbbr: '賬目簡稱',
        adjustment: '調整',
        position: '位置',
        blankLedger: '空賬頁',

        tableHeaderColumns: '表頭欄位',
        tableContentColumns: '內容欄位',

        itemNumPerPage: '每頁貨品數量',
        itemNumPerPageTips: '當貨品數量超過設置的值時會分頁顯示',

        stepCN: '階段(中)',
        stepEN: '階段(英)',
        postCN: '職位(中)',
        postEN: '職位(英)',

        rowNum: '行數',
        colNum: '列數',

        paperPreview: '紙張預覽',

        offset: '基數',
        cheque: '支票',
        fontWeight: '字體粗細',
        amount: '金額',
        text_amount: '金額大寫',
        text_width: '金額寬度',
        text_indent: '金額縮排',
        line_height: '金額行高',
        ac_payee_only: '存入收款人戶口',
        crossed_bearer: '刪或持票人',
        crossed_width_space: '刪線寬度間距',
        date: '日期',
        payee: '收款人',
        payee_width: '收款人寬度',

        stub_date: '票根日期',
        stub_payee: '票根收款人',
        stub_payee_width: '票根收款寬度',
        stub_desc: '票根描述',
        stub_amount: '票根金額',
        stubFontSize: '票根字大小',
      },
      content: {
        english: '英文',
        chinese: '中文',
        auto: '自動',
        numeric: '數字',

        noSignature: '無簽名',
        floating: '浮動',
        fixed: '固定',
        signatureNumber_1: '一項簽名',
        signatureNumber_2: '兩項簽名',
        signatureNumber_3: '三項簽名',
        signatureNumber_4: '四項簽名',
        signatureNumber_5: '五項簽名',
        signatureNumber_6: '六項簽名',

        line: '橫線',
        box: '方格',

        alignment_left: '靠左',
        alignment_center: '置中',
        alignment_right: '靠右',

        noPageBreak: '不分頁',
        pageBreak: '分頁',

        toPrint: '列印',
        notPrint: '不列印',

        landscape: '橫向',
        portrait: '豎向',

        normal: '正常',
        bold: '加粗',
      },
      button: {
        update: '更新',
        defaultValue: '預設值',
      },
      navMenu: {
        daily: '日常',
        daily_payment: '付款傳票',
        daily_receipt: '收款傳票',
        daily_petty_cash: '零用現金傳票',
        daily_bank_transfer: '轉賬傳票',
        daily_journal: '一般傳票',
        daily_voucher_list: '傳票列表',
        daily_voucher_consolidate: '綜合傳票',
        daily_payment_receipt: '付款收據',
        daily_receipt_receipt: '收款收據',
        daily_mailing_label: '郵寄標籤',
        daily_envelope: '信封',
        daily_cheque: '支票',
        periodic: '週期',
        periodic_bank_reconciliation: '銀行對賬表',
        periodic_daily_collection: '每日收款表',
        periodic_autopay_list: '自動轉賬列表',
        periodic_balance_transfer: '年結轉賬傳票',
        report: '報表',
        report_trial_balance: '試算表',
        report_ledger: '總賬賬目列表',
        // report_ledger_complex: '總賬賬目綜合列表',
        report_cash_book: '零用現金賬簿',
        report_cash_book_bank: '銀行現金賬簿',
        report_budget: '預算賬目列表',
        report_staff: '職員賬目列表',
        report_department: '部門賬目列表',
        report_voucher: '傳票列表',
        budget: '預算',
        budget_balance: '預算結餘報表',
        stock: '庫存',
        stock_profit_report: '預算結餘報表',
        test: '測試',
        test_portrait: '竪向測試',
        test_landscape: '橫向測試',
        test_print: '打印調試',
        stock_sales_proffit: '利潤報表',

        bg_budget_list: '預算列表',
        to: '致',
        enclosed: '謹附上銀行支票一張號碼',
        amount: '港幣',
        in_payment_of: '以付',
        with_invoice: '發票號碼',
        please_issue_receipt: '請寄回收據為荷！',
        year: '年',
        month: '月',
        day: '日',
        address: '收據請寄',
        Please_end_receipts_to: 'Please send receipt(s) to ',
        Tel: '電話Tel. : ',
        Fax: '傳真Fax. : ',
      },
      daily: {
        payment: {
          content: {
            style_standard: '標準付款傳票',
            style_standard_2: '標準付款傳票2',
          },
        },
        voucherList: {
          index: '#',
          vc_no: '傳票號碼',
          vc_date: '日期',
          vc_summary: '內容描述',
          vc_amount: '總金額',
          vc_amount_detail: '明細',
          vc_method: '形式',
          ref: '支票號碼',
          vc_payee: '收款公司/收款人',
          vc_payer: '付款公司/付款人',
          ac_name: '賬目名稱',
          ac_code: '會計賬號',
          vc_receipt: '收據編號',
          row: '行',

          category: '類別',
          month: '月份',
        },
        consolidateVoucher: {
          title_p: '綜合付款傳票({year}年度)',
          title_r: '綜合收款傳票({year}年度)',
          title_c: '綜合零用現金傳票({year}年度)',
          title_t: '綜合轉賬傳票({year}年度)',
          title_j: '綜合一般傳票({year}年度)',
          cheque_count: '共{count}張',
          transf_count: '共{count}單',
          other_count: '共{count}單',

          index: '#',
          vc_no: '傳票號碼',
          vc_date: '日期',
          vc_summary: '內容描述',
          ac_name: '會計賬目',
          payment_info: '付款資料',
          cheque_date: '支票日期',
          cheque_amount: '支票金額',
          cheque_no: '支票號碼',
          transfer_amount: '經轉賬金額',
          other_amount: '現金或其他',
          vc_payee: '收付款人',
          st_code: '職員編號',
          st_name: '職員名稱',

          bank: '銀行賬戶',
          account_type: '賬戶類別',
          date: '日期',
          allBank: '所有賬戶',
          allAccount: '所有類別',
          tips1: '* 全部支出均需附有正式單據連同支票及轉帳文件經學統呈校監校董簽署',
          tips2: '* 不同帳戶需分開使用不同的傳票',
        },
        receipt: {
          title_payment: '付款收據',
          title_receipt: '收款收據',
        },
      },
      periodic: {
        label: {
          autoPayList: '自動轉賬列表',
          bankReconciliation: '銀行對賬表',
          dailyCollection: '每日收款報告',
          openingBalance: '期初結餘轉賬傳票',
          closingBalance: '期末結餘轉賬傳票',

          fund: '撥款',
          bank: '銀行',
          period: '時期',
          year: '年份',
          category: '類別',

          all: '所有',
        },
        autopayList: {
          label: {
            // columnsField

            vc_date: '日期',
            vc_no: '傳票號碼',
            ref: '支票號碼',
            ac_code: '會計賬號',
            ac_name_: '賬目名稱',
            ac_name_cn: '賬目名稱(中)',
            ac_name_en: '賬目名稱(英)',
            ac_abbr_cn: '賬目簡稱(中)',
            ac_abbr_en: '賬目簡稱(英)',
            descr: '內容描述',
            lg_amount: '明細',
            vc_amount: '總金額',
            fund_name_: '撥款',
            bank_ac_code: '銀行編號',
            vc_payee: '收付款人',
            vc_receipt: '收據編號',
            vc_receipt_date: '收據日期',
            vc_method: '形式',
          },
          content: {
            style_standard: '標準自動轉賬列表',

            page_style_a4_landscape: 'A4 橫向',
            page_style_a4_pdf_landscape: 'A4 PDF 橫向',
            page_style_a4_portrait: 'A4 豎向',
            page_style_a4_pdf_portrait: 'A4 PDF 豎向',
            page_style_a5_pdf_landscape: 'A5 PDF 橫向',
            page_style_boc_cheque: '中銀支票',
            page_style_dbs_cheque: '星展支票',
            page_style_bea_cheque: '東亞支票',
            page_style_standard_envelope_1: '標準信封1',
            page_style_standard_envelope_2: '標準信封2',
            page_style_hsb_cheque: '恆生支票',
            page_style_hsbc_cheque: '匯豐支票',
            page_style_scb_cheque: '渣打支票',
            page_style_wlb_cheque: '永隆支票',
          },
          button: {
            update: '更新',
            defaultValue: '預設值',
          },
        },
        bankReconciliation: {
          label: {
            vc_date: '日期',
            vc_no: '傳票號碼',
            descr: '內容描述',
            vc_payee: '收付款者',
            ref: '支票號碼',
            amount: '金額',
            total: '總計',
            bf_amount: '截至 {date} 銀行賬戶結餘金額：',
            end_amount: '截至 {date} 銀行月結單結餘金額：',
            unpresentedPayment: '加 未兌現付款：',
            unpresentedReceipt: '減 未兌現收款：',
          },
        },
        dailyCollection: {
          label: {
            vc_date: '日期',
            vc_no: '傳票號碼',
            ref: '支票號碼',
            ac_code: '會計賬號',
            ac_name: '賬目名稱',
            descr: '內容描述',
            lg_amount: '明細',
            vc_amount: '總金額',
            fund_name_: '撥款',
            vc_receipt: '收據編號',
            vc_receipt_date: '收據日期',
            vc_payee: '收款人',
            signature: '簽名',
          },
        },
        balanceTransfer: {
          label: {
            index: '#',
            ac_code: '賬目編號',
            ac_name: '賬目名稱',
            descr: '內容描述',
            amount_dr: '借方金額',
            amount_cr: '貸方金額',
          },
        },
      },
      report: {
        cashBook: {
          label: {
            vc_date: '日期',
            vc_no: '傳票號碼',
            ref: '支票號碼',
            mark: '標記',
            vc_payee: '收付款者',
            descr: '內容描述',
            vc_method: '形式',
            break_down: '明細',
            ac_name_: '賬目名稱',
            amount_dr: '收入',
            amount_cr: '支出',
            amount_balance: '結餘',
          },
        },
        cashBookBank: {
          label: {
            vc_date: '日期',
            vc_no: '傳票號碼',
            ref: '支票號碼',
            mark: '標記',
            vc_payee: '收付款者',
            descr: '內容描述',
            vc_method: '形式',
            break_down: '明細',
            ac_name_: '賬目名稱',
            amount_dr: '收入',
            amount_cr: '支出',
            amount_balance: '結餘',
          },
        },
        budget: {
          label: {
            ac_code: '會計賬號',
            vc_date: '日期',
            vc_no: '傳票號碼',
            tx_num: '#',
            descr: '內容描述',
            // ref: '支票號碼',
            vc_payee: '收付款者',
            st_code: '職員編號',
            st_name_: '職員名稱',
            budget_code: '預算編號',
            amount_dr: '借方金額',
            amount_cr: '貸方金額',
            // amount_net: '凈金額',
            amount_balance: '結餘',
            ref: '參考號',
            vc_dept: '部門編號',
            dept_name_: '部門名稱',
            title: '預算賬目列表',
            budget_name_: '預算名稱',
            IE: '收支',
            I: '收入',
            E: '支出',
            B: '結餘',
            all: '所有',
            total: '總計：',
          },
        },
        staff: {
          label: {
            vc_date: '日期',
            vc_no: '傳票號碼',
            // ref: '支票號碼',
            vc_payee: '收付款者',
            descr: '內容描述',
            tx_num: '#',
            ac_code: '會計賬號',
            ref: '參考號',
            st_code: '職員編號',
            st_name_: '職員名稱',
            amount_dr: '借方金額',
            amount_cr: '貸方金額',
            amount_net: '凈金額',
            amount_balance: '結餘',
            title: '職員賬目列表',
          },
        },
        department: {
          label: {
            vc_date: '日期',
            vc_no: '傳票號碼',
            // ref: '支票號碼',
            vc_payee: '收付款者',
            descr: '內容描述',
            tx_num: '#',
            ac_code: '會計賬號',
            ref: '參考號',
            vc_dept: '部門編號',
            dept_name_: '部門名稱',
            amount_dr: '借方金額',
            amount_cr: '貸方金額',
            amount_net: '凈金額',
            amount_balance: '結餘',
            title: '部門賬目列表',
          },
        },
        trialBalance: {
          label: {
            index: '#',
            code: '編號',
            name: '賬目名稱',
            bf: '期初',
            mid: '期內',
            end: '期末',
          },
        },
        voucher: {
          label: {
            vc_no: '傳票編號',
            vc_date: '日期',
            tx_num: '#',
            ac_code: '會計賬號',
            ac_name_: '賬目名稱',
            descr: '內容描述',
            vc_payee: '收付款人',
            ref: '參考號',
            st_code: '職員編號',
            st_name_: '職員名稱',
            budget_code: '預算編號',
            amount_dr: '借方金額',
            amount_cr: '貸方金額',
            tx_type: 'IE',
          },
        },
        ledger: {
          label: {
            ac_code: '會計賬號',
            vc_date: '日期',
            vc_no: '傳票編號',
            descr: '內容描述',
            vc_payee: '收付款人',
            st_code: '職員編號',
            st_name_: '職員名稱',
            budget_code: '預算編號',
            amount_dr: '借方金額',
            amount_cr: '貸方金額',
            net_amount: '淨金額',
            amount_balance: '結餘',
            dept_code: '部門編號',
            dept_name_: '部門名稱',
            ref: '參考號',
            ac_name_: '賬目名稱',
            chq_no: '支票號碼',
          },
        },
      },
      budget: {
        budgetList: {
          label: {
            budget_code: '編號',
            name: '預算項目',
            manager: '負責人',
            budget_stage: '狀態',
            budget_IE: '分類',
            amount: '金額',
            lastYear: '上年度',
            thisYear: '本年度',

            budget_amount: '預算金額',
            proposed_amount: '建議金額',
            approved_amount: '確認金額',
            actual_amount: '實際金額',
            balance: '結餘',
          },
        },
      },
    },
    dataImport: {
      year: '學年',
    },
    dataBackup: {
      dataBackup: '數據備份',
      dataRestore: '數據還原',
      backupTips: '還原數據前建議先進行備份，以確保數據安全。',
      restoreTips: '選擇數據備份記錄，進行還原',
      backup: '立即備份',
      backupSuccess: '備份成功',
      restoreSuccess: '還原成功',
      selectFile: '請選擇備份文件',
    },
    restoreDemoData: {
      confirmTitle: '還原Demo數據',
      confirmContent: '確認所有Demo數據?',
      restoreSuccess: '還原成功',
      restoreSuccessContent: '點擊確認，重新登入',
    },
  },
  budget: {
    budgetSet: {
      label: {
        first_field: '編號/名稱',
        manager_name_: '組長',
        member_name_: '成員',
        amount: '金額',

        budget_code: '預算編號',
        name_cn: '名稱(中)',
        name_en: '名稱(英)',
        parent: '上級',
        position: '位置(前置)',
        EIType: '收支類別',
        budgetGroup: '預算群組',
        budget_stage: '狀態',
        budget_active: '活躍',
        audit: '審批',
        member: '成員',
        manager: '組長',
        proposed_amount: '建議金額',
        approved_amount: '審批金額',
        last_proposed_amount: '上次修訂',
        last_approved_amount: '上次審批',
        budget_account: '會計賬',

        income: '收入',
        expense: '支出',
        incomeAndExpenditure: '收支',

        parent_default: '所有群組',
        seq_default: '--- 置於最後 ---',

        stage_x: '未遞交',
        stage_s: '已遞交',
        stage_a: '已審批',
        stage_o: '已接納',
        stage_c: '已確認',
        stage_r: '修訂中',
        stage_t: '已修訂',
        stage_k: '已重批',
        stage_b: '已重接納',
        stage_d: '已重確認',

        memberTypeE: '額外',
        memberTypeN: '正常',
        memberTypeL: '限制',

        emptyManager: '暫無組長',
        emptyMember: '暫無成員',
        cn: '中',
        en: '英',
      },
    },
    accountInput: {
      label: {
        type: '類別',
        account: '會計賬',
      },
    },
    budgetList: {
      label: {
        category: '類別',
        budget_stage: '狀態',
        manager: '負責',
        approve: '審批',

        budget_code: '編號',
        name_: '預算項目',
        manager_staff_name_: '負責人',
        budget_IE: '分類',

        budget_amount: '預算金額',

        proposed_amount: '建議金額',
        approved_amount: '審批金額',
        actual_amount: '實際金額',
        balance: '結餘',

        total: '總計：',

        lastYear: '上年度',
        thisYear: '本年度',

        budgetList: '預算列表',
        year: '年份',
      },
    },
    budgetManagement: {
      label: {
        budget: '預算',
        budget_stage: '狀態',
        manager: '負責',
        approve: '審批',

        // budget_code: '編號',
        name_: '預算項目',
        manager_staff_name_: '負責人',
        budget_IE: '分類',

        budget_amount: '預算金額',

        proposed_amount: '建議金額',
        approved_amount: '確認金額',
        actual_amount: '實際金額',
        balance: '結餘',
        balancePercentage: '結餘率',

        revised_amount: '修訂金額',

        total: '總計：',

        lastYear: '上年度',
        thisYear: '本年度',

        vc_date: '日期',
        vc_no: '傳票編號',
        descr: '內容描述',
        vc_payee: '收/付款者',
        ref: '發票號碼',
        amount: '金額',
        budget_code: '預算編號',
        ac_code: '賬目編號',
        fullList: 'Full List',
        canNextApproval: '可審批',
      },
      button: {
        // X 未遞交
        notSubmitted: '未遞交',
        submitBudget: '遞交預算',
        // S 已遞交
        withdrawSubmission: '撤回遞交',
        approveBudget: '審核預算',
        submitted: '已遞交',
        // A 已審批
        withdrawApproval: '撤回審批',
        acceptApproval: '接納審批',
        approved: '已審批',
        // O 已接納
        withdrawAcceptance: '撤回接納',
        accepted: '已接納',
        confirmAcceptance: '確認接納',
        // C 已確認
        withdrawConfirmation: '撤回確認',
        confirmed: '已確認',
        reviseMode: '修訂模式',
        // R 修訂中
        cancelReviseMode: '取消修訂',
        revising: '修訂中',
        summitRevision: '遞交修訂',
        // T 己修訂
        withdrawRevision: '撤回修訂',
        revised: '已修訂',
        reApproveBudget: '重批預算',
        // K 已重批
        withdrawReApproval: '撤回重批',
        reApproved: '已重批',
        acceptReApproval: '接納重批',
        // B 已重接納
        reAccepted: '已重接納',
        // D 己重確認
        reConfirmed: '已重確認',

        copyComposition: '複製結構及金額',
        copyCompositionAndAmount: '複製結構',
        clearComposition: '清除結構',
        clearCompositionAndAmount: '清空金額',
        addItem: '新增事項',
        addDetail: '新增詳細',
        addItemBySelect: '在選中事項下新增事項',
        addDetailBySelect: '在選中事項下新增詳細',
        edit: '編輯',
        delete: '刪除',
        up: '上移',
        down: '下移',
      },
      message: {
        tips: '提示',
        // X 未遞交
        submitBudgetForApproval: '遞交預算以待審批？',
        // S 已遞交
        withdrawSubmittedBudget: '撤回已遞交預算？',
        approveBudget: '通過預算？',
        // A 已審批
        withdrawApprovedBudget: '撤回已通過預算？',
        acceptApproval: '接納審批？',
        // O 已接納
        withdrawAcceptance: '撤回已接納審批預算？',
        confirmAcceptBudget: '確認己接納之預算？',
        // C 已確認
        withdrawConfirmation: '撤回已確認接納之預算？',
        // enterReviseBudgetMode: '進入修訂預算式？',
        // R 修訂中
        cancelReviseMode: '取銷修訂預算？',
        submitRevisionForApproval: '遞交修訂以待審批？',
        // T 己修訂
        withdrawSubmittedRevision: '撤回已遞交修訂？',
        reApproveBudget: '通過修訂？',
        // K 已重批
        withdrawReApprovedBudget: '撤回已通過修訂？',
        reAcceptApproval: '接納重批？',
        // B 已重接納
        withdrawReAcceptance: '撤回已接納重批預算？',
        confirmReAcceptedBudget: '確認己接納之重批預算？',
        // D 己重確認
        withdrawReConfirmation: '撤回已確認接納之重批預算？',
        enterReviseBudgetMode: '進入修訂預算式？',

        noYear: '未錄入會計週期！',
        loadingPageDataFailed: '加載頁面數據失敗！',
        loadingLastYearDataFailed: '加載上年度預算失敗！',
        loadingThisYearDataFailed: '加載本年度預算失敗！',
        hasChildren: '選擇項包含其他內容，請清除後重試',

        inputBudgetCode: '請輸入預算編號',
        inputBudgetName: '請輸入預算項目',
        cleanComposition: '是否確認清除本年度所有有關預算項目？',
        cleanCompositionAndAmount: '是否確認清除本年度所有有關預算金額？',
        addTip: '請先保存預算項目',
      },
    },
  },
  purchase: {
    master: {
      procurementLevel: {
        pro_level_code: '等級編號',
        pro_level_name_: '等級描述',
        pro_level_name_cn: '等級描述(中)',
        pro_level_name_en: '等級描述(英)',
        pro_no_format: '編號規則',
        amount_range: '金額範圍',
        min_amount: '金額範圍(起始)',
        max_amount: '金額範圍(結束)',
        suppliers_at_least: '供應商個數(最少)',
        invitation_letter_code: '邀請信',
        invitation_letter_type: '邀請信類型',
        reject_letter_code: '不接納通知信',
        accept_letter_code: '接納通知信',
        reason_for_suppliers: '必須填寫邀請少於限定的供應商個數原因',
        reason_for_low_price: '必須填寫不選擇最低報價原因',
        review: '覆核',
        reviewSetting: '覆核設定',
        approve: '審批',
        approveSetting: '審批設定',
        free: '申請人自選',
        auto: 'A',
        manual: 'M',
        acceptNotice: '接納通知信',
        rejectNotice: '不接納通知信',
        rules: {
          required: '必填',
          proNoRequired: '編號必須包含 #',
        },
      },
      setting: {
        principal_cn: '校長姓名(中)',
        principal_en: '校長姓名(英)',
        sch_tel: '學校電話',
        sch_fax: '學校傳真',
        sch_addr_cn: '學校地址(中)',
        sch_addr_en: '學校地址(英)',
        no_month_skip: '採購編號規則(每月重置數字序號)',
        no_budget_skip: '採購編號規則(各個預算獨立計數)',
      },
    },
    daily: {
      procurementApplications: {
        companyName: '公司名稱',
        companyShortName: '公司簡稱',
        companyTel: '電話',
        companyFax: '傳真',
        companyContact: '聯繫人',
        level: '分類',
        filterRemark: '描述',
        startDate: '開始時間',
        endDate: '結束時間',
        budgetGroup: '預算組別',
        applyStaff: '申請人',

        g_budget_name_: '科組',
        pro_no: '編號',
        pro_title: '描述 ',
        apply_date: '申請日期 ',
        invite_date: '發出日期 ',
        applicant_name_: '索價人',
        r_staff_name_: '覆核人',
        a_staff_name_: '審批人',
        // amount: '金額',
        status: '狀態',

        year: '學年',
        applyDate: '申請日期',
        title: '標題',
        proNo: '編號',
        budget: '預算項目',
        reviewHandler: '覆核人',
        approvalHandler: '審批人',
        type: '類別',
        inviteDate: '邀請發出時間',
        contact: '聯絡人',
        contact_name: '姓名',
        contact_title: '稱呼',
        contactTel: '聯絡電話',
        replyEndDate: '供應商回覆截止日期',
        quotedValidDays: '邀請報價有效天數',
        remark: '備註',
        // column
        itemName: '物品或服務說明',
        itemQty: '採購數量',
        itemRemark: '用途',
        action: '操作',

        supplierName: '供應商名稱',
        supplierType: '性質',
        supplierTel: '電話號碼',
        supplierFax: '傳真號碼',
        supplierContact: '聯絡人',
        letters: '邀請信',
        notEnoughSuppliersReason: '供應商數量不足原因',
        notSelectLowestReason: '不選擇最低報價原因',
        com: '公司',
        org: '機構',

        auto: '自動',
        stName: '姓名',
        updatedAt: '覆核時間',
        reviewStatus: '覆核結果',
        reviewComment: '覆核意見',

        uploadQuotes: '上傳報價單',
        filename: '文件名',

        qty: '數量',
        price: '單價',
        amount: '金額',
        total: '總報價',
        select: '接納',
        filePreview: '文件預覽',

        // message
        saveSuccess: '儲存成功！',
        levelsEmpty: '未設定採購等級',
        levelsEmptyInput: '請先新增採購等級',
        itemsEmpty: '《物品/服務》尚未填寫!',
        suppliersEmpty: '最少填寫一條供應商信息!',
        quotesSelectEmpty: '請選擇供應商!',
        quotesEmpty: '請輸入供應商報價!',
        // itemsEmpty: '物品/服務不能為空！',
        passConfirm: '確認通過本次申請？',
        backComment: '退回意見',
        rejectComment: '拒絕意見',
        commentRequired: '必填',
        reviewerHandlersInput: '覆核人未填寫！',
        approveHandlersInput: '審批人未填寫',
        fileUploadFailing: '{num} 個文件上傳失敗',

        goto: '前往',
        back: '返回',

        stepList: {
          basis: '基本資料',
          items: '物品/服務',
          suppliers: '供應商',
          review: '覆核',
          quotesInput: '報價輸入',
          quotesSelect: '選擇報價',
          approval: '審批',
          print: '文件列印',
        },
        button: {
          pass: '通過',
          reviewBack: '退回',
          reviewBackToQuotes: '退回到報價',
          reviewBackToStart: '退回到基本資料',
          reject: '拒絕',
        },
        uploadStatus: {
          success: '已上傳',
          ready: '待上傳',
          error: '上傳失敗',
        },
      },
      procurementApproval: {
        unhandled: '待處理',
        handled: '已處理',

        year: '年度',
        level: '分類',
        remark: '描述',

        step: '階段',
        g_budget_name_: '科組',
        pro_no: '編號',
        pro_title: '描述 ',
        apply_date: '申請日期 ',
        invite_date: '發出日期 ',
        applicant_name_: '索價人',
        handler_name_: '處理人',
        handle_time: '處理日期',
        apply_status: '申請狀態',
        handler_status: '狀態',
        comment: '意見',
      },
    },
    status: {
      A: '未遞交',
      G: '覆核中',
      GR: '覆核拒絕',
      GA: '覆核退回',
      I: '報價中',
      K: '選擇報價',
      M: '審批中',
      MR: '審批拒絕',
      MI: '退回到報價',
      MA: '退回到基本資料',
      O: '已通過',
    },
    approvalStatus: {
      W: '未審核',
      A: '通過',
      B: '退回',
      C: '退回到開始',
      R: '拒絕',
    },
    supplierStatus: {
      W: '待回覆',
      Q: '已報價',
      R: '不擬報價',
      N: '沒有回應',
    },
    reviewStep: {
      R: '覆核',
      A: '審批',
    },
    category: {
      P: '物品',
      S: '服務',
    },
  },
  permission: {
    Y: '使用',
    A: '新增',
    D: '刪除',
    E: '修改',
    F: '更新',
    O: '匯出',
    I: '匯入',
    V: '傳票',
    L: '列表',
    C: '綜合',
    R: '收據',
    M: '標籤',
    N: '信封',
    P: '列印',
    T: '上載',
    W: '下載',
    U: '還原',
    Z: '重排',
    B: '複製',
  },
  voucher_type_category: {
    ALL: '所有類別',
    P: '付款傳票',
    R: '收款傳票',
    C: '現金傳票',
    T: '轉賬傳票',
    J: '一般轉賬傳票',
    A: 'A/P傳票',
  },
  voucher_method: {
    transf: '轉賬',
    cash: '現金',
    cheque: '支票',
    auto: '自動',
    other: '其他',
    value: {
      transf: 'TRANSF',
      cash: 'CASH',
      cheque: 'CHEQUE',
      auto: 'AUTO',
      other: 'OTHER',
    },
  },
  view: {
    add: '新增',
    edit: '修改',
    copy: '複製',
    addStaff: '新增記錄',
    editStaff: '修改記錄',
    addStaffType: '新增同事類別',
    editStaffType: '修改同事類別',
    addAccountType: '新增科目類別',
    addDepartmentType: '新增部門類別',
    editDepartmentType: '修改部門類別',
    editAccountType: '修改科目類別',
    addCompanyGroup: '新增公司組別',
    editCompanyGroup: '修改公司組別',
    addStockGroup: '新增貨品組別',
    editStockGroup: '修改貨品組別',
    addBudgetGroup: '新增預算群組',
    editBudgetGroup: '編輯預算群組',
    addBudgetItem: '新增預算事項',
    editBudgetItem: '編輯預算事項',
    addBudgetDetail: '新增預算事項詳細',
    editBudgetDetail: '編輯預算事項詳細',
  },
  table: {
    action: '操作',
  },
  style: {
    left: '靠左',
    center: '居中',
    right: '靠右',
    position: '位置',
    leftWidth: '左欄寬度',
    defaultTitle: '頁面設置',
    lineHeight: '行高',
    theme: '主題色',
  },
  file: {
    import: '匯入',
    export: '匯出',
    excelImport: 'Excel匯入',
    excelExport: 'Excel匯出',
    browse: '瀏覽',
    downTemplate: '下載模板',
    pleaseSelectExcelFile: "請選擇：'Excel5 ',' Excel 2007 '類型的文件",
    pleaseSelectPdfFile: "請選擇：'PDF'類型的文件",
    exportSuccess: '匯出成功',
    exportError: '匯出失敗',
  },
  message: {
    dropExcel: '拖放Excel文件到此處',
    dropPdf: '拖放PDF文件到此處',
    success: '操作成功',
    leaveSave: '放棄當前未保存的內容？',
    tips: '提示',
    showColumn: '至少顯示一列',
    addSuccess: '新增成功',
    editSuccess: '編輯成功',
    modifySuccess: '修改成功',
    deleteSuccess: '刪除成功',
    cancelDelete: '已取消刪除',
    confirmPasswordError: '新密碼輸入不符',
    dateError: '此日期不在會計範圍內,請重新選擇',
    pleaseContactTheAdministrator: '系統錯誤，請聯繫管理員',
    pleaseReload: '系統錯誤，請刷新頁面',
    pleaseSelectFund: '請選擇賬目類別',
    theGoodsDoNotExist: '貨品不存在',
    theYearDoNotExist: '會計週期不存在',
    theMonthDoNotExist: '月份不存在',
    editPaperRepeat: '修改編號已存在',
    printSettingEmpty: '未配置打印設置',
    receiptReceived: '茲　收　到',
    chequeNumber: '支票號碼',
    totalAmount: '總　　額',
    payment: '繳　　　付',
    receiptDate: '收款日期',
    payee: '收款人',
    receipt: '收據',
    numberNo: '編號 No.',
    receiptLeft: '收',
    receiptRight: '據',
    workInProgress: '開發中',
  },
  // 組件
  component: {
    sticky: '置頂',
  },
  // 表格
  table: {
    index: '序號',
    account: '帳戶',
    action: '操作',
    staff: '職員',
    type: '類型',
  },
  // 主題
  theme: {
    optionA: '選項 A',
    optionB: '選項 B',
    optionC: '選項 C',
    noReverseVoucherType: '沒有找到對應的傳票類別',
    pdfLoading: '列印組件正在載入中，請稍後',
    pdfFailedToLoad: '列印組件載入失敗，請刷新後重試！',
    pdfError: '列印組件加載失敗，請刷新重試！',
    bankInvalid: '銀行 無效',
    chequeSettingWidthInvalid: '支票寬度不能大於{width}mm, 請先修正',
    chequeSettingHeightInvalid: '支票高度不能大於{height}mm, 請先修正',

    importExcelError: '匯入失敗！',
    noSelectedFile: '未選擇文件！',

    uninitializedYear: '未初始化會計周期！',

    responseError: '發生了錯誤，請聯絡系統管理員。',

    loginAgainMsg: '登錄信息已過期，請重新登錄！',
    loginAgain: '重新登錄',
    loginAgainBtn: '確定重新登錄',
    supportUploadingOneFile: '僅支持上載一個文件',
    supportUploadingExcel: '僅支持上載 .xlsx, .xls, .csv suffix 格式文件',

    networkError: '網絡連接失敗，刷新頁面重試',
    noVoucherType: '沒有找到傳票類別',
    noAccount: '沒有找到會計科目',
    noVoucherNo: '沒有找到傳票編號',
    pleaseSelected: '請選擇記錄',
    confirmGenerateVoucher: '確認生成傳票',
    voucherDateError: '傳票日期不能小於截止日期',

    pdfPrintError: '列印失敗',
    pdfWinBlock: '瀏覽器阻止了彈出窗口，請允許彈出窗口',
    confirmDelete: '確認刪除？',
    clearSuccess: '清除成功',
    configurationError: '配置錯誤',
    hasPermission: '擁有',
    noPermission: '無',
    receipt: '收據',
    receiptChar1: '收',
    receiptChar2: '據',
    totalWithColon: '總計：',
    pageNotAccessible: '網管說這個頁面你不能進......',
    historicalYearFieldError: '歷史學年字段錯誤',
    noCurrentYearRecord: '當前無學年記錄',
  },
  placeholder: {
    desc: '描述',
    staff: '職員',
    dept: '部門',
    payee: '收款人',
    ref: '參考編號',
    budget: '預算',
    quote: '報價',
    ac_code: '賬目編號',
    ac_name: '會計科目',
    contra: '對沖編號',
    select: '請選擇',
    selectDate: '選擇日期',
    beginDate: '開始日期',
    endDate: '結束日期',
    selectDragSort: '請選擇顯示列，並拖拽排序',
  },
  filters: {
    fund: '賬目',
    type: '類別',
    all: '所有',
    bank: '銀行',
    chequeCode: '支票號碼',
    period: '時期',
    isMatching: '僅顯示未匹配傳票',
    to: '-',
    cheque_book: '支票簿',
    years: '年份',
    months: '月份',
    all_months: '所有月份',
    budget: '預算',
    staff: '職員',
    dept: '部門',
    group: '分類',
    range: '範圍',
    date: '日期',
    style: '樣式',
    wholeYear: '全年',
    fundRange: '賬目範圍',
  },
  print: {
    footer: '第 {currentPage} / {pageCount} 頁',
    total: '總計：',
    pdfLabel: '列印',
    title: {},
    pages: {
      left: '左',
      center: '中',
      right: '右',
      top: '上',
      bottom: '下',
      noData: '沒有數據',
      companyName: '公司名稱',
      reportTitle: '報表標題',
      dateRange: '日期範圍',
      printDate: '列印日期',
      pageNumber: '頁數',
    },
    // 签名相关
    prepared: '製賬',
    approved: '審批',
    checked: '覆核',
    received: '收款',
    staff: '職員',
    principal: '校長',
    supervisor: '校監',
    // 报表名称
    autoPayList: '自動轉賬',
    balanceTransfer: '結餘轉移',
    bankReconciliation: '銀行對賬',
    budget: '預算',
    cashBook: '現金簿',
    cashBookBank: '銀行現金簿',
    cashVoucher: '現金憑證',
    consolidateVoucher: '合併憑證',
    dailyCollection: '每日收款',
    department: '部門',
    envelope: '信封',
    journalVoucher: '日記賬憑證',
    mailingLabel: '郵寄標籤',
    paymentReceipt: '付款收據',
    paymentVoucher: '付款憑證',
    receiptReceipt: '收款收據',
    receiptVoucher: '收款憑證',
    reportLedger: '報表分類賬',
    reportVoucher: '報表憑證',
    salesProfit: '銷售利潤',
    staff_report: '職員報表',
    transferVoucher: '轉賬憑證',
    trialBalance: '試算表',
    voucherList: '憑證列表',
    bgBudgetList: '背景預算列表',
    cheque: '支票',
  },
  components: {
    accountTreeSelect: {
      label: {
        allAccount: '所有會計賬目',
      },
    },
    imageCropper: {
      hint: '點擊，或拖動圖片至此處',
      loading: '正在上傳……',
      noSupported: '瀏覽器不支持該功能，請使用IE10以上或其他現代瀏覽器！',
      success: '上傳成功',
      fail: '圖片上傳失敗',
      preview: '頭像預覽',
      btn: {
        off: '取消',
        close: '關閉',
        back: '上一步',
        save: '保存',
      },
      error: {
        onlyImg: '僅限圖片格式',
        outOfSize: '單文件大小不能超過 ',
        lowestPx: '圖片最低像素為（寬*高）：',
      },
      rotate: {
        left: '↺',
        right: '↻',
      },
    },
    dropzone: {
      uploadImage: '上傳圖片',
      maxFilesExceeded: '只能一個圖',
    },
    themePicker: {
      changeThemeSuccess: '換膚成功',
    },
    tinymce: {
      uploadImage: '上傳圖片',
      clickUpload: '點擊上傳',
      cancel: '取 消',
      confirm: '確 定',
      uploadError: '請等待所有圖片上傳成功 或 出現了網絡問題，請刷新頁面重新上傳！',
    },
  },
  btnTitle: {
    add: '新增',
    view: '查看',
    edit: '編輯',
    delete: '刪除',
    search: '搜索',
    pageSetting: '頁面設置',
    exportExcel: '匯出',
    importExcel: '匯入',
    exportExcelPage: '匯出',
    exportExcelAll: '匯出(全部)',
    multipleVoucher: '傳票匯入',
    print: '列印',
    paymentSlip: '付款單',
    voucher: '傳票',
    voucherList: '傳票列表',
    consolidateVoucherList: '綜合傳票',
    receipt: '收據',
    mailLabel: '地址',
    envelope: '信封',

    enquiry: {
      grant: '津貼查詢',
      budget: '預算查詢',
      staff: '職員查詢',
      dept: '部門查詢',
      contra: '對沖查詢',
      general: '綜合查詢',
    },

    addStockGroup: '新增貨品組別',
  },
  printList: {
    error: '失敗',
    success: '成功',
    ing: '進行中',
    name: '名稱',
    status: '狀態',
    startTime: '開始時間',
    finishTime: '完成時間',
    action: '操作',
    check: '查看',
    back: '返 回',
    title: '列印',
  },
  bankAiReconciliation: {
    dataHedging: '對沖日期：使用推薦選項',
    dataHedgingShowBank: '對沖日期不顯示已被選擇的銀行交易',
    bankMonth: '銀行月結單',
    fileScan: '{number} 個文件AI中...',
    vc_date: '日期',
    vc_no: '傳票號碼',
    descr: '內容描述',
    bank: '銀行',
    summary: '摘要',
    vc_payee: '收付款者',
    uploadMonthly: '上載銀行月結單',
    ref: '支票號碼',
    amount_dr: '收入金額',
    amount_cr: '支出金額',
    Unpaid: 'Unpaid',
    vc_rdate: '對沖日期',
    uploadOptions: '拖放PDF文件到此處',
    bower: '瀏覽',
    uploadText1: '1. 同銀行並且同月份的文件頁碼需要連續',
    uploadText2: '2. 單份 PDF 不能多於 50 頁，及不能大於 50 MB',
    RUNNING: 'AI 中',
    SUCCESS: 'AI 成功',
    FAILURE: 'AI 失敗',
    user: '賬號',
    fileName: '文件名',
    index: '#',
    options: '操作',
    uploadDate: '上載日期',
    result: 'AI 結果',
    letsGo: '立即前往',
    noData: '暫無數據',
    letUploadFile: '請先上載文件',
    success: '操作成功',
    fail: '操作失敗',
    currentPage: '當前頁數：第 {current} 頁 (共 {total} 頁)',
    addLedger: '新增交易',
    addPre: '添加到上方',
    enable: '已被選擇',
    disable: '未被選擇',
    addNext: '添加到下方',
    account: '賬戶',
    isSelect: '已被選擇：',
    SAVINGS: 'S/A',
    CURRENT: 'C/A',
    FIXED_DEPOSIT: 'F/D',
    OVERDRAFTS: 'O',
    selectUnpaidDate: '選擇Unpaid日期',
    selectUnpaidDateRequired: '請選擇Unpaid日期',
    vc_method: '形式',
    CASH: '現金',
    TRANSF: '轉賬',
    CHEQUE: '支票',
    AUTO: '自動',
    OTHER: '其它',
    autoSelectHedgingConfirm: '確認使用推薦選項覆蓋當前對沖日期',
    ABANDONED: '已廢棄',
    filterAbnormalLedger: '僅顯示已廢棄文件',
    clearAll: '一鍵清除',
    undo: '撤回推薦選項',
    isClearAll: '是否確認一鍵清除當前所有傳票的對沖日期',
    isUndo: '上次使用推薦選項已成功配對{num}條記錄，是否確認撤回上一步的使用推薦選項操作',
    pairing: '已成功配對{num}條記錄',
  },
  paperPreview: {
    view: '紙張預覽',
  },
  checkConfiguration: '查看配置',
  copySuccess: '複製成功',
  copyFailed: '複製失敗，請手動複製',

  // 打印页面相关翻译
  print: {
    pdfLabel: '列印',
    left: '左',
    center: '中',
    right: '右',
    top: '上',
    bottom: '下',
    noData: '沒有數據',
    companyName: '公司名稱',
    reportTitle: '報表標題',
    dateRange: '日期範圍',
    printDate: '列印日期',
    pageNumber: '頁數',
    // 签名相关
    prepared: '製賬',
    approved: '審批',
    checked: '覆核',
    received: '收款',
    staff: '職員',
    principal: '校長',
    supervisor: '校監',
    // 报表名称
    autoPayList: '自動轉賬',
    balanceTransfer: '結餘轉移',
    bankReconciliation: '銀行對賬',
    budget: '預算',
    cashBook: '現金簿',
    cashBookBank: '銀行現金簿',
    cashVoucher: '現金憑證',
    consolidateVoucher: '合併憑證',
    dailyCollection: '每日收款',
    department: '部門',
    envelope: '信封',
    journalVoucher: '日記賬憑證',
    mailingLabel: '郵寄標籤',
    paymentReceipt: '付款收據',
    paymentVoucher: '付款憑證',
    receiptReceipt: '收款收據',
    receiptVoucher: '收款憑證',
    reportLedger: '報表分類賬',
    reportVoucher: '報表憑證',
    salesProfit: '銷售利潤',
    staff_report: '職員報表',
    transferVoucher: '轉賬憑證',
    trialBalance: '試算表',
    voucherList: '憑證列表',
    bgBudgetList: '背景預算列表',
    cheque: '支票',
  },
  chequePreview: {
    amountCn: '壹佰貳拾叁萬肆仟伍佰陸拾柒元捌角玖分',
  },
  error: {
    invalidCode: '編號無效！',
    invalidStaff: '職員無效！',
    invalidDept: '部門無效！',
    invalidContra: '對衝編號無效！',
    duplicateEntry: '重複輸入！',
    historyYearFieldError: '歷史學年字段錯誤',
    noCurrentYearRecord: '當前無學年記錄',
  },
  amount: {
    ch: '零壹貳叁肆伍陸柒捌玖',
    ch_u: '個拾佰仟萬億兆京',
    ch_f: '負',
    ch_d: '點',
    m_u: '元角分厘',
    m_t: '',
    m_z: '正',
  },
  customReport: {
    addStatement: '添加報表',
    exportAll: '匯出所有的報表',
    reportName: '報表名稱',
    title: '標題',
    openingBalance: '期初結餘',
    actualIncome: '實際收入',
    actualExpense: '實際支出',
    closingBalance: '期末結餘',
    actions: '操作',
    accountSituation: '賬目情況',
    bankSituation: '銀行情況',
    signature: '簽名欄',
    addContentArea: '添加內容區域',
    areaType: '區域類型',
    reportName: '報表名稱',
    total: '總計: ',
    date: '日期',
    placeholder: {
      input: '請輸入',
    },
    rules: {
      reportName: '必填',
    },
    tableKey: {
      TITLE: '標題',
      OB: '期初結餘',
      BUDGET_IE: '預算收入&支出',
      ACT_IE: '實際收入&支出',
      BAL: '當期結餘',
      TOTAL_BAL: '期末結餘',
      ACT_IE_PERCENT: '實際支出佔實際收入%',
    },
    sectionTitle: '區域 #{number}：{title}',
    areaTitle: '區域標題',
    columnContent: '列內容',
    rowContent: '行內容',
    add1Level: '新增1級',
    TEXT: '文本',
    SUB_TOTAL: '下級合計',
    ACCOUNT_TOTAL: '科目合計',
    SUB_ACCOUNT: '下級會計科目',
    dataType: '數據類型',
    relatedAccountCategory: '相關科目類別/會計科目',
    showTotal: '顯示總計',
    selectAccount: '選擇會計科目',
    selectContentArea: '選擇科目類别',
    selected: '已選擇:',
  },
}
