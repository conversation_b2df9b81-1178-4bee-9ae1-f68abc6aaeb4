<template>
  <div v-if="show" class="page-procurement-applications-add">
    <div class="page-header">
      <div v-if="isNew" class="levels">
        <el-button
          v-for="item in levels"
          :key="item.pro_level_id"
          :type="form.pro_level_id === item.pro_level_id ? 'primary' : ''"
          :class="{
            'active-level': form.pro_level_id === item.pro_level_id,
          }"
          size="mini"
          @click="onChangeLevel(item.pro_level_id)"
        >
          {{ item[isEnglish ? 'pro_level_name_en' : 'pro_level_name_cn'] }}
        </el-button>
      </div>
      <div v-else class="levels">
        <el-button v-if="form && form.pro_level" type="primary" class="active-level" size="mini">
          {{ form.pro_level[isEnglish ? 'pro_level_name_en' : 'pro_level_name_cn'] }}
        </el-button>
        <div v-if="view === 'review'" class="review-actions">
          <el-button type="success" size="mini" @click="onPass">
            {{ t('button.pass') }}
          </el-button>
          <el-button
            v-if="form.status === 'G'"
            type="warning"
            size="mini"
            @click="onReviewBack('C')"
          >
            {{ t('button.reviewBack') }}
          </el-button>
          <el-button
            v-else-if="form.status === 'M'"
            type="warning"
            size="mini"
            @click="onReviewBack('B')"
          >
            {{ t('button.reviewBackToQuotes') }}
          </el-button>
          <el-button
            v-if="form.status === 'M'"
            type="warning"
            size="mini"
            @click="onReviewBack('C')"
          >
            {{ t('button.reviewBackToStart') }}
          </el-button>
          <el-button type="danger" size="mini" @click="onReject">
            {{ t('button.reject') }}
          </el-button>
        </div>
      </div>
      <div class="steps">
        <el-steps :space="100" :active="currentStep + 1" finish-status="success">
          <el-step
            v-for="item in stepList"
            :key="item.value"
            :class="{ 'can-active': item.canActive }"
            :title="item.label"
            :status="item.status"
            class="step-item"
            @click.native="onClickStep(item.value)"
          />
          <!--          <el-step :class="{ 'can-active': canActive(0) }" title="基本資料" class="step-item" @click.native="onClickStep(0)"/>-->
          <!--          <el-step title="物品/服務" class="step-item" @click.native="onClickStep(1)"/>-->
          <!--          <el-step title="供應商" class="step-item" @click.native="onClickStep(2)"/>-->
          <!--          <el-step title="覆核" class="step-item" @click.native="onClickStep(3)"/>-->
          <!--          <el-step title="報價輸入" class="step-item" @click.native="onClickStep(4)"/>-->
          <!--          <el-step title="選擇報價" class="step-item" @click.native="onClickStep(5)"/>-->
          <!--          <el-step title="審批" class="step-item" @click.native="onClickStep(6)"/>-->
          <!--          <el-step title="文件列印" class="step-item" @click.native="onClickStep(7)"/>-->
        </el-steps>
      </div>
    </div>
    <div class="page-content">
      <!--      <transition name="slide-fade">-->
      <basis-step
        v-if="currentStep === 0"
        ref="basis"
        :data.sync="form"
        :staffs="staffs"
        :years="years"
        :approve="approve"
        :review="review"
        :is-readonly="isReadonly || stage !== 0 || (!hasLevels && isNew)"
        @reshow="reshow"
        @back="onBack"
        @next="onNext"
      />
      <items-step
        v-if="isLoaded && currentStep === 1"
        :id="form.pro_application_id"
        ref="items"
        :data.sync="form.items"
        :is-readonly="isReadonly || stage !== 0"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <suppliers-step
        v-if="isLoaded && currentStep === 2"
        :id="form.pro_application_id"
        ref="suppliers"
        :data.sync="form.suppliers"
        :not_enough_suppliers_reason.sync="form.not_enough_suppliers_reason"
        :suppliers_at_least="form.pro_level.suppliers_at_least"
        :reason_for_suppliers="form.pro_level.reason_for_suppliers"
        :is-readonly="isReadonly || stage !== 0"
        :stage="stage"
        :status="form.status"
        :fy-code="fy_code"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <review-step
        v-if="isLoaded && currentStep === 3"
        :id="form.pro_application_id"
        ref="review"
        :data.sync="form.review_handlers"
        :logs.sync="form.review_logs"
        :is-readonly="isReadonly || stage !== 1"
        :can-next="form.status !== 'G' && stage !== 1 && !(form.status === 'GA')"
        :show-items="approve"
        type="review"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <quotes-input-step
        v-if="isLoaded && currentStep === 4"
        :id="form.pro_application_id"
        ref="quotesInput"
        :data.sync="form.suppliers"
        :items="form.items"
        :is-readonly="isReadonly || stage !== 2"
        :not_enough_suppliers_reason.sync="form.not_enough_suppliers_reason"
        :suppliers_at_least="form.pro_level.suppliers_at_least"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <quotes-select-step
        v-if="isLoaded && currentStep === 5"
        :id="form.pro_application_id"
        ref="quotesSelect"
        :data.sync="form.suppliers"
        :items="form.items"
        :is-readonly="isReadonly || stage !== 2"
        :not_select_lowest_reason.sync="form.not_select_lowest_reason"
        :suppliers_at_least="form.pro_level.suppliers_at_least"
        :reason_for_low_price="form.pro_level.reason_for_low_price"
        :reason_for_suppliers="form.pro_level.reason_for_suppliers"
        :stage="stage"
        :status="form.status"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <review-step
        v-if="isLoaded && currentStep === 6"
        :id="form.pro_application_id"
        ref="approval"
        :data.sync="form.approve_handlers"
        :logs.sync="form.approve_logs"
        :is-readonly="isReadonly || stage !== 1"
        :suppliers_at_least="form.pro_level.suppliers_at_least"
        :can-next="view !== 'review' && stage !== 3"
        :show-items="approve"
        type="approval"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <print-step
        v-if="isLoaded && currentStep === 7"
        :id="form.pro_application_id"
        ref="print"
        :data.sync="form.suppliers"
        :items="form.items"
        :is-readonly="isReadonly || stage !== 4"
        :not_enough_suppliers_reason.sync="form.not_enough_suppliers_reason"
        :suppliers_at_least="form.pro_level.suppliers_at_least"
        @reshow="reshow"
        @back="onBack"
        @prev="onPrev"
        @next="onNext"
      />
      <!--      </transition>-->
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import { fetchYears } from '@/api/master/years'
import customStyle from '@/views/customStyle/index.vue'
import { fetchStaffs } from '@/api/assistance/staff'
import { fetchProcurementLevels, getProcurementLevel } from '@/api/purchase/procurementLevels'
import ETable from '@/components/ETable/index'
// import { fetchProcurementApplications } from '@/api/purchase/procurementApplications'
// import { fetchBudgetGroupList } from '@/api/budget'
import BudgetSelectTreeVue from '@/views/budget/common/BudgetSelectTreeVue'
import BasisStep from '@/views/purchase/procurementApplications/steps/basis'
import ItemsStep from '@/views/purchase/procurementApplications/steps/items'
import { deepCloneByJSON } from '@/utils'
import { fetchYears } from '@/api/master/years'
import {
  editProcurementApplicationsStatus,
  getProcurementApplication,
  reviewApproveProcurementApplications,
} from '@/api/purchase/procurementApplications'
import SuppliersStep from '@/views/purchase/procurementApplications/steps/suppliers'
import ReviewStep from '@/views/purchase/procurementApplications/steps/review'
import QuotesInputStep from '@/views/purchase/procurementApplications/steps/quotesInput'
import QuotesSelectStep from '@/views/purchase/procurementApplications/steps/quotesSelect'
import PrintStep from '@/views/purchase/procurementApplications/steps/print'

export default {
  name: 'ProcurementApplicationsAdd',
  components: {
    PrintStep,
    QuotesSelectStep,
    QuotesInputStep,
    ReviewStep,
    SuppliersStep,
    ItemsStep,
    BasisStep,
    BudgetSelectTreeVue,
    ETable,
    customStyle,
  },
  mixins: [],
  props: {
    id: {
      type: [String, Number],
      default: '',
    },
    handleId: {
      type: [String, Number],
      default: '',
    },
    view: {
      type: [String],
      default: 'view',
    },
    fyCode: {
      type: [String],
      default: '',
    },
  },
  data() {
    return {
      show: true,
      loading: true,
      langKey: 'purchase.daily.procurementApplications.',

      levels: [],

      years: [],
      staffs: [],
      budgetGroupList: [],
      currentLevel: '',
      form: {
        fy_code: '', // 會計週期編號
        pro_level_id: '', // 採購等級id
        pro_no: '', // 採購編號
        pro_title: '', // 採購申請標題
        pro_remark: '', // 採購申請備註
        apply_date: this.dateFormatToStr(new Date()), // 申請日期
        invite_date: this.dateFormatToStr(new Date()), // 邀請供應商日期
        type: 'P', // 採購類新: P=貨品: '',S=服務
        contact: '', // 聯繫人
        contact_title: '', // 聯繫人稱呼
        contact_tel: '', // 聯繫人電話
        budget_id: '', // 預算編號
        budget_code: '', // 預算編號
        reply_end_date: '', // 供應商回復截止日期
        quoted_valid_days: '', // 供應商報價有效日期(供應商回復截止日期開始計算)
        apply_staff_id: '', // 申請人staff_id
        apply_review_handlers: [[]], // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
        apply_review_handlers_json: '', // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
        apply_approve_handlers: [[]], // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
        apply_approve_handlers_json: '', // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
        pro_level: {},
      },
      levelInfo: {},
      currentStep: -1,
      currentStep2: 0,
      fy_code: '',
    }
  },
  computed: {
    ...mapGetters([]),
    canNextSix() {
      if (this.isReadonly) return true
      console.log(this.form, 'this.form.suppliers')
      if (this.form.suppliers) {
        const list = this.form.suppliers.filter(item => item.supplier_status === 'Q')
        return list.length > 0
      }
      return false
    },
    statusList() {
      return [
        { value: 'A', label: this.$t('purchase.status.A') },
        { value: 'G', label: this.$t('purchase.status.G') },
        { value: 'GR', label: this.$t('purchase.status.GR') },
        { value: 'GA', label: this.$t('purchase.status.GA') },
        { value: 'I', label: this.$t('purchase.status.I') },
        { value: 'K', label: this.$t('purchase.status.K') },
        { value: 'M', label: this.$t('purchase.status.M') },
        { value: 'MR', label: this.$t('purchase.status.MR') },
        { value: 'MI', label: this.$t('purchase.status.MI') },
        { value: 'MA', label: this.$t('purchase.status.MA') },
        { value: 'O', label: this.$t('purchase.status.O') },
      ]
    },
    categoryList() {
      return [
        { value: 'P', label: this.$t('purchase.category.P') },
        { value: 'S', label: this.$t('purchase.category.S') },
      ]
    },
    columns() {
      return this.styleColumns.filter(i => i.ss_key !== '_index')
    },
    approve() {
      return this.form && this.form.pro_level && this.form.pro_level.approve === 'Y'
    },
    review() {
      return this.form && this.form.pro_level && this.form.pro_level.review === 'Y'
    },
    // currentStep() {
    //   try {
    //     switch (this.data.status) {
    //       case 'G':
    //       case 'GR':
    //       case 'GA':
    //         return 3
    //       case 'I':
    //         return 4
    //       case 'K':
    //         return 5
    //       case 'M':
    //       case 'MR':
    //       case 'MI':
    //       case 'MA':
    //         return 6
    //       case 'O':
    //         return 7
    //       case 'A':
    //       default:
    //         if (this.data.pro_application_id) {
    //           return this.currentStep2
    //         } else {
    //           return 0
    //         }
    //     }
    //   } catch (e) {
    //     return 0
    //   }
    // },
    isNew() {
      return !this.id
    },
    stage() {
      switch (this.form.status) {
        case 'G':
        case 'GR':
          return 1
        case 'I':
        case 'MI':
        case 'K':
          return 2
        case 'M':
        case 'MR':
          return 3
        case 'O':
          return 4
        case 'A':
        case 'GA':
        case 'MA':
        default:
          return 0
      }
    },
    stepList() {
      const isNew = this.isNew

      return [
        {
          value: 0,
          label: this.t('stepList.basis'),
          canActive: true,
          status: this.getStepStatus(0),
        },
        {
          value: 1,
          label: this.t('stepList.items'),
          canActive: !isNew,
          status: this.getStepStatus(1),
        },
        {
          value: 2,
          label: this.t('stepList.suppliers'),
          canActive: !isNew,
          status: this.getStepStatus(2),
        },
        {
          value: 3,
          label: this.t('stepList.review'),
          canActive: !isNew && this.stage > 0,
          status: this.getStepStatus(3),
        },
        {
          value: 4,
          label: this.t('stepList.quotesInput'),
          canActive: !isNew && this.stage > 1,
          status: this.getStepStatus(4),
        },
        {
          value: 5,
          label: this.t('stepList.quotesSelect'),
          canActive: !isNew && this.stage > 1 && this.canNextSix,
          status: this.getStepStatus(5),
        },
        {
          value: 6,
          label: this.t('stepList.approval'),
          canActive: !isNew && this.stage > 2,
          status: this.getStepStatus(6),
        },
        {
          value: 7,
          label: this.t('stepList.print'),
          canActive: !isNew && this.stage > 3,
          status: this.getStepStatus(7),
        },
      ]
    },
    isReadonly() {
      if (this.$route.name === 'purchaseDailyProcurementAdd') return false
      return this.view === 'view'
    },
    hasLevels() {
      return this.levels.length > 0
    },
    isLoaded() {
      return !!(this.form && this.form.pro_application_id)
    },
  },
  created() {
    this.init()
    console.log(this.view)
  },
  methods: {
    getStepStatus(step) {
      if (this.currentStep === step) {
        return 'finish'
      }
      switch (step) {
        case 0:
        case 1:
        case 2:
          switch (this.stage) {
            case 0:
              if (this.isNew) {
                return 'wait'
              } else if (this.form.status === 'GA' || this.form.status === 'MA') {
                return 'process'
              } else {
                return 'success'
              }
            case 1:
            case 2:
            case 3:
            case 4:
              return 'success'
          }
          break
        case 3:
          switch (this.stage) {
            case 0:
              if (this.form.status === 'GA') {
                return 'error'
              } else if (this.form.status === 'MA') {
                return 'process'
              } else {
                return 'wait'
              }
            case 1:
              return 'process'
            case 2:
            case 3:
            case 4:
              return 'success'
          }
          break
        case 4:
        case 5:
          if (!this.canNextSix) {
            return 'wait'
          }
          switch (this.stage) {
            case 0:
              if (this.form.status === 'MA') {
                return 'success'
              } else {
                return 'wait'
              }
            case 1:
              return 'wait'
            case 2:
              if (this.canNextSix) {
                return 'success'
              } else {
                return 'process'
              }
            case 3:
            case 4:
              return 'success'
          }
          break
        case 6:
          switch (this.stage) {
            case 0:
              if (this.form.status === 'MA') {
                return 'error'
              } else {
                return 'wait'
              }
            case 1:
              return 'wait'
            case 2:
              if (this.form.status === 'MI') {
                return 'error'
              } else {
                return 'wait'
              }
            case 3:
              return 'process'
            case 4:
              return 'success'
          }
          break
        case 7:
          switch (this.stage) {
            case 0:
            case 1:
            case 2:
            case 3:
              return 'wait'
            case 4:
              return 'success'
          }
          break
      }
      return 'wait'
    },
    t(key) {
      return this.$t(this.langKey + key)
    },
    async init() {
      try {
        this.years = await fetchYears()
        if (!this.form.year) {
          if (this.years.length > 0) {
            this.fy_code = this.years[0].fy_code
            this.set('fy_code', this.years[0].fy_code)
          } else {
            return
          }
        }

        // 獲取採購等級
        this.levels = await fetchProcurementLevels()
        if (this.levels.length > 0) {
          this.currentLevel = this.levels[0].pro_level_id
        } else {
          // this.$message.error(this.t('levelsEmpty'))
          this.$alert(this.t('levelsEmptyInput'), this.t('levelsEmpty'), {
            confirmButtonText: this.t('goto'),
            cancelButtonText: this.t('back'),
            showCancelButton: true,
            callback: action => {
              switch (action) {
                case 'cancel':
                case 'close':
                  this.onBack()
                  break
                case 'confirm':
                  this.$router.push({
                    name: 'purchaseMasterProcurementLevel',
                    params: {},
                  })
                  break
              }
            },
          })
          this.fetchData()
          return
        }
        // 設置默認採購等級
        this.onChangeLevel(this.currentLevel)

        // // 獲取年度
        const fy_code = this.form.fy_code
        // const st_grade = 'S'
        // 獲取職員
        this.staffs = await fetchStaffs({ fy_code })
        //
        // const budget_types = 'F,G'
        // this.budgetGroupList = await fetchBudgetGroupList({ fy_code, budget_types })
      } catch (e) {
        console.log(e)
        return
      }
      try {
        //
        await this.fetchData()
      } catch (e) {
        return
      }
    },
    async fetchData() {
      try {
        if (this.id) {
          const res = await getProcurementApplication(this.id)
          try {
            // const data = {
            //   pro_application_id: res.pro_application_id, // 會計週期編號
            //   fy_code: res.fy_code, // 會計週期編號
            //   pro_level_id: res.pro_level_id, // 採購等級id
            //   pro_no: res.pro_no, // 採購編號
            //   pro_title: res.pro_title, // 採購申請標題
            //   pro_remark: res.pro_remark, // 採購申請備註
            //   apply_date: res.apply_date, // 申請日期
            //   invite_date: res.invite_date, // 邀請供應商日期
            //   type: res.type, // 採購類新: P=貨品: '',S=服務
            //   contact: res.contact, // 聯繫人
            //   contact_title: res.contact_title, // 聯繫人稱呼
            //   contact_tel: res.contact_tel, // 聯繫人電話
            //   budget_code: res.budget_code, // 預算編號
            //   reply_end_date: res.reply_end_date, // 供應商回復截止日期
            //   quoted_valid_days: res.quoted_valid_days, // 供應商報價有效日期(供應商回復截止日期開始計算)
            //   apply_staff_id: res.apply_staff_id, // 申請人staff_id
            //   apply_review_handlers: res.review_handlers, // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
            //   apply_review_handlers_json: '', // 根據採購等級的覆核結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
            //   apply_approve_handlers: res.approve_handlers, // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
            //   apply_approve_handlers_json: '' // 根據採購等級的審批結構填入staff_id,格式: [[staff_id,staff_id],[staff_id]]
            // }
            const data = res
            data.pro_level_id = res.pro_level.pro_level_id
            data.apply_review_handlers = res.review_handlers
            data.apply_approve_handlers = res.approve_handlers
            this.form = data
            switch (this.form.status) {
              case 'GA':
              case 'GR':
                this.currentStep = 3
                break
              case 'I':
              case 'M':
                this.currentStep = 4
                break
              case 'K':
                this.currentStep = 5
                break
              case 'MR':
              case 'MI':
              case 'MA':
                this.currentStep = 6
                break
              case 'O':
                this.currentStep = 7
                break
              case 'A':
              case 'G':
              default:
                this.currentStep = 0
                break
            }
            // switch (this.stage) {
            //   case 0:
            //     if (this.form.status === 'GA') {
            //       this.currentStep = 3
            //     } else if (this.form.status === 'MA') {
            //       this.currentStep = 6
            //     } else {
            //       this.currentStep = 0
            //     }
            //     break
            //   case 1:
            //     this.currentStep = 3
            //     break
            //   case 2:
            //     if (this.form.status === 'K') {
            //       this.currentStep = 5
            //     } else if (this.form.status === 'MI') {
            //       this.currentStep = 6
            //     } else {
            //       this.currentStep = 4
            //     }
            //     break
            //   case 3:
            //     if (['MR', 'MI', 'MA'].includes(this.form.status)) {
            //       this.currentStep = 6
            //     } else {
            //       this.currentStep = 4
            //     }
            //     break
            //   case 4:
            //     this.currentStep = 7
            //     break
            // }
          } catch (e) {
            console.error(e)
          }
          console.log(this.form)
        } else {
          this.currentStep = 0
          if (this.fyCode) {
            if (this.years.findIndex(y => y.fy_code === this.fyCode) !== -1) {
              this.form.fy_code = this.fyCode
            }
          }
          const res = ''
          console.log(res)
        }
      } catch (e) {
        console.log(e)
      }
    },
    onSetting() {
      this.showDialog = true
    },
    onChangeBudgetGroup(budget) {
      console.log(budget)
    },
    budgetDisabledMethod(item) {
      return false
    },
    onAdd() {
      this.$router.push({
        name: '',
      })
    },
    setStep(step) {
      this.currentStep = step
    },
    onClickStep(step) {
      if (this.form.pro_application_id) {
        // 已經保存
        switch (this.form.status) {
          case 'G':
          case 'GR':
          case 'GA':
            if (step < 4) {
              this.setStep(step)
            }
            break
          case 'I':
          case 'K':
            if (!this.canNextSix && step === 5) {
              return
            }
            if (step < 6) {
              this.setStep(step)
            }
            break
          case 'M':
          case 'MR':
          case 'MI':
          case 'MA':
            if (step < 7) {
              this.setStep(step)
            }
            break
          case 'O':
            this.setStep(step)
            break
          case 'A':
          default:
            if (step < 3) {
              this.setStep(step)
            }
            return
        }
      }
    },
    async onChangeLevel(val) {
      // 僅開始階段可修改
      if (!this.isNew) return
      this.form.pro_level_id = val
      try {
        const levelInfo = await getProcurementLevel(val)
        this.set('apply_review_handlers', deepCloneByJSON(levelInfo.review_handlers))
        this.set('apply_approve_handlers', deepCloneByJSON(levelInfo.approve_handlers))
        this.levelInfo = levelInfo
        this.$set(this.form, 'pro_level', levelInfo)
      } catch (e) {
        console.log(e)
      }
    },
    set(key, data) {
      this.$set(this.form, key, data)
      // this.$refs['basis'] && this.$refs['basis'].set(key, data)
    },
    async reshow() {
      await this.init()
      this.show = false
      await this.$nextTick()
      this.show = true
    },
    async onNext() {
      switch (this.currentStep) {
        case 0:
          // if (!this.isNew) {
          try {
            if (this.stage === 0 && !this.isReadonly) {
              await this.$refs['basis'].onSave(false)
            }
            if (!this.isReadonly) {
              await this.reshow()
              this.currentStep = 1
            } else {
              this.currentStep = 1
            }
          } catch (e) {
            console.log(e)
            return
          }
          // }
          break
        case 1:
          try {
            if (this.stage === 0 && !this.isReadonly) {
              await this.$refs['items'].onSave(false)
            }
            this.currentStep += 1
          } catch (e) {
            console.log(e)
            return
          }
          break
        case 2:
          try {
            if (this.stage === 0 && !this.isReadonly) {
              await this.$refs['suppliers'].onSave(false)
            }
          } catch (e) {
            console.log(e)
            return
          }
          if (this.isReadonly) {
            this.currentStep = 3
          } else if (
            this.form.status === 'A' ||
            this.form.status === 'GA' ||
            this.form.status === 'MA'
          ) {
            const count = this.form.items.filter(
              item => item.item_name && item.item_name.length > 0,
            ).length
            if (count === 0) {
              this.$message.error(this.t('itemsEmpty'))
              return
            }

            const pro_application_id = this.form.pro_application_id
            const status = 'G' // A > G
            try {
              await editProcurementApplicationsStatus({
                pro_application_id,
                status,
              })
              await this.reshow()
              this.currentStep = 3
              this.$message.success(this.$t('message.success'))
            } catch (e) {
              console.log(e)
            }
          } else if (this.stage > 0) {
            this.currentStep += 1
          }
          break
        case 3:
          if (this.stage > 1) {
            this.currentStep += 1
          }
          break
        case 4:
          if (this.form.status === 'I' && this.isReadonly) {
            this.currentStep += 1
            return false
          }
          try {
            if (this.form.status === 'I') {
              await this.$refs['quotesInput'].onSave(false)
            }
          } catch (e) {
            console.log(e)
            return
          }
          if (this.form.status === 'I' || this.form.status === 'MI') {
            const pro_application_id = this.form.pro_application_id
            const status = 'K' // A > G
            try {
              await editProcurementApplicationsStatus({
                pro_application_id,
                status,
              })
              this.reshow()
              this.$message.success(this.$t('message.success'))
            } catch (e) {
              console.log(e)
            }
          } else if (this.stage > 1) {
            this.currentStep += 1
          }
          break
        case 5:
          try {
            if (this.form.status === 'K') {
              await this.$refs['quotesSelect'].onSave(false)
            }
          } catch (e) {
            console.log(e)
            return
          }
          if (this.form.status === 'K') {
            const pro_application_id = this.form.pro_application_id
            const status = 'M' // A > G
            try {
              await editProcurementApplicationsStatus({
                pro_application_id,
                status,
              })
              await this.reshow()
              this.currentStep = 6
              this.$message.success(this.$t('message.success'))
            } catch (e) {
              console.log(e)
            }
          } else if (this.stage > 2) {
            this.currentStep += 1
          }
          break
        case 6:
          this.currentStep += 1
          break
        case 7:
          break
      }
    },
    onBack() {
      // this.$router.push({
      //   // name: this.$route.matched[1].name,
      //   path: this.$route.matched[1].redirect,
      //   params: {
      //     isBack: 'Y'
      //   }
      // })

      this.$router.push({
        name: this.$route.matched[1].redirect.name,
        params: {
          isBack: 'Y',
        },
      })
    },
    onPrev() {
      switch (this.currentStep) {
        case 0:
          break
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
          this.currentStep -= 1
          break
      }
    },
    onPass() {
      this.$confirm(`${this.t('passConfirm')}`, this.$t('confirm.warningTitle'), {
        confirmButtonText: this.$t('confirm.confirmButtonText'),
        cancelButtonText: this.$t('confirm.cancelButtonText'),
        type: 'warning',
      })
        .then(() => {
          const pro_apply_handler_id = this.handleId
          const status = 'A'
          const comment = ''
          return new Promise((resolve, reject) => {
            reviewApproveProcurementApplications({
              pro_apply_handler_id,
              status,
              comment,
            })
              .then(res => {
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          // this.reshow()
          this.onBack()
          this.$message({ type: 'success', message: this.$t('message.success') })
        })
    },
    commentValidator(text) {
      if (text && text.length > 0) {
        return true
      } else {
        return this.t('commentRequired')
      }
    },
    onReviewBack(status) {
      this.$prompt('', this.t('backComment'), {
        confirmButtonText: this.$t('button.submit'),
        cancelButtonText: this.$t('confirm.cancelButtonText'),
        inputType: 'textarea',
        customClass: 'reject-comment',
        inputValidator: this.commentValidator,
        // inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        // inputErrorMessage: '邮箱格式不正确'
      })
        .then(({ value }) => {
          const pro_apply_handler_id = this.handleId
          return new Promise((resolve, reject) => {
            reviewApproveProcurementApplications({
              pro_apply_handler_id,
              status,
              comment: value,
            })
              .then(res => {
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.onBack()
          this.$message({ type: 'success', message: this.$t('message.success') })
        })
        .catch(() => {})
    },
    onReject() {
      this.$prompt('', this.t('rejectComment'), {
        confirmButtonText: this.$t('button.submit'),
        cancelButtonText: this.$t('confirm.cancelButtonText'),
        inputType: 'textarea',
        customClass: 'reject-comment',
        inputValidator: this.commentValidator,
        // inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        // inputErrorMessage: '邮箱格式不正确'
      })
        .then(({ value }) => {
          const pro_apply_handler_id = this.handleId
          const status = 'R'
          return new Promise((resolve, reject) => {
            reviewApproveProcurementApplications({
              pro_apply_handler_id,
              status,
              comment: value,
            })
              .then(res => {
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.onBack()
          this.$message({ type: 'success', message: this.$t('message.success') })
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.page-procurement-applications-add {
  padding: 10px;
  .page-header {
    .levels {
      padding: 10px 0;
      min-height: 50px;
      max-width: 1000px;

      .review-actions {
        display: inline-block;
        float: right;
      }
    }
    .steps {
      padding: 10px 0;

      .step-item {
        &.can-active {
          cursor: pointer;
        }
      }
    }
    /deep/ {
      .el-step__title.is-process,
      .el-step__head.is-process {
        font-weight: normal;
        color: #606266;
        border-color: #606266;
      }
    }
  }
  .page-content {
    width: 1000px;
    position: relative;
    /deep/ {
      .el-input-number .el-input-number__increase,
      .el-input-number .el-input-number__decrease {
        top: 4px;
      }
      div.el-table div.el-table__body-wrapper table > tbody > tr > td {
        vertical-align: top;
        .cell {
          line-height: 36px;
        }
      }

      .is-disabled {
        .el-input__inner,
        .el-textarea__inner {
          color: #606266;
        }
      }

      .align-right {
        input {
          text-align: right;
        }
      }

      .el-message-box__input {
        padding-top: 0;
      }

      .expand-table {
        .el-table__body-wrapper {
          table {
            tbody {
              tr {
                td {
                  .cell {
                    height: 28px !important;
                    line-height: 28px !important;
                  }
                }
              }
            }
          }
        }
        .el-table__header-wrapper {
          table {
            thead {
              tr {
                th {
                  .cell {
                    height: 28px !important;
                    line-height: 28px !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.reject-comment {
  .el-message-box__input {
    padding-top: 0;
    height: 100px;
  }
  .el-textarea,
  textarea {
    height: 100%;
  }
}
</style>
