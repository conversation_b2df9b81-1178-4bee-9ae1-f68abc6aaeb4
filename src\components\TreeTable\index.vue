<template>
  <el-table
    ref="treeTable"
    :data="formatData"
    :row-class-name="isStripe"
    v-bind="$attrs"
    class="tree-table"
    v-on="$listeners"
    @header-dragend="onHeaderDragend"
  >
    <span v-html="styleSheets" />
    <el-table-column
      v-if="columns.length === 0"
      :align="firstFieldAlign"
      :width="firstFieldWidth"
      :label="firstLabel"
      column-key="first_field"
      show-overflow-tooltip
      highlight-current-row
      min-width="150"
    >
      <template v-if="scope && scope.row" slot-scope="scope">
        <div
          v-if="!loading"
          :class="
            scope.row.children && scope.row.children.length > 0 ? 'first-column-has-children' : ''
          "
          @click="toggleExpanded(scope.$index)"
        >
          <span v-for="space in scope.row._level" :key="space" class="ms-tree-space" />
          <span v-if="iconShow(0, scope.row)" class="tree-ctrl">
            <i v-if="!scope.row._expanded" class="el-icon-caret-right" />
            <i v-else class="el-icon-caret-bottom" />
          </span>
          <!--          <svg-icon v-if="scope.row[folderField]" icon-class="folder"/>-->
          <!--          <i v-if="scope.row[folderField]" class="edacIconFont edac-icon-folder"/>-->
          <!--          <svg-icon v-else icon-class="file"/>-->
          <!--          <i v-else class="edacIconFont edac-icon-file"/>-->
          <slot :scope="scope" name="firstField">
            <!--文件夾圖標-->
            <i
              v-if="scope.row[folderField]"
              :class="
                'edac-icon edac-icon-folder' +
                  (scope.row.active_year && scope.row.active_year.includes(currentYear)
                    ? ' activate'
                    : '')
              "
            />
            <!--文件圖標-->
            <i v-else class="edac-icon edac-icon-file" />
            <!--顯示編號-->
            <span
              v-if="!scope.row[folderField]"
              :class="
                'number-field' +
                  (scope.row.active_year && scope.row.active_year.includes(currentYear)
                    ? ' selected'
                    : '')
              "
            >{{ scope.row[numberField] }}</span>
            <!--名稱-->
            <span class="key">{{ scope.row[firstField] }}</span>
            <!--            <span v-if="!scope.row[folderField]" class="number-field)">{{ scope.row[numberField] }}</span>-->
            <!--            <span class="key">{{ scope.row[firstField] }}</span>-->
          </slot>
        </div>
      </template>
    </el-table-column>
    <template v-else>
      <el-table-column
        v-for="(column, index) in columns"
        :key="column.value"
        :label="column.text"
        :width="column.width"
      >
        <template v-if="scope && scope.row" slot-scope="scope">
          <template v-if="index === 0">
            <span v-for="space in scope.row._level" :key="space" class="ms-tree-space" />
          </template>
          <span
            v-if="iconShow(index, scope.row)"
            class="tree-ctrl"
            @click="toggleExpanded(scope.$index)"
          >
            <i v-if="!scope.row._expanded" class="el-icon-caret-right" />
            <i v-else class="el-icon-caret-bottom" />
          </span>
          {{ scope.row[column.value] }}
        </template>
      </el-table-column>
    </template>
    <slot />
  </el-table>
</template>

<script>
/**
 Auth: Lei.j1ang
 Created: 2018/1/19-13:59
 */
import treeToArray from './eval'
import { max } from '@/utils'

export default {
  name: 'TreeTable',
  props: {
    /* eslint-disable */
    data: {
      type: [Array, Object],
      required: true,
    },
    columns: {
      type: Array,
      default: () => [],
    },
    evalFunc: Function,
    evalArgs: Array,
    expandAll: {
      type: Boolean,
      default: false,
    },
    firstField: {
      type: String,
      default: '',
    },
    firstLabel: {
      type: String,
      default: '',
    },
    firstFieldAlign: {
      type: String,
      default: 'left',
    },
    firstFieldWidth: {
      type: [String, Number],
      default: '150',
    },
    folderField: {
      type: String,
      default: '',
    },
    numberField: {
      type: String,
      default: '',
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    actionsMinWidth: {
      type: Number,
      default: 150,
    },
    yearField: {
      type: String,
      default: '', // Table Data激活年份字段
    },
    currentYear: {
      type: String,
      default: '', // 當前激活年份，應該初始化一個值
    },
  },
  data() {
    return {
      maxLevel: 0,
      loading: false,
      styleSheets: '',
    }
  },
  watch: {
    data() {},
  },
  updated() {
    // this.$nextTick(() => {
    //   this.updateHeight()
    // })
  },
  computed: {
    // 格式化数据源
    formatData: function () {
      let tmp
      if (!Array.isArray(this.data)) {
        tmp = [this.data]
      } else {
        tmp = this.data
      }
      const func = this.evalFunc || treeToArray
      const args = this.evalArgs
        ? [tmp, this.expandAll].concat(this.evalArgs)
        : [tmp, this.expandAll]

      this.reload()
      return func.apply(null, args)
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.updateHeight()
    })
  },
  methods: {
    updateHeight() {
      if (!this.$refs['treeTable'] || !this.$refs['treeTable'].$el) {
        return
      }
      const top = this.$refs['treeTable'].$el.parentNode.parentNode.parentNode.offsetTop
      const headerHeight = this.$refs['treeTable'].$el.children[1].offsetHeight
      this.styleSheets = `
<style>
  .tree-table {
    height: calc(100vh - 140px - ${top}px);
  }
  .tree-table .el-table__body-wrapper {
    height: calc(100vh - 142px - ${top}px - ${headerHeight}px);
  }
</style>

      `
    },
    reload() {
      this.loading = true
      this.$nextTick(() => {
        this.loading = false
      })
      // setTimeout(()=>{
      //     this.hockReload = false
      // },100)
    },
    /**
     * Table斑馬紋
     */
    isStripe(row) {
      let text = ''
      const show = row.row.parent ? row.row.parent._expanded && row.row.parent._show : true
      row.row._show = show
      text = show ? 'show-row' : 'hidden-row'
      // if ((row.rowIndex % 2) === 0) {
      //   text += ' table-stripe'
      // }
      return text
    },
    showRow: function (row) {
      return ''
      const show = row.row.parent ? row.row.parent._expanded && row.row.parent._show : true
      row.row._show = show
      return show
        ? 'animation:treeTableShow 1s;-webkit-animation:treeTableShow 1s;'
        : 'display:none;'
    },
    // 切换下级是否展开
    toggleExpanded: function (trIndex) {
      const record = this.formatData[trIndex]
      record._expanded = !record._expanded
      this.$emit('changeExpanded', this.getExpandedList())
    },
    // 图标显示
    iconShow(index, record) {
      return index === 0 && record.children && record.children.length > 0
    },
    delete(row) {
      const { _index, parent } = row
      if (parent) {
        parent.children.splice(_index, 1)
      } else {
        this.data.splice(_index, 1)
      }
    },
    showLevel(level) {
      this.getMaxLevel()
      let maxLevel = 0
      this.data.forEach(item => {
        const cLevel = this.expanded(item, level)
        maxLevel = maxLevel < cLevel ? cLevel : maxLevel
      })
      return maxLevel
    },
    getMaxLevel() {
      if (this.data.length === 0) {
        return 0
      }
      const getLevel = item => {
        if (item.children) {
          const cList = item.children.map(getLevel)
          const cLevel = max(cList)
          return max([item._level, cLevel])
        }
        return item._level
      }
      return max(this.data.map(getLevel))
    },
    expanded(data, level) {
      let maxLevel = data._level
      data._expanded = data._level < level
      if (data.children) {
        data.children.forEach(item => {
          const cLevel = this.expanded(item, level)
          maxLevel = maxLevel < cLevel ? cLevel : maxLevel
        })
      }
      return maxLevel
    },
    /**
     * 修改列寬提交到父組件
     * @param newWidth
     * @param oldWidth
     * @param column
     * @param event
     */
    onHeaderDragend(newWidth, oldWidth, column, event) {
      const key = column.columnKey
      this.$emit('changeWidth', key, newWidth)
    },
    getExpandedList() {
      const getList = item => {
        let s = item._expanded ? [item[this.numberField]] : []
        if (item.children) {
          s = [...s, ...item.children.map(getList)]
        }
        return s.filter(i => i).join(',')
      }
      let s = this.data
        .map(getList)
        .filter(i => i !== '')
        .join(',')
      return s
    },
    setExpandItem(listStr) {
      const list = listStr.split(',')
      if (list.length === 0) {
        return
      }
      const setExpanded = item => {
        item._expanded = list.indexOf(item[this.numberField]) !== -1
        if (item.children) {
          item.children.map(setExpanded)
        }
      }
      this.data.map(setExpanded)
    },
    setCurrentRow(row) {
      this.$refs.treeTable.setCurrentRow(row)
    },
  },
}
</script>
<style rel="stylesheet/css" lang="scss">
@keyframes treeTableShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes treeTableShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.tree-table {
  /*height: calc(100vh - 210px);*/
  .el-table__body-wrapper {
    /*height: calc(100vh - 240px);*/
    overflow-y: auto;
  }

  .number-field {
    vertical-align: middle;
    color: #fff;
    background-color: #b9b7b8;
    padding: 0 3px;
    &.selected {
      background-color: #3e97dd;
    }
  }
  .key {
    vertical-align: middle;
  }
  .edac-icon {
    vertical-align: middle;
    color: #707070;

    &.activate {
      color: #3e97dd;
    }
  }
}
</style>

<style lang="scss" rel="stylesheet/scss" scoped>
$color-blue: #2196f3;
$space-width: 13px;
.ms-tree-space {
  position: relative;
  top: 1px;
  display: inline-block;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  width: 5px;
  height: 14px;
  &::before {
    content: '';
  }
}
.processContainer {
  width: 100%;
  height: 100%;
}
table td {
  line-height: 26px;
}

.tree-ctrl {
  position: relative;
  cursor: pointer;
  /*color: $color-blue;*/
  margin-left: -$space-width;
}

.tree-table {
  /deep/ {
    .show-row {
      animation: treeTableShow 1s;
      -webkit-animation: treeTableShow 1s;
    }
    .hidden-row {
      display: none;
    }
  }

  i.tree-ctrl {
    font-size: 16px !important;
    color: #707070;
  }
  .svg-icon {
    vertical-align: -0.2em;
    font-size: 16px !important;
    color: #707070;
  }
  .first-column-has-children {
    cursor: pointer;
  }
  .cell {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    .key {
      -webkit-touch-callout: unset;
      -webkit-user-select: text;
      -khtml-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
    }
  }
}
</style>
