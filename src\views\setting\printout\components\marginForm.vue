<template>
  <el-form-item
    id="margin-form-item"
    :label="$t('setting.printout.label.margin')"
    class="margin-form-item"
  >
    <el-row class="margin-row-1">
      <el-input-number
        v-model="margin_top"
        :min="0"
        :max="999"
        :controls="false"
        class="margin-input-top"
      />
    </el-row>
    <el-row class="margin-row-2">
      <el-input-number
        v-model="margin_left"
        :min="0"
        :max="999"
        :controls="false"
        :style="{
          'line-height': pageDemoHeight,
        }"
        class="margin-input-left"
      />
      <div
        :style="{
          width: pageDemoWidth,
          height: pageDemoHeight,
          backgroundImage: `url(${
            pageOrientation === 'L'
              ? require('@/assets/previews/word-l.png')
              : require('@/assets/previews/word-p.png')
          })`,
          border: '1px solid rgb(112, 112, 112)',
          backgroundRepeat: 'no-repeat',
          backgroundSize: `${pageDemoWidth} ${pageDemoHeight}`,
        }"
        class="page-content"
      />
      <el-input-number
        v-model="margin_right"
        :min="0"
        :max="999"
        :controls="false"
        :style="{
          'line-height': pageDemoHeight,
        }"
        class="margin-input-right"
      />
    </el-row>
    <el-row class="margin-row-3">
      <el-input-number
        v-model="margin_bottom"
        :min="0"
        :max="999"
        :controls="false"
        class="margin-input-bottom"
      />
    </el-row>
  </el-form-item>
</template>

<script>
// import imgWordL from '@/assets/previews/word-l.png'
// import imgWordP from '@/assets/previews/word-p.png'

export default {
  name: 'MarginForm',
  props: {
    marginTop: {
      type: [Number, String],
      default: 0,
    },
    marginLeft: {
      type: [Number, String],
      default: 0,
    },
    marginRight: {
      type: [Number, String],
      default: 0,
    },
    marginBottom: {
      type: [Number, String],
      default: 0,
    },
    pageWidth: {
      type: [Number, String],
      default: 100,
    },
    pageHeight: {
      type: [Number, String],
      default: 100,
    },
    pageOrientation: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {}
  },
  computed: {
    margin_top: {
      get() {
        return this.marginTop
      },
      set(val) {
        this.$emit('update:marginTop', val)
      },
    },
    margin_left: {
      get() {
        return this.marginLeft
      },
      set(val) {
        this.$emit('update:marginLeft', val)
      },
    },
    margin_right: {
      get() {
        return this.marginRight
      },
      set(val) {
        this.$emit('update:marginRight', val)
      },
    },
    margin_bottom: {
      get() {
        return this.marginBottom
      },
      set(val) {
        this.$emit('update:marginBottom', val)
      },
    },
    pageDemoWidth() {
      // let w, h
      // if (this.pageWidth > this.pageHeight) {
      //   w = 100
      //   h = 100 / this.pageWidth * this.pageHeight
      // } else {
      //   h = 100
      //   w = 100 / this.pageHeight * this.pageWidth
      // }
      // return w + 'px'
      const w = Number(this.pageWidth)
      const h = Number(this.pageHeight)

      return (w > h ? 100 : (100 / h) * w) * 2 + 'px'
    },
    pageDemoHeight() {
      // let w, h
      // if (this.pageWidth > this.pageHeight) {
      //   w = 100
      //   h = 100 / this.pageWidth * this.pageHeight
      // } else {
      //   h = 100
      //   w = 100 / this.pageHeight * this.pageWidth
      // }
      // return h + 'px'
      const w = Number(this.pageWidth)
      const h = Number(this.pageHeight)
      return (h > w ? 100 : (100 / w) * h) * 2 + 'px'
    },
  },
}
</script>

<style lang="scss" scoped>
#margin-form-item {
  padding-top: 3px;
  /deep/ {
    .el-form-item__content {
      height: auto;
      /*width: 302px;*/
      width: max-content;
      border: 1px solid #e6e6e6;
    }
    .el-row::after,
    .el-row::before {
      display: none !important;
    }
    .margin-row-3 {
      margin-bottom: 4px;
    }
    .margin-row-1,
    .margin-row-3 {
      &.el-row::before,
      &.el-row::after {
        /*content: unset!important;*/
        /*display:none!important;*/
      }
      line-height: 25px;
      height: 25px;
      /*width: 300px;*/
      width: 100%;
      text-align: center;
    }
    .margin-row-2 {
      /*width: 300px;*/
      text-align: center;
      display: inline-flex;
      margin-top: 3px;
    }
    .margin-input-left,
    .margin-input-right {
      margin-left: 1px;
      margin-right: 1px;
      /*line-height: 120px!important;*/
      /*line-height: 100%!important;*/
      width: 60px !important;
      .el-input {
        width: 60px !important;
        line-height: inherit;
      }
    }
    .margin-input-top,
    .margin-input-bottom {
      width: 60px !important;
      margin: 0 2px;
      .el-input {
        width: 60px !important;
        line-height: 25px;
        height: 25px;
      }
    }
  }
  .page-content {
    width: 100%;
    height: 100%;
    /*min-width: 120px;*/
    /*min-height: 120px;*/
    background: #aeb7bf;
    line-height: 80px;
    color: white;
    transition: all 0.5s cubic-bezier(0.82, -0.26, 0.4, 1.13);
  }
}
</style>
