<template>
  <!--傳票明細彈窗-->
  <el-dialog
    :visible.sync="showDialog"
    :title="$t('daily.dialog.description')"
    @open="onOpenDialog"
  >
    <addPage
      v-if="dialogView === 'add'"
      :default-parent="searchFilter.fund_id"
      :edit-parent="addParent"
      class="voucher-dialog-add"
      @onCancel="onCancel"
    />
    <div v-else class="selectDescription">
      <div class="search-bar">
        <el-form :inline="true">
          <el-form-item :label="$t('daily.label.allFundType')">
            <el-select v-model="searchFilter.fund_id" style="width: 100px" @change="onChangeFund">
              <el-option
                v-for="item in fund_list_f"
                :key="item.value"
                :label="conversionParentAccountType(item)"
                :value="item.fund_id"
                v-html="conversionParentAccountType(item, true)"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('daily.label.accountType')">
            <el-select v-model="searchFilter.fund_id_target" style="width: 100px">
              <el-option :label="AllFund.label" :value="AllFund.value" />
              <el-option
                v-for="item in fund_list_g"
                :key="item.value"
                :label="conversionParentAccountType(item)"
                :value="item.fund_id"
                v-html="conversionParentAccountType(item, true)"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('daily.label.desc')">
            <el-input
              v-model="searchFilter.name"
              style="width: 100px"
              @keyup.enter.native="fetchData"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" @click="fetchData">
              {{ $t('button.fetch') }}
            </el-button>
            <el-button size="mini" @click="onClear">
              {{ $t('button.clear') }}
            </el-button>
            <el-button v-if="hasPermission_Add" size="mini" type="primary" @click="onAdd">
              {{ $t('button.add') }}
            </el-button>
          </el-form-item>
          <el-form-item :label="$t('daily.label.type')">
            <el-select v-model="searchFilter.type" style="width: 100px" @change="fetchData">
              <el-option
                v-for="item in accountTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-table">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :row-class-name="isStripe"
          border=""
          height="100%"
          @current-change="handleCurrentChange"
        >
          <el-table-column :label="$t('daily.label.number')" property="ac_code" width="150" />
          <el-table-column :label="$t('daily.label.desc')" property="desc" width="200" />
          <el-table-column :label="$t('daily.label.type')" min-width="100" property="BIE" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import addPage from '@/views/assistance/description/add'
import mixinPermission from '@/views/mixins/permission'

import { fetchFunds } from '@/api/master/funds'
import { fetchDescriptions } from '@/api/assistance/description'

export default {
  name: 'DialogDescription',
  components: {
    addPage,
  },
  mixins: [mixinPermission],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    fy_code: {
      type: [String, Object],
      required: true,
    },
    fundId: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      p_code: 'ac.assistance.description',
      p_isComponent: true,
      // 行
      // 選擇會計科目
      currentRowItem: null,
      currentRowIndex: -1,
      showDialog: this.dialogVisible,
      dialogSelectAccount: null,
      dialogView: 'list',
      fund_list_f: [],
      fund_list_g: [],
      tableData: [],
      searchFilter: {
        fund_id: '',
        fund_id_target: '',
        name: '',
        type: 'E',
      },

      loading: false,
      tableLoading: false,

      addParent: null,
      // temp
    }
  },
  computed: {
    TQOptions() {
      return [
        {
          label: 'N/A',
          value: '',
        },
        {
          label: 'Quotation',
          value: 'Q',
        },
        {
          label: 'Tender',
          value: 'T',
        },
      ]
    },
    AllFund() {
      return {
        label: this.$t('daily.label.all'),
        value: '',
      }
    },
    accountTypes() {
      return [
        {
          label: this.$t('daily.label.allType'),
          value: '',
        },
        {
          label: 'B',
          value: 'B',
        },
        {
          label: 'I',
          value: 'I',
        },
        {
          label: 'E',
          value: 'E',
        },
      ]
    },
  },
  watch: {
    showDialog() {
      this.$emit('update:dialogVisible', this.showDialog)
    },
    dialogVisible() {
      this.showDialog = this.dialogVisible
    },
  },
  mounted() {},
  methods: {
    isStripe() {
      return 'pending-row'
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },

    onOpenDialog() {
      // 清空
      this.addParent = null
      this.tableData = []
      this.searchFilter = {
        fund_id: '',
        fund_id_target: '',
        name: '',
        type: '',
      }
      fetchFunds({ fund_type: 'F', fy_code: this.fy_code })
        .then(res => {
          this.fund_list_f = res
          if (res.length > 0) {
            const fund = res.find(i => i.fund_id === this.fundId)
            if (fund) {
              this.searchFilter.fund_id = fund.fund_id
            } else {
              this.searchFilter.fund_id = res[0].fund_id
            }
          } else {
            return Promise.reject()
          }
        })
        .then(() =>
          fetchFunds({
            fund_type: 'G',
            parent_fund_id: this.searchFilter.fund_id,
            fy_code: this.fy_code,
          }),
        )
        .then(res => {
          this.fund_list_g = res
        })
        .then(this.fetchData)
    },
    fetchData() {
      const parent_fund_id = this.searchFilter.fund_id_target
        ? this.searchFilter.fund_id_target
        : this.searchFilter.fund_id
      if (!parent_fund_id) {
        return
      }
      this.tableLoading = true
      fetchDescriptions({
        fy_code: this.fy_code,
        fund_id: this.searchFilter.fund_id_target
          ? this.searchFilter.fund_id_target
          : this.searchFilter.fund_id,
        desc: this.searchFilter.name,
        // ac_code: this.searchFilter.ac_code,
        BIE: this.searchFilter.type,
      })
        .then(res => {
          this.tableData = res
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    onAdd() {
      const fund_id = this.searchFilter.fund_id
      if (fund_id) {
        if (this.searchFilter.fund_id_target) {
          this.addParent = { fund_id: this.searchFilter.fund_id_target }
        }
        this.dialogView = 'add'
      } else {
        this.$message.error(this.$t('message.pleaseSelectFund'))
      }
    },
    onCancel(update) {
      this.dialogView = 'list'
      if (update) this.fetchData()
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      if (currentRow == null) return
      this.$emit('selectRow', currentRow)
      this.showDialog = false
    },
    onClear(update) {
      this.searchFilter = {
        fund_id: '',
        fund_id_target: '',
        name: '',
        type: '',
      }
      if (this.fund_list_f.length) {
        this.searchFilter.fund_id = this.fund_list_f[0].fund_id
      }
      if (update) this.fetchData()
    },
    onChangeFund() {
      this.fetchData()
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ {
  .el-dialog {
    .el-dialog__body {
      min-height: 200px;
      height: 60vh;
    }
  }
}
.selectDescription {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.dialog-table {
  flex: 1;
}
</style>

<style lang="scss">
.el-checkbox {
  margin-right: 30px !important;
}
</style>
