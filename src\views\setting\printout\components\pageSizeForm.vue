<template>
  <div class="page-size-form">
    <!-- 紙樣式 -->
    <el-form-item :label="$t('setting.printout.label.pageStyle')">
      <el-select
        v-model="page_style"
        :disabled="!edit"
        :placeholder="$t('setting.printout.label.pageStyle')"
      >
        <el-option
          v-for="item in pageStyleList"
          :key="item.paper_set_id"
          :label="isEN ? item.ps_name_en : item.ps_name_cn"
          :value="item.ps_code"
        />
      </el-select>
    </el-form-item>
    <!-- 紙大小 -->
    <el-form-item :label="$t('setting.printout.label.pageSize')" class="form-item-simple">
      <el-input-number :value="page_width" :min="0" :controls="false" readonly disabled />
      <span style="width: 10px; display: inline-block; text-align: center">{{
        $t('periodic.balanceTransfer.symbol.multiply')
      }}</span>
      <el-input-number :value="page_height" :min="0" :controls="false" readonly disabled />
      <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
    </el-form-item>
    <!-- 方向 -->
    <el-form-item :label="$t('setting.printout.label.pageOrientation')">
      <el-input :value="page_orientation" readonly />
    </el-form-item>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchPaper } from '@/api/settings/paper'

export default {
  name: 'PageSizeForm',
  props: {
    pageStyle: {
      type: String,
      default: '',
    },
    pageWidth: {
      type: [Number, String],
      default: 210,
    },
    pageHeight: {
      type: [Number, String],
      default: 297,
    },
    pageOrientation: {
      type: [Number, String],
      default: 0,
    },
    edit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      pageStyleList: [],
    }
  },
  computed: {
    ...mapGetters(['language']),
    isEN() {
      return this.language === 'en'
    },
    pageStyleList0() {
      return [
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_a4_landscape'),
          value: 'A4L~1050~750~L',
          width: 1050,
          height: 750,
          orientation: 1,
        },
        {
          label: this.$t(
            'setting.printout.periodic.autopayList.content.page_style_a4_pdf_landscape',
          ),
          value: 'A4LPDF~297~210~L',
          width: 297,
          height: 210,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_a4_portrait'),
          value: 'A4P~750~1050~P',
          width: 750,
          height: 1050,
          orientation: 0,
        },
        {
          label: this.$t(
            'setting.printout.periodic.autopayList.content.page_style_a4_pdf_portrait',
          ),
          value: 'A4PPDF~210~297~P',
          width: 210,
          height: 297,
          orientation: 0,
        },
        {
          label: this.$t(
            'setting.printout.periodic.autopayList.content.page_style_a5_pdf_landscape',
          ),
          value: 'A5LPDF~210~148~L',
          width: 210,
          height: 148,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_boc_cheque'),
          value: 'BOC~172~89~L',
          width: 172,
          height: 89,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_dbs_cheque'),
          value: 'DBS~204~89~L',
          width: 204,
          height: 89,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_bea_cheque'),
          value: 'EAB~204~89~L',
          width: 204,
          height: 89,
          orientation: 1,
        },
        {
          label: this.$t(
            'setting.printout.periodic.autopayList.content.page_style_standard_envelope_1',
          ),
          value: 'EN1~230~120~L',
          width: 230,
          height: 120,
          orientation: 1,
        },
        {
          label: this.$t(
            'setting.printout.periodic.autopayList.content.page_style_standard_envelope_2',
          ),
          value: 'EN2~210~148~L',
          width: 210,
          height: 148,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_hsb_cheque'),
          value: 'HSB~203~87~L',
          width: 203,
          height: 87,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_hsbc_cheque'),
          value: 'HSBC~159~82~L',
          width: 159,
          height: 82,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_scb_cheque'),
          value: 'SCB~203~89~L',
          width: 203,
          height: 89,
          orientation: 1,
        },
        {
          label: this.$t('setting.printout.periodic.autopayList.content.page_style_wlb_cheque'),
          value: 'WLB~203~89~L',
          width: 230,
          height: 89,
          orientation: 1,
        },
      ]
    },
    page_style: {
      get() {
        return this.pageStyle
      },
      set(val) {
        const item = this.pageStyleList.find(i => i.ps_code === val)
        if (item) {
          this.$emit('update:pageStyle', val)
          this.$emit('update:pageWidth', item.width)
          this.$emit('update:pageHeight', item.height)
          this.$emit('update:pageOrientation', item.orientation)
        }
      },
    },
    page_width: {
      get() {
        return this.pageWidth
      },
      set(val) {
        this.$emit('update:pageWidth', val)
      },
    },
    page_height: {
      get() {
        return this.pageHeight
      },
      set(val) {
        this.$emit('update:pageHeight', val)
      },
    },
    page_orientation: {
      get() {
        return this.pageOrientation === 'L'
          ? this.$t('setting.printout.content.landscape')
          : this.$t('setting.printout.content.portrait')
      },
      set(val) {
        this.$emit('update:pageOrientation', val)
      },
    },
  },
  created() {
    this.loadPageSet()
  },
  methods: {
    loadPageSet() {
      fetchPaper().then(res => {
        this.pageStyleList = res
      })
    },
  },
}
</script>

<style scoped></style>
