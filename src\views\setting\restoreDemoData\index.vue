<template>
  <div class="app-container">
    <VBreadCrumb :show="true" class="breadcrumb" />

    <el-form ref="form" label-position="right" label-width="80px">
      <el-form-item>
        <el-button size="mini" type="primary" @click="onRestore">
          {{ $t('button.restore') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import VBreadCrumb from '@/views/layout/components/VBreadcrumb'
import { mapGetters } from 'vuex'
import loadPreferences from '@/views/mixins/loadPreferences'
import permission from '@/views/mixins/permission'
import { restoreDemoData } from '@/api/settings/restoreDemoData'

export default {
  name: 'PeriodicEDBInput',
  components: {
    VBreadCrumb,
  },
  mixins: [loadPreferences, permission],
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['system', 'currentYear']),
  },
  created() {},
  methods: {
    onRestore() {
      const that = this
      that.$confirm(
        that.$t('setting.restoreDemoData.confirmContent'),
        that.$t('setting.restoreDemoData.confirmTitle'),
        {
          confirmButtonText: that.$t('confirm.confirmButtonText'),
          cancelButtonText: that.$t('confirm.cancelButtonText'),
          type: 'warning',
          closeOnClickModal: false,
          showClose: false,
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              instance.cancelButtonLoading = true
              restoreDemoData()
                .then(res => {
                  done()
                  instance.confirmButtonLoading = false
                  instance.cancelButtonLoading = false
                  that.$nextTick(() => {
                    setTimeout(() => {
                      that.$confirm(
                        that.$t('setting.restoreDemoData.restoreSuccessContent'),
                        that.$t('setting.restoreDemoData.restoreSuccess'),
                        {
                          confirmButtonText: that.$t('confirm.confirmButtonText'),
                          type: 'success',
                          showCancelButton: false,
                          closeOnClickModal: false,
                          showClose: false,
                          beforeClose: (action2, instance2, done2) => {
                            if (action2 === 'confirm') {
                              instance2.confirmButtonLoading = true
                              that.$store.dispatch('LogOut').then(() => {
                                done2()
                                window.location.reload() // 刷新頁面
                              })
                            } else {
                              done2()
                            }
                          },
                        },
                      )
                    }, 300)
                  })
                })
                .catch(err => {
                  console.log('restoreDemoData', err)
                  done()
                  instance.confirmButtonLoading = false
                  instance.cancelButtonLoading = false
                })
            } else {
              done()
            }
          },
        },
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  header {
    margin: 0 20px 20px 0;
  }
}
.year {
  width: 400px;
}
</style>
