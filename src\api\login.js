import request from '@/utils/request'

export function login(username, password, system) {
  const data = {
    username,
    password,
    system,
  }
  return request({
    url: '/login',
    method: 'post',
    data,
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'post',
  })
}

export function getUserInfo() {
  return request({
    url: '/users/me',
    method: 'get',
  })
}

export function getUserInfoByStaff() {
  return request({
    url: '/staffs/me',
    method: 'get',
  })
}
