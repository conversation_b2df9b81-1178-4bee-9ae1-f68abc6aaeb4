<script>
import { mapGetters, mapActions } from 'vuex'
import {
  getDefaultStyleSheets,
  getUserStyleSheets,
  editDefaultStyleSheets,
  editUserStyleSheets,
} from '@/api/settings/user/style'

export default {
  name: 'SettingScreenMixin',
  data() {
    return {
      hookVisible: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'user_id', 'system']),
    fontFamilyList() {
      return [
        {
          value: 'SimSun',
          title: this.$t('setting.basic.fontFamilyList.sim_sun'),
        },
        {
          value: 'SimHei',
          title: this.$t('setting.basic.fontFamilyList.sim_hei'),
        },
        {
          value: 'Microsoft YaHei',
          title: this.$t('setting.basic.fontFamilyList.microsoft_yahei'),
        },
        {
          value: 'KaiTi',
          title: this.$t('setting.basic.fontFamilyList.kai_ti'),
        },
      ]
    },
  },
  created() {
    this.onFetch(1)
  },
  methods: {
    ...mapActions(['fetchGlobalStyle']),
    reload() {
      this.hookVisible = true
      this.$nextTick(() => {
        this.hookVisible = false
      })
    },
    onSave(user) {
      const api = user ? editUserStyleSheets : editDefaultStyleSheets
      const user_id = user ? this.user_id : undefined
      const system = this.system
      const ss_code = this.ss_code
      const ss_type = this.ss_type
      const data = []
      let i = 1
      for (const key in this.form) {
        data.push({
          ss_id: i,
          user_id: user_id,
          staff_id: null,
          system: system,
          ss_code: ss_code,
          ss_type: ss_type,
          seq: i++,
          ss_key: key,
          value: this.form[key],
          alignment: null,
          width: null,
          x: null,
          y: null,
        })
      }

      const params = {
        user_id,
        system: this.system,
        ss_code: this.ss_code,
        ss_setting_json: data,
      }

      api(params)
        .then(() => {
          this.$message.success(this.$t('message.success'))
        })
        .then(() => {
          this.fetchGlobalStyle()
          // window.location.reload()
          this.reload()
        })
        .catch(() => {})
    },
    onFetch(user) {
      const user_id = user ? this.user_id : undefined
      const ss_type = this.ss_type

      const api = user ? getUserStyleSheets : getDefaultStyleSheets
      const params = {
        user_id,
        system: this.system,
        ss_code: this.ss_code,
      }
      api(params)
        .then(res => {
          console.log(res, '111res')
          res
            .filter(item => item.ss_type === ss_type)
            .forEach(item => {
              if (this.form[item.ss_key]) {
                this.form[item.ss_key] = item.value
              }
            })
        })
        .catch(() => {})
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-main {
  /*width: 500px;*/
}
.header > header {
  font-size: 18px !important;
  margin-left: 0;
}
.actions {
  margin-top: 30px;
}
</style>
<style rel="stylesheet/scss" lang="scss">
.setting-basic {
  .el-form-item__content {
    line-height: 30px;

    .el-input-number {
      height: 30px;
      line-height: 30px;
    }
    .el-input-number .el-input-number__increase,
    .el-input-number .el-input-number__decrease {
      height: 27px;
      line-height: 27px;
      transform: translate(0, -50%);
      top: calc(50% + 1px);
    }
  }
}
</style>
