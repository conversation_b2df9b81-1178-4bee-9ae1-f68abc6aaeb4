import request from '@/utils/request'

/**
 * 查詢試算表數據
 * @param {string} parent_fund_id 賬目類別(大類)id
 * @param {string} fy_code 選擇的會計週期
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} ignore_zero 跳過零值,傳1=跳過,0=不跳過
 * @param {integer} audit_adjust 0=所有(此時必須填寫begin_date和end_date),1=無,2=#1,3=#2,4=#3
 * @param {string} data_type 數據格式類型,ARRAY=數組,TREE=樹狀
 * @return {Promise}
 */
export function getReportAccountsTrialBalance({
  parent_fund_id,
  fy_code,
  begin_date,
  end_date,
  ignore_zero,
  audit_adjust,
  data_type,
}) {
  return request({
    url: '/reports/accounts/trial-balance',
    method: 'get',
    params: {
      parent_fund_id,
      fy_code,
      begin_date,
      end_date,
      ignore_zero,
      audit_adjust,
      data_type,
    },
  })
}

/**
 * 返回現金/銀行賬簿報表數據
 * @param {string} ac_code 選擇的會計科目(ac_bank=C,S,F,P)
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function getReportAccountsCashLedgers({ ac_code, begin_date, end_date }) {
  return request({
    url: '/reports/accounts/cash-ledgers',
    method: 'get',
    params: {
      ac_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 返回部門報表數據
 * @param {string} parent_dept_type_id 部門id
 * @param {string} dept_code 部門編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} BIE 可傳:B,I,E,IE
 * @return {Promise}
 */
export function getReportDepartmentsLedgers({
  parent_dept_type_id,
  dept_code,
  begin_date,
  end_date,
  BIE,
}) {
  return request({
    url: '/reports/departments/ledgers',
    method: 'get',
    params: {
      parent_dept_type_id,
      dept_code,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 返回會計賬目報表數據
 * @param {string} parent_fund_id 賬目id
 * @param {string} ac_code 會計科目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} mode 顯示: N=正常賬目,A=正常賬目+審計修正,B=正常賬目+審計修正+年結轉賬
 * @return {Promise}
 */
export function getReportLedgers({ parent_fund_id, ac_code, begin_date, end_date, mode }) {
  return request({
    url: '/reports/ledgers',
    method: 'get',
    params: {
      parent_fund_id,
      ac_code,
      begin_date,
      end_date,
      mode,
    },
  })
}

/**
 * 返回傳票報表數據
 * @param {string} fund_id 撥款賬目id
 * @param {string} vt_category 傳票類別類型,P=付款傳票,R=收款傳票,C=現金傳票,T=轉賬傳票,J=一般轉賬傳票,A=A/P傳票
 * @param {string} vt_code 傳票類別編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function getReportVoucher({ fund_id, vt_category, vt_code, begin_date, end_date }) {
  return request({
    url: '/reports/vouchers',
    method: 'get',
    params: {
      fund_id,
      vt_category,
      vt_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 返回期初報表數據
 * @param {string} fy_code 會計週期編號
 * @return {Promise}
 */
export function getOpeningBalance(fy_code) {
  return request({
    url: '/reports/cycle/opening-balance',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 返回期末報表數據
 * @param {string} fy_code 會計週期編號
 * @return {Promise}
 */
export function getClosingBalance(fy_code) {
  return request({
    url: '/reports/cycle/closing-balance',
    method: 'get',
    params: {
      fy_code,
    },
  })
}

/**
 * 返回预算報表數據
 * @param {string} fy_code 會計週期編號
 * @param {string} budget_id 预算id
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} BIE 可傳:B,I,E,IE
 * @return {Promise}
 */
export function getReportBudgets({ fy_code, budget_id, begin_date, end_date, BIE }) {
  return request({
    url: '/reports/budgets/ledgers',
    method: 'get',
    params: {
      fy_code,
      budget_id,
      begin_date,
      end_date,
      BIE,
    },
  })
}

/**
 * 返回會計賬目報表數據
 * @param {string} [parent_fund_id] 賬目id
 * @param {string} [ac_code] 會計科目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @param {string} mode 顯示: N=正常賬目,A=正常賬目+審計修正,B=正常賬目+審計修正+年結轉賬
 * @return {Promise}
 */
export function fetchLedgersPrint({ parent_fund_id, ac_code, begin_date, end_date, mode }) {
  return request({
    url: '/reports/ledgers-print',
    method: 'get',
    params: {
      parent_fund_id,
      ac_code,
      begin_date,
      end_date,
      mode,
    },
  })
}

/**
 * 匯出財務報表
 * @param {string} pd_code 月份
 * @return {Promise}
 */
export function exportFinancialReport({ pd_code }) {
  return request({
    url: '/reports/financial-report/actions/export',
    method: 'get',
    responseType: 'blob',
    params: {
      pd_code,
    },
  })
}
