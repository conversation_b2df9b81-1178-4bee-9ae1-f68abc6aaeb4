import request from '@/utils/request'

/**
 * 返回銀行賬目對沖統計
 * @param {string} ac_code 銀行會計賬目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function fetchBankLedgers({ ac_code, begin_date, end_date }) {
  return request({
    url: '/cycle/bank-ledgers',
    method: 'get',
    params: {
      ac_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 返回銀行賬目對沖統計
 * @param {string} ac_code 銀行會計賬目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function fetchAiBankLedgers({ ac_code, begin_date, ref, vc_rstatus, end_date }) {
  return request({
    url: '/ai/bank-ledgers',
    method: 'get',
    params: {
      ac_code,
      begin_date,
      ref,
      vc_rstatus,
      end_date,
    },
  })
}

/**
 * 獲取銀行賬目對沖統計(銀行AI對賬)建議結
 * @param {string} ac_code 銀行會計賬目編號
 * @param {string} begin_date 開始日期
 * @param {string} end_date 結束日期
 * @return {Promise}
 */
export function fetchSuggestions({
  ac_code,
  vc_rdate,
  show_already_selected,
  amount_dr,
  amount_cr,
  ref,
  select_ai_document_file_ledger_id,
}) {
  return request({
    url: '/ai/bank-ledgers/actions/suggestions',
    method: 'get',
    params: {
      ac_code,
      vc_rdate,
      show_already_selected,
      amount_dr,
      amount_cr,
      ref,
      select_ai_document_file_ledger_id,
    },
  })
}

/*
 * 返回上傳銀行月結單API
 */
export function uploadAPI({ account_id, file }) {
  const data = new FormData()
  data.append('account_id', account_id)
  data.append('file', file)
  return request({
    url: '/ai/document-files/actions/upload',
    method: 'post',
    data,
  })
}

/**
 * 更新銀行賬目對沖日期
 * @param {string} contra_json 對沖日期json,[{"fy_code":"18","lg_id":"1","vc_rstatus":"可傳E,D,U","vc_rdate":"2019-04-23"},...]
 * @return {Promise}
 */
export function editBankLedgers(contra_json) {
  return request({
    url: '/cycle/bank-ledgers/actions/update',
    method: 'post',
    data: {
      contra_json: JSON.stringify(contra_json),
    },
  })
}

/**
 * 獲取銀行賬目對沖日期
 * @param {string} ac_code
 * @return {Promise}
 */
export function getAiFiles({ ac_code, abandoned }) {
  return request({
    url: '/ai/document-files',
    method: 'GEt',
    params: {
      ac_code: ac_code,
      abandoned: abandoned,
    },
  })
}

/**
 * 獲取銀行賬目AI對賬文件
 * @param {string} ai_document_file_id
 * @return {Promise}
 */
export function getFileActions(ai_document_file_id) {
  return request({
    url: '/ai/document-files/actions/inquire',
    method: 'GEt',
    params: {
      ai_document_file_id: ai_document_file_id,
    },
  })
}

/**
 * 創建AI銀行月結單結果
 * @param {string} ai_document_file_id
 * @return {Promise}
 */
export function createFileLedgers({
  ai_document_file_id,
  ai_document_file_ledger_id,
  insert_type,
  ledger_date,
  particulars,
  cr_amount,
  dr_amount,
}) {
  return request({
    url: '/ai/document-file-ledgers/actions/create',
    method: 'POST',
    data: {
      ai_document_file_id,
      ai_document_file_ledger_id,
      insert_type,
      ledger_date,
      particulars,
      cr_amount,
      dr_amount,
    },
  })
}

/**
 *刪除AI銀行月結單結果
 * @param {string} ai_document_file_id
 * @return {Promise}
 */
export function deleteFIleLedgers(ai_document_file_ledger_id) {
  return request({
    url: '/ai/document-file-ledgers/actions/delete',
    method: 'POST',
    data: {
      ai_document_file_ledger_id,
    },
  })
}

/**
 *自動選擇AI銀行月結單結果
 * @param {string} ai_document_file_id
 * @return {Promise}
 */
export function autoSelectFileLedgers({ ac_code, begin_date, end_date }) {
  return request({
    url: '/ai/document-file-ledgers/actions/auto-select',
    method: 'POST',
    data: {
      ac_code,
      begin_date,
      end_date,
    },
  })
}

/**
 * 保存月結單結果
 * @param {string} ai_document_file_id
 * @return {Promise}
 */
export function saveBankLedgers({ fy_code, lg_id, ai_document_file_ledger_id }) {
  return request({
    url: '/ai/bank-ledgers/actions/save',
    method: 'POST',
    data: {
      fy_code,
      lg_id,
      ai_document_file_ledger_id,
    },
  })
}

/**
 * 獲取文件結果
 * @param {string} ai_document_file_id
 * @return {Promise}
 */
export function getDocumentFileLedger(ai_document_file_ledger_id) {
  return request({
    url: '/ai/document-file-ledgers/actions/inquire',
    method: 'GET',
    params: {
      ai_document_file_ledger_id,
    },
  })
}

/**
 * 更新銀行月結單結果
 * @param {string}
 * @return {Promise}
 */
export function updateAiLedger({
  particulars,
  ledger_date,
  ai_document_file_ledger_id,
  cr_amount,
  dr_amount,
}) {
  return request({
    url: '/ai/document-file-ledgers/actions/update',
    method: 'POST',
    data: {
      particulars,
      ledger_date,
      ai_document_file_ledger_id,
      cr_amount,
      dr_amount,
    },
  })
}

/**
 * 获取进行中和已完成的文件
 * @param {string}
 * @return {Promise}
 */
export function fetchDocumentFilesSummary({ channel, ac_code }) {
  return request({
    url: '/ai/document-files/actions/summary',
    method: 'GET',
    params: {
      channel,
      ac_code,
    },
  })
}

/**
 * 废弃文件
 * @param {string}
 * @return {Promise}
 */
export function abandonedDocumentFiles({ ai_document_file_id }) {
  return request({
    url: '/ai/document-files/actions/abandoned',
    method: 'POST',
    data: {
      ai_document_file_id,
    },
  })
}

export default {
  fetchBankLedgers,
  editBankLedgers,
}
