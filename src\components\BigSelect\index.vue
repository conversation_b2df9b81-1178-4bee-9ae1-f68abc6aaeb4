<template>
  <vxe-pulldown ref="xDown" class="big-select" transfer @hide-panel="onHidePanel">
    <template>
      <div @click="popoverClick">
        <el-input
          v-model="text"
          placeholder=""
          :disabled="disabled"
          readonly
          class="big-select-input"
          @focus="focusEvent"
        >
          <i
            slot="suffix"
            :class="visible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            @click="focusEvent"
          />
        </el-input>
      </div>
    </template>
    <template #dropdown>
      <vxe-list
        v-cloak
        ref="xList"
        auto-resize
        class="big-select-list-wrapper"
        max-height="200"
        :loading="loading"
        :data="list"
      >
        <template #default="{ items }">
          <div
            v-for="item in items"
            :key="item.value"
            :class="'big-select-list-item' + (currentValue === item.value ? ' selected' : '')"
            @click="changeItem(item)"
          >
            {{ item.label }}
          </div>
        </template>
      </vxe-list>
    </template>
  </vxe-pulldown>
</template>

<script>
export default {
  name: 'BigSelect',
  props: {
    list: {
      type: Array,
      required: true,
    },
    value: {
      type: [String, Number],
      default: '',
    },
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      visible: false,
      text: '',
    }
  },
  computed: {
    currentValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  watch: {
    value(val) {
      const item = this.list.find(i => i.value === val)
      if (item) {
        this.text = item.label
        this.currentValue = item.value
      } else {
        this.currentValue = ''
        this.text = ''
      }
    },
    visible(val) {
      if (val) {
        this.onShowPanel()
      }
    },
  },
  created() {},
  mounted() {
    const item = this.list.find(i => i.value === this.value)
    if (item) {
      this.text = item.label
      this.currentValue = item.value
    } else {
      this.currentValue = ''
      this.text = ''
    }
  },
  methods: {
    focusEvent() {
      if (this.disabled) return
      const index = this.list.findIndex(i => i.value === this.value)
      if (index !== -1) {
        this.$refs.xList.scrollTo(0, 22 * index)
      }
      this.$refs.xDown.showPanel()
      this.visible = true
    },
    changeItem(item) {
      this.text = item.label
      this.currentValue = item.value
      this.visible = false
      this.$refs.xDown.hidePanel()
      this.$emit('change')
    },
    onHidePanel() {
      this.visible = false
    },
    async onShowPanel() {
      this.$nextTick(() => {
        this.$refs.xList.refreshScroll()
        const index = this.list.findIndex(i => i.value === this.value)
        if (index !== -1) {
          this.$refs.xList.scrollTo(0, 22 * index - 22 * 3)
        }
        this.$refs.xList.recalculate()
      })
    },
    popoverClick() {
      this.$emit('popoverClick')
    },
  },
}
</script>

<style lang="scss" scoped></style>

<style lang="scss">
.big-select {
  .big-select-input {
    width: calc(100%);
    input {
      cursor: pointer;
    }
  }
}
.vxe-pulldown--panel.is--transfer {
  position: absolute;
  z-index: 1001;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 5px 0;
  top: -99999px;
  left: -99999px;
  .big-select-list-wrapper {
    pointer-events: none;
    &[v-cloak] {
      display: none;
    }
    background: #ffffff;
    .vxe-list--virtual-wrapper {
      pointer-events: auto;
      .vxe-list--body {
        padding: 6px 0;
        .big-select-list-item {
          cursor: pointer;
          padding: 0 20px;
          line-height: 22px;
          &.selected {
            color: #409eff;
            font-weight: 700;
          }
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}
.is-disabled {
  cursor: not-allowed;
  input {
    border-radius: 4px 0 0 4px !important;
  }
}
</style>
