<template>
  <div class="app-content">
    <LRPane v-if="!paneLoading" :left-view="leftView" v-bind="styleAttr">
      <!-- 篩選 -->
      <!--<div slot="pane-right-filters"/>-->
      <!-- 右上按鈕 -->
      <div slot="pane-right-action">
        <div>
          <!-- 新增按鈕 -->
          <div
            v-if="hasPermission_Add"
            :title="$t('btnTitle.add')"
            class="icon add"
            @click="onAddYear"
          >
            <svg-icon icon-class="add" class="action-icon" />
          </div>
        </div>
      </div>
      <!-- 右內容 -->
      <div slot="pane-right-content">
        <ETable
          :data="years"
          :style-columns="styleColumns"
          :lang-key="langKey"
          border
          @changeWidth="changeColumnWidth"
        >
          <!--          <template slot="columns">-->
          <!--            <el-table-column-->
          <!--              v-for="item in styleColumns"-->
          <!--              :key="item.ss_key"-->
          <!--              :label="$t(langKey + item.ss_key)"-->
          <!--              :align="item.alignment"-->
          <!--              :width="item.width"-->
          <!--              :property="item.ss_key"-->
          <!--              :column-key="item.ss_key"-->
          <!--            />-->
          <!--          </template>-->
          <template v-if="scope && scope.row" slot="actions" slot-scope="{ scope }">
            <div class="operation_icon">
              <i v-if="hasPermission_Edit" class="el-icon-edit" @click="onEdit(scope)" />
              <i
                v-if="scope.$index === years.length - 1 && hasPermission_Delete"
                class="el-icon-close"
                @click="onDelete(scope)"
              />
              <svg-icon v-else class="no-cursor" icon-class="" />
            </div>
          </template>
        </ETable>
      </div>
      <!-- 左內容 -->
      <addPage
        v-if="leftView === 'add' || leftView === 'edit'"
        :edit-year="editYear"
        @onCancel="onViewCancel"
      />
    </LRPane>
    <!-- 頁面設置 -->
    <customStyle
      :dialog-visible.sync="showDialog"
      :columns="tableColumns"
      :lang-key="langKey"
      :title="$t('style.defaultTitle')"
      @reloadStyleSheets="loadUserStyle"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LRPane from '@/views/layout/components/pane.vue'
import ETable from '@/components/ETable'
import customStyle from '@/views/customStyle/index.vue'
import addPage from './add'
import { deleteYear, fetchYears } from '@/api/master/years'
import mixinPermission from '@/views/mixins/permission'
import loadCustomStyle from '@/views/mixins/loadCustomStyle'

export default {
  name: 'MasterYearIndex',
  components: {
    LRPane,
    ETable,
    customStyle,
    addPage,
  },
  mixins: [mixinPermission, loadCustomStyle],
  data() {
    return {
      tableColumns: ['fy_code', 'fy_name'],
      langKey: 'master.year.label.',
      leftView: '',
      years: [],
      editYear: null,
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  created() {
    this.fetchData()
  },
  methods: {
    onAddYear() {
      this.editYear = null
      this.leftView = 'add'
    },
    onEdit(scope) {
      this.editYear = scope.row
      this.leftView = 'edit'
    },
    onDelete(scope) {
      this.$confirm(
        `${this.$t('confirm.deleteConfirm')}${this.$t('master.year.fy_name')}：${
          scope.row.fy_name
        }?`,
        this.$t('confirm.warningTitle'),
        {
          confirmButtonText: this.$t('confirm.confirmButtonText'),
          cancelButtonText: this.$t('confirm.cancelButtonText'),
          type: 'warning',
        },
      )
        .then(() => {
          const fy_id = scope.row.fy_id
          return new Promise((resolve, reject) => {
            deleteYear(fy_id)
              .then(res => {
                if (this.editYear && this.editYear.fy_id === fy_id) {
                  this.onViewCancel()
                }
                resolve(res)
              })
              .catch(err => {
                reject(err)
              })
          })
        })
        .then(() => {
          this.fetchData()
          this.$message({ type: 'success', message: this.$t('message.deleteSuccess') })
        })
    },
    fetchData() {
      fetchYears()
        .then(res => {
          this.years = res
        })
        .catch(() => {})
    },
    onViewCancel(update) {
      this.editYear = null
      this.leftView = null
      if (update) {
        this.fetchData()
      }
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-content {
  height: 100%;
}
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
.operation_icon {
  user-select: none;
}
</style>
<style rel="stylesheet/scss" lang="scss">
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}
</style>
