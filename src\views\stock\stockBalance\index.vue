<template>
  <!-- 篩選 -->
  <div class="app-container">
    <header v-if="false">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          {{ $t('router.settingScreenBasicSetting') }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ $t($route.meta.title) }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </header>
    <div class="filter">
      <el-form :inline="true" class="mini-form">
        <!-- 樣式 -->
        <el-form-item :label="$t('stock.style')">
          <el-select
            v-model="preferences.filters.selectedStyle"
            class="year"
            style="width: 85px"
            @change="onChangeStyle"
          >
            <el-option
              v-for="item in styleOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 會計週期 -->
        <el-form-item :label="$t('stock.years')">
          <el-select
            v-model="preferences.filters.selectedYear"
            class="year"
            style="width: 110px"
            @change="onChangeYear"
          >
            <el-option
              v-for="item in years"
              :key="item.fy_id"
              :label="item.fy_name"
              :value="item.fy_code"
            />
          </el-select>
        </el-form-item>

        <!-- 月份 -->
        <el-form-item v-if="preferences.filters.selectedStyle !== 'Y'" :label="$t('stock.months')">
          <el-select
            v-model="preferences.filters.selectedMonth"
            class="year"
            style="width: 100px"
            @change="reloadTable"
          >
            <!-- <el-option
            :label="allMonth.label"
            :value="allMonth.value"
            /> -->
            <el-option
              v-for="item in monthList"
              :key="item.pd_id"
              :label="item.pd_name"
              :value="item.pd_code"
            />
          </el-select>
        </el-form-item>

        <!-- 模式 -->
        <el-form-item :label="$t('stock.mode')">
          <el-select
            v-model="preferences.filters.selectedMode"
            class="year"
            style="width: 120px"
            @change="reloadTable"
          >
            <el-option
              v-for="item in modeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="actions-icon">
        <i
          v-if="hasPermission_Output"
          :title="$t('btnTitle.exportExcel')"
          class="edac-icon action-icon edac-icon-excel"
          @click="onExport"
        />
      </div>
    </div>
    <div class="balanceTable">
      <el-table :data="showData" :row-class-name="isStripe" height="100%" style="width: 100%">
        <!--   合併     -->
        <!--        :span-method="spanMethod"-->

        <el-table-column
          :formatter="formatCode"
          :label="$t('stock.stockBalance.label.sk_code')"
          prop="sk_code"
          width="100"
          fixed
          align="center"
        />
        <el-table-column
          :prop="language === 'en' ? 'name_en' : 'name_cn'"
          :label="$t('stock.stockBalance.label.name')"
          :formatter="formatName"
          width="200"
          fixed
          align="center"
        />
        <el-table-column :label="$t('stock.stockBalance.label.opening')" align="center">
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.quantity')"
            prop="bf_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.unitPrice')"
            prop="bf_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.amount')"
            prop="bf_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockBalance.label.purchase')" align="center">
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.quantity')"
            prop="in_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.unitPrice')"
            prop="in_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.amount')"
            prop="in_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockBalance.label.sale')" align="center">
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.quantity')"
            prop="out_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.out_sale_avg')"
            prop="out_sale_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.out_sale_amount')"
            prop="out_sale_amount"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.unitPrice')"
            prop="out_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.out_amount')"
            prop="out_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockBalance.label.balance')" align="center">
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.quantity')"
            prop="bal_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.unitPrice')"
            prop="bal_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.amount')"
            prop="bal_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockBalance.label.adjustment')" align="center">
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.adj_d_qty')"
            prop="adj_d_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.adj_u_qty')"
            prop="adj_u_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.adj_w_qty')"
            prop="adj_w_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.adj_amount')"
            prop="adj_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
        <el-table-column :label="$t('stock.stockBalance.label.finalBalance')" align="center">
          <el-table-column
            :formatter="formatQuantity"
            :label="$t('stock.stockBalance.label.quantity')"
            prop="fin_qty"
            width="60"
            align="center"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.unitPrice')"
            prop="fin_avg"
            width="100"
            header-align="center"
            align="right"
          />
          <el-table-column
            :formatter="formatAmount"
            :label="$t('stock.stockBalance.label.amount')"
            prop="fin_amount"
            width="100"
            header-align="center"
            align="right"
          />
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getStockBalance } from '@/api/stock/stockBalance'
import { fetchYears, getYear } from '@/api/master/years'
import loadPreferences from '@/views/mixins/loadPreferences'
import permission from '@/views/mixins/permission'

import { amountFormat } from '@/utils'
import { exportExcel } from '@/utils/excel'
import { stockBalanceExport } from '@/api/report/excel'

export default {
  name: 'StockStockBalanceIndex',
  mixins: [loadPreferences, permission],
  data() {
    return {
      years: '',
      tableData: [],
      monthList: [],
      preferences: {
        filters: {
          selectedYear: '',
          selectedMonth: '',
          selectedStyle: 'M',
          selectedMode: 'B',
        },
      },
      childPreferences: ['selectedMonth'],
    }
  },
  computed: {
    ...mapGetters(['language']),
    allMonth() {
      return {
        label: this.$t('stock.monthlySales.allMonth'),
        value: '',
      }
    },
    showData() {
      return this.tableData.filter(i => {
        if (i.type === 'group') {
          return true
        }
        const m = this.preferences.filters.selectedMode
        switch (m) {
          case 'A':
            if (i.type === 'detail') {
              return true
            }
            break
          case 'B':
            return true
          case 'C':
            if (i.type !== 'detail') {
              return true
            }
            break
          default:
            return false
        }
        return false
      })
    },

    styleOption() {
      return [
        {
          value: 'M',
          label: this.$t('stock.label.month'),
        },
        {
          value: 'Y',
          label: this.$t('stock.label.year'),
        },
        {
          value: 'T',
          label: this.$t('stock.label.to'),
        },
      ]
    },
    modeOption() {
      return [
        {
          value: 'A',
          label: this.$t('stock.stockBalance.label.breakdown'),
        },
        {
          value: 'B',
          label: this.$t('stock.stockBalance.label.breakdownAndSummary'),
        },
        {
          value: 'C',
          label: this.$t('stock.stockBalance.label.summary'),
        },
      ]
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },

  methods: {
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      if (!row.sk_code) {
        return 'count-row'
      } else if (row.type === 'stock' && this.preferences.filters.selectedMode === 'B') {
        return 'stock'
      }
    },
    formatName(row, column, cellValue, index) {
      if (row.type === 'group') {
        return cellValue + ' ' + this.$t('stock.stockBalance.label.total') + '：'
      }
      if (!row.show_name) return ''
      return cellValue
    },
    formatCode(row, column, cellValue, index) {
      if (!row.show_name) return ''
      return cellValue
    },
    formatAmount(row, column, cellValue, index) {
      if (typeof cellValue === 'string') {
        return cellValue
      } else if (cellValue == null) {
        return ''
      } else {
        return amountFormat(cellValue)
      }
    },
    formatQuantity(row, column, cellValue, index) {
      // return cellValue == null ? 0 : cellValue
      return cellValue
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (!row.sk_code) {
        // 組別 第一個單元格不顯示
        if (columnIndex === 0) {
          return { rowspan: 0, colspan: 0 }
        } else if (columnIndex === 1) {
          // 組別 第二個單元格占2列
          return { rowspan: 1, colspan: 2 }
        }
      } else if (row.type === 'detail') {
        // 詳細 合併同一類
        if ([0, 1].includes(columnIndex)) {
          // 編號和名稱
          if (row.show_name) {
            // 是否顯示（第一行才顯示）
            return { rowspan: row.row, colspan: 1 }
          } else {
            return { rowspan: 0, colspan: 0 }
          }
        }
      }
      // 正常
      return { rowspan: 1, colspan: 1 }
    },
    // conversionMonth(month) {
    //   return month.substr(2, 2) + '/' + '20' + month.substr(0, 2)
    // },
    getMonthList(val) {
      // 月份的名稱可以手動修改的，不能自己生成
      const item = this.years.find(i => i.fy_code === val)
      if (item) {
        return new Promise((resolve, reject) => {
          getYear(item.fy_id)
            .then(res => {
              const periods = res.periods
              this.monthList = periods
              const pd = periods.findIndex(
                i => i.pd_code === this.preferences.filters.selectedMonth,
              )
              if (pd === -1 && periods && res.periods.length) {
                this.preferences.filters.selectedMonth = periods[0].pd_code
              }
              resolve(res)
            })
            .catch(err => {
              reject(err)
            })
        })
      } else {
        return Promise.reject()
      }
    },
    formatData(data) {
      const newData = []

      for (let i = 0; i < data.length; i++) {
        // StockGroup
        const sg = data[i]
        const statistic = sg.statistic
        for (let j = 0; j < sg.stocks.length; j++) {
          // stocks
          const sk = sg.stocks[j]
          if (!sk || !sk.statistics || !sk.statistics.details) {
            continue
          }
          const statistics = sk.statistics
          // const hasChildren = statistics.details.length > 0
          const len = statistics.details.length
          for (let k = 0; k < len; k++) {
            // detail
            const item = sk.statistics.details[k]
            // 添加詳情
            newData.push({
              show_name: k === 0,
              row: len,
              type: 'detail',
              sk_code: sk.sk_code,
              name_cn: sk.sk_name_cn,
              name_en: sk.sk_name_en,

              ...item,
              // bf_qty: item.bf_qty,
              // bf_avg: item.bf_avg,
              // bf_amount: item.bf_amount,
              // in_qty: item.in_qty,
              // in_avg: item.in_avg,
              // in_amount: item.in_amount,
              // out_qty: item.out_qty,
              // out_sale_avg: item.out_sale_avg,
              // out_sale_amount: item.out_sale_amount,
              // out_avg: item.out_avg,
              // out_amount: item.out_amount,
              // bal_qty: item.bal_qty,
              // bal_avg: item.bal_avg,
              // bal_amount: item.bal_amount,
              // adj_d_qty: item.adj_d_qty,
              // adj_u_qty: item.adj_u_qty,
              // adj_w_qty: item.adj_w_qty,
              // adj_amount: item.adj_amount,
              // fin_qty: item.fin_qty,
              // fin_avg: item.fin_avg,
              // fin_amount: item.fin_amount
            })
          }
          // 添加 貨品總計
          newData.push({
            show_name: true,
            type: 'stock',
            sk_code: sk.sk_code,
            name_cn: sk.sk_name_cn,
            name_en: sk.sk_name_en,

            bf_qty: statistics.bf_qty,
            bf_avg: statistics.bf_avg,
            bf_amount: statistics.bf_amount,
            in_qty: statistics.in_qty,
            in_avg: statistics.in_avg,
            in_amount: statistics.in_amount,
            out_qty: statistics.out_qty,
            out_sale_avg: statistics.out_sale_avg,
            out_sale_amount: statistics.out_sale_amount,
            out_avg: statistics.out_avg,
            out_amount: statistics.out_amount,
            bal_qty: statistics.bal_qty,
            bal_avg: statistics.bal_avg,
            bal_amount: statistics.bal_amount,
            adj_d_qty: statistics.adj_d_qty,
            adj_u_qty: statistics.adj_u_qty,
            adj_w_qty: statistics.adj_w_qty,
            adj_amount: statistics.adj_amount,
            fin_qty: statistics.fin_qty,
            fin_avg: statistics.fin_avg,
            fin_amount: statistics.fin_amount,
          })
        }

        // 添加 類別總計
        newData.push({
          show_name: true,
          type: 'group',
          name_cn: sg.sg_name_cn,
          name_en: sg.sg_name_en,

          bf_qty: statistic.bf_qty,
          bf_avg: statistic.bf_avg,
          bf_amount: statistic.bf_amount,
          in_qty: statistic.in_qty,
          in_avg: statistic.in_avg,
          in_amount: statistic.in_amount,
          out_qty: statistic.out_qty,
          out_sale_avg: statistic.out_sale_avg,
          out_sale_amount: statistic.out_sale_amount,
          out_avg: statistic.out_avg,
          out_amount: statistic.out_amount,
          bal_qty: statistic.bal_qty,
          bal_avg: statistic.bal_avg,
          bal_amount: statistic.bal_amount,
          adj_d_qty: statistic.adj_d_qty,
          adj_u_qty: statistic.adj_u_qty,
          adj_w_qty: statistic.adj_w_qty,
          adj_amount: statistic.adj_amount,
          fin_qty: statistic.fin_qty,
          fin_avg: statistic.fin_avg,
          fin_amount: statistic.fin_amount,
        })
      }
      return newData
    },
    fetchData() {
      this.loading = false
      fetchYears()
        .then(res => {
          this.years = res
        })
        .then(this.loadUserPreference)
        .then(() => {
          if (this.years.length) {
            const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
            if (year) {
              return
            }
            this.preferences.filters.selectedYear = this.years[0].fy_code
          } else {
            return Promise.reject(this.$t('message.theYearDoNotExist'))
          }
        })
        .then(() => this.getMonthList(this.preferences.filters.selectedYear))
        .then(this.updateChildPreference)
        .then(() => {
          if (this.monthList.length) {
            const month = this.monthList.find(
              i => i.pd_code === this.preferences.filters.selectedMonth,
            )
            if (month) {
              return
            }
            this.preferences.filters.selectedMonth = this.monthList[0].pd_code
          } else {
            return Promise.reject(this.$t('message.theMonthDoNotExist'))
          }
        })
        .then(this.reloadTable)
        .finally(() => {
          this.loading = false
        })
    },
    fetchLedgerBudgets() {},
    reloadTable() {
      let date_mode, pd_code
      switch (this.preferences.filters.selectedStyle) {
        case 'M':
          date_mode = 'M'
          pd_code = this.preferences.filters.selectedMonth
          break
        case 'Y':
          date_mode = 'T'

          if (this.monthList.length <= 0) {
            return Promise.resolve()
          }
          pd_code = this.monthList[this.monthList.length - 1].pd_code
          break
        case 'T':
          date_mode = 'T'
          pd_code = this.preferences.filters.selectedMonth
          break
        default:
          return Promise.resolve()
      }
      return new Promise(resolve => {
        this.loading = true
        getStockBalance({
          pd_code,
          date_mode,
        })
          .then(res => {
            console.log('getStockBalance', res)
            this.tableData = this.formatData(res)
            console.log('tableData', this.tableData)
          })
          .finally(() => {
            this.loading = false
            resolve()
          })
      })
    },
    onChangeStyle(val) {
      this.reloadTable()
    },
    onChangeYear(val) {
      this.getMonthList(val).then(this.reloadTable)
    },
    /**
     * Button Export
     */
    onExport() {
      if (this.loading) {
        return
      }

      let date_mode, pd_code
      switch (this.preferences.filters.selectedStyle) {
        case 'M':
          date_mode = 'M'
          pd_code = this.preferences.filters.selectedMonth
          break
        case 'Y':
          date_mode = 'T'

          if (this.monthList.length <= 0) {
            return Promise.resolve()
          }
          pd_code = this.monthList[this.monthList.length - 1].pd_code
          break
        case 'T':
          date_mode = 'T'
          pd_code = this.preferences.filters.selectedMonth
          break
        default:
          return Promise.resolve()
      }
      const mode = this.preferences.filters.selectedMode
      if (!pd_code || !date_mode || !mode) {
        // this.$message.error('')
        return
      }
      this.loading = true
      stockBalanceExport({ pd_code, date_mode, mode })
        .then(exportExcel)
        .then(() => {
          this.$message.success(this.$t('file.exportSuccess'))
        })
        .catch(() => {
          this.$message.error(this.$t('file.exportError'))
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style>
/*.balanceTable .el-table th{*/
/*    background-color:#3E97DD !important;*/
/*}*/
</style>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  header {
    margin: 0 20px 20px 0;
  }
  .filter {
    width: 900px;
    margin: 5px 0;
    display: flex;
    text-align: center;
    /*align-items: center;*/
    /*justify-content: space-around*/
    span {
      line-height: 30px;
      height: 30px;
      color: gray;
      padding: 0 5px;
    }
    input {
      line-height: 30px;
      height: 30px;
    }
    /deep/ .el-input--medium .el-input__icon {
      line-height: 30px;
    }
  }
  /deep/ table {
    tbody {
      .cell {
        .el-input-number--medium {
          width: 100%;
        }
        .el-input {
          border-radius: 0;
        }
      }

      .count-row {
        td {
          background-color: #e2e2e2;
          color: #404246;
          &:first-child {
            text-align: right;
          }
        }
        .cell {
          @extend .count-row;
        }
      }
      .stock {
        td {
          background-color: #f5f5f5;
          color: #404246;
        }
        .cell {
          @extend .stock;
        }
      }
    }
  }

  .balanceTable {
    height: calc(100vh - 210px);
    .el-table {
      height: 100%;
    }
    /deep/ {
      .el-table__body-wrapper {
        height: 100%;
      }
    }
  }
}

.actions-icon {
  vertical-align: middle;
  margin-left: 20px;
  .edac-icon {
    font-size: 20px;
    vertical-align: middle;
    line-height: 30px;
  }
}
</style>
