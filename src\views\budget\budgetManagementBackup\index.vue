<template>
  <!-- 篩選 -->
  <div v-loading="loading" class="app-container">
    <split-pane
      :min-percent="30"
      :default-percent="horizontalPercent"
      split="horizontal"
      @resize="resize"
    >
      <template slot="paneL">
        <split-pane :min-percent="30" :default-percent="verticalPercent" split="vertical">
          <template slot="paneL">
            <page-left
              v-if="showLastYear"
              :years="years"
              :fy-code.sync="preferences.filters.selectedYear"
              :budget-group-list="budgetGroupList"
              :budget-id.sync="preferences.filters.selectedGroupId"
              :data="lastTableData"
              :summary="lastSummary"
              :fold="fold"
              @changeBudgetGroup="onChangeBudgetGroup"
              @changeYear="onChangeYear"
              @onFold="onFold(true)"
              @onUnfold="onFold(false)"
            />
          </template>
          <template slot="paneR">
            <page-right
              :fy-code.sync="preferences.filters.selectedYear"
              :budget-group="currentBudgetGroup"
              :data="thisTableData"
              :summary="thisSummary"
              :tree-data="thisTableData"
              @handleCopyComposition="handleCopyComposition"
              @handleCleanComposition="handleCleanComposition"
              @reloadThis="loadThisData"
              @handleUp="handleUp"
              @handleDown="handleDown"
              @handleEdit="handleEdit"
              @handleCancel="handleCancel"
              @handleUpdate="handleUpdate"
              @handleAdd="handleAdd"
            />
          </template>
        </split-pane>
      </template>
      <template slot="paneR">
        <page-bottom
          ref="bottomPage"
          :years="years"
          :fy-code.sync="preferences.filters.bottomYear"
          :parent-budget-id.sync="preferences.filters.selectedGroupId"
          :budget-tree="bottomBudgetTree"
          :parent-budget="bottomBudgetGroup"
          :selected-budget-id.sync="preferences.filters.bottomBudgetId"
          :refresh.sync="bottomRefresh"
          @changeBudget="onChangeBottomBudget"
          @changeYear="handleChangeBottomYear"
        />
      </template>
    </split-pane>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { fetchYears } from '@/api/master/years'
import {
  copyBudgetTree,
  cleanBudgetTree,
  editBudgetSeq,
  editBudget,
  createBudget,
} from '@/api/budget'
import loadPreferences from '@/views/mixins/loadPreferences'
import splitPane from 'vue-splitpane'
import pageLeft from './left'
import pageRight from './right'
import pageBottom from './bottom'

import { amountFormat, convertTreeToList, toDecimal } from '@/utils'
import { fetchBudgetTree, getBudgetByCode } from '@/api/budget'
import {
  formatBudgetData,
  getFirstItem,
  getTreeBudgetByCode,
  getTreeBudgetById,
} from '@/views/budget/common/utils'

export default {
  name: 'BudgetListIndex',
  components: {
    splitPane,
    pageLeft,
    pageRight,
    pageBottom,
  },
  mixins: [loadPreferences],
  data() {
    return {
      loading: false,
      years: [],
      tableData: [],
      stockList: [],
      preferences: {
        filters: {
          selectedYear: '',
          inquireYear: '',

          selectedGroupId: '',
          selectedStage: '',
          selectedManager: '',
          selectedApprove: '',

          // bottom
          bottomYear: '',
          bottomBudgetId: '',
        },
      },
      childPreferences: ['selectedCode'],
      langKey: 'budget.budgetList.label.',
      budgetGroupList: [],
      lastTableData: [],
      lastSummary: {},
      thisTableData: [],
      thisSummary: {},
      horizontalPercent: 50,
      verticalPercent: 50,
      showLastYear: true,

      // bottom
      bottomRefresh: false, // true 刷新列表,
      bottomBudgetTree: [],
      currentBudgetGroup: {},
      bottomBudgetGroup: {},

      fold: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'styles']),
    stageOptions() {
      const s = ['X', 'S', 'A', 'O', 'C', 'R', 'T', 'K', 'B', 'D']
      return s.map(key => {
        return {
          label: this.$t('budget.budgetSet.label.stage_' + key.toLowerCase()),
          value: key,
        }
      })
    },
  },
  watch: {},
  created() {
    this.fetchData()
    this.saveUserLastPage()
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    deepCloneBudget(budgetTreeArray, filter = () => true) {
      const newArray = []
      budgetTreeArray.forEach(item => {
        const newItem = Object.assign({}, item)
        if (item.children) {
          newItem.children = this.deepCloneBudget(item.children, filter)
        }
        if (filter(newItem)) {
          newArray.push(newItem)
        }
      })
      return newArray
    },
    scrollEvent() {
      this.dataTableScrollEle.scrollLeft = this.summaryTableScrollEle.scrollLeft
    },
    /**
     * Table斑馬紋
     */
    isStripe({ row }) {
      // if (!row.date) {
      //   return 'count-row'
      // }
    },
    /**
     * 類型格式轉換
     */
    typeFormat(row, column) {
      console.log(row.type)
      switch (row.type) {
        case 'S':
          return this.$t('stock.label.sales')
        case 'D':
          return this.$t('stock.label.damagedOrLost')
        case 'U':
          return this.$t('stock.label.internalUse')
        case 'W':
          return this.$t('stock.label.writeOff')
        case 'P':
          return this.$t('stock.label.purchase')
        default:
          return ''
      }
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === this.tableData.length - 1) {
        if (columnIndex < 2 || columnIndex === 3) {
          return { rowspan: 0, colspan: 0 }
        } else if (columnIndex === 2) {
          return { rowspan: 1, colspan: 4 }
        }
      }
      return { rowspan: 1, colspan: 1 }
    },
    // fetchData() {
    //   this.loading = false
    //   fetchYears()
    //     .then(res => {
    //       this.years = res
    //     })
    //     .then(this.loadUserPreference)
    //     .then(() => {
    //       if (this.years.length) {
    //         const month = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
    //         if (month) {
    //           return
    //         }
    //         this.preferences.filters.selectedYear = this.years[0].fy_code
    //       } else {
    //         return Promise.reject(this.$t('message.theYearDoNotExist'))
    //       }
    //     })
    //     .then(() => {
    //       // 類別
    //       const fy_code = this.preferences.filters.selectedYear
    //       const type = 'G'
    //       return fetchBudgetTree({ fy_code, type })
    //     })
    //     .then(res => {
    //       this.budgetGroupList = res
    //       this.currentBudgetGroup = getTreeBudgetById(res, this.preferences.filters.selectedGroupId)
    //       // 上年
    //       const yearIndex = this.years.findIndex(i => i.fy_code === this.preferences.filters.selectedYear)
    //       if (yearIndex > 0) {
    //         const fy_code = this.years[yearIndex - 1].fy_code
    //         const parent_budget_id = this.preferences.filters.selectedGroupId
    //         return fetchBudgetTree({ fy_code, parent_budget_id })
    //       } else {
    //         return { data: [], summary: {}}
    //       }
    //     })
    //     .then(res => {
    //       const { data, summary } = formatBudgetData(res)
    //       this.lastTableData = data
    //       this.lastSummary = summary
    //       // 本年
    //       const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
    //       if (year) {
    //         const fy_code = year.fy_code
    //         const parent_budget_id = this.preferences.filters.selectedGroupId
    //         return fetchBudgetTree({ fy_code, parent_budget_id })
    //       } else {
    //         return []
    //       }
    //     })
    //     .then(res => {
    //       // this.thisTableData = res
    //       const { data, summary } = formatBudgetData(res)
    //       this.thisTableData = data
    //       this.thisSummary = summary
    //     })
    //     .then(() => this.handleChangeBottomYear(this.preferences.filters.bottomYear))
    //     .finally(() => {
    //       this.loading = false
    //     })
    // },
    showError(msgKey) {
      const langKey = 'budget.budgetManagement.message.'
      this.$message.error(this.$t(langKey + msgKey))
    },
    async fetchData() {
      this.loading = true
      // 所有年份
      try {
        this.years = await fetchYears()
      } catch (e) {
        return
      }
      // 用戶配置
      await this.loadUserPreference()

      // 設置當前年份
      if (this.years.length) {
        const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
        if (!year) {
          this.preferences.filters.selectedYear = this.years[0].fy_code
        }
      } else {
        // this.showError('noYear')
        return
      }
      // 獲取預算群組
      const fy_code = this.preferences.filters.selectedYear
      const gType = 'G'
      try {
        const budgetGroupList = await fetchBudgetTree({ fy_code, type: gType })
        this.budgetGroupList = budgetGroupList
        // 當前選中的群組
        this.currentBudgetGroup = getTreeBudgetById(
          budgetGroupList,
          this.preferences.filters.selectedGroupId,
        )
        this.bottomBudgetGroup = this.currentBudgetGroup
        // this.preferences.filters.selectedGroupId = ''
        // this.setBottomFirstBudget()
        this.handleChangeBottomYear(this.preferences.filters.bottomYear, false)
        // this.$refs['bottomPage'].fetchData()
      } catch (e) {
        // this.showError('loadingPageDataFailed')
        return
      }

      // 上年
      this.loadLastData()
      // try {
      //   const yearIndex = this.years.findIndex(i => i.fy_code === this.preferences.filters.selectedYear)
      //   if (yearIndex > 0 && this.currentBudgetGroup) {
      //     // 獲取上年數據
      //     const fy_code = this.years[yearIndex - 1].fy_code
      //     const budget_code = this.currentBudgetGroup.budget_code
      //     let lastYearHasCode
      //     let lastGroup
      //     try {
      //       lastGroup = await getBudgetByCode({ fy_code, budget_code })
      //       lastYearHasCode = true
      //     } catch (e) {
      //       lastYearHasCode = false
      //     }
      //     if (lastYearHasCode) {
      //       const parent_budget_id = lastGroup.budget_id
      //       const lastData = await fetchBudgetTree({ fy_code, parent_budget_id })
      //       // 格式化
      //       const { data, summary } = formatBudgetData(lastData)
      //       this.lastTableData = data
      //       this.lastSummary = summary
      //     } else {
      //       // 無上年數據
      //       this.lastTableData = []
      //       this.lastSummary = {}
      //     }
      //   } else {
      //     // 無上年數據
      //     this.lastTableData = []
      //     this.lastSummary = {}
      //   }
      // } catch (e) {
      //   this.showError('loadingLastYearDataFailed')
      // }
      //   // 本年
      this.loadThisData()
      // try {
      //   const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
      //   if (year) {
      //     const fy_code = year.fy_code
      //     const parent_budget_id = this.preferences.filters.selectedGroupId
      //     const thisData = await fetchBudgetTree({ fy_code, parent_budget_id })
      //     // 本年數據
      //     const { data, summary } = formatBudgetData(thisData)
      //     this.thisTableData = data
      //     this.thisSummary = summary
      //   } else {
      //     // 無本年數據
      //     this.thisTableData = []
      //     this.thisSummary = {}
      //   }
      // } catch (e) {
      //   this.showError('loadingThisYearDataFailed')
      // }
      this.loading = false
    },
    async loadLastData() {
      // return new Promise((resolve, reject) => {
      // 上年
      // const yearIndex = this.years.findIndex(i => i.fy_code === this.preferences.filters.selectedYear)
      // if (yearIndex > 0) {
      //   const fy_code = this.years[yearIndex - 1].fy_code
      //   const parent_budget_id = this.currentBudgetGroup.budget_id // this.preferences.filters.selectedGroupId
      //   const budget_code = this.currentBudgetGroup.budget_code
      //   return new Promise((resolve1, reject1) => {
      //     let newYearHasCode = false
      //     getBudgetByCode({ fy_code, budget_code })
      //       .then(res => {
      //         newYearHasCode = true
      //       })
      //       .finally(() => {
      //         if (newYearHasCode) {
      //           fetchBudgetTree({ fy_code, parent_budget_id })
      //             .then(res => {
      //               resolve(res)
      //             })
      //             .catch(err => {
      //               reject(err)
      //             })
      //         } else {
      //           resolve({ data: [], summary: {}})
      //         }
      //       })
      //   })
      // } else {
      //   return { data: [], summary: {}}
      // }
      // })
      // 上年
      try {
        const yearIndex = this.years.findIndex(
          i => i.fy_code === this.preferences.filters.selectedYear,
        )
        if (yearIndex > 0 && this.currentBudgetGroup) {
          // 獲取上年數據
          const fy_code = this.years[yearIndex - 1].fy_code
          const budget_code = this.currentBudgetGroup.budget_code
          let lastYearHasCode
          let lastGroup
          try {
            lastGroup = await getBudgetByCode({ fy_code, budget_code })
            lastYearHasCode = true
          } catch (e) {
            lastYearHasCode = false
          }
          if (lastYearHasCode) {
            const parent_budget_id = lastGroup.budget_id
            const res = await fetchBudgetTree({ fy_code, parent_budget_id })
            // 格式化
            const lastData = convertTreeToList(res, 0, false)
            const { data, summary } = formatBudgetData(lastData)
            this.lastTableData = data
            this.lastSummary = summary
          } else {
            // 無上年數據
            this.lastTableData = []
            this.lastSummary = {}
          }
        } else {
          // 無上年數據
          this.lastTableData = []
          this.lastSummary = {}
        }
      } catch (e) {
        this.showError('loadingLastYearDataFailed')
      }
    },
    handleReload(position) {
      if (position === 2) {
        this.loadThisData()
      }
    },
    async loadThisData() {
      // return new Promise((resolve, reject) => {
      //   // 本年
      //   const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
      //   if (year) {
      //     const fy_code = year.fy_code
      //     const parent_budget_id = this.preferences.filters.selectedGroupId
      //     resolve(fetchBudgetTree({ fy_code, parent_budget_id }))
      //   } else {
      //     resolve({ data: [], summary: {}})
      //   }
      // })
      try {
        // 本年
        const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
        if (year) {
          const fy_code = year.fy_code
          const parent_budget_id = this.preferences.filters.selectedGroupId
          const res = await fetchBudgetTree({ fy_code, parent_budget_id })
          // 本年數據
          // const thisData = convertTreeToList(res, 0, false)
          const { data, summary } = formatBudgetData(res)
          this.thisTableData = data
          this.thisSummary = summary
        } else {
          // 無本年數據
          this.thisTableData = []
          this.thisSummary = {}
        }
      } catch (e) {
        this.showError('loadingThisYearDataFailed')
      }
    },
    reloadData() {
      // 上年
      this.loadLastData()
      // .then(res => {
      //   const { data, summary } = formatBudgetData(res)
      //   this.lastTableData = data
      //   this.lastSummary = summary
      // })
      // .then(() => {
      //   // 本年
      //   const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
      //   if (year) {
      //     const fy_code = year.fy_code
      //     const parent_budget_id = this.preferences.filters.selectedGroupId
      //     return fetchBudgetTree({ fy_code, parent_budget_id })
      //   } else {
      //     return { data: [], summary: {}}
      //   }
      // })
      // .then(res => {
      //   const { data, summary } = formatBudgetData(res)
      //   this.thisTableData = data
      //   this.thisSummary = summary
      // })

      // 本年
      this.loadThisData()
      // .then(res => {
      //   const { data, summary } = formatBudgetData(res)
      //   this.thisTableData = data
      //   this.thisSummary = summary
      // })
      // const fy_code = this.preferences.filters.selectedYear
      // const parent_budget_id = this.preferences.filters.selectedGroupId || undefined
      // const budget_stage = undefined
      // this.loading = true
      // return new Promise((resolve, reject) => {
      //   fetchBudgetTree({
      //     fy_code,
      //     parent_budget_id,
      //     budget_stage
      //   })
      //     .then(res => {
      //       const { data, summary } = formatBudgetData(res)
      //       this.lastTableData = data
      //       this.lastSummary = summary
      //
      //       // 本年
      //       const year = this.years.find(i => i.fy_code === this.preferences.filters.selectedYear)
      //       if (year) {
      //         const fy_code = year.fy_code
      //         const parent_budget_id = this.preferences.filters.selectedGroupId
      //         return fetchBudgetTree({ fy_code, parent_budget_id })
      //       } else {
      //         return []
      //       }
      //     })
      //     .then(res => {
      //       const { data, summary } = formatBudgetData(res)
      //       this.thisTableData = data
      //       this.thisSummary = summary
      //     })
      //     .catch(err => {
      //       reject(err)
      //     }).finally(() => {
      //       this.loading = false
      //     })
      // })
    },
    formatData(data) {
      const newData = []
      const sum = {
        this_budget_IE: '',
        this_I_total_proposed_amount: 0,
        this_I_total_approved_amount: 0,
        this_I_actual_amount: 0,
        this_I_balance: 0,
        this_E_total_proposed_amount: 0,
        this_E_total_approved_amount: 0,
        this_E_actual_amount: 0,
        this_E_balance: 0,
        last_budget_IE: '',
        last_I_total_budget_amount: 0,
        last_I_actual_amount: 0,
        last_I_balance: 0,
        last_E_total_budget_amount: 0,
        last_E_actual_amount: 0,
        last_E_balance: 0,
        actual_amount: 0,

        proposed_amount: 0,
        approved_amount: 0,
        budget_amount: 0,
      }
      let thisI = false
      let lastI = false
      let thisE = false
      let lastE = false
      const cols = [
        'this_I_total_proposed_amount',
        'this_I_total_approved_amount',
        'this_I_actual_amount',
        'this_E_total_proposed_amount',
        'this_E_total_approved_amount',
        'this_E_actual_amount',
        'last_I_total_budget_amount',
        'last_I_actual_amount',
        'last_E_total_budget_amount',
        'last_E_actual_amount',

        'proposed_amount',
        'approved_amount',
        'budget_amount',
        'actual_amount',
      ]
      const balanceCols = {
        this_I_balance: ['this_I_total_proposed_amount', 'this_I_total_approved_amount'],
        this_E_balance: ['this_E_total_proposed_amount', 'this_E_total_approved_amount'],
        last_I_balance: ['this_I_total_approved_amount', 'last_I_actual_amount'],
        last_E_balance: ['this_E_total_approved_amount', 'last_E_actual_amount'],
      }

      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        const newItem = Object.assign({}, item)
        newData.push(newItem)
        cols.forEach(key => {
          sum[key] = this.add(sum[key], item[key])
        })
        for (const key in balanceCols) {
          const l = balanceCols[key][0]
          const r = balanceCols[key][1]
          const balance = toDecimal(sum[key] + (newItem[l] - newItem[r]))
          newItem[key] = balance
          sum[key] = balance
        }
        if (!thisI && 'IB'.includes(newItem.this_budget_IE)) {
          thisI = true
        }
        if (!thisE && 'EB'.includes(newItem.this_budget_IE)) {
          thisE = true
        }
        if (!lastI && 'IB'.includes(newItem.last_budget_IE)) {
          lastI = true
        }
        if (!lastE && 'EB'.includes(newItem.last_budget_IE)) {
          lastE = true
        }
      }
      if (thisI && thisE) {
        sum.this_budget_IE = 'B'
      } else if (thisI) {
        sum.this_budget_IE = 'I'
      } else {
        sum.this_budget_IE = 'E'
      }
      if (lastI && lastE) {
        sum.last_budget_IE = 'B'
      } else if (lastI) {
        sum.last_budget_IE = 'I'
      } else {
        sum.last_budget_IE = 'E'
      }
      return {
        data: newData,
        summary: sum,
      }
    },
    add(a, b) {
      const a1 = Number(a)
      const a2 = isNaN(a1) ? 0 : a1
      const b1 = Number(b)
      const b2 = isNaN(b1) ? 0 : b1
      return toDecimal(a2 + b2)
    },
    setFirstBudget(tree) {
      const budget = getFirstItem(tree, item => item.budget_type !== 'F')
      if (budget) {
        this.preferences.filters.selectedGroupId = budget.budget_id
        return true
      } else {
        this.preferences.filters.selectedGroupId = ''
        return false
      }
    },
    setBottomFirstBudget(tree) {
      const budget = getFirstItem(tree, item => item.budget_type !== 'F')
      if (budget) {
        this.preferences.filters.bottomBudgetId = budget.budget_id
        return true
      } else {
        this.preferences.filters.bottomBudgetId = ''
        return false
      }
    },
    languageKey(key) {
      return key + (this.language === 'en' ? 'en' : 'cn')
    },
    formatterByStage(row, column, cellValue, index) {
      const stage = this.stageOptions.find(i => i.value === cellValue)
      return stage ? stage.label : ''
    },
    amountFormat: amountFormat,
    summarySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3,
        }
      } else if (columnIndex < 3) {
        return {
          rowspan: 0,
          colspan: 0,
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1,
        }
      }
    },
    resize() {},
    async handleChangeBottomYear(val, changeGroup) {
      const fy_code = val // this.preferences.filters.bottomYear
      const parent_budget_id = this.preferences.filters.selectedGroupId
      if (!val || !parent_budget_id) {
        this.bottomBudgetGroup = {}
        return Promise.reject()
      }
      let oldItem
      if (changeGroup) {
        const budget = getTreeBudgetById(
          this.budgetGroupList,
          this.preferences.filters.selectedGroupId,
        )
        if (budget) {
          this.bottomBudgetGroup = budget
        }
      } else {
        this.bottomBudgetGroup = {}
        oldItem = getTreeBudgetById(this.bottomBudgetTree, this.preferences.filters.bottomBudgetId)
      }
      const budget_code =
        (this.bottomBudgetGroup && this.bottomBudgetGroup.budget_code) || undefined
      let newYearHasCode = false
      if (budget_code) {
        try {
          await getBudgetByCode({ fy_code, budget_code })
          newYearHasCode = true
        } catch (e) {
          //
        }
      }

      try {
        const res = await fetchBudgetTree({
          fy_code,
          parent_budget_id: newYearHasCode ? parent_budget_id : undefined,
        })
        this.bottomBudgetTree = res
        if (changeGroup) {
          this.preferences.filters.bottomBudgetId =
            (this.bottomBudgetGroup && this.currentBudgetGroup.budget_id) || ''
        } else {
          if (oldItem && oldItem.budget_code) {
            const thisBudget = getTreeBudgetByCode(res, oldItem.budget_code)
            if (thisBudget) {
              this.preferences.filters.bottomBudgetId = thisBudget.budget_id
              return
            }
          }
          const hasBudget = this.setBottomFirstBudget(res)

          if (!hasBudget) {
            return Promise.reject()
          } else {
            this.$nextTick(() => {
              this.$refs['bottomPage'].fetchData()
            })
          }
        }
      } catch (e) {
        //
      }

      // return new Promise((resolve, reject) => {
      //   const budget_code = this.bottomBudgetGroup && this.bottomBudgetGroup.budget_code || ''
      //   if (!budget_code) {
      //     this.bottomBudgetTree = []
      //     reject()
      //     return
      //   }
      //   let newYearHasCode = false
      //   getBudgetByCode({ fy_code, budget_code })
      //     .then(() => {
      //       newYearHasCode = true
      //     })
      //     .finally(() => {
      //       fetchBudgetTree({ fy_code, parent_budget_id: newYearHasCode ? parent_budget_id : undefined })
      //         .then(res => {
      //           this.bottomBudgetTree = res
      //           if (changeGroup) {
      //             this.preferences.filters.bottomBudgetId = this.bottomBudgetGroup && this.currentBudgetGroup.budget_id || ''
      //           } else {
      //             if (oldItem && oldItem.budget_code) {
      //               const thisBudget = getTreeBudgetByCode(res, oldItem.budget_code)
      //               if (thisBudget) {
      //                 this.preferences.filters.bottomBudgetId = thisBudget.budget_id
      //                 return
      //               }
      //             }
      //             const hasBudget = this.setBottomFirstBudget(res)
      //             if (!hasBudget) {
      //               return Promise.reject()
      //             }
      //           }
      //         })
      //         .then(() => {
      //           this.bottomRefresh = true
      //         })
      //         .catch(err => {
      //           reject(err)
      //         })
      //     })
      // })
    },
    onChangeBottomBudget(val) {},

    // *******************  聯動 *******************//
    /*
      | update  |           load              |
      |   Y1    |  B1 |  Y2 |  B2 |  T1 |  T2 |
      |   B1    |     |  Y2 |  B2 |  T1 |  T2 |
      |   Y2    |     |     |  B2 |     |  T2 |
      |   B2    |     |     |     |     |  T2 |
      |         |     |     |     |     |     |
    */
    /*          一. Y1
        1. B1: 重設轉賬budget_id
        2. Y2: 同步Y1 fy_code
        3. B2: 同步B2 budget_id
        4. T1：刷新
        5. T2: 刷新

     */
    onChangeYear(val) {
      const year = this.years.find(i => i.fy_code === val)
      if (year) {
        const type = 'G'
        // 獲取當前選中的BudgetGroup
        const oldItem = getTreeBudgetById(
          this.budgetGroupList,
          this.preferences.filters.selectedGroupId,
        )
        fetchBudgetTree({ fy_code: year.fy_code, type })
          .then(res => {
            // set B1
            this.budgetGroupList = res
            if (oldItem) {
              const thisBudget = getTreeBudgetByCode(res, oldItem.budget_code)
              if (thisBudget) {
                this.preferences.filters.selectedGroupId = thisBudget.budget_id
                this.currentBudgetGroup = Object.assign({}, thisBudget)
                return
              }
            }
            const hasBudget = this.setFirstBudget(res)
            if (!hasBudget) {
              return Promise.reject()
            }
          })
          .then(() => {
            // set B2
            this.preferences.filters.bottomBudgetId = this.preferences.filters.selectedGroupId
            // set Y2 異步 => T2
            this.preferences.filters.bottomYear = year.fy_code
            this.handleChangeBottomYear(year.fy_code, true)
          })
          .then(this.reloadData)
          .catch(() => {
            this.lastTableData = []
            this.lastSummary = {}
          })
      }
    },
    /*          二. B1
        2. Y2: 同步Y1 fy_code
        3. B2: 同步B2 budget_id
        4. T1：刷新
        5. T2: 刷新

     */
    onChangeBudgetGroup(val) {
      if (val) {
        // set B2
        this.currentBudgetGroup = val
        this.bottomBudgetGroup = val
        this.preferences.filters.bottomBudgetId = val.budget_id

        // set Y2 => T2
        this.preferences.filters.bottomYear = this.preferences.filters.selectedYear
        this.handleChangeBottomYear(this.preferences.filters.bottomYear, true)
      } else {
        this.currentBudgetGroup = {}
        this.bottomBudgetGroup = {}
        this.preferences.filters.bottomBudgetId = ''
      }
      this.reloadData()
    },

    /* 複製結構 */
    async handleCopyComposition(mode) {
      const fy_code = this.preferences.filters.selectedYear
      const budget_id = this.preferences.filters.selectedGroupId
      try {
        await copyBudgetTree({ fy_code, budget_id, mode })
        this.loadThisData()
        this.$message.success(this.$t('copySuccess'))
      } catch (e) {
        // this.$message.error('複製失敗')
      }
    },
    /* 清除結構 */
    async handleCleanComposition(mode) {
      const fy_code = this.preferences.filters.selectedYear
      const budget_id = this.preferences.filters.selectedGroupId
      try {
        await cleanBudgetTree({ fy_code, budget_id, mode })
        this.loadThisData()
        this.$message.success(this.$t('message.clearSuccess'))
      } catch (e) {
        // this.$message.error('清除失敗')
      }
    },
    /* 調整位置 */
    async handleChangeSeq(budget_id, seq) {
      const fy_code = this.preferences.filters.selectedYear
      try {
        await editBudgetSeq({ fy_code, budget_id, seq })
        this.loadThisData()
      } catch (e) {
        //
      }
    },
    handleUp(scope) {
      const budget_id = scope.row.budget_id
      const seq = scope.row.seq - 1
      this.handleChangeSeq(budget_id, seq)
    },
    handleDown(scope) {
      const budget_id = scope.row.budget_id
      const seq = scope.row.seq + 2
      this.handleChangeSeq(budget_id, seq)
    },
    async handleAdd(scope, budget_type) {
      const group = getTreeBudgetById(
        this.budgetGroupList,
        this.preferences.filters.selectedGroupId,
      )
      const data = {
        fy_code: this.preferences.filters.selectedYear,
        budget_type,
        budget_code: '',
        budget_IE: group.budget_IE,
        name_cn: '',
        name_en: '',
        budget_stage: group.budget_stage,
        proposed_amount: 0,
        approved_amount: 0,
        last_proposed_amount: 0,
        last_approved_amount: 0,
        budget_active: 'Y',
        seq: 99,
        edit: true,
        temp_id: Date.now(),
      }
      const list = this.deepCloneBudget(this.thisTableData)
      if (scope && scope.row) {
        // 有上級
        data.parent_budget_id = scope.row.budget_id
        const budget = getTreeBudgetById(list, scope.row.budget_id)
        if (budget.children && Array.isArray(budget.children)) {
          budget.children.push(data)
        } else {
          budget.children = [data]
        }
      } else {
        data.parent_budget_id = group.budget_id
        // 無上級
        list.push(data)
      }
      try {
        this.thisTableData = list

        // await createBudget(data)
        // this.$message.success(this.$t('message.success'))
        // this.loadThisData()
      } catch (e) {
        //
      }
    },
    changeEditState(scope, state) {
      if (scope.row.budget_id) {
        const list = this.deepCloneBudget(this.thisTableData)
        // const index = list.findIndex(i => i.budget_id === scope.row.budget_id)
        const budget = getTreeBudgetById(list, scope.row.budget_id)
        budget.edit = state
        // this.$set(list[index], 'edit', true)
        this.thisTableData = list
      } else {
        const list = this.deepCloneBudget(
          this.thisTableData,
          i => !i.temp_id || i.temp_id !== scope.row.temp_id,
        )
        this.thisTableData = list
      }
    },
    handleEdit(scope) {
      this.changeEditState(scope, true)
    },
    handleCancel(scope) {
      this.changeEditState(scope, false)
    },
    async handleUpdate(scope) {
      // const data = {
      //   fy_code: this.fyCode,
      //   budget_id: scope.row.budget_id,
      //   budget_code: scope.row.budget_code,
      //   budget_IE: scope.row.budget_IE,
      //   name_cn: scope.row.name_cn,
      //   name_en: scope.row.name_en,
      //   budget_active: scope.row.budget_active,
      //   seq: scope.row.seq
      // }
      const list = this.deepCloneBudget(this.thisTableData)
      // const index = list.findIndex(i => i.budget_id === scope.row.budget_id)
      const budget = getTreeBudgetById(list, scope.row.budget_id)
      if (budget) {
        const data = Object.assign({}, budget)
        data.fy_code = this.preferences.filters.selectedYear
        budget.name_en =
          budget.name_cn =
          data.name_en =
          data.name_cn =
            data['name_' + (this.language === 'en' ? 'en' : 'cn')]

        try {
          if (data.budget_id) {
            await editBudget(data)
          } else {
            await createBudget(data)
          }
          this.$message.success(this.$t('message.success'))
          // this.$emit('reloadThis')
          // this.$set(scope.row, 'edit', false)
          // this.$set(list[index], 'edit', false)
          budget.edit = false
          this.thisTableData = list
          this.loadThisData()
        } catch (e) {
          //
        }
      }
    },
    onFold(val) {
      this.fold = val
      if (val) {
        this.verticalPercent = 10
      } else {
        this.verticalPercent = 50
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.table-stripe th,
.table-stripe td {
  background: rgb(232, 245, 254);
}

.app-container {
  height: 100%;
  padding: 0;
}

/deep/ {
  .splitter-pane.splitter-paneL.vertical,
  .splitter-pane-resizer.vertical {
    transition: all 0.1s ease 0s;
  }
  .splitter-pane.splitter-paneR.vertical {
    transition: all 0.1s ease 0s;
  }
}
</style>
