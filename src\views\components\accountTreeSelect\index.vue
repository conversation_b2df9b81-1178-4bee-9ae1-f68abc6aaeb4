<template>
  <div>
    <el-select
      v-model="fund_id"
      :style="{
        width: fundWidth + 'px',
      }"
      class="fund"
      @change="onChangeFund"
    >
      <!--          @change="changeFund"-->
      <el-option :label="$t('filters.all')" value="" />
      <el-option
        v-for="item in fundList"
        :key="item.fund_id"
        :label="language === 'en' ? item.fund_name_en : item.fund_name_cn"
        :value="item.fund_id"
      />
    </el-select>
    <SelectTree
      v-model="ac_code"
      :options="accountTree"
      :props="{
        label: language === 'en' ? 'name_en' : 'name_cn',
        value: 'code',
        code: 'code',
        group_id: 'fund_id',
        value_id: 'account_id',
        children: 'children',
      }"
      :group-id.sync="ac_fund_id"
      :select-group.sync="select_group"
      :show-all-option="true"
      :all-option-text="$t('components.accountTreeSelect.label.allAccount')"
      :style="{
        width: accountWidth + 'px',
      }"
      @change="onChangeAccount"
      @changeItem="onChangeAccountItem"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SelectTree from '@/components/SelectTree'
import { getAccountTree } from '@/api/master/account'
import { fetchFunds } from '@/api/master/funds'

export default {
  name: 'AccountTreeSelect',
  components: {
    SelectTree,
  },
  props: {
    fundId: {
      type: [Number, String],
      required: true,
    },
    acFundId: {
      type: [Number, String],
      required: true,
    },
    acCode: {
      type: String,
      required: true,
    },
    selectGroup: {
      type: Boolean,
      required: true,
    },
    onlyShowGroup: {
      type: Boolean,
      default: false,
    },
    fundWidth: {
      type: [Number, String],
      default: 'auto',
    },
    accountWidth: {
      type: [Number, String],
      default: 'auto',
    },
    natureCode: {
      type: [String],
      default: '',
    },
    acNameEn: {
      type: [String],
      default: '',
    },
    acNameCn: {
      type: [String],
      default: '',
    },
  },
  data() {
    return {
      fundList: [],
      accountTree: [],
      fund_id: '',
      ac_code: '',
      ac_fund_id: '',
      select_group: false,
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    fundId(val) {
      if (val !== this.fund_id && val > 0) {
        this.fund_id = val
        this.onChangeFund(val)
      }
    },
    fund_id(val) {
      // if (this.fundId && val !== this.fundId && val > 0) {
      this.$emit('update:fundId', val)
      this.onChangeFund(val)
      // }
      if (!val) {
        this.ac_code = ''
        this.ac_fund_id = ''
      }
    },

    acCode(val) {
      this.ac_code = !val ? '' : val
    },
    ac_code(val) {
      if (val !== this.acCode) {
        this.$emit('update:acCode', val)
      }
    },
    acFundId(val) {
      this.ac_fund_id = val
    },
    ac_fund_id(val) {
      if (val !== this.acFundId) {
        this.$emit('update:acFundId', val)
      }
    },
    selectGroup(val) {
      this.select_group = val
    },
    select_group(val) {
      if (val !== this.selectGroup) {
        this.$emit('update:selectGroup', val)
      }
    },
  },
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    init() {
      fetchFunds({ fund_type: 'F' }).then(res => {
        this.fundList = res
      })
    },
    onChangeFund(fund_id) {
      console.log('change fund_id', fund_id)
      if (!fund_id) {
        this.accountTree = []
        this.fund_id = ''
        this.ac_code = ''
        this.select_group = false
      } else {
        this.ac_fund_id = ''
        this.ac_code = ''
        this.select_group = false
        this.loadAccountTree(fund_id)
      }
      this.$emit('changeFund', fund_id)
    },
    formatAccountTree(array) {
      for (let i = 0; i < array.length; i++) {
        if (array[i].account_id) {
          delete array[i]
        } else {
          if (array[i].children) {
            if (array[i].children.length) {
              array[i].children = this.formatAccountTree(array[i].children)
            } else {
              delete array[i].children
            }
          }
        }
      }
      // for (let i = array.length - 1; i > 0; i--) {
      //   if (array[i] == null) {
      //     array = array.splice(i, 1)
      //   }
      // }
      return array
    },
    // 會計科目樹
    loadAccountTree(fund_id) {
      return new Promise((resolve, reject) => {
        getAccountTree(fund_id)
          .then(res => {
            if (this.onlyShowGroup) {
              this.accountTree = JSON.parse(JSON.stringify(this.formatAccountTree(res)))
            } else {
              this.accountTree = res
            }
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    onChangeAccount(res) {
      this.onChangeAccountItem(res)
    },
    onChangeAccountItem(res) {
      if (res === null || res === undefined) {
        this.$emit('update:natureCode', '')
        this.$emit('update:acNameEn', '')
        this.$emit('update:acNameCn', '')
        this.$emit('changeAccountItem', res)
      } else {
        const natureCode = res.nature_code || ''
        const acNameEn = res.name_en || ''
        const acNameCn = res.name_cn || ''
        this.$emit('update:natureCode', natureCode)
        this.$emit('update:acNameEn', acNameEn)
        this.$emit('update:acNameCn', acNameCn)
        this.$emit('changeAccountItem', res)
      }
    },
  },
}
</script>

<style scoped></style>
