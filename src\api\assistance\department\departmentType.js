import request from '@/utils/request'

/**
 * 返回新增后的部門類別id
 * @param {string} dept_type_code 部門類別編號
 * @param {string} dept_type_cn 部門類別中文
 * @param {string} dept_type_en 部門類別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_dept_type_id 父部門id
 * @param {integer} seq 所處層級里的位置
 */
export function createDepartmentType(
  dept_type_code,
  dept_type_cn,
  dept_type_en,
  active_year,
  parent_dept_type_id,
  seq,
) {
  return request({
    url: '/department-types/actions/create',
    method: 'post',
    data: {
      dept_type_code,
      dept_type_cn,
      dept_type_en,
      active_year,
      parent_dept_type_id,
      seq,
    },
  })
}

/**
 * 修改部門類別
 * @param {integer} dept_type_id 部門類別id
 * @param {string} dept_type_code 部門類別編號
 * @param {string} dept_type_cn 部門類別中文
 * @param {string} dept_type_en 部門類別英文
 * @param {string} active_year 活躍的會計週期編號,格式: 17,18,...
 * @param {integer} parent_dept_type_id 父部門id
 * @param {integer} seq 所處層級里的位置
 */
export function updateDepartmentType(
  dept_type_id,
  dept_type_code,
  dept_type_cn,
  dept_type_en,
  active_year,
  parent_dept_type_id,
  seq,
) {
  return request({
    url: '/department-types/actions/update',
    method: 'post',
    data: {
      dept_type_id,
      dept_type_code,
      dept_type_cn,
      dept_type_en,
      active_year,
      parent_dept_type_id,
      seq,
    },
  })
}

/**
 * 刪除職員類別
 * @param {integer} dept_type_id 部門類別id
 */
export function deleteDepartmentType(dept_type_id) {
  return request({
    url: '/department-types/actions/delete',
    method: 'post',
    data: {
      dept_type_id,
    },
  })
}

/**
 * 返回部門類別列表
 * @param {integer} dept_type_id 部門類別id(返回結果將會除此id)
 * @param {string} fy_code 選擇的會計週期
 */
export function fetchDepartmentTypes({ dept_type_id, fy_code }) {
  return request({
    url: '/department-types',
    method: 'get',
    params: {
      dept_type_id,
      fy_code,
    },
  })
}

/**
 * 獲取某部門類別詳情
 * @param {integer} dept_type_id 部門類別id
 */
export function getDepartmentType(dept_type_id) {
  return request({
    url: '/department-types/actions/inquire',
    method: 'get',
    params: {
      dept_type_id,
    },
  })
}

export default {
  createDepartmentType,
  updateDepartmentType,
  deleteDepartmentType,
  fetchDepartmentTypes,
  getDepartmentType,
}
