<template>
  <div>
    <!-- 簽名高度 -->
    <el-form-item :label="$t('setting.printout.label.signHeight')">
      <el-input-number v-model="sign_height" :min="0" :controls="false" />
      <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
    </el-form-item>
    <!-- 簽名間隔 -->
    <el-form-item :label="$t('setting.printout.label.signSpace')">
      <el-input-number v-model="sign_space" :min="0" :controls="false" />
      <span class="unit-tips">{{ $t('setting.printout.label.unitTips') }}</span>
    </el-form-item>
    <!-- 簽名樣式 -->
    <el-form-item :label="$t('setting.printout.label.signStyle')">
      <!-- 簽名位置 -->
      <el-select v-model="sign_style" :placeholder="$t('setting.printout.label.signStyle')">
        <el-option :label="$t('setting.printout.content.noSignature')" value="0" />
        <el-option :label="$t('setting.printout.content.floating')" value="1" />
        <el-option :label="$t('setting.printout.content.fixed')" value="2" />
      </el-select>
      <!-- 簽名項數 -->
      <el-select v-model="sign_num" :placeholder="$t('setting.printout.label.signNum')">
        <el-option
          v-for="i in 6"
          :key="i"
          :label="$t('setting.printout.content.signatureNumber_' + i)"
          :value="i"
        />
      </el-select>
      <!-- 簽名線格式 -->
      <el-select v-model="sign_line" :placeholder="$t('setting.printout.label.signLine')">
        <el-option
          v-for="i in 4"
          :key="i"
          :label="$t('setting.printout.content.line') + i"
          :value="i - 1"
        />
        <el-option :label="$t('setting.printout.content.box')" :value="4" />
      </el-select>
      <!-- 獲取簽名預設值 -->
      <!--            <el-button size="mini" type="info">{{ $t('setting.printout.button.defaultValue') }}</el-button>-->
      <br>
    </el-form-item>
    <!-- 簽名表格  -->
    <signTable :data.sync="sign_data" :col-length="sign_num" @setDefault="setDefault" />
  </div>
</template>

<script>
import signTable from './signTable'
export default {
  name: 'SignForm',
  components: { signTable },
  props: {
    signData: {
      type: Array,
      default: () => [],
    },
    signStyle: {
      type: [Number, String],
      default: 1,
    },
    signNum: {
      type: [Number, String],
      default: 3,
    },
    signLine: {
      type: [Number, String],
      default: 0,
    },
    signHeight: {
      type: [Number, String],
      default: 10,
    },
    signSpace: {
      type: [Number, String],
      default: 5,
    },
  },
  computed: {
    sign_style: {
      get() {
        return this.signStyle
      },
      set(val) {
        this.$emit('update:signStyle', val)
      },
    },
    sign_num: {
      get() {
        return Number(this.signNum)
      },
      set(val) {
        this.$emit('update:signNum', val)
      },
    },
    sign_line: {
      get() {
        return Number(this.signLine)
      },
      set(val) {
        this.$emit('update:signLine', val)
      },
    },
    sign_data: {
      get() {
        return this.signData
      },
      set(val) {
        this.$emit('update:signData', val)
      },
    },
    sign_height: {
      get() {
        return this.signHeight
      },
      set(val) {
        this.$emit('update:signHeight', val)
      },
    },
    sign_space: {
      get() {
        return this.signSpace
      },
      set(val) {
        this.$emit('update:signSpace', val)
      },
    },
  },
  methods: {
    setDefault() {
      this.$emit('setDefault')
    },
  },
}
</script>

<style scoped></style>
