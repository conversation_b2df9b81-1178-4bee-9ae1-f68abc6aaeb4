<template>
  <div class="LRPane">
    <split-pane
      ref="splitPane"
      :min-percent="30"
      :default-percent="defaultScale"
      split="vertical"
      @resize="resize"
    >
      <template slot="paneL">
        <div class="pane-left">
          <!-- 左側麵包屑 -->
          <div v-if="leftView" class="breadcrumb">
            <div>
              <el-breadcrumb separator="/">
                <el-breadcrumb-item v-for="item in $route.matched" :key="item.name">
                  {{ $t(item.meta.title) }}
                </el-breadcrumb-item>
                <el-breadcrumb-item>
                  {{ $t('view.' + leftView) }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
          </div>
          <!-- 左側內容 -->
          <slot class="pane-left-content" />
        </div>
      </template>
      <template slot="paneR" :min-percent="30">
        <div v-loading="loading" class="pane-right">
          <el-row :gutter="10">
            <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
              <!-- 右側麵包屑 -->
              <div class="breadcrumb">
                <el-breadcrumb separator="/">
                  <el-breadcrumb-item v-for="item in $route.matched" :key="item.name">
                    {{ $t(item.meta.title) }}
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </div>
            </el-col>
            <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
              <!-- 頁面設置按鈕 -->
              <div class="setting-box">
                <div
                  class="settingStyle"
                  :title="$t('btnTitle.pageSetting')"
                  @click="onShowSetting"
                >
                  <svg-icon icon-class="setting-full" class="setting-full action-icon" />
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
              <div class="action-bar">
                <div class="filters" @keyup.enter="handleFetch(true)">
                  <slot name="pane-right-filters">
                    <div>&nbsp;</div>
                  </slot>
                </div>
              </div>
            </el-col>

            <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
              <!-- 右側操作 -->
              <div class="pane-right-action">
                <slot name="pane-right-action" />
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <!-- 右側內容 -->
            <div class="pane-right-content">
              <slot name="pane-right-content" />
            </div>
          </el-row>
        </div>
      </template>
    </split-pane>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import splitPane from 'vue-splitpane'
import { editStaffPreference, editUserPreference } from '@/api/settings/user/preference'

export default {
  name: 'LRPane',
  components: { splitPane },
  props: {
    leftView: {
      type: String,
      default: '',
    },
    defaultScale: {
      type: Number,
      default: 50,
    },
    saveScale: {
      type: Function,
      default: () => {},
    },
    onShowSetting: {
      type: Function,
      default: () => {},
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      scale: this.defaultScale,
      isSave: false,
    }
  },
  computed: {
    ...mapGetters({ thisSystem: 'system', user_id: 'user_id' }),
  },
  mounted() {
    // this.$watch(
    //   () => {
    //     return this.$refs.splitPane.scale
    //   },
    //   (val) => {
    //     console.log(val)
    //     this.scale = val
    //   }
    // )
    this.$refs.splitPane.$el.children[1].onmouseup = this.save
    this.$refs.splitPane.$el.onmouseup = this.save
  },
  created() {
    this.saveUserLastPage()
  },
  methods: {
    save() {
      if (this.isSave) {
        this.saveScale(this.scale)
        this.isSave = false
      }
    },
    resize() {
      this.isSave = true
      this.scale = this.$refs.splitPane.percent
    },
    saveUserLastPage() {
      const user_id = this.user_id
      const system = this.thisSystem
      const content = {
        page: this.$route.name,
      }
      let api = editStaffPreference
      if (system === 'AC') {
        api = editUserPreference
      }
      const pf_code = system.toLowerCase() + '.home'
      api(user_id, system, pf_code, 'tab', content)
        .then(res => {})
        .catch(() => {})
    },
  },
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.LRPane {
  position: relative;
  height: 100%;
  .pane-left,
  .pane-right {
    padding: 20px;
    overflow-y: auto;
    height: 100%;
  }
}

.left-container {
  background-color: #f38181;
  height: 100%;
}

.pane-right-action {
  float: right;
  display: table;
  height: 25px;
}
.pane-right-content {
  padding: 10px 0;
  height: 100%;
  max-height: 450px;
  min-height: 300px;
}

.breadcrumb {
  margin: 0 0 10px 0;
  display: flex;
  justify-content: space-between;
}

.setting-box {
  float: right;
  .settingStyle {
    /*background:rgb(185,183,184);*/
    border-radius: 4px;
    /* padding: 0 2px; */
    font-size: 20px !important;
    cursor: pointer;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss">
.pane-right-action {
  div {
    position: relative;
    display: table-cell;
    vertical-align: middle;
    div {
      position: relative;
      display: flex;

      margin: 0 0 0px 5px;
    }
  }
  /*.icon{*/
  /*  background: #68AFFF;*/
  /*  border-radius: 4px;*/
  /*  font-size: 20px!important;*/
  /*  cursor: pointer;*/
  /*}*/
}

.pane-right-content {
  .operation_icon i {
    margin: 0 10px;
    cursor: pointer;
  }
}
.LRPane {
  /deep/ {
    .splitter-pane-resizer {
      opacity: 1 !important;
      background: #eaeaea !important;
      background-clip: padding-box !important;
    }
  }
  .el-breadcrumb {
    line-height: 20px;
  }
  .filters {
    input,
    i,
    .el-input__suffix {
      height: 25px !important;
      line-height: 25px !important;
    }
    .el-input__suffix {
      top: 0px;
    }
  }
}
</style>
