import request from '@/utils/request'

/**
 * 返回結餘轉賬列表
 * @param {string} fy_code 會計週期編號
 * @param {integer} fund_id 賬目類別(大類)
 * @return {Promise}
 */
export function fetchBalanceTransfer({ fy_code, fund_id }) {
  return request({
    url: '/cycle/balance-transfer',
    method: 'get',
    params: {
      fy_code,
      fund_id,
    },
  })
}
/**
 * 返回結餘轉賬列表 V2
 * @param {string} fy_code 會計週期編號
 * @param {integer} fund_id 賬目類別(大類)
 * @return {Promise}
 */
export function fetchBalanceTransferV2({ fy_code, fund_id }) {
  return request({
    url: '/cycle/balance-transfer/v2',
    method: 'get',
    params: {
      fy_code,
      fund_id,
    },
  })
}

/**
 * 更新結餘轉賬的期初調整
 * @param {string} fy_code 會計週期編號
 * @param {string} adjust_json 期初調整json(即使adj_amount=0都要傳),[{"account_id":"1","adj_amount":"100.00"},...]
 * @return {Promise}
 */
export function editBalanceTransfer({ fy_code, adjust_json }) {
  return request({
    url: '/cycle/balance-transfer/actions/update',
    method: 'post',
    data: {
      fy_code,
      adjust_json: JSON.stringify(adjust_json),
    },
  })
}
