<template>
  <div v-if="!hookVisible" class="setting-basic">
    <right>
      <el-main slot="content">
        <el-form ref="form" :model="form" class="form" label-width="120px">
          <el-form-item :label="$t('setting.basic.font_family')">
            <el-select
              v-model="form.font_family"
              :placeholder="$t('setting.basic.select_font_placeholder')"
            >
              <el-option
                v-for="item in fontFamilyList"
                :key="item.value"
                :label="item.title"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('setting.basic.font_size')">
            <el-input-number v-model="form.font_size" :min="10" :max="18" :controls="true" />
          </el-form-item>
          <!--          <el-form-item label="正常顏色">-->
          <!--            <el-color-picker v-model="form.color"/>-->
          <!--          </el-form-item>-->
          <el-row class="actions">
            <el-button size="mini" type="info" @click="onFetch(0)">
              {{ $t('button.loadDefault') }}
            </el-button>
            <el-button size="mini" type="info" @click="onSave(0)">
              {{ $t('button.saveDefault') }}
            </el-button>
            <el-button size="mini" type="danger" @click="onFetch(1)">
              {{ $t('button.reset') }}
            </el-button>
            <el-button size="mini" type="primary" @click="onSave(1)">
              {{ $t('button.update') }}
            </el-button>
          </el-row>
        </el-form>
      </el-main>
    </right>
  </div>
</template>

<script>
import right from '../right'
import common from './mixin'

export default {
  name: 'SettingScreenBasic',
  components: {
    right,
  },
  mixins: [common],
  data() {
    return {
      form: {
        font_family: 'Microsoft YaHei',
        color: '#000000',
        font_size: 12,
      },

      ss_code: 'screen_basic_basic',
      ss_type: 'basic',
    }
  },
  computed: {},
  created() {},
  methods: {},
}
</script>
