<template>
  <div class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="100px"
    >
      <!-- 賬目編號 -->
      <el-form-item
        :rules="codeRules"
        :label="$t('master.account.label.ac_code')"
        prop="fund_code"
        :class="{ 'code-rules-error': haveExist }"
      >
        <el-input v-model="form.fund_code" clearable />
      </el-form-item>
      <!-- 中文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.account.label.ac_name_cn')"
        prop="fund_name_cn"
      >
        <el-input v-model="form.fund_name_cn" clearable />
      </el-form-item>
      <!-- 英文名稱 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.account.label.ac_name_en')"
        prop="fund_name_en"
      >
        <el-input v-model="form.fund_name_en" clearable />
      </el-form-item>
      <!-- 上級 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.account.label.parent_fund_id')"
        prop="parent_fund_id"
      >
        <el-select
          v-model="form.parent_fund_id"
          :placeholder="$t('placeholder.select')"
          @change="changeParentType"
        >
          <el-option
            v-for="item in accountTypes"
            :key="item.value"
            :value="item.fund_id"
            :label="conversionParentAccountType(item)"
            v-html="conversionParentAccountType(item, true)"
          />
        </el-select>
      </el-form-item>

      <!-- 位置(前置) -->
      <el-form-item :label="$t('master.account.label.seq')" prop="seq">
        <el-select v-model="form.seq" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in sepOptions"
            :key="item.fund_id"
            :label="language === 'en' ? item.name_en : item.name_cn"
            :value="item.fund_id ? item.fund_id : item.account_id"
          >
            {{ language === 'en' ? item.name_en : item.name_cn }}
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 賬目性質 -->
      <el-form-item
        :rules="rules"
        :label="$t('master.account.label.nature_code')"
        prop="nature_code"
      >
        <el-select v-model="form.nature_code" :placeholder="$t('placeholder.select')">
          <el-option
            v-for="item in natureOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <!-- {{ language === 'en' ? item.name_en : item.name_cn }} -->
          </el-option>
        </el-select>
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('master.account.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 收入預算 -->
      <el-form-item :label="$t('master.account.label.income_bg_year')">
        <el-checkbox-group v-model="income_bg_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- 支出預算 -->
      <el-form-item :label="$t('master.account.label.expense_bg_year')">
        <el-checkbox-group v-model="expense_bg_year_arr" @click="forceUpdate">
          <el-checkbox v-for="item in years" :key="item.fy_id" :label="item.fy_code">
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editObject ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getAccountTreeNode } from '@/api/master/account'
import { createFund, editFund, getFund, fetchFunds } from '@/api/master/funds'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'MasterFundAdd',
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    editParent: {
      type: Object,
      default: null,
    },
    defaultParent: {
      type: Number,
      default: null,
    },
    fyCode: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        fund_id: '',
        fund_code: '',
        fund_name_cn: '', // 賬目中文全稱
        fund_name_en: '', // 賬目英文全稱
        fund_abbr_cn: '', // 賬目中文簡稱
        fund_abbr_en: '', // 賬目英文簡稱
        nature_code: '', // 賬目性質
        fund_type: 'G', // 賬目類別為G
        parent_fund_id: '', // 父賬目類別id
        active_year: '',
        income_bg_year: '',
        expense_bg_year: '',
        seq: '', // 所處層級的位置
      },
      defaultForm: {
        fund_id: '', // 賬目類別id
        fund_code: '', // 賬目類別編號
        fund_name_cn: '', // 賬目中文全稱
        fund_name_en: '', // 賬目英文全稱
        fund_abbr_cn: '', // 賬目中文簡稱
        fund_abbr_en: '', // 賬目英文簡稱
        nature_code: '', // 賬目性質
        fund_type: 'G', // 賬目類別為G
        parent_fund_id: '', // 父賬目類別id
        active_year: '', // 活躍年份
        income_bg_year: '',
        expense_bg_year: '',
        seq: '', // 所處層級的位置
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      defaultSepOptions: [
        {
          // 初始位置
          fund_id: '',
          account_id: '',
          name_cn: this.$t('master.fund.label.seq_default'),
          name_en: this.$t('master.fund.label.seq_default'),
        },
      ],
      sepOptions: [
        {
          // 位置
          fund_id: '',
          account_id: '',
          name_cn: this.$t('master.fund.label.seq_default'),
          name_en: this.$t('master.fund.label.seq_default'),
        },
      ],

      yearsOptions: [
        {
          value: 2019,
          label: '2019',
        },
      ],
      years: [],
      defaultAccountTypes: [
        {
          fund_id: '',
          fund_name_cn: this.$t('master.fund.label.parent_default'),
          fund_name_en: this.$t('master.fund.label.parent_default'),
        },
      ],
      accountTypes: [],
      seqStartLevel: 1,
      active_year_arr: [],
      income_bg_year_arr: [],
      expense_bg_year_arr: [],
      loading: true,
      haveExist: false,
    }
  },
  computed: {
    ...mapGetters(['language']),

    natureOptions() {
      return [
        {
          value: 'G',
          label: this.$t('master.account.natureOptions.option_G'),
        },
        {
          value: 'I/E',
          label: this.$t('master.account.natureOptions.option_IE'),
        },
        {
          value: 'I',
          label: this.$t('master.account.natureOptions.option_I'),
        },
        {
          value: 'E',
          label: this.$t('master.account.natureOptions.option_E'),
        },
        {
          value: 'Dr',
          label: this.$t('master.account.natureOptions.option_Dr'),
        },
        {
          value: 'Cr',
          label: this.$t('master.account.natureOptions.option_Cr'),
        },
      ]
    },
    codeRules() {
      return [
        {
          required: true,
          validator: this.checkCode,
          trigger: 'blur',
        },
      ]
    },
  },
  watch: {
    editObject() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
    editParent() {
      this.$refs['form'].clearValidate()
      this.haveExist = false
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    conversionYear(year) {
      return '20' + year.fy_code
    },
    conversionParentAccountType(accountType, html, startLevel = 1) {
      // 轉換父賬戶類型
      let text = this.language === 'en' ? accountType.fund_name_en : accountType.fund_name_cn
      if (html) {
        text = '&nbsp;'.repeat((accountType.level - 1 - startLevel + 1) * 4) + text
      }
      return text
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    changeParentType() {
      if (!this.form.parent_fund_id) {
        this.form.seq = ''
        return
      }
      getAccountTreeNode('', this.form.parent_fund_id, this.form.fund_id, '').then(res => {
        this.sepOptions = [...res, ...this.defaultSepOptions]
        if (this.editObject && this.form.seq_i) {
          let isLast = true
          for (let i = 0; i < res.length; i++) {
            if (res[i].seq > this.form.seq_i) {
              isLast = false
              this.form.seq = res[i].fund_id ? res[i].fund_id : res[i].account_id
              break
            }
          }
          if (isLast) {
            this.form.seq = ''
          }
        } else {
          this.form.seq = ''
        }
      })
    },
    checkRequired(rule, value, callback) {},
    setParent() {
      if (this.editParent && this.editParent.fund_id) {
        this.form.parent_fund_id = this.editParent.fund_id
      } else if (this.editObject && this.editObject.parent && this.editObject.parent.fund_id) {
        this.form.parent_fund_id = this.editObject.parent.fund_id
        // this.form.seq = this.editObject.parent.seq
      } else {
        this.form.parent_fund_id = this.defaultParent
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getFund(this.editObject.fund_id)
            .then(res => {
              this.form = Object.assign({}, res)
              this.setParent()
              this.form.parent_fund_id =
                res.fundAccountRelation && res.fundAccountRelation.parent_fund_id
                  ? res.fundAccountRelation.parent_fund_id
                  : ''
              this.form.seq_i = res.fundAccountRelation ? res.fundAccountRelation.seq : ''
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              this.income_bg_year_arr = res.income_bg_year
                ? res.income_bg_year.split(',')
                : ''.split(',')
              this.expense_bg_year_arr = res.expense_bg_year
                ? res.expense_bg_year.split(',')
                : ''.split(',')
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.setParent()
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => getFund(this.defaultParent))
        .then(res => {
          this.defaultAccountTypes = [
            {
              fund_id: this.defaultParent,
              fund_name_cn: res.fund_name_cn,
              fund_name_en: res.fund_name_en,
            },
          ]
        })
        .then(() =>
          fetchFunds({
            fund_type: 'G',
            parent_fund_id: this.defaultParent,
            fund_id: this.form.fund_id,
          }),
        )
        .then(res => {
          this.accountTypes = [...this.defaultAccountTypes, ...res]
          this.changeParentType(this.form.parent_fund_id)
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editObject) {
            const selected = res.find(i => i.fy_code === this.fyCode)
            if (selected) {
              this.active_year_arr.push(selected.fy_code)
              this.income_bg_year_arr.push(selected.fy_code)
              this.expense_bg_year_arr.push(selected.fy_code)
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    newSeq() {
      let seq = 1
      const lastSeq = () => {
        if (this.sepOptions && this.sepOptions.length > 1) {
          return this.sepOptions[this.sepOptions.length - 2].seq + 1
        } else {
          return 1
        }
      }
      if (this.form.seq) {
        if (this.sepOptions.length > 1) {
          const seq_ele = this.sepOptions.find(i => {
            const key = i.fund_id ? 'fund_id' : 'account_id'
            return i[key] === this.form.seq
          })
          if (seq_ele) {
            seq = seq_ele.seq
          } else {
            seq = lastSeq()
          }
        }
        // else { seq = 1 }
      } else {
        seq = lastSeq()
      }
      return seq
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }
        const fund_id = this.form.fund_id
        const fund_code = this.form.fund_code
        const parent_fund_id = this.form.parent_fund_id
        const fund_name_cn = this.form.fund_name_cn
        const fund_name_en = this.form.fund_name_en
        const fund_abbr_cn = this.form.fund_name_cn
        const fund_abbr_en = this.form.fund_name_en
        const fund_type = 'G'
        const nature_code = this.form.nature_code
        const active_year = this.active_year_arr.filter(i => i).join(',')
        const income_bg_year = this.income_bg_year_arr.filter(i => i).join(',')
        const expense_bg_year = this.expense_bg_year_arr.filter(i => i).join(',')

        const seq = this.newSeq()

        if (this.editObject) {
          // 編輯
          editFund(
            fund_id,
            fund_code,
            fund_name_cn,
            fund_name_en,
            fund_abbr_cn,
            fund_abbr_en,
            fund_type,
            active_year,
            nature_code,
            income_bg_year,
            expense_bg_year,
            parent_fund_id,
            seq,
          )
            .then(() => {
              this.$message.success(this.$t('message.modifySuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {
              // this.$message.err(err)
            })
        } else {
          // 新增
          createFund(
            fund_code,
            fund_name_cn,
            fund_name_en,
            fund_abbr_cn,
            fund_abbr_en,
            fund_type,
            active_year,
            nature_code,
            income_bg_year,
            expense_bg_year,
            parent_fund_id,
            seq,
          )
            .then(() => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    checkCode(rule, value, callback) {
      console.log('checkCode', value)
      if (!value) {
        console.log('checkCode', !value)
        this.haveExist = false
        callback(new Error(' '))
        return
      }
      console.log('this.tableData', this.tableData)
      const codeArr = this.getCodeArr(this.tableData)
      console.log(
        'codeArr',
        codeArr,
        codeArr.some(i => i === value),
        value,
      )
      if (codeArr.some(i => i === value)) {
        this.haveExist = true
        callback(new Error(this.$t('master.voucher_type.rules.isExist')))
      } else {
        this.haveExist = false
        console.log('checkCode', value)
        callback()
      }
    },
    getCodeArr(arr) {
      const codeArr = []
      arr.forEach(i => {
        if (this.editObject && this.editObject.code === i.code) {
          return
        } else {
          codeArr.push(i.code)
        }
        if (i.children) {
          codeArr.push(...this.getCodeArr(i.children))
        }
      })
      return codeArr
    },
  },
}
</script>

<style lang="scss" scoped>
.code-rules-error {
  margin-bottom: 20px !important;
}
</style>
