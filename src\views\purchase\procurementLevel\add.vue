<template>
  <div v-if="showPage" ref="page" class="fundInfo">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      label-position="right"
      label-width="120px"
    >
      <!-- 採購等級編號 -->
      <el-form-item :rules="rules" :label="t('pro_level_code')" prop="pro_level_code">
        <el-input v-model="form.pro_level_code" clearable />
      </el-form-item>
      <!-- 描述 中 -->
      <el-form-item :rules="rules" :label="t('pro_level_name_cn')" prop="pro_level_name_cn">
        <el-input v-model="form.pro_level_name_cn" clearable />
      </el-form-item>
      <!-- 描述 英 -->
      <el-form-item :rules="rules" :label="t('pro_level_name_en')" prop="pro_level_name_en">
        <el-input v-model="form.pro_level_name_en" clearable />
      </el-form-item>
      <!-- 編號規則 -->
      <el-form-item
        :rules="proNoRules"
        :label="t('pro_no_format')"
        :class="proNoError ? 'pro-no-item-error' : ''"
        prop="pro_no_format"
      >
        <el-input v-model="form.pro_no_format" clearable />
      </el-form-item>
      <!-- 金額範圍 -->
      <div class="amount_range">
        <!-- 最小 -->
        <el-form-item
          :rules="rules"
          :label="t('amount_range')"
          prop="min_amount"
          class="amount_range_min"
        >
          <el-input-number
            v-model="form.min_amount"
            :controls="false"
            :min="0"
            clearable
            @change="formatNumberMin"
          />
        </el-form-item>
        <span>-</span>
        <!-- 最大 -->
        <el-form-item :rules="maxRules" prop="max_amount" class="amount_range_max">
          <el-input-number
            v-model="form.max_amount"
            :controls="false"
            :min="maxMin"
            clearable
            @change="formatNumberMax"
          />
        </el-form-item>
      </div>
      <!-- 供應商個數 -->
      <el-form-item
        :rules="suppliersRules"
        :label="t('suppliers_at_least')"
        class="suppliers_at_least"
        prop="suppliers_at_least"
      >
        <el-input-number v-model="form.suppliers_at_least" :min="0" clearable />
      </el-form-item>
      <!-- 邀請信 -->
      <el-form-item :label="t('invitation_letter_code')" prop="invitation_letter_code">
        <el-select v-model="form.invitation_letter_code">
          <el-option label="-" value="" />
          <el-option
            v-for="item in invitation_letter_list"
            :key="item.pro_letter_id"
            :label="item[language === 'en' ? 'pro_letter_name_en' : 'pro_letter_name_cn']"
            :value="item.pro_letter_code"
          />
        </el-select>
      </el-form-item>
      <!-- 不接納邀請信 -->
      <el-form-item :label="t('reject_letter_code')" prop="reject_letter_code">
        <el-select v-model="form.reject_letter_code">
          <el-option label="-" value="" />
          <el-option
            v-for="item in reject_letter_list"
            :key="item.pro_letter_id"
            :label="item[language === 'en' ? 'pro_letter_name_en' : 'pro_letter_name_cn']"
            :value="item.pro_letter_code"
          />
        </el-select>
      </el-form-item>
      <!-- 接納邀請信 -->
      <el-form-item :label="t('accept_letter_code')" prop="accept_letter_code">
        <el-select v-model="form.accept_letter_code">
          <el-option label="-" value="" />
          <el-option
            v-for="item in accept_letter_list"
            :key="item.pro_letter_id"
            :label="item[language === 'en' ? 'pro_letter_name_en' : 'pro_letter_name_cn']"
            :value="item.pro_letter_code"
          />
        </el-select>
      </el-form-item>
      <!-- 覆核 -->
      <el-form-item :rules="rules" :label="t('review')" prop="review">
        <el-checkbox v-model="form.review" true-label="Y" false-label="N" />
      </el-form-item>
      <!-- 覆核設定 -->
      <el-form-item v-if="form.review === 'Y'" :label="t('reviewSetting')" prop="review_handlers">
        <procurement-level-select
          v-if="showPage && form.review_handlers"
          :data.sync="form.review_handlers"
          :can-change-type="true"
          :staffs="staffs"
        />
      </el-form-item>
      <!-- 審批 -->
      <el-form-item :rules="rules" :label="t('approve')" prop="approve">
        <el-checkbox v-model="form.approve" true-label="Y" false-label="N" />
      </el-form-item>
      <!-- 審批設定 -->
      <el-form-item
        v-if="form.approve === 'Y'"
        :label="t('approveSetting')"
        prop="approve_handlers"
      >
        <procurement-level-select
          v-if="showPage && form.approve_handlers"
          :data.sync="form.approve_handlers"
          :can-change-type="true"
          :staffs="staffs"
        />
      </el-form-item>
      <!-- 必須填寫邀請少於限定的供應商個數原因 -->
      <el-form-item :rules="rules" prop="reason_for_suppliers">
        <el-checkbox v-model="form.reason_for_suppliers" true-label="Y" false-label="N">
          {{ t('reason_for_suppliers') }}
        </el-checkbox>
      </el-form-item>
      <!-- 必須填寫不選擇最低報價原因 -->
      <el-form-item :rules="rules" prop="reason_for_low_price">
        <el-checkbox v-model="form.reason_for_low_price" true-label="Y" false-label="N">
          {{ t('reason_for_low_price') }}
        </el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          <!--          {{ editObject ? $t('button.edit') : $t('button.add') }}-->
          {{ $t('button.' + view) }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  createProcurementLevel,
  editProcurementLevel,
  getProcurementLevel,
} from '@/api/purchase/procurementLevels'
import ProcurementLevelSelect from '@/views/purchase/procurementLevel/level'
import { fetchStaffs } from '@/api/assistance/staff'
import { fetchProcurementLetters } from '@/api/purchase/procurementLetters'
import { toDecimal } from '@/utils'

export default {
  name: 'MasterProcurementLevelAdd',
  components: { ProcurementLevelSelect },
  props: {
    editObject: {
      type: Object,
      default: null,
    },
    defaultAccount: {
      type: String,
      default: '',
    },
    fyCode: {
      type: String,
      default: '',
    },
    view: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: {
        pro_level_id: '',
        pro_level_code: '',
        pro_level_name_cn: '',
        pro_level_name_en: '',
        pro_no_format: '',
        min_amount: 0,
        max_amount: '',
        suppliers_at_least: '',
        invitation_letter_code: '',
        reject_letter_code: '',
        accept_letter_code: '',
        reason_for_suppliers: 'Y',
        reason_for_low_price: 'Y',
        review: 'Y',
        approve: 'Y',
        review_handlers: [],
        approve_handlers: [],
      },
      defaultForm: {
        pro_level_id: '',
        pro_level_code: '',
        pro_level_name_cn: '',
        pro_level_name_en: '',
        pro_no_format: '',
        min_amount: 0,
        max_amount: '',
        suppliers_at_least: '',
        invitation_letter_code: '',
        reject_letter_code: '',
        accept_letter_code: '',
        reason_for_suppliers: 'Y',
        reason_for_low_price: 'Y',
        review: 'Y',
        approve: 'Y',
        review_handlers: [
          [
            {
              type: 'F',
              mandatory: 'Y',
              staff_id: '',
              auto: 'N',
            },
          ],
        ],
        approve_handlers: [
          [
            {
              type: 'F',
              mandatory: 'Y',
              staff_id: '',
              auto: 'N',
            },
          ],
        ],
      },
      rules: [
        {
          // 必填
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
      proNoRules: [{ required: true, validator: this.validateProNo, trigger: ['blur', 'change'] }],
      years: [],
      funds: [],
      accounts: [],
      active_year_arr: [],
      BIE_arr: [],
      account_arr: [],
      selectAccountList: [],
      loading: true,
      dialogVisible: false,
      treeProps: {
        children: 'children',
        label: 'name_cn',
      },
      fund_id: '',

      langKey: 'purchase.master.procurementLevel.',
      staffs: [],
      invitation_letter_list: [],
      reject_letter_list: [],
      accept_letter_list: [],

      showPage: true,
      proNoError: false,
    }
  },
  computed: {
    ...mapGetters(['language', 'currentYear']),
    BIE_list() {
      return ['B', 'I', 'E'].map(i => {
        return {
          label: i,
          value: i,
        }
      })
    },
    maxMin() {
      return toDecimal(Number(this.form.min_amount) + 1)
    },
    maxRules() {
      return this.view === 'view'
        ? []
        : [{ validator: this.validateMax, message: ' ', trigger: ['blur', 'change'] }]
    },
    suppliersRules() {
      return this.view === 'view'
        ? []
        : [{ validator: this.validateSuppliers, message: ' ', trigger: ['blur', 'change'] }]
    },
  },
  watch: {
    editObject() {
      this.initData()
    },
    view() {
      if (this.editObject) {
        this.initData()
      }
    },
  },
  created() {
    this.initData()
  },
  methods: {
    t(key) {
      return this.$t(this.langKey + key)
    },
    validateProNo(rule, value, callback) {
      if (!value) {
        this.proNoError = false
        callback(new Error(' '))
      } else if (!value.includes('#')) {
        this.proNoError = true
        callback(new Error(this.t('rules.proNoRequired')))
      } else {
        this.proNoError = false
        callback()
      }
    },
    validateMax(rule, value, callback) {
      const err = new Error(' ')
      if (value === undefined || value === null || value === '') {
        callback(err)
      } else {
        const val = Number(value)
        if (val < 0 || val < this.maxMin) {
          callback(err)
        } else {
          callback()
        }
      }
    },
    validateSuppliers(rule, value, callback) {
      const val = Number(value)
      if (value && (val === 0 || val < 1)) {
        callback(new Error(' '))
      } else {
        callback()
      }
    },
    forceUpdate() {
      this.$forceUpdate()
      this.showPage = false
      this.$nextTick(() => {
        this.showPage = true
      })
    },
    checkRequired(rule, value, callback) {},
    nameAdd(name, type = 1) {
      const reg = type === 1 ? new RegExp(/(.*)\((\d+)\)$/) : new RegExp(/(.*)-(\d+)$/)
      const nameReg = name.match(reg)
      if (nameReg) {
        if (type === 1) {
          return nameReg[1] + '(' + (Number(nameReg[2]) + 1) + ')'
        } else {
          return nameReg[1] + '-' + (Number(nameReg[2]) + 1)
        }
      } else {
        if (type === 1) {
          return name + '(1)'
        } else {
          return name + '-1'
        }
      }
    },
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editObject) {
          // 編輯
          getProcurementLevel(this.editObject.pro_level_id)
            .then(res => {
              if (this.view === 'copy') {
                const newItem = JSON.parse(JSON.stringify(res))
                newItem.pro_level_id = ''
                newItem.pro_level_name_cn = '' // this.nameAdd(newItem.pro_level_name_cn)
                newItem.pro_level_name_en = '' // this.nameAdd(newItem.pro_level_name_en)
                newItem.pro_level_code = ''
                newItem.min_amount = ''
                newItem.max_amount = ''
                this.form = newItem
              } else {
                const defaultForm = JSON.parse(JSON.stringify(this.defaultForm)) // Object.assign({}, this.defaultForm)
                this.form = Object.assign(defaultForm, res)
              }
              // this.forceUpdate()
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          resolve()
        }
      })
    },
    initData() {
      this.loading = true
      this.initForm()
        .then(() => fetchStaffs({}))
        .then(res => {
          this.staffs = res
        })
        .then(() => fetchProcurementLetters('I'))
        .then(res => {
          this.invitation_letter_list = res
        })
        .then(() => fetchProcurementLetters('R'))
        .then(res => {
          this.reject_letter_list = res
        })
        .then(() => fetchProcurementLetters('A'))
        .then(res => {
          this.accept_letter_list = res
        })
        .finally(() => {
          this.loading = false
        })
    },
    onSave() {
      this.$refs['form'].validate((valid, a) => {
        if (!valid) {
          try {
            const field = a[Object.keys(a)[0]][0].field
            this.$refs.page.parentNode.scrollTop =
              document.querySelector('[for="' + field + '"]').offsetTop - 20
          } catch (e) {
            //
          }
          return false
        }

        const pro_level_id = this.form.pro_level_id
        const pro_level_code = this.form.pro_level_code
        const pro_level_name_cn = this.form.pro_level_name_cn
        const pro_level_name_en = this.form.pro_level_name_en
        const pro_no_format = this.form.pro_no_format
        const min_amount = this.form.min_amount
        const max_amount = this.form.max_amount
        const suppliers_at_least = this.form.suppliers_at_least
        const invitation_letter_code = this.form.invitation_letter_code || ''
        const reject_letter_code = this.form.reject_letter_code || ''
        const accept_letter_code = this.form.accept_letter_code || ''
        const reason_for_suppliers = this.form.reason_for_suppliers
        const reason_for_low_price = this.form.reason_for_low_price
        const review = this.form.review
        const review_handlers_json = JSON.stringify(
          this.form.review_handlers.map(row => {
            return row.map(item => ({
              type: item.staff_id ? 'S' : 'F',
              mandatory: 'Y',
              staff_id: item.staff_id || null,
              auto: item.auto,
            }))
          }),
        )
        const approve = this.form.approve
        const approve_handlers_json = JSON.stringify(
          this.form.approve_handlers.map(row => {
            return row.map(item => ({
              type: item.staff_id ? 'S' : 'F',
              mandatory: 'Y',
              staff_id: item.staff_id || null,
              auto: item.auto,
            }))
          }),
        )

        if (this.editObject && pro_level_id) {
          editProcurementLevel({
            pro_level_id,
            pro_level_code,
            pro_level_name_cn,
            pro_level_name_en,
            pro_no_format,
            min_amount,
            max_amount,
            suppliers_at_least,
            invitation_letter_code,
            reject_letter_code,
            accept_letter_code,
            reason_for_suppliers,
            reason_for_low_price,
            review,
            review_handlers_json,
            approve,
            approve_handlers_json,
          })
            .then(() => {
              this.$message.success(this.$t('message.editSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        } else {
          // 新增
          createProcurementLevel({
            pro_level_code,
            pro_level_name_cn,
            pro_level_name_en,
            pro_no_format,
            min_amount,
            max_amount,
            suppliers_at_least,
            invitation_letter_code,
            reject_letter_code,
            accept_letter_code,
            reason_for_suppliers,
            reason_for_low_price,
            review,
            review_handlers_json,
            approve,
            approve_handlers_json,
          })
            .then(() => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            })
            .catch(() => {})
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    formatNumber(val) {
      return val.replace(/[^0-9\.]/g, '')
    },
    formatNumberMin(val) {
      const newVal = this.formatNumber(val)
      if (newVal !== val) {
        this.form.min_amount = newVal
        this.$refs.form.validateField('min_amount')
      }
    },
    formatNumberMax(val) {
      const newVal = this.formatNumber(val)
      if (newVal !== val) {
        this.form.max_amount = newVal
        this.$refs.form.validateField('max_amount')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.account-item {
  width: 100%;
  text-align: left;
}
#multi-ac-code /deep/ .el-form-item__content {
  height: auto !important;
}
.pro-no-item-error {
  margin-bottom: 20px !important;
}
/deep/ {
  .el-input-number {
    .el-input-number__decrease,
    .el-input-number__increase {
      top: 5px;
    }
    .el-input-number__decrease,
    .el-input-number__increase {
      top: 2px !important;
      height: 28px !important;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .amount_range {
    white-space: nowrap;
    .amount_range_max,
    .amount_range_min {
      display: inline-block;
      width: auto;
      .el-input-number--medium {
        width: 150px;
        input {
          text-align: left;
        }
      }
    }

    .amount_range_min {
      .el-form-item__content {
        margin-right: 10px !important;
      }
    }

    .amount_range_max {
      .el-form-item__content {
        margin-left: 10px !important;
      }
    }
  }
  .suppliers_at_least {
    .el-input-number--medium {
      width: 150px;
    }
  }
}
</style>
