import Layout from '@/views/layout/Layout'

const masterRouter = {
  path: '/master',
  component: Layout,
  redirect: 'noredirect',
  name: 'master',
  meta: {
    title: 'router.master',
    p_code: 'ac.master',
    icon: 'mainArchives',
  },
  children: [
    {
      path: 'fund',
      component: () => import('@/views/master/funds/index.vue'),
      name: 'masterFund',
      meta: {
        title: 'router.masterFund',
        p_code: 'ac.master.fund',
        noCache: true,
      },

      children: [
        {
          path: 'add',
          component: () => import('@/views/master/funds/add.vue'),
          name: 'masterFundAdd',
          meta: {
            title: 'router.masterFund',
            p_code: 'ac.master.fund',
            noCache: true,
          },
        },
      ],
    },
    {
      path: 'account',
      component: () => import('@/views/master/account/index.vue'),
      name: 'masterAccount',
      meta: {
        title: 'router.masterAccount',
        p_code: 'ac.master.account',
        noCache: true,
      },
    },
    {
      path: 'voucher_type',
      component: () => import('@/views/master/voucherType/index.vue'),
      name: 'masterVoucherType',
      meta: {
        title: 'router.masterVoucherType',
        p_code: 'ac.master.voucher_type',
        noCache: true,
      },
    },
    {
      path: 'user',
      component: () => import('@/views/master/user/index.vue'),
      name: 'masterUser',
      meta: {
        title: 'router.masterUser',
        p_code: 'ac.master.user',
        noCache: true,
      },
    },
    {
      path: 'role_set',
      component: () => import('@/views/master/role/index.vue'),
      name: 'masterRoleSet',
      meta: {
        title: 'router.masterRoleSet',
        p_code: 'ac.master.role_set',
        noCache: true,
      },
    },
    {
      path: 'ac_period',
      component: () => import('@/views/master/years/index.vue'),
      name: 'masterACPeriod',
      meta: {
        title: 'router.masterACPeriod',
        p_code: 'ac.master.ac_period',
        noCache: true,
      },
    },
  ],
}

export default masterRouter
