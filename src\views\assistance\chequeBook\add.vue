<template>
  <div class="fundInfo">
    <el-form ref="form" :model="form" label-position="right" label-width="100px">
      <!-- 撥款 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.chequeBook.label.fund_id')"
        prop="fund_id"
      >
        <el-select v-model="form.fund_id">
          <el-option
            v-for="item in fundtypes"
            :key="item.fund_id"
            :label="item.fund_name_cn"
            :value="item.fund_id"
          />
        </el-select>
      </el-form-item>
      <!-- 銀行 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.chequeBook.label.bank_code')"
        prop="vt_code"
      >
        <el-select v-model="form.vt_code" style="width: 100%">
          <el-option
            v-for="item in voucherTypes"
            :key="item.value"
            :label="item.vt_description_cn"
            :value="item.vt_code"
          />
        </el-select>
      </el-form-item>
      <!-- 編號 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.chequeBook.label.chqbk_code')"
        prop="chqbk_code"
      >
        <el-input v-model="form.chqbk_code" clearable />
      </el-form-item>
      <!-- 從 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.chequeBook.label.chqbk_from')"
        prop="chqbk_from"
      >
        <el-input
          v-model="form.chqbk_from"
          clearable
          @change="onChangeChqbkFrom"
          @input.native="onInputChqbkFrom"
          @keydown.native="onCodeKeyDown"
        />
        <!--         @change="onChangeCode" @keydown.native="onCodeKeyDown" -->
      </el-form-item>
      <!-- 至 -->
      <el-form-item
        :rules="rules"
        :label="$t('assistance.chequeBook.label.chqbk_to')"
        prop="chqbk_to"
      >
        <el-input
          v-model="form.chqbk_to"
          clearable
          @change="onChangeChqbkTo"
          @input.native="onInputChqbkTo"
          @keydown.native="onCodeKeyDown"
        />
        <!--        @paste.native.capture.preven="onPaste"-->
      </el-form-item>
      <!-- 活躍年度 -->
      <el-form-item :label="$t('assistance.chequeBook.label.active_year')">
        <el-checkbox-group v-model="active_year_arr" @click="forceUpdate">
          <el-checkbox
            v-for="item in years"
            :key="item.fy_id"
            :label="item.fy_code"
            :value="item.fy_code"
          >
            {{ conversionYear(item) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" type="primary" @click="onSave">
          {{ editCheque ? $t('button.edit') : $t('button.add') }}
        </el-button>
        <el-button size="mini" @click="onCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { createChequeBook, updateChequeBook, getChequeBook } from '@/api/assistance/chequeBook'
import { fetchFunds } from '@/api/master/funds'
import { fetchVoucherTypes } from '@/api/master/voucherType'
import { fetchYears } from '@/api/master/years'

export default {
  name: 'AssistanceChequeBookAdd',
  props: {
    editCheque: {
      type: Object,
      default: null,
    },
    defaultSelectedFundType: {
      type: [String, Number],
      default: '',
    },
    defaultSelectedVoucherTypeCode: {
      type: [String, Number],
      default: '',
    },
    defaultSelectedYear: {
      type: [String, Number],
      default: '',
    },
    fyCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      system: 'AC',
      years: [],
      active_year_arr: [],
      fundtypes: [],
      selectedFundType: '',
      voucherTypes: [],
      selectedVoucherType: '',
      form: {
        cheque_book_id: '',
        fund_id: '',
        vt_code: '',
        chqbk_code: '',
        chqbk_from: '',
        chqbk_to: '',
      },
      defaultForm: {
        cheque_book_id: '',
        fund_id: '',
        vt_code: '',
        chqbk_code: '',
        chqbk_from: '',
        chqbk_to: '',
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
          message: ' ',
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['language']),
  },
  watch: {
    editCheque() {
      this.initData()
    },
    'form.chqbk_from'(newVal, oldVal) {
      if (oldVal === newVal) return
      const n = oldVal.replace(/\D/g, '')
      if (n !== oldVal) {
        setTimeout(() => {
          this.$set(this.form, 'chqbk_from', n)
        }, 10)
      }
    },
    'form.chqbk_to'(newVal, oldVal) {
      if (oldVal === newVal) return
      const n = newVal.replace(/\D/g, '')
      if (n !== newVal) {
        setTimeout(() => {
          this.$set(this.form, 'chqbk_to', n)
        }, 10)
      }
    },
  },
  created() {
    this.initData()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    conversionYear(year) {
      return '20' + year.fy_code
    },
    checkRequired(rule, value, callback) {},
    initForm() {
      return new Promise((resolve, reject) => {
        if (this.editCheque) {
          // 編輯
          // this.form = Object.assign({}, this.editObject)
          getChequeBook(this.editCheque.cheque_book_id)
            .then(res => {
              this.form = Object.assign({}, res)
              this.active_year_arr = res.active_year ? res.active_year.split(',') : ''.split(',')
              resolve()
            })
            .catch(err => {
              reject(err)
            })
        } else {
          // 新增
          this.form = Object.assign({}, this.defaultForm)
          this.form.fund_id = this.defaultSelectedFundType
          this.form.vt_code = this.defaultSelectedVoucherTypeCode
          // this.active_year_arr.push(this.defaultSelectedYear)
          resolve()
        }
      })
    },
    initData() {
      this.initForm()
        .then(() => fetchFunds({ fund_type: 'F' }))
        .then(res => {
          this.fundtypes = res
        })
        .then(fetchYears)
        .then(res => {
          this.years = res
          if (!this.editCheque && res.length && this.fyCode) {
            const year = res.find(i => i.fy_code === this.fyCode)
            if (year) this.active_year_arr.push(year.fy_code)
          }
        })
        .then(() =>
          fetchVoucherTypes({
            fund_id: this.selectedFundType,
            vt_category: 'P', // 只關聯付款傳票
            fy_code: this.selectedYear,
          }),
        )
        .then(res => {
          this.voucherTypes = res
        })
      // ???
      // if (this.editCheque) {
      //   // 編輯
      //   this.initForm().then(
      //     this.form = Object.assign({}, this.editCheque) // init活躍年份
      //   )
      // } else {
      //   // 新增
      //   this.form = Object.assign({}, this.defaultForm)
      // }
    },
    onSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return false
        }

        const cheque_book_id = this.form.cheque_book_id
        const fund_id = this.form.fund_id
        const vt_code = this.form.vt_code
        const chqbk_code = this.form.chqbk_code
        const chqbk_from = this.form.chqbk_from
        const chqbk_to = this.form.chqbk_to
        const active_year = this.active_year_arr.filter(i => i).join(',')
        if (this.editCheque) {
          // 編輯
          updateChequeBook(
            cheque_book_id,
            fund_id,
            vt_code,
            chqbk_code,
            chqbk_from,
            chqbk_to,
            active_year,
          ).then(res => {
            this.$message.success(this.$t('message.modifySuccess'))
            this.$emit('onCancel', true)
          })
        } else {
          // 新增
          createChequeBook(fund_id, vt_code, chqbk_code, chqbk_from, chqbk_to, active_year).then(
            res => {
              this.$message.success(this.$t('message.addSuccess'))
              this.$emit('onCancel', true)
            },
          )
        }
      })
    },
    onCancel() {
      this.$emit('onCancel')
    },
    onChangeChqbkFrom(val) {
      const n = val.replace(/\D/g, '')
      if (n !== val) {
        this.form.chqbk_from = n
      }
    },
    onChangeChqbkTo(val) {
      const n = val.replace(/\D/g, '')
      if (n !== val) {
        this.form.chqbk_to = n
      }
    },
    onCodeKeyDown(e) {
      const keyCode = e.keyCode
      if (
        (keyCode >= 48 && keyCode <= 57) ||
        (keyCode >= 96 && keyCode <= 105) ||
        [8, 9, 46, 17, 37, 38, 39, 40].includes(keyCode) ||
        ([65, 67, 86, 88].includes(keyCode) && e.ctrlKey)
      ) {
        if (e.key === 'Process' || (e.key.length === 1 && !e.ctrlKey && !e.key.match(/^\d$/))) {
          e.preventDefault()
          return false
        }
      } else {
        e.preventDefault()
        return false
      }
    },
    onInputChqbkFrom(e) {
      this.onChangeChqbkFrom(e.target.value || '')
    },
    onInputChqbkTo(e) {
      this.onChangeChqbkTo(e.target.value || '')
    },
  },
}
</script>

<style scoped></style>
