import request from '@/utils/request'

/**
 * 返回相關類型查詢結果
 * @param {string} type A=所有,Y=全年,T=年度截至,M=當月
 * @param {string} fy_code 週期編號
 * @param {string} pd_code 週期月份編號
 * @param {string} contra_code 對沖編號
 * @param {string} ac_B 資產類，負債類，儲備金類,可填N或Y
 * @param {string} ac_I 收入類,可填N或Y
 * @param {string} ac_E 支出類,可填N或Y
 * @return {Promise}
 */
export function enquiryContra({ type, fy_code, pd_code, contra_code, ac_B, ac_I, ac_E }) {
  return request({
    url: '/enquiry/contra',
    method: 'get',
    params: {
      type,
      fy_code,
      pd_code,
      contra_code,
      ac_B,
      ac_I,
      ac_E,
    },
  })
}
